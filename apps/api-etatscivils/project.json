{"name": "api-etatscivils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api-etatscivils/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "sourceMap": true, "outputPath": "dist/apps/api-etatscivils", "main": "apps/api-etatscivils/src/main.ts", "tsConfig": "apps/api-etatscivils/tsconfig.app.json", "assets": ["apps/api-etatscivils/src/assets", {"glob": "**/*", "input": "libs/crossplatform/api-etatscivils/openapi", "output": "assets"}], "webpackConfig": "apps/api-etatscivils/webpack.config.js"}, "configurations": {"production": {"generatePackageJson": true, "optimization": true, "extractLicenses": true, "inspect": false, "production": true}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "api-etatscivils:build"}, "configurations": {"development": {"buildTarget": "api-etatscivils:build:development"}, "production": {"buildTarget": "api-etatscivils:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/api-etatscivils"], "cache": true, "dependsOn": ["prisma-codegen", "^prisma-codegen", {"projects": ["backend-shared-prisma-infra"], "target": "prisma-setup-testing"}], "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"jestConfig": "apps/api-etatscivils/jest.config.ts", "testPathIgnorePatterns": [".*\\.scheduled\\.spec\\.ts"], "testPathPattern": [".*\\.spec\\.ts"]}}}, "tags": ["platform:backend", "scope:api-etatscivils", "type:app"]}