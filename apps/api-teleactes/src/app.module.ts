import { Module } from '@nestjs/common';

import { provideDemandeCopieDocumentScope } from '@mynotary/backend/demande-copie-document/providers';
import { DemandeCopieDocumentController } from '@mynotary/backend/demande-copie-document/feature';
import { provideTeleactesScope } from '@mynotary/backend/teleactes/providers';
import { ScheduleModule } from '@nestjs/schedule';
import { provideFeatureOrganizationsScope } from '@mynotary/backend/feature-organizations/providers';
import { provideUsersScope } from '@mynotary/backend/users/providers';
import { provideEmailsScope } from '@mynotary/backend/emails/providers';
import { provideThemesScope } from '@mynotary/backend/themes/providers';
import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { OpenapiValidatorModule } from '@mynotary/backend/shared/openapi-validator-infra';
import { join } from 'path';
import { AnfController } from '@mynotary/backend/anf/feature';
import { provideAnfScope } from '@mynotary/backend/anf/providers';
import { provideSecretsScope } from '@mynotary/backend/secrets/providers';
import { provideFilesClientScope } from '@mynotary/backend/files-client/providers';
import { RequisitionController } from '@mynotary/backend/requisitions/feature';
import { provideRequisitionScope } from '@mynotary/backend/requisitions/providers';
import { TeleactesDispatcherController } from '@mynotary/backend/teleactes-dispatcher/feature';
import { provideTeleactesDispatcherScope } from '@mynotary/backend/teleactes-dispatcher/providers';
import { provideOrganizationHoldingsScope } from '@mynotary/backend/organization-holdings/providers';
import { provideDefaultRecordsScope } from '@mynotary/backend/default-records/providers';
import { provideExternalAppsScope } from '@mynotary/backend/external-apps/providers';
import { provideAuthenticationsScope } from '@mynotary/backend/authentications/providers';
import { provideAuthorizationsScope } from '@mynotary/backend/authorizations/providers';
import { provideAsyncTasksScope } from '@mynotary/backend/async-tasks/providers';
import { providePlaneteScope } from '@mynotary/backend/planete/providers';
import { provideLegalsScope } from '@mynotary/backend/legals/providers';
import { provideDrivesScope } from '@mynotary/backend/drives/providers';
import { provideNotificationsScope } from '@mynotary/backend/notifications/providers';
import { provideLegacyJavaScope } from '@mynotary/backend/legacy-java/providers';
import { provideDataSyncScope } from '@mynotary/backend/data-sync/providers';
import { provideReferentielsScope } from '@mynotary/backend/referentiels/providers';
import { provideMembersScope } from '@mynotary/backend/members/providers';
import { providePdfClientScope } from '@mynotary/backend/pdf-client/providers';
import { provideOrganizationsScope } from '@mynotary/backend/organizations/providers';
import { provideRolesScope } from '@mynotary/backend/roles/providers';
import { provideFeaturesScope } from '@mynotary/backend/features/providers';
import { provideCustomViewsScope } from '@mynotary/backend/custom-views/providers';
import { provideOperationAccessScope } from '@mynotary/backend/operation-access/providers';
import { provideOperationAccessUpdateScope } from '@mynotary/backend/operation-access-update/providers';
import { provideEventsScope } from '@mynotary/backend/events/providers';

@Module({
  controllers: [DemandeCopieDocumentController, RequisitionController, AnfController, TeleactesDispatcherController],
  imports: [
    OpenapiValidatorModule.forRoot({
      apiSpecPath: join(__dirname, 'assets/api-teleactes.openapi.yaml')
    }),
    ScheduleModule.forRoot()
  ],
  providers: [
    ...provideAnfScope(),
    ...provideAsyncTasksScope(),
    ...provideAuthenticationsScope(),
    ...provideAuthorizationsScope(),
    ...provideDataSyncScope(),
    ...provideDefaultRecordsScope(),
    ...provideDemandeCopieDocumentScope(),
    ...provideDrivesScope(),
    ...provideEmailsScope(),
    ...provideEventsScope(),
    ...provideFeatureOrganizationsScope(),
    ...provideFilesClientScope(),
    ...provideLegacyJavaScope(),
    ...provideLegalsScope(),
    ...provideMembersScope(),
    ...provideNotificationsScope(),
    ...provideOrganizationHoldingsScope(),
    ...providePdfClientScope(),
    ...providePlaneteScope(),
    ...provideTeleactesScope(),
    ...provideReferentielsScope(),
    ...provideRequisitionScope(),
    ...provideSecretsScope(),
    ...provideTeleactesDispatcherScope(),
    ...provideThemesScope(),
    ...provideUsersScope(),
    ...provideOrganizationsScope(),
    ...provideExternalAppsScope(),
    ...provideRolesScope(),
    ...provideFeaturesScope(),
    ...provideCustomViewsScope(),
    ...provideOperationAccessScope(),
    ...provideOperationAccessUpdateScope(),
    PrismaService
  ]
})
export class AppModule {}
