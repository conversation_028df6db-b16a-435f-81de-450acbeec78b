export function groupProjects(projects: string[]): string[][] {
  const maxGroups = 10; // Nombre maximum de groupes
  const minGroupSize = 5; // Taille minimale par groupe
  const totalProjects = projects.length;

  if (totalProjects === 0) {
    return [];
  }

  if (totalProjects <= minGroupSize) {
    return [projects];
  }

  let numGroups = Math.floor(totalProjects / minGroupSize);

  if (numGroups > maxGroups) {
    numGroups = Math.min(maxGroups, numGroups);
  }

  const groupSize = Math.ceil(totalProjects / numGroups);

  const groups: string[][] = Array.from({ length: numGroups }, (_, groupIndex) => {
    const startIndex = groupIndex * groupSize;
    const endIndex = Math.min(startIndex + groupSize, totalProjects);
    return projects.slice(startIndex, endIndex);
  });

  return groups;
}
