{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "emitDecoratorMetadata": true, "target": "es2021", "strict": true, "moduleDetection": "force", "module": "Preserve", "resolveJsonModule": true, "allowJs": true, "esModuleInterop": true, "isolatedModules": true, "types": []}, "include": ["src/**/*.ts"], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}