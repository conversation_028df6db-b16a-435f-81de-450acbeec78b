{"name": "front-portalys-companion", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/front-portalys-companion/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"compiler": "swc", "outputPath": "dist/apps/front-portalys-companion", "index": "apps/front-portalys-companion/src/index.html", "baseHref": "/", "main": "apps/front-portalys-companion/src/main.tsx", "tsConfig": "apps/front-portalys-companion/tsconfig.app.json", "assets": ["apps/front-portalys-companion/src/favicon.ico", {"glob": "**/*", "input": "apps/front-portalys-companion/src/assets", "output": "/assets"}, {"glob": "**/*", "input": "libs/frontend/assets/ui/", "output": "./assets/"}], "stylePreprocessorOptions": {"includePaths": ["apps/front-portalys-companion/src"]}, "styles": ["apps/front-portalys-companion/src/style/_main.scss"], "scripts": [], "webpackConfig": "apps/front-portalys-companion/webpack.config.js"}, "configurations": {"development": {"commonChunk": false, "extractLicenses": false, "fileReplacements": [{"replace": "libs/frontend/shared/environments-util/environment.ts", "with": "apps/front-portalys-companion/src/environment/environment.ts"}], "optimization": false, "runtimeChunk": false, "sourceMap": true, "vendorChunk": false, "baseHref": "./"}, "production": {"fileReplacements": [{"replace": "libs/frontend/shared/environments-util/environment.ts", "with": "apps/front-portalys-companion/src/environment/environment.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "baseHref": "./"}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "front-portalys-companion:build", "hmr": true, "proxyConfig": "apps/front-portalys-companion/proxy.conf.json"}, "configurations": {"development": {"buildTarget": "front-portalys-companion:build:development", "baseHref": "/", "port": 4260}, "production": {"buildTarget": "front-portalys-companion:build:production", "baseHref": "./", "hmr": false}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "stylelint": {"cache": true, "executor": "nx:run-commands", "inputs": ["{projectRoot}/**/*.scss", "{workspaceRoot}/.stylelintrc.json"], "options": {"commands": ["pnpm stylelint {projectRoot}/**/*.scss"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/front-portalys-companion/jest.config.ts", "testPathIgnorePatterns": [".*\\.scheduled\\.spec\\.ts"], "testPathPattern": [".*\\.spec\\.ts"]}}}, "tags": ["platform:frontend", "scope:front-portalys-companion", "type:app"]}