mod anf_queue;
mod backend;
mod systray;
pub mod utils;

use crate::backend::Backend;
use log;
use std::env;
use std::sync::Mutex;
use tauri::{Manager, WindowEvent};

#[derive(Default)]
pub struct AppState {
    anf_listening: bool,
    operation_id: Option<String>,
    planete_dossier_id: Option<String>,
    user_id: Option<String>,
}

// Commands accessible from front-portalys-companion
//

#[tauri::command]
fn set_user_data(app_handle: tauri::AppHandle, user_id: &str, token: &str) {
    log::info!("Setting user data: user_id: {}, token: {}", user_id, token);
    env::set_var("PORTALYS_USER_ID", user_id);
    env::set_var("PORTALYS_TOKEN", token);
    // now all necessary env vars are set, backend may be started
    Backend::init(&app_handle);
    // close main window
    app_handle
        .get_webview_window("main")
        .unwrap()
        .close()
        .unwrap();

    // update user_id in state
    let state = app_handle.state::<Mutex<AppState>>();
    let mut state = state.lock().unwrap();
    state.user_id = Some(user_id.to_string());
}

// App initialization
#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_shell::init())
        .setup(|app| {
            app.manage(Mutex::new(AppState::default()));
            systray::init_systray(app).expect("failed to init systray");

            let handle = app.handle().clone();
            anf_queue::listen(handle);
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![set_user_data])
        .on_window_event(|window, event| match event {
            WindowEvent::CloseRequested { api, .. } => {
                window.hide().unwrap();
                api.prevent_close();
            }
            _ => {}
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application")
}
