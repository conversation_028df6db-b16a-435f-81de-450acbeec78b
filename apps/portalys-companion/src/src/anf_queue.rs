use crate::utils::local_queue::LocalQueue;
use crate::AppState;
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use std::str::FromStr;
use std::sync::Mutex;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, WindowEvent};
use url::Url;
use base64::prelude::*;

pub fn listen(handle: AppHandle) {
    let handle_dl = handle.clone();

    let dl = handle_dl.path().download_dir().unwrap();
    let dl_ref = dl.as_os_str().to_str().unwrap();
    LocalQueue::watch_dir(String::from(dl_ref), move |downloaded_file_path| {
        handle_download(&handle_dl, downloaded_file_path);
    });

    LocalQueue::watch(
        String::from("ANF_STOCK_IN"),
        move |id, message: &SendAnfStockOpenEvent| {
            log::debug!("ANF_STOCK_IN id: {}", id);
            handle_incoming_message(&handle, &message);
        },
    );
}

fn handle_incoming_message(handle: &AppHandle, message: &&SendAnfStockOpenEvent) {
    log::debug!("ANF_STOCK_IN content: {:?}", message);

    let anf_win = handle.get_webview_window("anf").unwrap();
    anf_win
        .navigate(Url::from_str(&message.url).unwrap())
        .expect("failed to navigate to ANF_STOCK_IN");
    anf_win.show().unwrap();
    let handle_close = handle.clone();
    anf_win.on_window_event(move |event| {
        log::debug!("ANF_STOCK_IN event: {:?}", event);
        if let WindowEvent::CloseRequested { .. } = event {
            let state = handle_close.state::<Mutex<AppState>>();
            let mut state = state.lock().unwrap();
            state.anf_listening = false;
            state.operation_id = None;
            state.planete_dossier_id = None;
        }
    });
    let state = handle.state::<Mutex<AppState>>();
    let mut state = state.lock().unwrap();
    state.operation_id = message.operation_id.clone();
    state.planete_dossier_id = message.planete_dossier_id.clone();
    state.user_id = Some(message.user_id.clone());
    state.anf_listening = true;
}

fn handle_download(handle: &AppHandle, downloaded_file_path: &PathBuf) {
    // check if the file is a pdf file
    if let Some(extension) = downloaded_file_path.extension() {
        let file_name = downloaded_file_path.file_name().unwrap().to_str().unwrap();
        if extension.to_ascii_lowercase() == "pdf"
            && (file_name.to_ascii_lowercase().contains("_anf_")
                || file_name.to_ascii_lowercase().contains("_ana_"))
        {
            // looks like the PDF we were waiting for
            // close the window
            let anf_win = handle.get_webview_window("anf").unwrap();
            anf_win.hide().unwrap();

            // get stored data from state
            let state = handle.state::<Mutex<AppState>>();
            let mut state = state.lock().unwrap();
            if !state.anf_listening {
                return; // actually we weren't waiting for anything
            }

            // read the file as a base64 string

            // build the response and send it back
            match fs::read(downloaded_file_path) {
                Ok(content_binary) => {
                    let content = BASE64_STANDARD.encode(content_binary);
                    let response = SendAnfStockOpenResponse {
                        content,
                        filename: file_name.to_string(),
                        operation_id: state.operation_id.clone(),
                        planete_dossier_id: state.planete_dossier_id.clone(),
                        user_id: state.user_id.clone().unwrap(),
                    };
                    send_response(response);

                    // update state
                    state.anf_listening = false;
                    state.operation_id = None;
                    state.planete_dossier_id = None;
                }
                Err(error) => {
                    log::error!("failed to read downloaded file {:?} : {:?}", downloaded_file_path, error);
                },
            };

        }
    }
}

fn send_response(response: SendAnfStockOpenResponse) {
    LocalQueue::push(String::from("ANF_STOCK_OUT"), None, response)
        .expect("failed to push ANF_STOCK_OUT");
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
struct SendAnfStockOpenEvent {
    operation_id: Option<String>,
    planete_dossier_id: Option<String>,
    recipient_id: Option<String>,
    url: String,
    user_id: String,
}

#[derive(Serialize, Deserialize, Debug)]
#[serde(rename_all = "camelCase")]
struct SendAnfStockOpenResponse {
    content: String,
    filename: String,
    operation_id: Option<String>,
    planete_dossier_id: Option<String>,
    user_id: String,
}
