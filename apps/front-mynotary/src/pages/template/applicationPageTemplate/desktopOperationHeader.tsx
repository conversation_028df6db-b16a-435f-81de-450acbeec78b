import './operationHeader.scss';
import React, { ReactElement } from 'react';
import { MnButton } from '@mynotary/frontend/shared/ui';
import { MnSvg } from '@mynotary/frontend/shared/ui';
import { useSelector } from 'react-redux';
import { HeaderCredit } from '@mynotary/frontend/billings/feature';
import { HeaderHelp } from '@mynotary/frontend/front-mynotary-header/feature';
import { selectOperation, selectTemplate } from '@mynotary/frontend/legals/store';
import { useOperationIdFromParams } from '@mynotary/frontend/routes/feature';
import { NotificationIcon } from '@mynotary/frontend/notifications/feature';
import { LegalTemplate } from '@mynotary/crossplatform/legal-templates/core';

interface DesktopOperationHeaderProps {
  onRedirectToOperation: () => void;
  returnLabel: string;
  rightComponent?: ReactElement;
}

export const DesktopOperationHeader = ({
  onRedirectToOperation,
  returnLabel,
  rightComponent
}: DesktopOperationHeaderProps): ReactElement => {
  const operationId = useOperationIdFromParams();
  const operation = useSelector(selectOperation(operationId));
  const template = useSelector(selectTemplate<LegalTemplate>(operation?.template.id));

  const operationLabel = operation?.label ?? '';
  const operationType = template?.label ?? '';

  return (
    <>
      <div className='rh-flex'>
        <MnButton className='rh-button' label={returnLabel} onClick={onRedirectToOperation} variant='secondary-green' />
      </div>
      <button className='rh-button' onClick={onRedirectToOperation}>
        <MnSvg path='/assets/images/pictos/icon/folder-light.svg' size='small' variant='black' />
        <span
          className='rh-operation-type'
          data-testid='desktop-operation-header-operation-label'
        >{`${operationType} : ${operationLabel}`}</span>
      </button>
      <div className='rh-flex'>
        <NotificationIcon className='rh-notification' testId='icon-notifications' />
        <HeaderCredit />
        <HeaderHelp />
        {rightComponent}
      </div>
    </>
  );
};
