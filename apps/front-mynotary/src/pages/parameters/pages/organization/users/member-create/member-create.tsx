import { getPlanConfig } from '@mynotary/crossplatform/billings/core';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { selectCurrentSubscription } from '@mynotary/frontend/billings/store';
import { routePaths } from '@mynotary/frontend/routes/api';
import { MnButton, MnTooltip } from '@mynotary/frontend/shared/ui';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import { selectPermission } from '@mynotary/frontend/roles/store';

interface MemberCreateProps {
  remainingLicencesCount: number;
}

export const MemberCreate = ({ remainingLicencesCount }: MemberCreateProps) => {
  const navigate = useNavigate();
  const canCreateUsers = useSelector(
    selectPermission(PermissionType.CREATE_ORGANIZATION_MEMBERS, EntityType.ORGANIZATION)
  );
  const subscription = useSelector(selectCurrentSubscription);

  const handleAddClick = () => {
    navigate(routePaths.parameters.organization.users.creation.path);
  };

  const canAddInfiniteUsers = subscription?.planType
    ? getPlanConfig(subscription.planType).actions.includes('ADD_INFINITE_USERS')
    : false;

  const canAddMember = remainingLicencesCount > 0 || canAddInfiniteUsers;

  if (!canCreateUsers) {
    return null;
  }

  return (
    <MnTooltip
      content="Le nombre de licences dont vous disposez est épuisé. Pour ajouter un utilisateur, vous devez d'abord modifier votre abonnement."
      disabled={canAddMember}
    >
      <MnButton disabled={!canAddMember} label='Ajouter des utilisateurs' onClick={handleAddClick} />
    </MnTooltip>
  );
};
