import './parametersOrganizationManagementRegister.scss';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { isEmpty } from 'lodash';
import React, { ReactElement, useState } from 'react';
import { useSelector } from 'react-redux';
import { Route } from 'react-router-dom';
import { MnRouteSwitch } from '@mynotary/frontend/routes/api';
import { RegisterTable } from '../common/table/registerTable';
import { Form } from '@mynotary/frontend/legals/feature';
import { MnButton, MnSidePopin } from '@mynotary/frontend/shared/ui';
import { computeProgression, mergeAndCopyAnswer } from '@mynotary/frontend/legals/core';
import { FilesService } from '@mynotary/frontend/files/core';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { classNames, useGlobalLoader } from '@mynotary/frontend/shared/util';
import { routePaths } from '@mynotary/frontend/routes/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { exportRegister, selectCurrentManagementRegister } from '@mynotary/frontend/registers/store';
import { ManagementRegisterSetup } from '@mynotary/frontend/registers/feature';
import { Navigate, useNavigate } from 'react-router';
import {FormQuestions} from "@mynotary/crossplatform/shared/forms-util";

const form: FormQuestions = [
  {
    choices: [
      {
        id: 'pdf',
        label: 'PDF'
      },
      {
        id: 'csv',
        label: 'CSV'
      }
    ],
    id: 'export-format',
    label: 'Format',
    type: 'SELECT'
  }
];

const defaultAnswer = {
  'export-format': {
    value: 'pdf'
  }
};

const ParametersOrganizationManagementRegister = (): ReactElement => {
  const filesService = useService(FilesService);
  const navigate = useNavigate();
  const dispatch = useAsyncDispatch();
  const [setIsLoading] = useGlobalLoader(false);
  const [customizationPopin, setCustomizationPopin] = useState(false);
  const [answer, setAnswer] = useState<AnswerDict>(defaultAnswer);
  const { mandatoryFilled, mandatoryTotal } = computeProgression(form, answer);
  const managementRegister = useSelector(selectCurrentManagementRegister);

  const handleSetupRedirection = (): void => {
    navigate(routePaths.parameters.organization.register.management.setup.path);
  };

  const handleRegisterExportClick = (): void => {
    setCustomizationPopin(true);
  };

  const handleAnswerChanged = (update: AnswerDict): void => {
    setAnswer(mergeAndCopyAnswer(answer, update));
  };

  const handleRegisterExport = (): void => {
    if (!isEmpty(answer)) {
      setIsLoading(true);
      dispatch(
        exportRegister('MANAGEMENT', {
          format: answer['export-format'].value
        })
      )
        .then((file) => filesService.displayFile(file))
        .finally(() => {
          setIsLoading(false);
          setCustomizationPopin(false);
          setAnswer(defaultAnswer);
        });
    }
  };

  return (
    <div className={classNames('parameters-organization-management-register')}>
      <MnRouteSwitch>
        <Route
          element={<ManagementRegisterSetup />}
          path={routePaths.parameters.organization.register.management.setup.relativePath}
        />
        <Route
          element={
            <RegisterTable
              mode='MANAGEMENT'
              onExport={handleRegisterExportClick}
              onSetup={handleSetupRedirection}
              start={managementRegister?.config?.initialValue}
            />
          }
          path={routePaths.parameters.organization.register.management.table.relativePath}
        />
        <Route
          element={
            <Navigate
              replace
              to={
                managementRegister?.config != null
                  ? routePaths.parameters.organization.register.management.table.path
                  : routePaths.parameters.organization.register.management.setup.path
              }
            />
          }
          path={'/*'}
        />
      </MnRouteSwitch>
      <MnSidePopin isOpen={customizationPopin} onClose={() => setCustomizationPopin(false)}>
        {customizationPopin && (
          <div className='porr-customization-popin'>
            <h4 className='porr-popin-title'>Personnalisez votre export</h4>
            <Form answer={answer} forms={form} onChange={handleAnswerChanged} />
            <MnButton
              className='porr-popin-button'
              disabled={mandatoryFilled !== mandatoryTotal}
              label='Exporter'
              onClick={handleRegisterExport}
            />
          </div>
        )}
      </MnSidePopin>
    </div>
  );
};

export { ParametersOrganizationManagementRegister };
