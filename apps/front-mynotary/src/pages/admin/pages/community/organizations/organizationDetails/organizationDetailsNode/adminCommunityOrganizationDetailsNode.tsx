import './adminCommunityOrganizationDetailsNode.scss';
import { classNames, MnProps, openInNewTab, useParams } from '@mynotary/frontend/shared/util';
import React, { ReactElement, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { map } from 'lodash';
import { MnAccordion, MnActionPopover, MnConfirmationPopin, MnTable } from '@mynotary/frontend/shared/ui';
import {
  getFullNameWithCivility,
  getOrganizationUserStatusLabel,
  getOrganizationUserStatusOrder
} from 'helpers/userHelpers';
import { deleteOrganization, deleteOrganizationUser } from 'features/admin/admin.api';
import { reverse, routePaths } from '@mynotary/frontend/routes/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { AdminOrganization, organizationConfig } from '@mynotary/frontend/organizations/core';
import { fns } from '@mynotary/crossplatform/shared/dates-util';
import { useNavigate } from 'react-router';
import { selectAdminOrganizations } from '@mynotary/frontend/organizations/store';
import { useOrganizationActions } from '@mynotary/frontend/organizations/feature';
import { isMemberWithUser, Member } from '@mynotary/crossplatform/members/api';

interface OrganizationDetailsNodeProps extends MnProps {
  organization: AdminOrganization;
  selectedOrganizationId: number;
}

const searchPredicate = (text: string, data?: string): boolean => {
  return !!data?.toLowerCase().includes(text.toLowerCase());
};

const AdminCommunityOrganizationDetailsNode = ({
  className,
  organization,
  selectedOrganizationId
}: OrganizationDetailsNodeProps): ReactElement | null => {
  const dispatch = useAsyncDispatch();
  const navigate = useNavigate();
  const { organizationId } = useParams();
  const organizations = useSelector(selectAdminOrganizations);
  const [deletedUser, setDeletedUser] = useState<Member>();
  const { deletingOrganization, getOrganizationActions, setDeletingOrganization } = useOrganizationActions({
    canDelete: true,
    organizationId: organization.id
  });

  const infos = useMemo(() => {
    if (!organization) {
      return [];
    }
    return [
      {
        label: 'Id',
        value: `${organization.id}`
      },
      {
        label: 'Type',
        value: organization.type
      },
      {
        label: 'Nom',
        value: organization.name
      },
      {
        label: 'Adresse',
        value: organization.address?.formattedAddress ?? ''
      },
      {
        label: 'Clé API',
        value: organization.apiKey ?? ''
      },
      ...organizationConfig[organization.type]
        .getUniqueIdentifiers(organization.uniqueIdentifier)
        .map((identifier) => ({
          label: identifier.label,
          value: identifier.value
        }))
    ];
  }, [organization]);

  const users = useMemo(
    () => ({
      heads: [
        { id: 'name', label: 'Nom' },
        { id: 'email', label: 'E-mail' },
        { id: 'registration', label: `Date d'inscription` },
        { id: 'status', label: `Statut` },
        { id: 'role', label: `Rôle` }
      ],
      rows: map(organization?.users, (member) => {
        const creationTime = isMemberWithUser(member)
          ? fns.format(new Date(member.userCreationTime), 'dd MMM yyyy')
          : '';
        return {
          actions: [
            {
              icon: '/assets/images/pictos/icon/edit-2.svg',
              id: 'edit',
              label: 'Modifier',
              onClick: () => {
                navigate(
                  reverse(routePaths.admin.community.organizations.organization.editUser.path, {
                    orgaUserId: member.id,
                    organizationId: organization.id
                  })
                );
              }
            },
            {
              icon: '/assets/images/pictos/icon/trash-light.svg',
              id: 'delete',
              label: 'Supprimer',
              onClick: () => setDeletedUser(member)
            }
          ],
          columns: [
            { data: getFullNameWithCivility(member), id: 'name' },
            { data: member.email, id: 'email' },
            { data: creationTime, id: 'registration' },
            { data: getOrganizationUserStatusLabel(member), id: 'status' },
            { data: member.roleName ?? 'Aucun rôle', id: 'role' }
          ],
          order: getOrganizationUserStatusOrder(member)
        };
      }).sort((a, b) => {
        return b.order - a.order;
      }),
      searchPredicate: {
        email: searchPredicate,
        name: searchPredicate,
        role: searchPredicate,
        status: searchPredicate
      }
    }),
    [organization, navigate]
  );

  const handleDeleteOrgaUser = (memberId?: string): void => {
    if (memberId) {
      dispatch(deleteOrganizationUser(organization.id, parseInt(memberId))).then(() => {
        setDeletedUser(undefined);
      });
    }
  };

  return (
    <div
      className={classNames('admin-community-organization-details-node', className, {
        selected: selectedOrganizationId === organization?.id
      })}
    >
      {infos.map((info) => (
        <div className='acodn-info' key={info.label}>
          <span className='acodn-info-label'>{info.label}</span> :{' '}
          <span className='acodn-info-value'>{info.value}</span>
        </div>
      ))}
      {organization?.subscriptionId && (
        <div className='acodn-info acodn-subscripton'>
          <span className='acodn-info-label'>Abonnement Zoho : </span>
          <div
            className='acodn-info-link'
            onClick={() =>
              navigate(`${routePaths.admin.community.subscription.path}?id=${organization?.subscriptionId}`)
            }
          >
            Accéder à l'abonnement
          </div>
        </div>
      )}
      {organization?.hubspotId && (
        <div className='acodn-info acodn-subscripton'>
          <span className='acodn-info-label'>Lien Hubspot : </span>
          <div
            className='acodn-info-link'
            onClick={() =>
              openInNewTab(`https://app.hubspot.com/contacts/2169654/record/0-3/${organization.hubspotId}`)
            }
          >
            Accéder à hubspot
          </div>
        </div>
      )}
      <MnActionPopover actions={getOrganizationActions(organization)} className='acodn-actions' />
      <MnAccordion
        className='acodn-accordion'
        header={() => <div className={classNames('acodn-header-accordion')}>{'Utilisateurs'}</div>}
        opened={false}
      >
        <MnTable className='acodn-users' table={users} />
      </MnAccordion>
      {map(organization?.childrenOrganizations, (subOrganization) => (
        <AdminCommunityOrganizationDetailsNode
          key={subOrganization}
          organization={organizations[subOrganization]}
          selectedOrganizationId={selectedOrganizationId}
        />
      ))}
      <MnConfirmationPopin
        cancel='Annuler'
        content='Supprimer cet utilisateur de son organisation ?'
        onCancel={() => setDeletedUser(undefined)}
        onValidate={() => handleDeleteOrgaUser(deletedUser?.id)}
        opened={!!deletedUser}
        validate='Confirmer'
      />
      <MnConfirmationPopin
        content='Souhaitez-vous supprimer cet organisation? Les sous-organisations de celle-ci seront aussi supprimées.'
        onCancel={() => setDeletingOrganization(false)}
        onValidate={() =>
          dispatch(deleteOrganization(organization.id)).then(() => {
            if (parseInt(organizationId ?? '') === organization.id) {
              navigate(routePaths.admin.community.organizations.path);
            }
          })
        }
        opened={deletingOrganization}
        validate='supprimer'
      />
    </div>
  );
};

export { AdminCommunityOrganizationDetailsNode };
