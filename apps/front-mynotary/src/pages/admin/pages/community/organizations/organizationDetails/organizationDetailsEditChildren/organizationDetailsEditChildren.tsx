import './organizationDetailsEditChildren.scss';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { MnButton } from '@mynotary/frontend/shared/ui';
import { MnTitle } from '@mynotary/frontend/shared/ui';
import { addOrganizationChild, removeOrganizationChild, getOrganizationNetwork } from 'features/admin/admin.api';
import { isEmpty, forEach } from 'lodash';
import { ReactElement, useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from '@mynotary/frontend/shared/util';
import { reverse, routePaths } from '@mynotary/frontend/routes/api';
import { Form } from '@mynotary/frontend/legals/feature';
import { useNavigate } from 'react-router';
import { selectAdminOrganizations } from '@mynotary/frontend/organizations/store';
import { mergeAndCopyAnswer } from '@mynotary/frontend/legals/api';
import { FormQuestion, SelectFormQuestion } from '@mynotary/crossplatform/shared/forms-util';

const AdminCommunityOrganizationEditChildren = (): ReactElement => {
  const dispatch = useAsyncDispatch();
  const navigate = useNavigate();
  const { organizationId } = useParams();
  const organizations = useSelector(selectAdminOrganizations);
  const organization = organizations[parseInt(organizationId ?? '')];
  const [loading, setLoading] = useState(false);
  const [answer, setAnswer] = useState<AnswerDict>({});
  const [form, setForm] = useState<FormQuestion[]>([]);

  useEffect(() => {
    if (organization) {
      const form: FormQuestion[] = [
        {
          id: 'to_add',
          label: 'À ajouter',
          type: 'NUMBER'
        }
      ];
      if (organization.childrenOrganizations && !isEmpty(organization.childrenOrganizations)) {
        form.push({
          choices: organization.childrenOrganizations?.map((child) => ({
            id: `${child}`,
            label: `${child} (${organizations[child]?.name})`
          })),
          id: 'to_remove',
          label: 'À supprimer',
          multiple: true,
          type: 'SELECT'
        } as SelectFormQuestion);
      }
      setForm(form);
    }
  }, [organization, organizations]);

  const handleChange = (update?: AnswerDict): void => {
    if (organization) {
      const newAnswer = mergeAndCopyAnswer(answer, update);
      setAnswer(newAnswer);
    }
  };

  const handleSave = (): void => {
    setLoading(true);
    const promises: Promise<void>[] = [];
    if (answer['to_add']?.value) {
      promises.push(dispatch(addOrganizationChild(organization.id, answer['to_add']?.value)));
    }
    forEach(answer['to_remove']?.value, (childId) => {
      promises.push(dispatch(removeOrganizationChild(organization.id, childId)));
    });
    Promise.all(promises).finally(() => {
      setLoading(false);
      dispatch(getOrganizationNetwork(organization.id));
      navigate(reverse(routePaths.admin.community.organizations.organization.path, { organizationId }));
    });
  };

  return (
    <div className='admin-community-organization-edit-children'>
      <MnTitle label='Modifier les filiales' />
      <Form answer={answer} forms={form} onChange={handleChange} />
      <MnButton className='acoec-validate' disabled={loading} label='Valider' onClick={handleSave} />
    </div>
  );
};

export { AdminCommunityOrganizationEditChildren };
