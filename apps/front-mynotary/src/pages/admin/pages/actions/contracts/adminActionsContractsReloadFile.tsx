import React, { ReactElement, useState } from 'react';
import { MnButton, MnInputNumber, MnTitle } from '@mynotary/frontend/shared/ui';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { SignaturesClient } from '@mynotary/frontend/signatures/core';
import { setErrorMessage, setSuccessMessage } from '@mynotary/frontend/snackbars/store';

export const AdminActionsContractsReloadFile = (): ReactElement => {
  const dispatch = useAsyncDispatch();
  const [validating, setValidating] = useState<boolean>(false);
  const signaturesClient = useService(SignaturesClient);

  const handleValidate = async (id: number): Promise<void> => {
    if (validating) {
      return;
    }
    setValidating(true);

    try {
      await signaturesClient.syncSignature(id.toString());
      dispatch(setSuccessMessage('La signature a bien été synchronisée'));
    } catch (e) {
      console.error(e);
      dispatch(setErrorMessage('Une erreur est survenue'));
    } finally {
      setValidating(false);
    }
  };

  return (
    <div>
      <SyncSignatureForm onClick={handleValidate} validating={validating} />
    </div>
  );
};

function SyncSignatureForm({ onClick, validating }: SyncSignatureProps) {
  const [id, setId] = useState<number | null>(null);

  return (
    <>
      <MnTitle label={'Yousign'} />
      <MnInputNumber
        className='admin-block-input'
        onChange={setId}
        placeholder={`Id signature à récupérer dans l'url`}
        value={id}
      />
      <MnButton
        className='admin-block-button'
        disabled={validating || id == null}
        label='Valider'
        onClick={() => onClick(id as number)}
        variant='secondary'
      />
    </>
  );
}

interface SyncSignatureProps {
  onClick: (id: number) => Promise<void>;
  validating: boolean;
}
