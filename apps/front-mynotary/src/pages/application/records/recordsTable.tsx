import './recordsTable.scss';
import React, { MouseEvent, ReactElement, useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  ContactAvatar,
  MnConfirmationPopin,
  MnPopin,
  MnSvg,
  SheetPopin,
  usePagination
} from '@mynotary/frontend/shared/ui';
import { filter, find, flatten, forEach, isEmpty, map, mapKeys, merge, uniq } from 'lodash';
import { getRangeFromLocalStorage } from 'hooks/useLocalStorageFilter/useLocalStorageFilter';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { TemplateLabelRenderer } from 'components/_dataDisplay/simpleTable/rendererComponents/templateLabelRenderer';
import { RecordCreation } from '@mynotary/frontend/legal-record-creations/feature';
import { RecordTableType } from '../application';
import { TitleRendererRow } from 'components/_dataDisplay/simpleTable/rendererComponents/titleRendererRow';
import { MnDisplayTable } from 'components/_dataDisplay/simpleTable/displayTable';
import { RecordsTableActions } from 'pages/application/records/recordsTableActions';
import { RecordsTablePlaceholder } from 'pages/application/records/recordsTablePlaceholder';
import { selectCurrentUser } from '@mynotary/frontend/user-session/store';
import { getRecordLabel, isProperty, Record, RecordFiltering } from '@mynotary/frontend/legals/core';
import {
  deleteRecord,
  getRecords,
  selectLegalComponentsFeature,
  selectRecordsUnfoldForms,
  selectTemplatesByType
} from '@mynotary/frontend/legals/store';
import { Page, specificTypesToString, useGlobalLoader } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { useNavigate } from 'react-router';
import {
  Filters,
  FiltersKeys,
  hasSelectedFilters,
  isDateFilter,
  isSearchFilter,
  isTypeRecordFilter
} from '@mynotary/frontend/dashboard-filters/core';
import { selectPermission } from '@mynotary/frontend/roles/store';
import { navigateWithEvents, reverse, routePaths } from '@mynotary/frontend/routes/core';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { MnFilters, useFilterLoading } from '@mynotary/frontend/dashboard-filters/feature';
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/core';
import { getLegalRecordTemplate } from '@mynotary/crossplatform/legal-record-templates/core';
import {
  setErrorMessage,
  setPermanentSuccessMessage,
  setSuccessMessage,
  setWarningMessage
} from '@mynotary/frontend/snackbars/store';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { CsvLegalRecordsManager } from '@mynotary/frontend/csv-legal-records/feature';
import { findCsvLegalRecordConfig } from '@mynotary/crossplatform/csv-legal-records/core';
import { useService } from '@mynotary/frontend/shared/injector-util';
import {
  isNewLegalRecordExportRequestAllowed,
  LegalRecordExportsClient,
  setLegalRecordExportRequestTime
} from '@mynotary/frontend/legal-record-exports/core';
import { TypeFiche } from '@mynotary/crossplatform/legal-record-exports/core';
import { getPublicFile } from '@mynotary/frontend/files/api';

interface RecordsTableProps {
  tableType: RecordTableType;
  type: string[][];
}

const PAGE_SIZE = 20;

const getFiltersLocalStorageKey = (tableType: RecordTableType): string =>
  RecordTableType.BIEN === tableType ? 'RecordsTableBien' : 'RecordsTablePerson';

export const RecordsTable = ({ tableType, type }: RecordsTableProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const navigate = useNavigate();

  const [setIsLoading, isLoading] = useGlobalLoader(true);
  const { currentPage, isPaginating, setCurrentPage, setTotalItems, totalPages } = usePagination(PAGE_SIZE);
  const templates = useSelector(selectTemplatesByType('RECORD')) as LegalRecordTemplate[];

  const [recordIds, setRecordIds] = useState<number[]>([]);
  const legalComponents = useSelector(selectLegalComponentsFeature);
  const recordsForms = useSelector(selectRecordsUnfoldForms(recordIds));
  const [toDelete, setToDelete] = useState<{ id: number; label: string } | null>();
  const [toCreate, setToCreate] = useState(false);
  const [filters, setFilters] = useState<Filters>({});
  const [filtering, setFiltering] = useState<RecordFiltering>({});
  const [isImportOpen, setIsImportOpen] = useState(false);
  const legalRecordClient = useService(LegalRecordExportsClient);
  const organization = useSelector(selectCurrentOrganization);
  const hasExportCsvPermission = useSelector(
    selectPermission(PermissionType.CREATE_LEGAL_RECORD_EXPORTS, EntityType.ORGANIZATION)
  );
  const canImportCsv = useSelector(selectPermission(PermissionType.IMPORT_CSV, EntityType.ORGANIZATION));
  const user = useSelector(selectCurrentUser);
  const hasFilterSelected = useMemo(() => hasSelectedFilters(filters), [filters]);

  const { forceReload, handleLoadComplete, handleReload, hasFilterChanged, setHasFilterChanged, shouldReload } =
    useFilterLoading(true);

  const canExportCsv = hasExportCsvPermission;
  /**
   * setTriggerRender is used to force a re-render when the user requests an export. We store the last export time in
   * localStorage and disable the export button if the user already requested an export today. Since updating localStorage
   * doesn’t trigger a re-render, we use this state to manually refresh the component.
   */
  const [, setTriggerRender] = useState(1);

  const templateFilter = useMemo(
    () =>
      filter(
        templates,
        (template) =>
          template.type === 'RECORD' &&
          !isEmpty(flatten(flatten(type)).filter((selectedType) => template.specificTypes.includes(selectedType)))
      ),
    [templates, type]
  );

  const handleFiltersChange = useCallback(
    (defaultFilters?: Filters) => {
      setFilters({
        [FiltersKeys.DATE]: {
          children: {
            [FiltersKeys.CREATION_TIME]: {
              id: FiltersKeys.CREATION_TIME,
              label: 'Date création',
              onChange: (range) => {
                setFilters((prevState) => {
                  const prev = prevState[FiltersKeys.DATE];
                  const prevChildren = prev?.children[FiltersKeys.CREATION_TIME];

                  if (prev == null || prevChildren == null) {
                    return prevState;
                  }

                  return {
                    ...prevState,
                    [FiltersKeys.DATE]: {
                      ...prev,
                      children: { ...prev.children, [FiltersKeys.CREATION_TIME]: { ...prevChildren, value: range } }
                    }
                  };
                });
              },
              value: getRangeFromLocalStorage(defaultFilters?.DATE?.children?.CREATION_TIME?.value)
            }
          },
          id: FiltersKeys.DATE,
          label: 'Date'
        },
        [FiltersKeys.EXACT_SPECIFIC_TYPES]: {
          id: FiltersKeys.EXACT_SPECIFIC_TYPES,
          label: 'Type',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.EXACT_SPECIFIC_TYPES];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.EXACT_SPECIFIC_TYPES]: {
                  ...prev,
                  values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                }
              };
            });
          },
          values: merge(
            mapKeys(
              map(templateFilter, (template) => ({
                id: template.id,
                label: template.label,
                selected: false
              })),
              (template) => template.id
            ),
            defaultFilters?.EXACT_SPECIFIC_TYPES?.values
          )
        },
        [FiltersKeys.SEARCH]: {
          id: FiltersKeys.SEARCH,
          label:
            RecordTableType.BIEN === tableType
              ? 'Rechercher une adresse, par nom de dossier, négociateur… '
              : 'Rechercher un nom/raison sociale, un collaborateur',
          onChange: (text) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.SEARCH];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.SEARCH]: { ...prev, value: text }
              };
            });
          },
          value: defaultFilters?.SEARCH?.value
        }
      });
    },
    [tableType, templateFilter]
  );

  useEffect(() => {
    setHasFilterChanged(true);
    setCurrentPage(0);
  }, [filtering, setCurrentPage, setHasFilterChanged]);

  useEffect(() => {
    handleReload();
  }, [currentPage, handleReload]);

  const questionIds = useMemo(
    () => uniq(flatten(map(templateFilter, (template) => template.config.search ?? []))),
    [templateFilter]
  );

  useEffect(() => {
    if (!isEmpty(filters)) {
      localStorage.setItem(getFiltersLocalStorageKey(tableType), JSON.stringify(filters));
    }
  }, [filters, tableType]);

  useEffect(() => {
    if (!isEmpty(templateFilter)) {
      const localFilters = JSON.parse(localStorage.getItem(getFiltersLocalStorageKey(tableType)) ?? '{}');
      handleFiltersChange(localFilters);
    }
  }, [handleFiltersChange, tableType, templateFilter]);

  const headerColumns = {
    [RecordTableType.PERSONNE]: [
      {
        content: 'Personnes',
        id: 'title',
        isMobile: true,
        isTitle: true,
        style: 'title'
      },
      {
        content: 'TYPE',
        id: 'type',
        isMobile: true,
        style: 'hoverRowHidden'
      },
      {
        content: 'ADRESSE',
        id: 'address',
        isMobile: true
      },
      {
        content: 'CRÉÉ PAR',
        id: 'creator',
        isMobile: true,
        style: 'record-table-avatar personnes-table-avatar'
      },
      { content: '', id: 'actions' }
    ],
    [RecordTableType.BIEN]: [
      {
        content: 'Biens',
        id: 'title',
        isMobile: true,
        isTitle: true,
        style: 'title'
      },
      {
        content: 'TYPE',
        id: 'type',
        isMobile: true,
        style: 'hoverRowHidden'
      },
      {
        content: 'CRÉÉ PAR',
        id: 'creator',
        isMobile: true,
        style: 'record-table-avatar biens-table-avatar'
      },
      { content: '', id: 'actions' }
    ]
  };

  useEffect(() => {
    const filtering: RecordFiltering = {};

    forEach(filters, (f) => {
      if (isTypeRecordFilter(f)) {
        const types: string[][] = [];
        const selectedType = filter(f.values, (value) => value.selected);
        const selectedIds = map(selectedType, (type) => type.id);

        templateFilter.forEach((template) => {
          if (selectedIds.includes(template.id)) {
            types.push(template.specificTypes);
          }
        });

        filtering[FiltersKeys.EXACT_SPECIFIC_TYPES] = types;
      }
      if (isDateFilter(f)) {
        filtering[FiltersKeys.CREATION_TIME] = {
          after: f.children[FiltersKeys.CREATION_TIME].value?.to?.setHours(23, 59, 59),
          before: f.children[FiltersKeys.CREATION_TIME].value?.from?.setHours(0, 0, 1)
        };
      }

      if (isSearchFilter(f)) {
        filtering[FiltersKeys.SEARCH] = {
          QUESTION_IDS: questionIds,
          WORDS: f.value?.trim().split(' ') ?? []
        };
      }
    });
    setFiltering(filtering);
  }, [filters, questionIds, templateFilter]);

  /**
   * Force reload when switching members
   */
  useEffect(() => {
    handleReload();
  }, [handleReload, organization?.id]);

  useEffect(() => {
    if (shouldReload && hasFilterChanged && !isEmpty(filtering)) {
      setIsLoading(true);

      dispatch(
        getRecords(
          { ...filtering, ANSWER: { NOT_EMPTY: true }, SPECIFIC_TYPES: type },
          undefined,
          { page: currentPage, pageSize: PAGE_SIZE },
          'DESCENDING'
        )
      )
        .then((pageRecord: Page<Record>) => {
          setTotalItems(pageRecord.totalItems);
          setRecordIds(map(pageRecord.data, (record) => record.id));
        })
        .finally(() => {
          handleLoadComplete();
          setIsLoading(false);
        });
    }
  }, [
    currentPage,
    dispatch,
    filtering,
    hasFilterChanged,
    handleLoadComplete,
    questionIds,
    shouldReload,
    setIsLoading,
    setTotalItems,
    type
  ]);

  const rows = map(recordIds, (id) => {
    const record = legalComponents[id] as Record;
    const template = find(templateFilter, (template) => template.id === record.template.id);
    const bienRow =
      RecordTableType.PERSONNE === tableType
        ? {
            address: (
              <div className='record-address-renderer'>
                {recordsForms[id]?.answer?.['adresse']?.value?.formattedAddress}
              </div>
            )
          }
        : {};

    const IconComponent = (
      <RecordsTableActions
        onClick={() =>
          setToDelete({
            id: record.id,
            label: getRecordLabel({ answer: recordsForms[record.id]?.answer, recordTemplate: template })
          })
        }
        onFinishAction={handleReload}
        record={record}
      />
    );
    return {
      title: (
        <TitleRendererRow
          className='fullscreen'
          creator={record.creatorUser}
          hoverLabel='Ouvrir la fiche'
          icon={IconComponent}
          label={`${getRecordLabel({
            answer: recordsForms[id]?.answer,
            recordTemplate: template
          })}`}
          leftComponent={
            <MnSvg
              path={`/assets/images/pictos/icon/${tableType === 'PERSONNE' ? 'user.svg' : 'house-light.svg'}`}
              size='medium'
              variant='black'
            />
          }
        />
      ),
      type: <TemplateLabelRenderer label={template?.label ?? ''} />,
      ...bienRow,
      actions: IconComponent,
      creator: (
        <div className='operation-table-creator hoverRowHidden'>
          <ContactAvatar
            className='record-table-avatar'
            firstname={record.creatorUser?.firstname ?? ''}
            lastname={record.creatorUser?.lastname ?? ''}
            photoUrl={
              record.creatorUser?.profilePictureFileId
                ? getPublicFile(record.creatorUser?.profilePictureFileId)
                : undefined
            }
          />
        </div>
      ),
      responsiveSecondaryTitle: 'Ouvrir la fiche'
    };
  });

  const handleOnClick = (index: number, event: MouseEvent): void => {
    const id = recordIds[index];
    const record = legalComponents[id] as Record;
    return navigateWithEvents({ event, navigate, url: getRecordUrl(record) });
  };

  const handleValidateCreation = (record: Record): void => {
    navigate(getRecordUrl(record));
  };

  const handleDeleteRecord = (recordId: number): void => {
    setToDelete(null);
    dispatch(deleteRecord(recordId));
    setRecordIds(filter(recordIds, (id) => id !== recordId));
  };

  const handleFilterUpdate = (mustForceReload?: boolean, newFilters?: Filters, reset?: true): void => {
    if (reset) {
      handleFiltersChange();
    } else if (newFilters) {
      handleFiltersChange({ ...filters, ...newFilters });
    }

    if (mustForceReload) {
      forceReload();
    }
  };

  const pagination = useMemo(
    () => ({ currentPage, isPaginating, totalPages }),
    [currentPage, isPaginating, totalPages]
  );

  const handleExport = async (type: TypeFiche): Promise<void> => {
    assertNotNull(organization, 'Organization is missing');
    assertNotNull(user, 'User is missing');

    if (!isNewLegalRecordExportRequestAllowed({ email: user.email, organizationId: organization.id, type })) {
      dispatch(
        setWarningMessage(
          'Vous avez déjà une demande d’export en cours pour ce type de fiche. Le prochain export sera disponible demain.'
        )
      );
      return;
    }

    try {
      await legalRecordClient.createLegalRecordExportTask({
        email: user.email,
        organizationId: organization.id.toString(),
        type
      });
      dispatch(
        setPermanentSuccessMessage({
          title:
            'Nous préparons votre export de données. Il sera prêt dans la nuit et vous serez notifié(e) par email. Le lien de téléchargement ne sera valide que 24h à réception de l’email, afin de garantir la sécurité des informations partagées'
        })
      );
      setLegalRecordExportRequestTime({ email: user.email, organizationId: organization.id, type });
      setTriggerRender((prev) => prev + 1);
    } catch (error) {
      console.error(error);
      dispatch(setErrorMessage("Une erreur est survenue lors de la création de l'export"));
    }
  };

  const tooltip =
    "Vous avez déjà demandé un export du même type aujourd'hui, vous pourrez demander un nouvel export demain.";

  const actions =
    user && organization
      ? [
          {
            checkCondition: () => canImportCsv,
            id: 'import',
            label: `Importer des fiches ${RecordTableType.BIEN === tableType ? 'biens' : 'personnes'}`,
            onClick: () => setIsImportOpen(true)
          },
          {
            checkCondition: () => RecordTableType.PERSONNE === tableType && canExportCsv,
            disabled: !isNewLegalRecordExportRequestAllowed({
              email: user.email,
              organizationId: organization.id,
              type: TypeFiche.PERSONNE_PHYSIQUE
            }),
            id: 'export-fiche-personne-physique',
            label: `Exporter des fiches personnes physiques`,
            onClick: () => handleExport(TypeFiche.PERSONNE_PHYSIQUE),
            tooltip
          },
          {
            checkCondition: () => RecordTableType.PERSONNE === tableType && canExportCsv,
            disabled: !isNewLegalRecordExportRequestAllowed({
              email: user.email,
              organizationId: organization.id,
              type: TypeFiche.PERSONNE_MORALE
            }),
            id: 'export-fiche-personne-morale',
            label: `Exporter des fiches personnes morales`,
            onClick: () => handleExport(TypeFiche.PERSONNE_MORALE),
            tooltip
          },
          {
            checkCondition: () => RecordTableType.BIEN === tableType && canExportCsv,
            disabled: !isNewLegalRecordExportRequestAllowed({
              email: user.email,
              organizationId: organization.id,
              type: TypeFiche.BIEN
            }),
            id: 'export-fiche-bien',
            label: `Exporter des fiches biens`,
            onClick: () => handleExport(TypeFiche.BIEN),
            tooltip
          }
        ]
      : [];

  const handleImportRecords = async () => {
    handleReload();
    setIsImportOpen(false);
    dispatch(setSuccessMessage('Les fiches ont bien été crées.'));
  };

  const getCsvLegalRecordConfigColumns = (legalRecordTemplate: LegalRecordTemplate) => {
    const csvLegalRecordConfig = findCsvLegalRecordConfig(legalRecordTemplate.id);
    assertNotNull(csvLegalRecordConfig, `Batch import configuration is missing ${legalRecordTemplate.id}`);
    return csvLegalRecordConfig.columns;
  };

  const allowedLegalRecordTemplateIds = map(
    templateFilter,
    (template) => getLegalRecordTemplate(`RECORD__${specificTypesToString(template.specificTypes)}`).id
  );

  return (
    <>
      <MnFilters
        actions={actions}
        buttonLabel={RecordTableType.BIEN === tableType ? 'Nouveau bien' : 'nouvelle personne'}
        filters={filters}
        onButtonClick={() => setToCreate(true)}
        onHandleFilterUpdate={handleFilterUpdate}
        permissionCreation={organization != null}
      />
      <MnDisplayTable
        className='fullscreen'
        headerColumns={headerColumns[tableType]}
        onClick={handleOnClick}
        placeholder={
          <RecordsTablePlaceholder
            hasFilterSelected={hasFilterSelected}
            isLoading={isLoading}
            pagination={pagination}
            resetFilters={() => handleFilterUpdate(true, {}, true)}
            setCurrentPage={setCurrentPage}
            setToCreate={() => setToCreate(true)}
            tableType={tableType}
          />
        }
        rows={rows}
      />
      <SheetPopin direction={'left'} isOpened={toCreate} onClose={() => setToCreate(false)}>
        <RecordCreation
          authorizedTypes={type}
          onClosePopin={() => setToCreate(false)}
          onValidate={handleValidateCreation}
          templateMatchType='CONTAINS'
        />
      </SheetPopin>

      <MnConfirmationPopin
        content={`Êtes-vous sûr de vouloir supprimer la fiche "${toDelete?.label}" ?`}
        onCancel={() => setToDelete(null)}
        onValidate={() => toDelete && handleDeleteRecord(toDelete.id)}
        opened={!!toDelete}
      />
      <MnPopin className='application-records-table-popin' onClose={() => setIsImportOpen(false)} opened={isImportOpen}>
        <CsvLegalRecordsManager
          allowedLegalRecordTemplateIds={allowedLegalRecordTemplateIds}
          csvLegalRecordConfigColumns={getCsvLegalRecordConfigColumns}
          onRecordsCreated={handleImportRecords}
        />
      </MnPopin>
    </>
  );
};

export const getRecordUrl = (record: Record): string => {
  if (isProperty(record)) {
    return reverse(routePaths.property.path, { id: record.id });
  }
  return reverse(routePaths.person.path, { id: record.id });
};
