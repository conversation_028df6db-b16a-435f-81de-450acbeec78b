import { CustomViewType } from '@mynotary/frontend/custom-views/core';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { isEmpty } from 'lodash';
import { selectContractFiltersFeature } from '@mynotary/frontend/contracts-dashboard/store';
import { useGetInitialView } from '@mynotary/frontend/custom-views/feature';
import { Filters } from '@mynotary/frontend/dashboard-filters/core';
import { useFilterLoading } from '@mynotary/frontend/dashboard-filters/feature';
import { useFilterChange } from './use-contract-filter-view';

interface UseContractFilter {
  filters: Filters;
  forceReload: () => void;
  handleFilterUpdate: (mustForceReload?: boolean, newFilters?: Filters, reset?: boolean) => void;
  handleLoadComplete: () => void;
  resetFilters: () => void;
  shouldReload: boolean;
}

export function useContractFilter(): UseContractFilter {
  const [filters, setFilters] = useState<Filters>({});
  const contractFilters = useSelector(selectContractFiltersFeature);
  const { initialView, setTriggerApi, triggerApi } = useGetInitialView(CustomViewType.OPERATION_CONTRACT);

  const { forceReload, handleLoadComplete, handleReload, shouldReload } = useFilterLoading(false);

  const handleFiltersChange = useFilterChange(setFilters);

  useEffect(() => {
    if (!isEmpty(contractFilters) && triggerApi) {
      const initialFilters = initialView?.filters ? JSON.parse(initialView.filters) : {};

      handleFiltersChange(initialFilters);
      setTriggerApi(false);
      handleReload();
    }
  }, [contractFilters, handleFiltersChange, handleReload, initialView?.filters, setTriggerApi, triggerApi]);

  const handleFilterUpdate = (mustForceReload?: boolean, newFilters?: Filters, reset?: boolean): void => {
    if (reset) {
      handleFiltersChange(newFilters);
    } else if (newFilters) {
      handleFiltersChange({ ...filters, ...newFilters });
    }
    if (mustForceReload) {
      forceReload();
    }
  };

  return {
    filters,
    forceReload,
    handleFilterUpdate,
    handleLoadComplete,
    resetFilters: () => handleFilterUpdate(true, {}, true),
    shouldReload
  };
}
