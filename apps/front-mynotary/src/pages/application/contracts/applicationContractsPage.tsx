import './applicationContractsPage.scss';
import { CustomViewType } from '@mynotary/frontend/custom-views/core';
import React, { ReactElement, useEffect } from 'react';
import { ContractsTable } from 'pages/application/contracts/table/contractsTable';
import { MnCustomViews } from 'components/customViews/customViews';
import { useContractView } from './use-contract-filter-view';
import { setPageTitle } from '@mynotary/frontend/shared/util';

import { useContractFilter } from './use-contract-filter';

const ApplicationContractsPage = (): ReactElement => {
  useEffect(() => {
    setPageTitle('Suivi des contrats');
  }, []);

  const { filters, forceReload, handleFilterUpdate, handleLoadComplete, resetFilters, shouldReload } =
    useContractFilter();

  const { filtersView, handleViewChange } = useContractView();

  return (
    <>
      <MnCustomViews
        filters={filters}
        filtersView={filtersView}
        handleFiltersChange={handleViewChange}
        onHandleFilterUpdate={handleFilterUpdate}
        type={CustomViewType.OPERATION_CONTRACT}
      />
      <div className='application-contracts-page'>
        <ContractsTable
          filters={filters}
          onLoadComplete={handleLoadComplete}
          onReload={forceReload}
          reloadData={shouldReload}
          resetFilters={resetFilters}
        />
      </div>
    </>
  );
};

export { ApplicationContractsPage };
