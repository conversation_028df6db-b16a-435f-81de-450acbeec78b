import { find, keyBy, map, mapKeys, merge } from 'lodash';
import { Dispatch, SetStateAction, useCallback, useState } from 'react';
import { useSelector } from 'react-redux';
import { getRangeFromLocalStorage } from 'hooks/useLocalStorageFilter/useLocalStorageFilter';
import { SignatureType } from '@mynotary/crossplatform/signatures/api';
import { selectContractFiltersFeature } from '@mynotary/frontend/contracts-dashboard/store';
import {
  ArchiveFilterId,
  Filters,
  FiltersKeys,
  LastAccessFilterId,
  OriginFilterId
} from '@mynotary/frontend/dashboard-filters/core';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { useFeatureState } from '@mynotary/frontend/features/feature';
import { FeatureType } from '@mynotary/crossplatform/features/core';
import { OperationStatusState, selectOperationStatusFeature } from '@mynotary/frontend/operation-status/store';
import { getLegalOperationTemplate } from '@mynotary/crossplatform/legal-operation-templates/api';
import { isProgramConfig } from '@mynotary/crossplatform/legal-templates/api';

interface UseContractView {
  filtersView: Filters;
  handleViewChange: (defaultFilters?: Filters) => void;
}

export function useContractView(): UseContractView {
  const [filters, setFilters] = useState<Filters>({});
  const handleFiltersChange = useFilterChange(setFilters);

  return { filtersView: filters, handleViewChange: handleFiltersChange };
}

export function useFilterChange(setFilters: Dispatch<SetStateAction<Filters>>): (defaultFilters?: Filters) => void {
  const contractFilters = useSelector(selectContractFiltersFeature);
  const contractReviewFeature = useFeatureState(FeatureType.CONTRACT_REVIEWS);

  const allStatus = useSelector(selectOperationStatusFeature);

  const changeFilter = useCallback(
    (defaultFilters?: Filters) => {
      const { operationStatus, programStatus } = splitOperationStatus(allStatus);
      setFilters({
        [FiltersKeys.ORIGIN]: {
          id: FiltersKeys.ORIGIN,
          label: 'Créé par',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.ORIGIN];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.ORIGIN]: { ...prev, values: { ...prev.values, [id]: { ...prev.values[id], selected } } }
              };
            });
          },
          values: merge(
            {
              ORGANIZATION: {
                id: OriginFilterId.ORGANIZATION,
                label: 'Mon organisation',
                selected: false
              },
              OWN: {
                id: OriginFilterId.OWN,
                label: 'Moi',
                selected: false
              }
            },
            defaultFilters?.[FiltersKeys.ORIGIN]?.values
          )
        },
        [FiltersKeys.ARCHIVE_CONTRACT]: {
          id: FiltersKeys.ARCHIVE_CONTRACT,
          label: 'État',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.ARCHIVE_CONTRACT];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.ARCHIVE_CONTRACT]: {
                  ...prev,
                  values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                }
              };
            });
          },
          values: merge(
            {
              ACTIVE: {
                id: ArchiveFilterId.ACTIVE,
                label: 'Actif',
                selected: false
              },
              ARCHIVED: {
                id: ArchiveFilterId.ARCHIVED,
                label: 'Archivé',
                selected: false
              }
            },
            defaultFilters?.ARCHIVE_CONTRACT?.values
          )
        },
        [FiltersKeys.STATUS]: {
          id: FiltersKeys.STATUS,
          label: 'Statut du contrat',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.STATUS];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.STATUS]: { ...prev, values: { ...prev.values, [id]: { ...prev.values[id], selected } } }
              };
            });
          },
          values: merge(
            {
              [ContractStatus.DRAFT]: {
                id: ContractStatus.DRAFT,
                label: 'Brouillon',
                selected: false
              },
              [ContractStatus.REDACTION]: {
                id: ContractStatus.REDACTION,
                label: 'En rédaction',
                selected: false
              },
              [ContractStatus.VALIDATION_PENDING]: {
                id: ContractStatus.VALIDATION_PENDING,
                label: 'En attente de validation',
                selected: false
              },
              ...(contractReviewFeature?.isActive
                ? {
                    [ContractStatus.REVIEW_PENDING]: {
                      id: ContractStatus.REVIEW_PENDING,
                      label: 'En attente de relecture',
                      selected: false
                    },
                    [ContractStatus.REVIEWED]: {
                      id: ContractStatus.REVIEWED,
                      label: 'Relecture effectuée',
                      selected: false
                    }
                  }
                : undefined),
              [ContractStatus.VALIDATED]: {
                id: ContractStatus.VALIDATED,
                label: 'Contrat validé',
                selected: false
              },
              [ContractStatus.SIGNATURE_DRAFT]: {
                id: ContractStatus.SIGNATURE_DRAFT,
                label: 'Config signature en cours',
                selected: false
              },
              [ContractStatus.SIGNATURE_PENDING_CREATION]: {
                id: ContractStatus.SIGNATURE_PENDING_CREATION,
                label: 'Signature - Envoi en cours',
                selected: false
              },
              [ContractStatus.SIGNATURE_ERROR]: {
                id: ContractStatus.SIGNATURE_ERROR,
                label: "Signature - Échec de l'envoi",
                selected: false
              },
              [ContractStatus.SIGNATURE_PENDING]: {
                id: ContractStatus.SIGNATURE_PENDING,
                label: 'Signature en cours',
                selected: false
              },
              [ContractStatus.SIGNATURE_COMPLETED]: {
                id: ContractStatus.SIGNATURE_COMPLETED,
                label: 'Signé',
                selected: false
              },
              [ContractStatus.SIGNATURE_EXPIRED]: {
                id: ContractStatus.SIGNATURE_EXPIRED,
                label: 'Signature expirée',
                selected: false
              },
              [ContractStatus.NOTIFICATION_DRAFT]: {
                id: ContractStatus.NOTIFICATION_DRAFT,
                label: 'Config recommandé en cours',
                selected: false
              },
              [ContractStatus.NOTIFICATION_PENDING]: {
                id: ContractStatus.NOTIFICATION_PENDING,
                label: 'Recommandé élec. en cours',
                selected: false
              },
              [ContractStatus.NOTIFICATION_COMPLETED]: {
                id: ContractStatus.NOTIFICATION_COMPLETED,
                label: 'Recommandé élec. accepté',
                selected: false
              },
              [ContractStatus.NOTIFICATION_ERROR]: {
                id: ContractStatus.NOTIFICATION_ERROR,
                label: 'Recommandé en erreur',
                selected: false
              }
            },
            defaultFilters?.[FiltersKeys.STATUS]?.values
          )
        },

        [FiltersKeys.STATUS_OPERATION]: {
          id: FiltersKeys.STATUS_OPERATION,
          label: 'Statut du dossier',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.STATUS_OPERATION];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.STATUS_OPERATION]: {
                  ...prev,
                  values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                }
              };
            });
          },
          values: merge(
            {
              0: { id: 0, isDefault: true, label: 'Non défini', selected: false },
              ...mapKeys(
                map(operationStatus, (status) => ({
                  id: status.id,
                  label: status.label,
                  selected: false
                })),
                (value) => value.id
              )
            },
            defaultFilters?.STATUS_OPERATION?.values
          )
        },
        ...(programStatus.length > 0
          ? {
              [FiltersKeys.STATUS_PROGRAM]: {
                id: FiltersKeys.STATUS_PROGRAM,
                label: 'Statut du programme',
                onChange: (id, selected) => {
                  setFilters((prevState) => {
                    const prev = prevState[FiltersKeys.STATUS_PROGRAM];

                    if (prev == null) {
                      return prevState;
                    }

                    return {
                      ...prevState,
                      [FiltersKeys.STATUS_PROGRAM]: {
                        ...prev,
                        values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                      }
                    };
                  });
                },
                values: merge(
                  {
                    0: { id: 0, isDefault: true, label: 'Non défini', selected: false },
                    ...mapKeys(
                      map(programStatus, (status) => ({
                        id: status.id,
                        label: status.label,
                        selected: false
                      })),
                      (value) => value.id
                    )
                  },
                  defaultFilters?.STATUS_PROGRAM?.values
                )
              }
            }
          : {}),
        [FiltersKeys.CONTRACTS_TYPES]: {
          id: FiltersKeys.CONTRACTS_TYPES,
          label: 'Type',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.CONTRACTS_TYPES];

              if (prev == null) {
                return prevState;
              }

              const isGroupSelection = find(prev.values, (v) => v.id === id);
              if (isGroupSelection) {
                return {
                  ...prevState,
                  [FiltersKeys.CONTRACTS_TYPES]: {
                    ...prev,
                    values: {
                      ...prev.values,
                      [id]: {
                        ...prev.values[id],
                        selected,
                        values: keyBy(
                          map(prev.values[id]?.values, (v2) => ({ ...v2, selected })),
                          (v) => v.id
                        )
                      }
                    }
                  }
                };
              } else {
                const operation = find(prev.values, (v) => !!find(v.values, (v) => v.id === id));

                if (operation) {
                  const values = prev.values[operation.id].values;

                  if (values == null) {
                    return prevState;
                  }

                  return {
                    ...prevState,
                    [FiltersKeys.CONTRACTS_TYPES]: {
                      ...prev,
                      values: {
                        ...prev.values,
                        [operation.id]: {
                          ...prev.values[operation.id],
                          selected,
                          values: {
                            ...prev.values[operation.id].values,
                            [id]: {
                              ...values[id],
                              selected
                            }
                          }
                        }
                      }
                    }
                  };
                }
              }
              return prevState;
            });
          },
          values: merge(
            {},
            contractFilters[FiltersKeys.CONTRACTS_TYPES],
            defaultFilters?.[FiltersKeys.CONTRACTS_TYPES]?.values
          )
        },
        [FiltersKeys.DATE]: {
          children: {
            [FiltersKeys.CREATION_TIME]: {
              id: FiltersKeys.CREATION_TIME,
              label: 'Date création',
              onChange: (range) => {
                setFilters((prevState) => {
                  const prev = prevState[FiltersKeys.DATE];
                  const prevChildren = prev?.children[FiltersKeys.CREATION_TIME];

                  if (prev == null || prevChildren == null) {
                    return prevState;
                  }

                  return {
                    ...prevState,
                    [FiltersKeys.DATE]: {
                      ...prev,
                      children: { ...prev.children, [FiltersKeys.CREATION_TIME]: { ...prevChildren, value: range } }
                    }
                  };
                });
              },
              value: getRangeFromLocalStorage(defaultFilters?.[FiltersKeys.DATE]?.children?.CREATION_TIME?.value)
            },
            [FiltersKeys.SIGNATURE_TIME]: {
              id: FiltersKeys.SIGNATURE_TIME,
              label: 'Date signature',
              onChange: (range) => {
                setFilters((prevState) => {
                  const prev = prevState[FiltersKeys.DATE];
                  const prevChildren = prev?.children[FiltersKeys.SIGNATURE_TIME];

                  if (prev == null || prevChildren == null) {
                    return prevState;
                  }

                  return {
                    ...prevState,
                    [FiltersKeys.DATE]: {
                      ...prev,
                      children: {
                        ...prev.children,
                        [FiltersKeys.SIGNATURE_TIME]: { ...prevChildren, value: range }
                      }
                    }
                  };
                });
              },
              value: getRangeFromLocalStorage(defaultFilters?.[FiltersKeys.DATE]?.children?.SIGNATURE_TIME?.value)
            },
            [FiltersKeys.SIGNATURE_CREATION_TIME]: {
              id: FiltersKeys.SIGNATURE_CREATION_TIME,
              label: 'Date envoi signature',
              onChange: (range) => {
                setFilters((prevState) => {
                  const prev = prevState[FiltersKeys.DATE];
                  const prevChildren = prev?.children[FiltersKeys.SIGNATURE_CREATION_TIME];

                  if (prev == null || prevChildren == null) {
                    return prevState;
                  }

                  return {
                    ...prevState,
                    [FiltersKeys.DATE]: {
                      ...prev,
                      children: {
                        ...prev.children,
                        [FiltersKeys.SIGNATURE_CREATION_TIME]: { ...prevChildren, value: range }
                      }
                    }
                  };
                });
              },
              value: getRangeFromLocalStorage(
                defaultFilters?.[FiltersKeys.DATE]?.children?.SIGNATURE_CREATION_TIME?.value
              )
            }
          },
          id: FiltersKeys.DATE,
          label: 'Date'
        },
        [FiltersKeys.SEARCH]: {
          id: FiltersKeys.SEARCH,
          label: 'Rechercher un contrat par nom de dossier, négociateur…',
          onChange: (text) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.SEARCH];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.SEARCH]: { ...prev, value: text }
              };
            });
          },
          value: defaultFilters?.[FiltersKeys.SEARCH]?.value
        },
        [FiltersKeys.LAST_ACCESS]: {
          id: FiltersKeys.LAST_ACCESS,
          label: 'Accès',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.LAST_ACCESS];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.LAST_ACCESS]: {
                  ...prev,
                  values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                }
              };
            });
          },
          values: merge(
            {
              MONTH: {
                id: LastAccessFilterId.MONTH,
                label: '30 derniers jours',
                selected: false
              }
            },
            defaultFilters?.LAST_ACCESS?.values
          )
        },
        [FiltersKeys.CONTRACT_SIGNATURE_TYPE]: {
          id: FiltersKeys.CONTRACT_SIGNATURE_TYPE,
          label: 'Type de signature',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.CONTRACT_SIGNATURE_TYPE];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.CONTRACT_SIGNATURE_TYPE]: {
                  ...prev,
                  values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                }
              };
            });
          },
          values: merge(
            {
              ELECTRONIC: {
                id: SignatureType.ELECTRONIC,
                label: 'Électronique',
                selected: false
              },
              PAPER: {
                id: SignatureType.PAPER,
                label: 'Papier',
                selected: false
              }
            },
            defaultFilters?.[FiltersKeys.CONTRACT_SIGNATURE_TYPE]?.values
          )
        }
      });
    },
    [allStatus, contractFilters, contractReviewFeature?.isActive, setFilters]
  );

  return changeFilter;
}

interface Status {
  id: number;
  label: string;
}

function splitOperationStatus(allStatus: OperationStatusState): { operationStatus: Status[]; programStatus: Status[] } {
  const operationStatus = Object.values(allStatus)
    .filter((status) => !isProgramConfig(getLegalOperationTemplate(status.templateId)?.config))
    .map((s) => ({
      id: s.id,
      label: s.label
    }));

  const programStatus = Object.values(allStatus)
    .filter((status) => isProgramConfig(getLegalOperationTemplate(status.templateId)?.config))
    .map((s) => ({
      id: s.id,
      label: s.label
    }));

  return { operationStatus, programStatus };
}
