import './operations.scss';
import { CustomViewType } from '@mynotary/frontend/custom-views/core';
import React, { ReactElement, useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router';
import { useSelector } from 'react-redux';
import { routes } from 'app/routes/routes';
import { MnOperationCreation } from '@mynotary/frontend/legal-operation-creations/feature';
import { isEmpty, map, mapKeys, merge } from 'lodash';
import { getRangeFromLocalStorage } from 'hooks/useLocalStorageFilter/useLocalStorageFilter';
import { ApplicationOperationsTable, PaginationProps } from './applicationOperationsTable/applicationOperationsTable';
import { OperationsContext } from './operationsContext';
import { FloatingButton, MnSidePopin, TablePlaceholder } from '@mynotary/frontend/shared/ui';
import { MnPaginateTable } from '@mynotary/frontend/shared/ui';
import { Operation } from '@mynotary/frontend/legals/core';
import { setPageTitle } from '@mynotary/frontend/shared/util';
import { useRestrictAppAccess } from '@mynotary/frontend/billings/api';
import { selectOperationFilters } from '@mynotary/frontend/operations-dashboard/store';
import { useGetInitialView } from '@mynotary/frontend/custom-views/feature';
import {
  ArchiveFilterId,
  Filters,
  FiltersKeys,
  hasSelectedFilters,
  LastAccessFilterId,
  OriginFilterId
} from '@mynotary/frontend/dashboard-filters/core';
import { getOperations } from '@mynotary/frontend/legals/store';

const ApplicationOperationsPage = (): ReactElement => {
  const navigate = useNavigate();
  const operationfilters = useSelector(selectOperationFilters);
  const [filters, setFilters] = useState<Filters>({});
  const [toCreate, setToCreate] = useState(false);
  const { initialView, setTriggerApi, triggerApi } = useGetInitialView(CustomViewType.OPERATION);
  const hasFilterSelected = useMemo(() => hasSelectedFilters(filters), [filters]);

  const { isAccessRestricted, tooltip } = useRestrictAppAccess();

  useEffect(() => {
    setPageTitle('Suivi des dossiers');
  }, []);

  const handleCreation = (operation: Operation): void => {
    navigate(routes.toLegalComponent(operation));
  };

  const handleFiltersChange = useCallback(
    (defaultFilters?: Filters) => {
      setFilters({
        [FiltersKeys.ORIGIN_OPERATION]: {
          id: FiltersKeys.ORIGIN_OPERATION,
          label: 'Créé par',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.ORIGIN_OPERATION];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.ORIGIN_OPERATION]: {
                  ...prev,
                  values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                }
              };
            });
          },
          values: merge(
            {
              ORGANIZATION: {
                id: OriginFilterId.ORGANIZATION,
                label: 'Mon organisation',
                selected: false
              },
              OWN: {
                id: OriginFilterId.OWN,
                label: 'Moi',
                selected: false
              }
            },
            defaultFilters?.ORIGIN_OPERATION?.values
          )
        },
        [FiltersKeys.ARCHIVE_OPERATION]: {
          id: FiltersKeys.ARCHIVE_OPERATION,
          label: 'État',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.ARCHIVE_OPERATION];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.ARCHIVE_OPERATION]: {
                  ...prev,
                  values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                }
              };
            });
          },
          values: merge(
            {
              ACTIVE: {
                id: ArchiveFilterId.ACTIVE,
                label: 'Actif',
                selected: false
              },
              ARCHIVED: {
                id: ArchiveFilterId.ARCHIVED,
                label: 'Archivé',
                selected: false
              }
            },
            defaultFilters?.ARCHIVE_OPERATION?.values
          )
        },
        [FiltersKeys.STATUS_OPERATION]: {
          id: FiltersKeys.STATUS_OPERATION,
          label: 'Statut',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.STATUS_OPERATION];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.STATUS_OPERATION]: {
                  ...prev,
                  values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                }
              };
            });
          },
          values: merge(
            {
              0: { id: 0, isDefault: true, label: 'Non défini', selected: false },
              ...mapKeys(
                map(operationfilters?.status, (status) => ({
                  id: status.id,
                  label: status.label,
                  selected: false
                })),
                (value) => value.id
              )
            },
            defaultFilters?.STATUS_OPERATION?.values
          )
        },
        [FiltersKeys.TEMPLATE_IDS]: {
          id: FiltersKeys.TEMPLATE_IDS,
          label: 'Type',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.TEMPLATE_IDS];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.TEMPLATE_IDS]: {
                  ...prev,
                  values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                }
              };
            });
          },
          values: merge(
            mapKeys(
              map(operationfilters?.templates, (template) => ({
                id: template.id,
                label: template.label,
                selected: false
              })),
              (status) => status.id
            ),
            defaultFilters?.TEMPLATE_IDS?.values
          )
        },
        [FiltersKeys.DATE]: {
          children: {
            [FiltersKeys.CREATION_TIME]: {
              id: FiltersKeys.CREATION_TIME,
              label: 'Date création',
              onChange: (range) => {
                setFilters((prevState) => {
                  const prev = prevState[FiltersKeys.DATE];
                  const prevChildren = prev?.children[FiltersKeys.CREATION_TIME];

                  if (prev == null || prevChildren == null) {
                    return prevState;
                  }

                  return {
                    ...prevState,
                    [FiltersKeys.DATE]: {
                      ...prev,
                      children: { ...prev.children, [FiltersKeys.CREATION_TIME]: { ...prevChildren, value: range } }
                    }
                  };
                });
              },
              value: getRangeFromLocalStorage(defaultFilters?.DATE?.children?.CREATION_TIME?.value)
            }
          },
          id: FiltersKeys.DATE,
          label: 'Date'
        },
        [FiltersKeys.SEARCH]: {
          id: FiltersKeys.SEARCH,
          label: 'Rechercher par nom de dossier, négociateur, clients...',
          onChange: (text) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.SEARCH];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.SEARCH]: { ...prev, value: text }
              };
            });
          },
          value: defaultFilters?.SEARCH?.value
        },
        [FiltersKeys.LAST_ACCESS]: {
          id: FiltersKeys.LAST_ACCESS,
          label: 'Accès',
          onChange: (id, selected) => {
            setFilters((prevState) => {
              const prev = prevState[FiltersKeys.LAST_ACCESS];

              if (prev == null) {
                return prevState;
              }

              return {
                ...prevState,
                [FiltersKeys.LAST_ACCESS]: {
                  ...prev,
                  values: { ...prev.values, [id]: { ...prev.values[id], selected } }
                }
              };
            });
          },
          values: merge(
            {
              MONTH: {
                id: LastAccessFilterId.MONTH,
                label: '30 derniers jours',
                selected: false
              }
            },
            defaultFilters?.LAST_ACCESS?.values
          )
        }
      });
    },
    [operationfilters?.status, operationfilters?.templates]
  );

  useEffect(() => {
    if (!isEmpty(operationfilters) && triggerApi) {
      const initialFilters = initialView?.filters ? JSON.parse(initialView.filters) : {};
      handleFiltersChange(initialFilters);
      setTriggerApi(false);
    }
  }, [initialView, handleFiltersChange, operationfilters, setTriggerApi, triggerApi]);

  const Placeholder = ({
    isLoading,
    pagination,
    resetFilters
  }: {
    isLoading: boolean;
    pagination: PaginationProps;
    resetFilters: () => void;
  }) => (
    <>
      <TablePlaceholder
        buttonLabel='Nouveau dossier'
        description='Démarrez votre activité sur MyNotary en créant votre premier dossier'
        illustrationPath='/assets/images/pictos/illustrated/illu-dossier.svg'
        isDisabled={isAccessRestricted}
        isVisible={!isLoading && pagination.totalPages === 0 && !hasFilterSelected}
        label='Aucun dossier créé pour le moment'
        onClick={() => setToCreate(true)}
        tooltip={tooltip}
      />
      <TablePlaceholder
        buttonIcon={'/assets/images/pictos/icon/arrow-left-light.svg'}
        buttonLabel={'Revenir à la page principale'}
        description={`Vous avez consulté l'ensemble des éléments disponibles`}
        illustrationPath={'/assets/images/pictos/illustrated/file-search.svg'}
        isVisible={!isLoading && pagination.totalPages === 0 && hasFilterSelected}
        label={'Aucun élément ne correspond à votre recherche'}
        onClick={resetFilters}
      />
      <MnPaginateTable onChangePage={pagination.setCurrentPage} pagination={pagination} />
      <div className='operations-creation'>
        <FloatingButton
          color='primary'
          disabled={isAccessRestricted}
          icon='/assets/images/pictos/icon/plus-light.svg'
          onClick={() => !!setToCreate && setToCreate(true)}
        />
      </div>
    </>
  );

  return (
    <div className='operations'>
      <OperationsContext filters={filters} getOperationData={getOperations}>
        {(operationIds, pagination, isLoading, forceReload) => (
          <ApplicationOperationsTable
            buttonLabel='Nouveau Dossier'
            filters={filters}
            fullscreen={true}
            onButtonClick={setToCreate}
            onHandleFilterUpdate={(mustForceReload?: boolean, newFilters?: Filters, reset?: boolean) => {
              if (reset) {
                handleFiltersChange(newFilters);
              } else if (newFilters) {
                handleFiltersChange({ ...filters, ...newFilters });
              }

              if (mustForceReload) {
                forceReload();
              }
            }}
            operationIds={operationIds}
            pagination={pagination}
            permissionCreation={true}
            placeholder={
              <Placeholder
                isLoading={isLoading}
                pagination={pagination}
                resetFilters={() => {
                  handleFiltersChange({});
                  forceReload();
                }}
              />
            }
          />
        )}
      </OperationsContext>
      <MnSidePopin isOpen={!!toCreate} onClose={() => setToCreate(false)}>
        {toCreate && <MnOperationCreation onValidate={handleCreation} />}
      </MnSidePopin>
    </div>
  );
};
export { ApplicationOperationsPage };
