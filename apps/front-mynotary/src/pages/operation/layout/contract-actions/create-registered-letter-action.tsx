import React from 'react';
import { PopinType, openPopin } from '@mynotary/frontend/popins/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { useRestrictAppAccess } from '@mynotary/frontend/billings/feature';
import { ContractCreationWorkflowStepper, ContractCreationWorkflowType } from '@mynotary/frontend/popins/core';
import { MnTooltip, ActionIcon } from '@mynotary/frontend/shared/ui';
import { useSelector } from 'react-redux';
import { selectLegalComponentTemplate } from '@mynotary/frontend/legals/store';
import { specificTypesToString } from '@mynotary/frontend/shared/util';
import { selectPermission } from '@mynotary/frontend/roles/store';
import { PermissionType, SubEntity } from '@mynotary/crossplatform/roles/api';
import { LegalTemplate } from '@mynotary/crossplatform/legal-templates/core';

interface CreateRegisteredLetterActionProps {
  operationId: number;
}

export const CreateRegisteredLetterAction = ({ operationId }: CreateRegisteredLetterActionProps) => {
  const { isAccessRestricted, tooltip } = useRestrictAppAccess();
  const dispatch = useAsyncDispatch();
  const template = useSelector(selectLegalComponentTemplate<LegalTemplate>(operationId));
  const entity = specificTypesToString(template?.specificTypes);
  const hasPermission = useSelector(
    selectPermission(PermissionType.CREATE_CONTRACT_REGISTERED_LETTER, entity, SubEntity.IMPORT)
  );

  const handleClick = () => {
    if (isAccessRestricted) {
      return;
    }
    dispatch(
      openPopin({
        defaultStep: {
          step: ContractCreationWorkflowStepper.EXTERNAL_CONTRACT_SELECTION,
          type: ContractCreationWorkflowType.REGISTERED_LETTER
        },
        isModel: false,
        operationId,
        type: PopinType.CREATE_CONTRACT
      })
    );
  };

  if (!hasPermission || isAccessRestricted) {
    return null;
  }

  return (
    <MnTooltip content={tooltip} disabled={!isAccessRestricted}>
      <ActionIcon
        disabled={isAccessRestricted}
        icon='/assets/images/pictos/icon/send.svg'
        label='Envoyer un fichier en recommandé'
        onClick={handleClick}
      />
    </MnTooltip>
  );
};
