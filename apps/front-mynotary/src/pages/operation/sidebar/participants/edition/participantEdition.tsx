import './participantEdition.scss';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import React, { ReactElement, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Form } from '@mynotary/frontend/legals/feature';
import { MnCheckbox } from '@mynotary/frontend/shared/ui';
import { isEmpty } from 'lodash';
import { selectConnectedUserRole } from '@mynotary/frontend/roles/store';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { OperationInvitationRole, OperationInvitationsClient } from '@mynotary/frontend/operation-invitations/core';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';

interface MnParticipantEditionProps {
  answer: AnswerDict;
  isAlreadyInvited: boolean;
  isSameUserInvitation: boolean;
  onAnswerChange: (update: AnswerDict) => void;
  operationId?: number;
}

export const ParticipantEdition = ({
  answer,
  isAlreadyInvited,
  isSameUserInvitation,
  onAnswerChange,
  operationId
}: MnParticipantEditionProps): ReactElement | null => {
  const [availableRoles, setAvailableRoles] = useState<OperationInvitationRole[]>([]);
  const [emailNotSelected, setEmailNotSelected] = useState(false);

  const connectedUser = useSelector(selectCurrentUser);
  const currentUserRole = useSelector(selectConnectedUserRole);

  const operationInvitationsClient = useService(OperationInvitationsClient);
  const selectedEmail = answer['email']?.value?.[0]?.email;
  const selectedRole = answer['role']?.value;

  useEffect(() => {
    const getRoles = async (email: string) => {
      if (operationId == null || connectedUser == null) {
        return;
      }
      const roles = await operationInvitationsClient.getOperationRoles({
        email,
        operationId,
        userId: connectedUser.id
      });
      setAvailableRoles(roles);
    };

    if (!isEmpty(selectedEmail)) {
      getRoles(selectedEmail).catch(console.error);
      setEmailNotSelected(false);
    } else {
      setAvailableRoles([]);
    }
  }, [connectedUser, operationId, operationInvitationsClient, selectedEmail]);

  useEffect(() => {
    if (selectedRole && isEmpty(selectedEmail)) {
      setEmailNotSelected(true);
    }
  }, [selectedEmail, selectedRole]);

  if (currentUserRole == null) {
    return null;
  }

  const forms = [
    answer?.['email']?.value && !isEmpty(answer?.['email']?.value)
      ? {
          dataTestId: 'email',
          id: 'email',
          label: 'E-mail',
          multiple: false,
          type: 'SELECT-EMAIL'
        }
      : {
          dataTestId: 'email',
          description: 'Sélectionnez un email dans le menu déroulant',
          id: 'email',
          label: 'E-mail',
          multiple: false,
          type: 'SELECT-EMAIL'
        },
    {
      choices: availableRoles.map((role) => ({
        id: role.id,
        label: role.name
      })),
      id: 'role',
      label: 'Sélectionner les droits de l’intervenant invité',
      type: 'SELECT'
    }
  ];

  return (
    <div className='participant-edition'>
      <Form answer={answer} className='pe-form' forms={forms} onChange={onAnswerChange} />
      <MnCheckbox
        className='pe-checkbox'
        label='Prévenir l’intervenant par e-mail de ce partage'
        onChange={(value) => onAnswerChange({ sendEmail: { value } })}
        value={answer['sendEmail']?.value}
      />
      {emailNotSelected && (
        <div className='pe-same-user-error'>Vous n'avez pas sélectionné l'email dans le menu déroulant</div>
      )}
      {isSameUserInvitation && <div className='pe-same-user-error'>Vous avez déjà accès à ce dossier</div>}
      {isAlreadyInvited && <div className='pe-same-user-error'>L'intervenant a déjà été invité sur ce dossier</div>}
    </div>
  );
};
