import './taskEdition.scss';
import React, { ReactElement, useState } from 'react';
import { useSelector } from 'react-redux';
import { Task, Record, Operation } from '@mynotary/frontend/legals/core';
import { TaskType } from '@mynotary/crossplatform/legals/core';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { selectLegalComponentById, updateTask } from '@mynotary/frontend/legals/store';
import { Form, getTaskFormAnswer } from '@mynotary/frontend/legals/api';
import { MnButton, MnCustomLoader } from '@mynotary/frontend/shared/ui';
import {
  taskDeadline,
  taskDescription,
  TaskFormQuestionId,
  taskTitle,
  useTaskForm
} from '@mynotary/frontend/legals/feature';
import { setErrorMessage } from '@mynotary/frontend/snackbars/store';
import { DateFormQuestion, FormQuestion, SelectFormQuestion } from '@mynotary/crossplatform/shared/forms-util';

interface TaskEditionProps extends MnProps {
  onTaskValidation: () => void;
  task: Task;
}

export const TaskEdition = ({ className, onTaskValidation, task }: TaskEditionProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const legalComponent = useSelector(selectLegalComponentById(task.legalComponentId)) as Record | Operation;

  const [isLoading, setIsLoading] = useState(false);

  const { answer, form, handleAnswerChange, isCompleted } = useTaskForm({
    defaultAnswer: {
      [TaskFormQuestionId.title]: { value: task.title },
      [TaskFormQuestionId.description]: { value: task.description },
      [TaskFormQuestionId.deadline]: { value: task.dueDate ? 'oui' : 'non' },
      [TaskFormQuestionId.deadlineReminder]: { value: task.reminderType },
      [TaskFormQuestionId.deadlineDate]: { value: task.dueDate }
    },
    initialForm: getEditionForm(task.type)
  });

  const handleValidation = async () => {
    setIsLoading(true);

    const { deadlineDate, deadlineReminder, description, title } = getTaskFormAnswer(answer);

    try {
      await dispatch(
        updateTask(legalComponent.id, task.id, {
          description,
          dueDate: deadlineDate ?? null,
          reminderType: deadlineDate ? deadlineReminder : null,
          title
        })
      );
      onTaskValidation();
    } catch (error) {
      console.error(error);
      dispatch(setErrorMessage('Une erreur est survenue lors de la mise à jour de la tâche'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={classNames('task-edition', className)}>
      <div className='te-title'>Édition</div>
      <div className='te-description'>Éditer les informations de la tâche sélectionnée</div>
      <div className='te-container'>
        <Form answer={answer} editable={!isLoading} forms={form} onChange={handleAnswerChange} />
        <div className='te-validation'>
          <MnCustomLoader loading={isLoading} position='right'>
            <MnButton disabled={!isCompleted || isLoading} label={'Valider'} onClick={handleValidation} />
          </MnCustomLoader>
        </div>
      </div>
    </div>
  );
};

const getEditionForm = (type: TaskType): Array<FormQuestion | SelectFormQuestion | DateFormQuestion> => {
  switch (type) {
    case TaskType.CUSTOM:
      return [taskTitle, taskDescription, ...taskDeadline];
    case TaskType.VALIDATE_CONTRACT:
    case TaskType.REVIEW_CONTRACT:
      return [taskTitle, taskDescription];
    default:
      return [taskTitle, ...taskDeadline];
  }
};
