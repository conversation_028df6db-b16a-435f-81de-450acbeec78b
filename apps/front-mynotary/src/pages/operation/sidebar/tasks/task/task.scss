@use 'style/mixins' as *;
@use 'style/variables' as *;

.mn-task {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  align-items: center;

  height: 100%;

  .mnt-title {
    @include h4-font($bold);

    flex-shrink: 0;
    margin: 32px auto 0;
    color: var(--primary);
    text-align: center;
  }

  .mnt-title + .mnt-template {
    margin-top: 32px;
  }

  .mnt-template {
    overflow: auto;
    display: flex;
    flex-direction: column;

    width: 100%;
    height: 100%;
    padding: 0 16px;
  }

  .mnt-validate {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;

    width: 100%;
    min-height: 102px;
    padding: 16px 0;

    background-color: $white;
  }
}

.mnt-validated {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;

  width: 100%;
  margin-bottom: 16px;

  background-color: $white;

  .mnt-validated-title {
    @include h4-font;

    margin-bottom: 16px;
    color: var(--primary);
  }

  .mnt-validated-desc {
    @include medium-font;

    color: $black;
  }
}
