import './taskTile.scss';
import React, { ReactElement, useState } from 'react';
import { useSelector } from 'react-redux';
import { isEmpty, map } from 'lodash';
import { MnPopin, MnSvg } from '@mynotary/frontend/shared/ui';
import { selectCurrentUser } from '@mynotary/frontend/user-session/store';
import { getOperationLabel, Operation, ContractLegacy, Task } from '@mynotary/frontend/legals/core';
import { TaskType } from '@mynotary/crossplatform/legals/core';
import { classNames, MnProps, openInNewTab, User } from '@mynotary/frontend/shared/util';
import { fns } from '@mynotary/crossplatform/shared/dates-util';
import { selectContractsByOperationId, selectOperation } from '@mynotary/frontend/legals/store';
import { TaskDescription } from './taskDescription';
import { MnTask } from 'pages/operation/sidebar/tasks/task/task';
import { TaskActions } from 'pages/operation/sidebar/tasks/taskTile/task-actions';
import { getOperationContractRoute } from '@mynotary/frontend/routes/core';
import { isImportedContract } from '@mynotary/crossplatform/legals/api';

interface MnTaskTileProps extends MnProps {
  onDelete: () => void;
  task: Task;
}

const MnTaskTile = ({ className, onDelete, task }: MnTaskTileProps): ReactElement => {
  const currentUser = useSelector(selectCurrentUser);
  const operation = useSelector(selectOperation(task.legalComponentId));
  const contracts = useSelector(selectContractsByOperationId(task.legalComponentId));
  const label = getTaskLabel(task, operation, contracts);
  const [isPopinOpened, setIsPopinOpened] = useState(false);
  const isTaskPayment = task.type === TaskType.PAYMENT_REQUEST;

  const assignees = map(task.assignees, (assignee) => computeName(assignee.user, currentUser)).join(', ');

  const handleClick = () => {
    if (task.type === TaskType.VALIDATE_CONTRACT || task.type === TaskType.REVIEW_CONTRACT) {
      const contractId = task.reference.contractId;

      const contract = contracts.find((contract) => contract.id === contractId);

      if (contract) {
        const url = getOperationContractRoute({
          contractId: contract.id,
          isImportedContract: isImportedContract(contract.legalContractTemplateId),
          operationId: operation.id,
          parentOperationId: operation.parentOperationId
        });
        openInNewTab(url);
      }
    } else if (isTaskPayment) {
      return;
    } else {
      setIsPopinOpened(true);
    }
  };

  return (
    <>
      <div className={classNames('mn-task-tile', className)} data-testid={'sidebar-task-tile'} onClick={handleClick}>
        <div className='mntl-status-container'>
          <MnSvg
            className={classNames('mntl-status', { done: task.completionTime })}
            path={`/assets/images/pictos/icon/check-circle.svg`}
            size='small'
            testId={'sidebar-task-tile-completed'}
            variant={task.completionTime ? 'green300' : 'gray500'}
          />
        </div>
        <div className='mntl-content'>
          <div className={classNames('mntl-title', { striked: !!task.completionTime })} title={task.title}>
            {task.title}
          </div>
          <div className='mntl-context'>{label}</div>
          <div className='mntl-lines'>
            <div className='mntl-line'>
              <span className='mntl-line-item'>
                <span className='mntl-line-item-label'>De : </span>
                <span className='mntl-line-item-value'>{computeName(task.creatorUser, currentUser)}</span>
              </span>
              {!isEmpty(assignees) && (
                <span className='mntl-line-item'>
                  <span className='mntl-line-item-label'>Pour : </span>
                  <span className='mntl-line-item-value'>{assignees}</span>
                </span>
              )}
            </div>
            <div className='mntl-line'>
              <span className='mntl-line-item'>
                <span className='mntl-line-item-label'>Créé le : </span>
                <span className='mntl-line-item-value'>{fns.format(task.creationTime, 'd MMM yy,  HH:mm')}</span>
              </span>

              {task.dueDate && (
                <span className='mntl-line-item'>
                  <span className='mntl-line-item-label'>Échéance : </span>
                  <span
                    className={classNames('mntl-line-item-value', {
                      red: !task.completionTime && task.dueDate && task.dueDate < Date.now()
                    })}
                  >
                    {fns.format(task.dueDate, 'd MMM yy')}
                  </span>
                </span>
              )}
            </div>
            <div className='mntl-line'>
              {task.reminderTime != null && (
                <span className='mntl-line-item'>
                  <span className='mntl-line-item-label'>Relancée le : </span>
                  <span className='mntl-line-item-value'>{fns.format(task.reminderTime, 'd MMM yy,  HH:mm')}</span>
                </span>
              )}
            </div>
            <div className='mntl-line'>
              {task.completionTime && (
                <span className='mntl-line-item'>
                  <span className='mntl-line-item-label'>Réalisée le : </span>
                  <span className='mntl-line-item-value'>{fns.format(task.completionTime, 'd MMM yy, HH:mm')}</span>
                </span>
              )}
            </div>
          </div>
          <TaskDescription description={task.description} />
        </div>
        {!isTaskPayment && <TaskActions onDelete={onDelete} task={task} />}
      </div>
      {isPopinOpened && (
        <MnPopin className='mn-task-tile-popin' onClose={() => setIsPopinOpened(false)}>
          <MnTask onClose={() => setIsPopinOpened(false)} taskId={task.id} />
        </MnPopin>
      )}
    </>
  );
};

export { MnTaskTile };

const getTaskLabel = (task: Task, operation: Operation, contracts: ContractLegacy[]) => {
  const label = getOperationLabel(operation);

  switch (task.type) {
    case TaskType.CUSTOM:
      return `${label ?? 'Dossier'} > Tâche libre`;
    case TaskType.SHARE_OPERATION_RECORD:
    case TaskType.FILL_OPERATION_RECORDS:
    case TaskType.VALIDATE_CONTRACT:
    case TaskType.DOWNLOAD_FILES: {
      const contract = contracts.find(
        (contract) => task.reference.contractId && contract.id === task.reference.contractId
      );
      return `${label ?? 'Dossier'}${task.reference.contractId ? ' > ' : ''}${
        task.reference.contractId ? (contract?.label ?? 'Contrat') : ''
      }`;
    }
  }
  return '-';
};

const computeName = (participant?: Partial<User>, currentUser?: User): string => {
  if (participant?.email === currentUser?.email) {
    return 'Vous';
  } else if (participant?.firstname) {
    return `${participant?.firstname}. ${participant?.lastname} - ${participant?.email}`;
  }

  return participant?.email ?? '-';
};
