import { getTaskLink, Task } from '@mynotary/frontend/legals/core';
import { TaskType } from '@mynotary/crossplatform/legals/core';
import {
  ActionIcon,
  MnButtonIcon,
  Popover,
  PopoverActionList,
  PopoverContent,
  PopoverTrigger
} from '@mynotary/frontend/shared/ui';
import { useState } from 'react';
import { openSidebarAction, SidebarActionType, updateTaskCompletion } from '@mynotary/frontend/legals/store';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { selectCurrentUser } from '@mynotary/frontend/user-session/store';
import { useSelector } from 'react-redux';
import { isEmpty, map, some } from 'lodash';
import { useCopyToClipboard } from 'react-use';
import { selectCanDeleteTask } from '@mynotary/frontend/legals/store';
import {
  setErrorMessage,
  setSnackbarMessage,
  setSuccessMessage,
  SnackbarMessage
} from '@mynotary/frontend/snackbars/store';

interface TaskActionsProps {
  onDelete: () => void;
  task: Task;
}

export const TaskActions = ({ onDelete, task }: TaskActionsProps) => {
  const connectedUser = useSelector(selectCurrentUser);
  const dispatch = useAsyncDispatch();

  const isAssignee = some(task.assignees, (assignee) => assignee.user?.email === connectedUser?.email);
  const isCreator = connectedUser?.id === task.creatorUserId;
  const recipients = map(task.assignees, (assignee) => assignee.user.email);
  const isExternalCollaboration =
    task.type !== TaskType.VALIDATE_CONTRACT && task.type !== TaskType.CUSTOM && task.type !== TaskType.REVIEW_CONTRACT;
  const [, copytoClipboard] = useCopyToClipboard();
  const canDeleteTask = useSelector(selectCanDeleteTask(task.id));

  const { remindTask, status } = useTaskReminder();

  /** Hide the task link for payment request tasks because its the success link payment */
  const isTaskPayment = task.type === TaskType.PAYMENT_REQUEST;

  const [openPopover, setOpenPopover] = useState(false);

  const handleCopyAnonymousLink = (task: Task) => {
    copytoClipboard(getTaskLink(task));
    dispatch(setSuccessMessage('Lien copié dans le presse-papier'));
  };

  return (
    <Popover onOpenChange={setOpenPopover} open={openPopover}>
      <PopoverTrigger asChild={true}>
        <MnButtonIcon
          mode={'fill'}
          onClick={() => setOpenPopover(true)}
          path={'/assets/images/pictos/icon/more-vertical-light.svg'}
          size={'small'}
        />
      </PopoverTrigger>
      <PopoverContent align={'end'}>
        <PopoverActionList>
          {task.completionTime == null && (isAssignee || isCreator) && (
            <ActionIcon
              icon={'/assets/images/pictos/icon/check-circle.svg'}
              label={'Marquer comme effectuée'}
              onClick={() => dispatch(updateTaskCompletion(task.legalComponentId, task.id, true))}
              testId={'COMPLETE'}
            />
          )}
          {task.completionTime != null && (isAssignee || isCreator) && (
            <ActionIcon
              icon={'/assets/images/pictos/icon/clipboard-light.svg'}
              label={'Marquer comme à faire'}
              onClick={() => dispatch(updateTaskCompletion(task.legalComponentId, task.id, false))}
              testId={'UNCOMPLETE'}
            />
          )}
          {!isEmpty(recipients) && isCreator && (
            <ActionIcon
              disabled={status === 'loading'}
              icon={'/assets/images/pictos/icon/reload-light.svg'}
              label={'Relancer'}
              onClick={() => remindTask(task)}
              testId={'REMIND'}
            />
          )}
          {isExternalCollaboration && isCreator && !isTaskPayment && (
            <ActionIcon
              icon={'/assets/images/pictos/icon/clipboard-light.svg'}
              label={'Copier le lien'}
              onClick={() => handleCopyAnonymousLink(task)}
              testId={'COPY_LINK'}
            />
          )}
          <ActionIcon
            disabled={!isCreator}
            icon={'/assets/images/pictos/icon/edit-2.svg'}
            label={'Éditer'}
            onClick={() => dispatch(openSidebarAction({ task, type: SidebarActionType.TASK_EDITION }))}
            testId={'EDIT'}
          />
          <ActionIcon
            disabled={!canDeleteTask}
            icon={'/assets/images/pictos/icon/trash-light.svg'}
            label={'Supprimer'}
            onClick={() => onDelete()}
            testId={'DELETE'}
            tooltipOnDisabled={
              "Vous n'avez pas les droits pour effectuer cette action, veuillez contacter votre administrateur."
            }
          />
        </PopoverActionList>
      </PopoverContent>
    </Popover>
  );
};

type ReminderStatus = 'idle' | 'loading' | 'success' | 'error';

function useTaskReminder() {
  const [status, setStatus] = useState<ReminderStatus>('idle');

  const dispatch = useAsyncDispatch();

  const remindTask = async (task: Task) => {
    try {
      setStatus('loading');
      await dispatch(updateTaskCompletion(task.legalComponentId, task.id, false));
      setStatus('success');
      dispatch(setSnackbarMessage(TASK_REMINDER_SUCCESS));
    } catch {
      dispatch(setErrorMessage('Une erreur est survenue lors de la relance de la tâche.'));
      setStatus('error');
    }
  };

  return { remindTask, status };
}

const TASK_REMINDER_SUCCESS: SnackbarMessage = {
  options: {
    color: 'success',
    description: 'Un email a été envoyé a votre destinataire.',
    title: 'Tâche relancée avec succès !'
  },
  templateId: 'TEMPORARY'
};
