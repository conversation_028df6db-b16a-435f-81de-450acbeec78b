import './registeredLetterFollowUpRoute.scss';
import { ReactElement, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { MnRegisteredLetterFollowUp } from 'pages/operation/contracts/registeredLetterFollowUp/followUp/registeredLetterFollowUp';
import {
  getRegisteredLetterBatches,
  selectLastRegisteredLettersByContractId
} from '@mynotary/frontend/registered-letters/store';
import { getSteps } from '../creation/registeredLetter/workflow/workflowSteps';
import { useParams } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { selectRegisteredLettersFeature } from '@mynotary/frontend/registered-letters/store';
import { SideNavigation } from '@mynotary/frontend/shared/ui';
import { getOperationUrl } from '@mynotary/frontend/routes/core';
import { setInfoMessage } from '@mynotary/frontend/snackbars/store';
import { useNavigate } from 'react-router-dom';
import { selectOperation } from '@mynotary/frontend/legals/api';

type RegisteredLetterFollowUpRouteProps = {
  operationId: number;
};

export const RegisteredLetterFollowUpRoute = ({ operationId }: RegisteredLetterFollowUpRouteProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const { registeredLetterId } = useParams();
  const registeredLetters = useSelector(selectRegisteredLettersFeature);
  const batch = registeredLetters?.[parseInt(registeredLetterId as string)];
  const lastBatch = useSelector(selectLastRegisteredLettersByContractId(batch?.association.legalComponent.contractId));
  const navigate = useNavigate();
  const operation = useSelector(selectOperation(operationId));

  const steps = getSteps();

  useEffect(() => {
    dispatch(getRegisteredLetterBatches(operationId));
  }, [dispatch, operationId]);

  useEffect(() => {
    if (lastBatch?.cancelationTime != null) {
      dispatch(setInfoMessage('Le recommandé a été annulé. Vous avez été redirigé sur la page du dossier.'));
      navigate(
        { pathname: getOperationUrl({ id: operationId, parentOperationId: operation.parentOperationId }) },
        { replace: true }
      );
    }
  }, [lastBatch?.cancelationTime, dispatch, navigate, operation?.parentOperationId, operationId]);

  return (
    <div className='registered-letter-follow-up-route'>
      <SideNavigation canNavigate={false} currentStep={steps[steps.length - 1]} steps={steps} />
      <div className='rlfur-container'>
        <div className='rlfur-title'>Notification et suivi</div>
        <div className='rlfur-description'>Consultez l'état d'avancée de la notification.</div>
        {batch && <MnRegisteredLetterFollowUp batch={batch} />}
      </div>
    </div>
  );
};
