import './operationContractsModel.scss';
import React, { ReactElement, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { map } from 'lodash';
import { TablePlaceholder } from '@mynotary/frontend/shared/ui';
import { MnDisplayTable } from 'components/_dataDisplay/simpleTable/displayTable';
import { TitleRendererRow } from 'components/_dataDisplay/simpleTable/rendererComponents/titleRendererRow';
import { selectContractsByOperationId, selectOperation } from '@mynotary/frontend/legals/store';
import { routePaths } from '@mynotary/frontend/routes/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { useRestrictAppAccess } from '@mynotary/frontend/billings/feature';
import { fns } from '@mynotary/crossplatform/shared/dates-util';
import { useNavigate } from 'react-router';
import { getOperationUrl, navigateWithEvents } from '@mynotary/frontend/routes/core';
import { openPopin } from '@mynotary/frontend/popins/store';
import { ContractCreationWorkflowStepper, PopinType } from '@mynotary/frontend/popins/core';
import { getTemplateContracts, LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/core';
import { ContractsModelActions } from '@mynotary/frontend/contracts-dashboard/feature';

interface OperationContractsModelProps {
  operationId: number;
  operationTemplate: LegalOperationTemplate;
}

const headerColumns = [
  {
    content: 'NOM DU CONTRAT',
    id: 'modelLabel',
    isMobile: true,
    isTitle: true
  },

  {
    content: 'DATE CRÉATION',
    id: 'creationTime',
    isMobile: true
  },
  { content: '', id: 'actions' }
];

export const OperationContractsModel = ({
  operationId,
  operationTemplate
}: OperationContractsModelProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const contracts = useSelector(selectContractsByOperationId(operationId));
  const operation = useSelector(selectOperation(operationId));
  const navigate = useNavigate();
  const { isAccessRestricted, tooltip } = useRestrictAppAccess();
  const configContracts = getTemplateContracts(operationTemplate);

  const modelContracts = useMemo(
    () =>
      contracts.filter((contract) => configContracts?.some((model) => model.id === contract.legalContractTemplateId)),
    [contracts, configContracts]
  );

  const getUrl = (index: number): string => {
    const contractId = modelContracts?.[index]?.id;

    if (contractId) {
      return getOperationUrl(operation, routePaths.operation.contrats.redactionFollowUp.path, {
        contractId
      });
    }

    return '';
  };

  const contractRows = map(modelContracts, (contract) => {
    const PopOverComponent = <ContractsModelActions contract={contract} />;

    return {
      actions: PopOverComponent,
      creationTime: fns.format(contract.creationTime, 'dd/MM/yyyy'),
      modelLabel: <TitleRendererRow icon={PopOverComponent} label={contract.label} />,
      responsiveSecondaryTitle: 'Ouvrir la trame'
    };
  });

  return (
    <div className='operation-contracts-model'>
      <MnDisplayTable
        headerColumns={headerColumns}
        onClick={(index, e) => navigateWithEvents({ event: e, navigate, url: getUrl(index) })}
        placeholder={
          <TablePlaceholder
            buttonLabel='Nouvelle trame'
            isDisabled={isAccessRestricted}
            isVisible={contractRows.length === 0}
            label='Aucune trame créé pour le moment'
            onClick={() => {
              dispatch(
                openPopin({
                  defaultStep: { step: ContractCreationWorkflowStepper.MYNOTARY_CONTRACT_SELECTION },
                  isModel: true,
                  operationId,
                  type: PopinType.CREATE_CONTRACT
                })
              );
            }}
            tooltip={tooltip}
          />
        }
        rows={contractRows}
      />
    </div>
  );
};
