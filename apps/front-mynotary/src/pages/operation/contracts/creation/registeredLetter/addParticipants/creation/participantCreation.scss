@use 'style/mixins' as *;
@use 'style/variables/colors' as *;

.participant-creation {
  display: flex;
  flex-direction: column;
  align-items: center;

  .pc-search {
    @include mn-link;
    @include medium-font($bold);

    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    margin-right: auto;
    margin-bottom: 16px;
  }

  .pc-search-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }

  .pc-check {
    @include small-font;

    display: flex;
    flex-direction: row;
    justify-content: center;

    margin-top: 16px;
    margin-right: auto;

    font-weight: 500;
    color: var(--primary);
  }

  .pc-checkbox {
    margin-right: 5px;
  }

  .pc-validation {
    position: relative;
    margin-top: 20px;
  }

  .pc-loader-wrapper {
    position: absolute;
    top: 50%;
    left: calc(100% + 10px);
    transform: translateY(-50%);
  }

  .pc-loader {
    @include mn-loader;
  }
}
