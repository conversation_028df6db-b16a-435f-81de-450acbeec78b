import './commonCreationSubscribers.scss';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { ReactElement, ReactNode, useEffect, useState } from 'react';
import { Form } from '@mynotary/frontend/legals/feature';
import { FooterLayout, MnButton } from '@mynotary/frontend/shared/ui';
import { MnProps } from '@mynotary/frontend/shared/util';
import { EmailOption } from '@mynotary/crossplatform/shared/forms-util';

interface CommonCreationSubscribersProps extends MnProps {
  footerLeftAction: ReactNode;
  onChange: (subscribers: EmailOption[]) => Promise<void>;
  onPreviousStep: () => void;
  onValidate: () => void;
  subscribers?: EmailOption[];
}

const subscribersForm = [
  {
    id: 'email',
    label: 'Personnes externes (optionnel)',
    multiple: true,
    optional: true,
    type: 'SELECT-EMAIL'
  }
];

const CommonCreationSubscribers = ({
  footerLeftAction,
  onChange,
  onPreviousStep,
  onValidate,
  subscribers
}: CommonCreationSubscribersProps): ReactElement => {
  const [answer, setAnswer] = useState<AnswerDict>({});

  useEffect(() => {
    setAnswer({ email: { value: subscribers } });
  }, [subscribers]);

  const handleAddSubscriber = async (answer: AnswerDict) => {
    if (answer) {
      await onChange(answer['email'].value);
      setAnswer(answer);
    }
  };

  return (
    <div className='common-creation-subscribers'>
      <Form answer={answer} forms={subscribersForm} onChange={handleAddSubscriber} />
      <FooterLayout.Container hasSidebar={true}>
        <FooterLayout.LeftSide>{footerLeftAction}</FooterLayout.LeftSide>
        <FooterLayout.RightSide>
          <MnButton
            icon='/assets/images/pictos/icon/arrow-left-light.svg'
            label='Étape précédente'
            labelPosition='right'
            onClick={onPreviousStep}
            size='medium'
            variant='secondary'
          />
          <MnButton
            icon='/assets/images/pictos/icon/arrow-right-light.svg'
            label='Étape suivante'
            onClick={onValidate}
            size='medium'
            testId='workflow-footer-next-step'
            variant='primary'
          />
        </FooterLayout.RightSide>
      </FooterLayout.Container>
    </div>
  );
};

export { CommonCreationSubscribers };
