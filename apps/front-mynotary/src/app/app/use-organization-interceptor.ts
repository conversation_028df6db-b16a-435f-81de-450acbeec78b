import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { selectCurrentMemberOrganizationId } from '@mynotary/frontend/user-session/store';
import { ApiHelpers } from '@mynotary/frontend/shared/axios-util';
import { InternalAxiosRequestConfig } from 'axios';
import { selectCurrentOperationOrganizationId } from '@mynotary/frontend/legals/store';

let interceptorId: number | null = null;

export const useOrganizationInterceptor = (): void => {
  const organizationId = useSelector(selectCurrentMemberOrganizationId);
  const operationOrganizationId = useSelector(selectCurrentOperationOrganizationId);

  useEffect(() => {
    if (interceptorId != null) {
      ApiHelpers.interceptEject(interceptorId);
    }

    interceptorId = ApiHelpers.intercept((config: InternalAxiosRequestConfig) => {
      return {
        ...config,
        params: {
          organizationId: operationOrganizationId ?? organizationId,
          ...config.params
        }
      };
    });
    return () => {
      if (interceptorId != null) {
        return ApiHelpers.interceptEject(interceptorId);
      }
    };
  }, [operationOrganizationId, organizationId]);
};
