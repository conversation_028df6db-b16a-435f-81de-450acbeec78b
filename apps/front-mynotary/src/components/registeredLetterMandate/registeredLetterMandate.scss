@use 'style/mixins' as *;
@use 'style/variables/colors' as *;

.mn-registered-letter-mandate {
  overflow: auto;
  display: flex;
  flex-direction: column;

  width: 100%;
  height: 100%;
  padding: 24px 16px;

  .sla-signatory-list-tile {
    max-width: none;
  }

  .rlm-signature-actions {
    display: flex;
    gap:16px;
  }

  .rlm-title {
    @include h4-font($bold);

    margin-bottom: 20px;
    color: var(--primary);
  }

  .rlm-description {
    @include medium-font;

    display: block;

    margin-bottom: 20px;
    padding: 0 60px;

    color: $black;
    text-align: center;
  }

  .rlm-step {
    flex-grow: 1;
  }
}
