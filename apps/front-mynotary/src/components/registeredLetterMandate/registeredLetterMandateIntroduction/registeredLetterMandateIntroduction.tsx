import './registeredLetterMandateIntroduction.scss';
import React, { ReactElement } from 'react';
import { MnButton } from '@mynotary/frontend/shared/ui';
import { MnProps, classNames } from '@mynotary/frontend/shared/util';

interface RegisteredLetterMandateIntroductionProps extends MnProps {
  onValidate: () => void;
}

const RegisteredLetterMandateIntroduction = ({
  className,
  onValidate
}: RegisteredLetterMandateIntroductionProps): ReactElement => {
  return (
    <div className={classNames('rlm-introduction', className)}>
      <p className='rlmi-description'>
        <br />
        Depuis le 1er janvier 2019, de nouvelles dispositions légales concernant le recommandé électronique qualifié
        sont entrées en vigueur.
        <br />
        <br />
        Il est ainsi désormais nécessaire de nous donner l'autorisation d'expédier automatiquement, et en votre nom, les
        lettres recommandées électroniques de vos futurs dossiers.
        <br />
        <br />
        Il vous suffit, pour cela, de signer électroniquement le mandat ci-après. L'opération est totalement gratuite et
        prend seulement quelques minutes 🙂
      </p>
      <br />
      <br />
      <MnButton label='Compléter et faire signer le mandat' onClick={onValidate} />
    </div>
  );
};

export { RegisteredLetterMandateIntroduction };
