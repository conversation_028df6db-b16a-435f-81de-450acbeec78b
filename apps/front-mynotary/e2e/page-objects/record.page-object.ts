import { Page } from '@playwright/test';
import { expect } from '../shared/fixtures';
import { completeManualAddress } from '../utils/address-utils';

export class RecordPageObject {
  constructor(private page: Page) {}

  async addRecordLink(buttonLabel: string) {
    const isVisible = await this.page.getByRole('button', { name: buttonLabel }).isVisible();

    if (isVisible) {
      await this.page.getByRole('button', { name: 'Ajouter une copropriété' }).click();
      await this.page.getByRole('button', { name: 'Nouvelle fiche' }).click();
      await this.page.getByRole('button', { name: 'confirmer' }).click();
      await this.page.getByRole('progressbar', { name: 'loader' }).isVisible();
      const duplicatePromise = this.page.waitForResponse((resp) => resp.url().includes('duplicates'));
      await duplicatePromise;

      await expect(this.page.getByRole('progressbar', { name: 'loader' })).toBeHidden();
      await this.page.getByRole('button', { name: 'Créer une copropriété +' }).click();
    }
  }

  async addPersonnalInformation() {
    await this.page
      .getByTestId('informations_personnelles_ville_naissance')
      .getByPlaceholder('Entrez votre texte*')
      .fill('Lyon');
    await this.page.getByTestId('informations_personnelles_nationalite').getByText('Sélectionner*').click();
    await this.page.getByTestId('option-France').getByText('France').click();
    await this.page.getByPlaceholder('Adresse e-mail*').fill('<EMAIL>');
    await this.page.getByTestId('telephone').getByRole('textbox').fill('+33 6 28 52 09 28');
  }

  async selectLink(args: SelectLinkArgs) {
    const promise = this.page.waitForResponse((resp) => resp.url().includes('legal-links'));
    await this.page.getByTestId(`link-creation-${args.type}`).getByText('Sélectionner*').click();
    await this.page.getByTestId(`option-${args.option}`).locator('div').click();
    await promise;
  }

  async removeLink(args: SelectLinkArgs) {
    await this.page.getByTestId(`link-creation-${args.type}`).getByText(args.option).click();
    await this.page.getByTestId(`option-${args.option}`).locator('div').click();
  }

  async addPersonnePhysique({ firstname, lastname }: { firstname: string; lastname: string }) {
    /* Create record in side popin */
    await this.page.getByTestId('record-creation').getByText('Sélectionner*').click();
    await this.page.getByTestId('option-Monsieur').getByText('Monsieur').click();
    await this.page
      .getByTestId('record-creation')
      .getByTestId('nom')
      .getByPlaceholder('Entrez votre texte*')
      .fill(lastname);
    await this.page
      .getByTestId('record-creation')
      .getByTestId('prenoms')
      .getByPlaceholder('Entrez votre texte*')
      .fill(firstname);
  }

  async createConjoint(args: { firstname: string; lastname: string; selectDuplicateFirst?: boolean }) {
    await this.selectLink({ option: 'Mariage', type: 'situation_maritale' });
    await this.page.getByText('Ajouter un conjoint').click();
    await this.page.getByText('Nouvelle fiche').click();
    await this.addPersonnePhysique(args);

    if (args.selectDuplicateFirst) {
      await this.selectFirstDuplicatedRecordIfExists();
    } else {
      await this.page.getByRole('button', { name: 'Confirmer' }).click();
    }
  }

  async createRepresentant({
    contractId,
    firstname,
    lastname
  }: {
    contractId: string;
    firstname: string;
    lastname: string;
  }) {
    await this.selectLink({ option: 'Oui', type: `procuration-${contractId}` });
    await this.page.getByText('Ajouter le représentant').click();
    await this.page.getByText('Nouvelle fiche').click();
    await this.addPersonnePhysique({ firstname, lastname });
  }

  async searchAndSelectFirstItem(label: string) {
    await this.page.getByText('Fiche existante').click();
    await this.page.getByPlaceholder(/^Rechercher un/).fill(label);
    await this.page.getByTestId('record-item').first().click();
  }

  async takeRegisterNumber(entryValue: string) {
    await this.page.getByPlaceholder('entrez votre numéro de mandat').fill(entryValue);
  }

  async overrideRegisterNumber(override: boolean) {
    override
      ? await this.page.getByRole('button', { name: 'Oui' }).click()
      : await this.page.getByRole('button', { name: 'Non' }).click();
  }

  async triggerMandatDefaultAnswer() {
    const answer = this.page.waitForResponse((resp) => resp.url().includes('/records/'));
    await this.page.getByText('En pourcentage').click();
    await answer;
    await this.closeRedactionWorkflow();
  }

  async closeRedactionWorkflow() {
    await this.page.getByRole('button', { name: 'Fermer' }).click();
  }

  async selectFirstDuplicatedRecordIfExists({
    buttonLabel = 'Confirmer',
    slowDown = false
  }: { buttonLabel?: string; slowDown?: boolean } = {}) {
    const duplicatePromise = this.page.waitForResponse((resp) => resp.url().includes('duplicates'));
    await this.page.getByRole('button', { name: buttonLabel }).click();
    await duplicatePromise;

    /**
     * Sometime we delay request voluntary so user can read the loader (eg: coproprietes search)
     * In theses case we need to explicitly wait to avoid flaky test
     */
    if (slowDown) {
      // eslint-disable-next-line playwright/no-wait-for-timeout
      await this.page.waitForTimeout(1000);
    }

    const visible = await this.page.getByTestId('record-item').first().isVisible();

    if (visible) {
      await this.page.getByTestId('record-item').first().click();
    }
  }

  async deleteLegalBranch(label: string) {
    await this.page.getByTestId(`branch-actions-${label}`).click();
    await this.page.getByRole('option', { name: 'Supprimer' }).click();
  }

  async addRepeatQuestion() {
    await this.page.getByTestId('repeat-question-add-button').click();
  }

  async fillMariageInformation() {
    const promiseVille = this.page.waitForResponse((resp) => resp.url().includes('records') && resp.ok());
    await this.page.getByTestId('ville_mariage').getByPlaceholder('Entrez votre texte*').fill('Lyon');
    await promiseVille;

    const promiseContratMariage = this.page.waitForResponse((resp) => resp.url().includes('records') && resp.ok());
    await this.page.getByTestId('contrat_mariage').getByText('NON').click();
    await promiseContratMariage;
  }

  async removeConjoint({ firstname, lastname }: { firstname: string; lastname: string }) {
    const promise = this.page.waitForResponse((resp) => resp.url().includes('legal-branches') && resp.ok());
    await this.page.getByTestId(`branch-actions-${firstname} ${lastname}`).click();
    await this.page.getByRole('option', { name: 'Supprimer' }).locator('div').click();
    await promise;
  }

  async fillConjointInformation({ firstname }: { firstname: string }) {
    const nomPromise = this.page.waitForResponse((resp) => resp.url().includes('records'));
    await this.page
      .getByTestId('record-links-SITUATION_MARITALE')
      .getByTestId('prenoms')
      .getByPlaceholder('Entrez votre texte*')
      .fill(firstname);
    await nomPromise;
  }

  async createLot(label: string) {
    await this.page.getByText('Sélectionner*').click();
    await this.page.getByText('Bien en copropriété - Habitation').click();
    await this.page.getByText('Sélectionner*').click();
    await this.page.getByTestId('option-Appartement').getByText('Appartement').click();
    await this.page.getByPlaceholder('Entrez votre texte*').fill(label);
    await completeManualAddress({ locator: this.page.getByLabel('Adresse'), page: this.page, zipCode: '69004' });
    await this.page.getByRole('button', { name: 'Confirmer' }).click();
  }

  async createCopro({ city = 'Lyon', street = '7 rue des fleurs', zipCode = '69004' }) {
    await completeManualAddress({
      city,
      locator: this.page.getByLabel('Adresse'),
      page: this.page,
      street,
      zipCode
    });
    await this.page.getByRole('button', { name: 'Confirmer' }).click();
    const searchCopro = this.page.waitForResponse((resp) => resp.url().includes('/duplicates'));
    await searchCopro;
  }
}

type SelectLinkArgs =
  | {
      option: 'Sauvegarde de justice' | 'Curatelle';
      type: 'capacite';
    }
  | {
      option: 'Mariage' | 'Célibataire';
      type: 'situation_maritale';
    }
  | {
      option: 'Oui';
      type: `procuration-${string}`;
    }
  | { option: 'Oui' | 'Non'; type: 'lot_annexe' };
