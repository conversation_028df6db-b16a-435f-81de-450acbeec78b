import { Page } from '@playwright/test';
import { importDummyJpeg } from '../utils/files-utils';
import { DocumentRequestsPageObject } from '../page-objects/document-requests.page-object';

export class DrivePage {
  documentRequestsPageObject: DocumentRequestsPageObject;
  constructor(private page: Page) {
    this.documentRequestsPageObject = new DocumentRequestsPageObject(page);
  }

  async goTo({ operationId }: { operationId: string }) {
    await this.page.goto(`/operation/${operationId}/espace-stockage`);
  }

  async uploadDummyDriveFile() {
    const waitForDriveFile = this.page.waitForResponse((resp) => resp.url().includes('/drive-files'));
    await importDummyJpeg(this.page, this.page.getByText('Ajouter de nouveaux fichiers'));
    await waitForDriveFile;
  }

  async createFolder(folderName: string) {
    const waitForDriveFolder = this.page.waitForResponse((resp) => resp.url().includes('/drive-folder'));
    await this.page.getByRole('button', { name: 'Nouveau' }).click();
    await this.page.getByRole('dialog').getByText('Créer un sous-dossier').click();
    await this.page.getByPlaceholder('Entrez votre texte*').fill(folderName);
    await this.page.getByRole('button', { exact: true, name: 'Créer' }).click();
    await waitForDriveFolder;
  }

  async dragDriveFileToFolder(fileName: string, folderName: string) {
    const waitForDriveFile = this.page.waitForResponse((resp) => resp.url().includes('/drive-files/'));
    await this.page.getByTestId('drive-folder').getByText(folderName).click();
    await this.page.getByTestId('drive-file').getByText(fileName).hover();
    await this.page.mouse.down();
    await this.page.getByTestId('drive-folder').getByText(folderName).hover();
    await this.page.mouse.up();
    await waitForDriveFile;
  }

  selectDriveFolder(label: string) {
    return this.page.getByTestId('drive-folder').filter({ hasText: label });
  }

  selectDriveFile(label: string) {
    return this.page.getByTestId('drive-file').filter({ hasText: label });
  }

  async initShareDocuments() {
    await this.page.getByTestId('drive-actions').click();
    await this.page.getByTestId('operation-document-share').click();
  }

  async clickDriveFileInSharePopin(label: string) {
    await this.page.getByTestId('operation-files-popin').getByTestId('drive-file').filter({ hasText: label }).click();
  }

  async clickShareDocument() {
    await this.page.getByRole('button', { name: 'Partager' }).click();
  }

  async clickAskDocument() {
    await this.page.getByRole('button', { name: 'Demander des documents' }).first().click();
  }
}
