import { Page } from '@playwright/test';
import { importDummyPdf, importErrorPdf } from '../utils/files-utils';
import { RecordPageObject } from '../page-objects/record.page-object';

export class SignaturesPage {
  recordPageObject: RecordPageObject;
  constructor(private page: Page) {
    this.recordPageObject = new RecordPageObject(page);
  }

  async goToPublicPage(token: string, signatureId: string) {
    const waitForArchive = this.page.waitForResponse((resp) => resp.url().includes('/signature-archives'));
    await this.page.goto(`/signatures?token=${token}&signatureId=${signatureId}`);
    await waitForArchive;
  }

  async createElectronicSignatureWithGroups() {
    await this.initializeSignature('Électronique');

    await this.createOnlineSignatory({ firstname: 'Jean', lastname: 'PLAYWRIGHT' });
    await this.createOnlineSignatory({ firstname: '<PERSON>', lastname: 'PLAYWRIGHT_2' });

    await this.createSignatoryOrder();

    await this.goToNextStep();

    await this.addSignatoryGroup();
    await this.moveSignatoryToGroup({ groupIndex: 1, signatoryIndex: 0 });

    await this.goToNextStep();

    await importDummyPdf(this.page, 'Importer');
    await this.waitForUpdate();

    await this.goToNextStep();
    await this.waitForUpdate();

    await this.goToNextStep();

    await this.startSignature('Électronique');
  }

  async createElectronicSignature() {
    await this.initializeSignature('Électronique');

    await this.createOnlineSignatory({ firstname: 'Jean', lastname: 'PLAYWRIGHT' });

    await this.goToNextStep();

    await importDummyPdf(this.page, 'Importer');
    await this.waitForUpdate();

    await this.goToNextStep();
    await this.waitForUpdate();

    await this.goToNextStep();

    await this.startSignature('Électronique');
  }

  async createPaperSignature() {
    await this.selectSignatureType('Papier');

    await this.goToNextStep();
    await this.goToNextStep();

    await this.startSignature('Papier');
  }

  async createCompletedSignature() {
    await this.initializeSignature('Papier');
    await this.createPaperSignatory({ firstname: 'Jean', lastname: 'PLAYWRIGHT' });
    await this.goToNextStep();

    await importDummyPdf(this.page, 'Importer');

    await this.waitForUpdate();
    await this.goToNextStep();

    await this.startSignature('Papier');

    await this.page.getByRole('button', { name: 'Indiquer comme signé' }).click();
    await importDummyPdf(this.page, 'Importer');
    await this.page.getByRole('button', { name: 'Étape suivante' }).click();
    await this.page.getByTestId('date-input').fill('12/05/2025');
    /**
     * We click to remove the date picker that stays open
     */
    await this.page.getByTestId('date-input').click();
    await this.page.getByRole('button', { name: 'Valider' }).click();
  }

  async createUncertifiedSignature() {
    await this.selectSignatureType('Non certifiée');

    await this.updateSignatoryInformation();

    await this.goToNextStep();

    await this.goToNextStep();

    await this.waitForUpdate();
    await this.goToNextStep();

    await this.startSignature('Non certifiée');
  }

  async createSignatureWithPdfError() {
    await this.initializeSignature('Électronique');

    await this.createOnlineSignatory({ firstname: 'Jean', lastname: 'PLAYWRIGHT' });

    await this.goToNextStep();

    await importErrorPdf(this.page, 'Importer');

    await this.waitForUpdate();

    await this.goToNextStep();
    await this.waitForUpdate();

    await this.goToNextStep();

    await this.startSignature('Électronique');
  }

  async goToNextStep() {
    await this.page.getByTestId('workflow-footer-next-step').click();
  }

  async chooseSignatureType(signatureType: SignatureType) {
    const waitForSignature = this.page.waitForResponse((resp) => resp.url().includes('/signatures'));
    await this.page.getByText(signatureType).click();
    await waitForSignature;
  }

  async startSignature(type: SignatureType) {
    const url = type === 'Papier' || type === 'Non certifiée' ? '/start' : '/signature-actives';
    const waitForSignatureStart = this.page.waitForResponse((resp) => resp.url().includes(url));
    await this.page.getByRole('button', { name: 'Lancer la signature' }).click();
    await waitForSignatureStart;
  }

  getFollowUpGroups() {
    return this.page.getByTestId('signatory-group');
  }

  getSignatoryTile() {
    return this.page.getByTestId('signatory-tile');
  }

  private async initializeSignature(type: SignatureType) {
    await this.page.getByRole('button', { name: 'Actions fréquentes' }).click();
    await this.page.getByText('Faire signer un fichier').click();

    await this.page
      .locator('div')
      .filter({ hasText: /^Mandat de venteImporter$/ })
      .getByRole('button')
      .click();
    await this.page.getByRole('button', { name: 'Créer' }).click();
    await this.page.getByRole('button', { name: 'Lancer la signature' }).click();

    await this.selectSignatureType(type);
  }

  private async selectSignatureType(type: SignatureType) {
    await this.page.getByTestId(convertToTestId(type)).first().click();
    await this.page.waitForResponse((resp) => resp.url().includes('/v3/signatures'));
  }

  private async createOnlineSignatory({ firstname, lastname }: { firstname: string; lastname: string }) {
    await this.page.getByText('Nouvelle fiche', { exact: true }).click();
    await this.page.getByTestId('nom').getByPlaceholder('Entrez votre texte*').fill(lastname);
    await this.page.getByTestId('prenoms').getByPlaceholder('Entrez votre texte*').fill(firstname);
    await this.page.getByPlaceholder('Adresse e-mail*').fill('<EMAIL>');
    await this.page.getByTestId('telephone').getByRole('textbox').fill('+33 06 06 06 06 06');
    await this.recordPageObject.selectFirstDuplicatedRecordIfExists();
  }

  private async createPaperSignatory({ firstname, lastname }: { firstname: string; lastname: string }) {
    await this.page.getByText('Nouvelle fiche', { exact: true }).click();
    await this.page.getByTestId('nom').getByPlaceholder('Entrez votre texte*').fill(lastname);
    await this.page.getByTestId('prenoms').getByPlaceholder('Entrez votre texte*').fill(firstname);
    await this.page.getByText('Non').click();

    await this.recordPageObject.selectFirstDuplicatedRecordIfExists();
  }

  private async updateSignatoryInformation() {
    await this.page.getByText('Modifier').click();

    await this.page.getByPlaceholder('Adresse e-mail*').fill('<EMAIL>');
    await this.page.getByTestId('telephone').getByRole('textbox').fill('+33 06 06 06 06 06');
    await this.page.getByRole('button', { name: 'Terminer' }).click();
  }

  private async addSignatoryGroup() {
    await this.page.getByRole('button', { name: 'Créer un groupe' }).click();
  }

  private async moveSignatoryToGroup({ groupIndex, signatoryIndex }: { groupIndex: number; signatoryIndex: number }) {
    await this.page.getByRole('button', { name: 'Créer un groupe' }).click();
    await this.page.getByRole('button', { name: 'Ajouter un signataire' }).nth(groupIndex).click();
    await this.page.getByTestId('signatory-selection').nth(signatoryIndex).click();
    await this.page.getByRole('button', { name: 'Confirmer' }).click();
  }

  private async createSignatoryOrder() {
    await this.page.getByTestId('signature-group-switch').click();
  }

  private async waitForUpdate() {
    await this.page.waitForResponse((resp) => resp.url().includes('/v3/signatures'));
  }
}

type SignatureType = 'Électronique' | 'Papier' | 'Non certifiée';

function convertToTestId(type: SignatureType) {
  switch (type) {
    case 'Électronique':
      return 'electronic-signature-simple';
    case 'Non certifiée':
      return 'uncertified-signature';
    case 'Papier':
      return 'paper-signature';
  }
}
