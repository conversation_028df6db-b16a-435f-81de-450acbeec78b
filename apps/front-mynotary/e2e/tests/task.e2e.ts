import { expect, test } from '../shared/fixtures';
import { OperationDetailPage } from '../pages/operation-detail.page';
import { RedactionPage } from '../pages/redaction.page';
import { TaskPage } from '../pages/task.page';
import { authUserStoragePath } from '../shared/utils';

test.use({ storageState: authUserStoragePath });

test('create a simple task', async ({ clients, page }) => {
  const operation = await clients.operations.createVenteAncien();
  const operationDetailPage = new OperationDetailPage(page);
  await operationDetailPage.goTo(operation.id);

  await operationDetailPage.sidebar.openAsideTask();
  await operationDetailPage.sidebar.createSimpleTask();

  await expect(page.getByTestId('sidebar-task-tile')).toBeVisible();
});

test('create a task with files sharing', async ({ clients, incognitoPage, page }) => {
  const operationDetailPage = new OperationDetailPage(page);
  const operationId = await clients.operations.createVenteAncien();
  const record = await clients.records.createHouse();
  const operation = await clients.operations.getOperation(operationId.id);

  await clients.operations.linkRecord({
    linkId: operation.bienVenduLinkId,
    operationId: operation.id,
    recordId: record.id,
    type: 'BIEN_VENDU'
  });

  await clients.contracts.createMandat({ operationId: operation.id });

  await operationDetailPage.goTo(operation.id);

  await operationDetailPage.createShareDocumentTask();
  const taskLink = await operationDetailPage.sidebar.shareTaskWithExternalLink();

  const tasksPage = new TaskPage(incognitoPage);

  await tasksPage.goTo(taskLink);

  const downloadSize = await tasksPage.downloadFiles();
  expect(downloadSize).toBeGreaterThan(200);
});

test('create a task who asks for document and then put documents', async ({ clients, incognitoPage, page }) => {
  const redactionPage = new RedactionPage(page);

  const operationId = await clients.operations.createVenteAncien();
  const contractId = await clients.contracts.createMandat({ operationId: operationId.id });
  const record = await clients.records.createContact();
  const operation = await clients.operations.getOperation(operationId.id);

  await clients.operations.linkRecord({
    linkId: operation.vendeurLinkId,
    operationId: operation.id,
    recordId: record.id,
    type: 'VENDEUR'
  });

  await redactionPage.goToAnnex({ contractId, operationId: operation.id });
  await redactionPage.requestClientToAddDocuments(`${record.answer['prenoms'].value} ${record.answer['nom'].value}`);
  const taskLink = await redactionPage.sidebar.shareTaskWithExternalLink();

  const tasksPage = new TaskPage(incognitoPage);
  await tasksPage.goTo(taskLink);

  await tasksPage.importFile();

  await redactionPage.goTo({ contractId, operationId: operation.id });

  await redactionPage.sidebar.openAsideTask();

  await expect(page.getByTestId('sidebar-task-tile-completed')).toBeVisible();
});

test('create a task of sharing form', async ({ clients, incognitoPage, page }) => {
  const redactionPage = new RedactionPage(page);
  const operationDetailPage = new OperationDetailPage(page);

  const operationId = await clients.operations.createVenteAncien();
  const contractId = await clients.contracts.createMandat({ operationId: operationId.id });
  const record = await clients.records.createContact();
  const operation = await clients.operations.getOperation(operationId.id);

  await clients.operations.linkRecord({
    linkId: operation.vendeurLinkId,
    operationId: operation.id,
    recordId: record.id,
    type: 'VENDEUR'
  });

  await redactionPage.goTo({ contractId, operationId: operation.id });
  await redactionPage.requestClientAddInformations(record);
  const taskLink = await redactionPage.sidebar.shareTaskWithExternalLink();

  const tasksPage = new TaskPage(incognitoPage);

  await tasksPage.goTo(taskLink);
  await tasksPage.closePopinOpeningPage();
  await tasksPage.recordPageObject.addPersonnalInformation();
  await tasksPage.recordPageObject.createConjoint({ firstname: 'Jean', lastname: 'PIERRE' });

  await tasksPage.finishTask();

  await redactionPage.goTo({ contractId, operationId: operation.id });

  await operationDetailPage.sidebar.openAsideTask();

  await expect(page.getByTestId('sidebar-task-tile-completed')).toBeVisible();
});
