import * as uuid from 'uuid';
import { test, expect } from '../../shared/fixtures';
import { authUserStoragePath } from '../../shared/utils';
import { RecordPage } from '../../pages/record.page';
import { RedactionPage } from '../../pages/redaction.page';

test.use({ storageState: authUserStoragePath });

test('situation maritale', async ({ clients, page }) => {
  const recordPage = new RecordPage(page);
  const record = await clients.records.createContact({ firstname: 'Foo', lastname: 'SituationMaritale' });
  await recordPage.goToContact(record.id);

  // Select Célbataire and remove
  await recordPage.recordPageObject.selectLink({ option: 'Célibataire', type: 'situation_maritale' });
  await recordPage.recordPageObject.removeLink({ option: 'Célibataire', type: 'situation_maritale' });

  // Conjoint data
  const firstname = `Jean`;
  const lastname = `conjoint-${uuid.v4()}`.toUpperCase();
  const conjointLabel = `${firstname} ${lastname}`;

  // Create conjoint
  await recordPage.recordPageObject.createConjoint({ firstname, lastname });
  await expect(page.getByText(conjointLabel)).toBeVisible();

  // Delete conjoint
  await recordPage.recordPageObject.deleteLegalBranch(conjointLabel);
  await expect(page.getByText(conjointLabel)).toBeHidden();

  // Search conjoint
  await recordPage.recordPageObject.selectLink({ option: 'Mariage', type: 'situation_maritale' });
  await page.getByText('Ajouter un conjoint').click();
  await recordPage.recordPageObject.searchAndSelectFirstItem(conjointLabel);
  await expect(page.getByText(conjointLabel)).toBeVisible();

  // Delete conjoint
  await recordPage.recordPageObject.deleteLegalBranch(conjointLabel);
  await expect(page.getByText(conjointLabel)).toBeHidden();

  // duplicate conjoint
  await recordPage.recordPageObject.createConjoint({ firstname, lastname, selectDuplicateFirst: true });
  await expect(page.getByText(conjointLabel)).toBeVisible();

  // Remove link
  await recordPage.recordPageObject.removeLink({ option: 'Mariage', type: 'situation_maritale' });
  await expect(page.getByText(conjointLabel)).toBeHidden();
});

/**
 * Capacite is interesting because it has many specificities.
 * This link have branch without legal record (sauvegarde de justice) and branch with legal record (curatelle) but
 * "one sided", this means that on the other side of the link, the person under curatelle is not visible.
 * Also these links have an initial question and can have multiple branches.
 */
test('capacite', async ({ clients, page }) => {
  const recordPage = new RecordPage(page);
  const record = await clients.records.createContact({ firstname: 'Foo', lastname: 'Capacite' });
  await recordPage.goToContact(record.id);

  // Select Sauvegarde de justice
  await recordPage.recordPageObject.selectLink({ option: 'Sauvegarde de justice', type: 'capacite' });
  await recordPage.recordPageObject.removeLink({ option: 'Sauvegarde de justice', type: 'capacite' });

  // Curateur 1 data
  const firstname = `Jean`;
  const lastname = `tuteur-${uuid.v4()}`.toUpperCase();
  const curateurLabel = `${firstname} ${lastname}`;

  // Create curateur 1
  await recordPage.recordPageObject.selectLink({ option: 'Curatelle', type: 'capacite' });
  await page.getByText('Ajouter un curateur').click();
  await page.getByText('Nouvelle fiche').click();
  await recordPage.recordPageObject.addPersonnePhysique({ firstname, lastname });
  await page.getByRole('button', { name: 'Confirmer' }).click();
  await expect(page.getByText(curateurLabel)).toBeVisible();

  // Remove curateur 1
  await recordPage.recordPageObject.deleteLegalBranch(curateurLabel);
  await expect(page.getByText(curateurLabel)).toBeHidden();

  // Search curateur 1
  await recordPage.recordPageObject.selectLink({ option: 'Curatelle', type: 'capacite' });
  await page.getByText('Ajouter un curateur').click();
  await recordPage.recordPageObject.searchAndSelectFirstItem(curateurLabel);
  await expect(page.getByText(curateurLabel)).toBeVisible();

  // Curateur 2 data
  const firstname2 = `John`;
  const lastname2 = `tuteur-${uuid.v4()}`.toUpperCase();
  const curateur2Label = `${firstname2} ${lastname2}`;

  // Create curateur 2
  await page.getByText('Ajouter un curateur').click();
  await page.getByText('Nouvelle fiche').click();
  await recordPage.recordPageObject.addPersonnePhysique({ firstname: firstname2, lastname: lastname2 });
  await page.getByRole('button', { name: 'Confirmer' }).click();
  await expect(page.getByText(curateur2Label)).toBeVisible();

  // TODO: remove and select from duplicate

  // Remove link
  await recordPage.recordPageObject.removeLink({ option: 'Curatelle', type: 'capacite' });
  await expect(page.getByText(curateurLabel)).toBeHidden();
  await expect(page.getByText(curateur2Label)).toBeHidden();
});

/**
 * Procuration is used in contracts.
 * It uses LinkInitialQuestion to first create a link, then LinkDisplay to create a single branch.
 * Procuration is  contract specific.
 * If contract A has a procuration link with a Vendeur, the same vendeur in contract does not have a procuration link.
 */
test('procuration', async ({ clients, page }) => {
  const redactionPage = new RedactionPage(page);
  const recordPage = new RecordPage(page);

  const operationCreated = await clients.operations.createVenteAncien();
  const operation = await clients.operations.getOperation(operationCreated.id)

  const contractId = await clients.contracts.createMandat({ operationId: operation.id });

  const firstnameVendeur = 'Foo';
  const lastnameVendeur = 'BAR';

  const vendeurRecord2 = await clients.records.createContact({
    firstname: firstnameVendeur,
    lastname: lastnameVendeur
  });
  await clients.operations.linkRecord({
    linkId: operation.vendeurLinkId,
    operationId: operation.id,
    recordId: vendeurRecord2.id,
    type: 'VENDEUR'
  });

  await redactionPage.goTo({ contractId, operationId: operation.id });

  await redactionPage.openRecordTile(`${firstnameVendeur} ${lastnameVendeur}`);

  const firstname = `Jean`;
  const lastname = `representant-${uuid.v4()}`.toUpperCase();
  const representantLabel = `${firstname} ${lastname}`;
  await recordPage.recordPageObject.createRepresentant({ contractId, firstname, lastname });
  await page.getByRole('button', { name: 'Confirmer' }).click();
  await expect(page.getByTestId('record-links-PROCURATION').getByText(representantLabel)).toBeVisible();

  // Delete Representant
  await recordPage.recordPageObject.deleteLegalBranch(representantLabel);
  await expect(page.getByTestId('record-links-PROCURATION').getByText(representantLabel)).toBeHidden();

  // search Representant

  await recordPage.recordPageObject.selectLink({ option: 'Oui', type: `procuration-${contractId}` });
  await page.getByText('Ajouter le représentant').click();
  await recordPage.recordPageObject.searchAndSelectFirstItem(representantLabel);
  await expect(page.getByTestId('record-links-PROCURATION').getByText(representantLabel)).toBeVisible();

  // Delete conjoint
  await recordPage.recordPageObject.deleteLegalBranch(representantLabel);
  await expect(page.getByTestId('record-links-PROCURATION').getByText(representantLabel)).toBeHidden();

  // duplicate Representant
  await recordPage.recordPageObject.createRepresentant({ contractId, firstname, lastname });
  await recordPage.recordPageObject.selectFirstDuplicatedRecordIfExists();
  await expect(page.getByTestId('record-links-PROCURATION').getByText(representantLabel)).toBeVisible();

  // Expect procuration to be contract specific
  const secondContractId = await clients.contracts.createMandat({ operationId: operation.id });
  await redactionPage.goTo({ contractId: secondContractId, operationId: operation.id });
  await redactionPage.openRecordTile(`${firstnameVendeur} ${lastnameVendeur}`);
  await expect(page.getByTestId('record-links-PROCURATION').getByText(representantLabel)).toBeHidden();
});
