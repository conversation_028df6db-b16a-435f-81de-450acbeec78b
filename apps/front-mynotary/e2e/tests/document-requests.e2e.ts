import { expect, test } from '../shared/fixtures';
import { DrivePage } from '../pages/drive.page';
import { TaskPage } from '../pages/task.page';
import { authUserStoragePath } from '../shared/utils';

test.use({ storageState: authUserStoragePath });

test('create a document request from an existing list', async ({
  clients,
  incognitoPage,
  page
}) => {
  const drivePage = new DrivePage(page);

  const operation = await clients.operations.createVenteAncien();
  await clients.contracts.createMandat({ operationId: operation.id });

  await drivePage.goTo({ operationId: operation.id });
  await drivePage.clickAskDocument();

  await drivePage.documentRequestsPageObject.selectDocumentRequestType('Vendeur');
  await drivePage.documentRequestsPageObject.createDocumentAndSelectIt();
  await drivePage.documentRequestsPageObject.fillDocumentRequestTask();
  const taskLink = await drivePage.documentRequestsPageObject.copyLink();

  const tasksPage = new TaskPage(incognitoPage);
  await tasksPage.goTo(taskLink);

  const documentName = await tasksPage.importDocument();

  await drivePage.goTo({ operationId: operation.id });
  await expect(drivePage.selectDriveFolder('Demande docs à Vincent')).toBeVisible();
  await drivePage.selectDriveFolder('Demande docs à Vincent').click();
  await expect(drivePage.selectDriveFile(documentName)).toBeVisible();
});
