import { expect, test } from '../shared/fixtures';
import { OperationDetailPage } from '../pages/operation-detail.page';
import { letterDrafRouteGlob, letterFollowUpRouteGlob } from '../pages/routes';
import { LettersPage } from '../pages/letters.page';
import { authUserStoragePath } from '../shared/utils';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';

test.use({ storageState: authUserStoragePath });
/* Serial is needed because we are updating credit which is global to organization */
test.describe.configure({ mode: 'serial' });

test('should send letter without credit', async ({ adminClient, clients, page }) => {
  const operation = await clients.operations.createVenteAncien();
  const operationDetailPage = new OperationDetailPage(page);
  const letterPage = new LettersPage(page);

  await operationDetailPage.goTo(operation.id);

  /* Remove letter credit */
  await adminClient.setCredit({ quantity: 0, type: FeatureType.REGISTERED_LETTER_CREDITS });

  /* Create letter and validate credit payment */
  await letterPage.createLetter();
  await page.getByRole('button', { name: 'Valider' }).click();
  await page.waitForResponse((resp) => resp.url().includes('/credit'));
  await page.waitForURL(letterFollowUpRouteGlob);

  await expect(page.getByRole('button', { name: 'Télécharger le courrier et les preuves' })).toBeVisible();
});

test('send letter with credit and update receiver', async ({ adminClient, clients, page }) => {
  const operation = await clients.operations.createVenteAncien();
  const operationDetailPage = new OperationDetailPage(page);
  const letterPage = new LettersPage(page);

  await operationDetailPage.goTo(operation.id);

  /* Add letter credit */
  await adminClient.setCredit({ quantity: 100, type: FeatureType.REGISTERED_LETTER_CREDITS });

  /* Create letter */
  await letterPage.createLetter();
  await page.waitForURL(letterFollowUpRouteGlob);

  await expect(page.getByRole('button', { name: 'Télécharger le courrier et les preuves' })).toBeVisible();

  /* Modify letter receiver email */
  const newEmail = '<EMAIL>';
  await page.getByTestId('registered-letter-tile').getByRole('button').click();
  await page.getByText('Modifier et renvoyer').click();
  await letterPage.modifyReceiverAndResend({ email: newEmail, firstname: 'Tests', lastname: 'Tests' });

  await expect(page.getByRole('button', { name: 'Télécharger le courrier et les preuves' })).toBeVisible();
});

test('check generated letter for contracts with email template', async ({ adminClient, clients, page }) => {
  const operation = await clients.operations.createVenteAncien();
  const contractId = await clients.contracts.createCompromis({ operationId: operation.id });
  await clients.redactions.validateContract({
    contractId,
    fileId: 'STATIC_FILES_MYNOTARY_LOGO',
    operationId: operation.id
  });

  await adminClient.setContractStatus({ contractId, status: ContractStatus.SIGNATURE_COMPLETED });

  const lettersPage = new LettersPage(page);

  await clients.letters.startLetter({ contractId, operationId: operation.id });

  await lettersPage.goToJointLetter({ contractId, operationId: operation.id });
  await page.waitForURL(letterDrafRouteGlob);

  await page.getByText("Courrier d'accompagnement", { exact: true }).click();

  const checkbox = lettersPage.getGeneratedLetterCheckbox();
  await expect(checkbox).toHaveClass(/isSelected/);
  await lettersPage.editGeneratedLetterContent();

  await expect(lettersPage.getAcknowledgementOfReceipt()).toBeVisible();
  await lettersPage.goToNextStep();

  await expect(page.getByText('Playwright JoinLetter - Courrier joint')).toBeVisible();
});
