import { test, expect } from '../../shared/fixtures';
import { RedactionPage } from '../../pages/redaction.page';
import { authUserStoragePath } from '../../shared/utils';
import { generateRandomString } from '@mynotary/testing';

test.use({ storageState: authUserStoragePath });

test('add created record in contract', async ({ clients, page }) => {
  const redactionPage = new RedactionPage(page);
  const operation = await clients.operations.createVenteAncien();
  const contractId = await clients.contracts.createMandat({ operationId: operation.id });

  await redactionPage.goTo({ contractId, operationId: operation.id });
  await page.getByText('Ajouter un vendeur').click();
  await page.getByText('Nouvelle fiche').click();
  await page.getByText('Sélectionner*').click();
  await page.getByTestId('option-Personne Physique').getByText('Personne Physique').click();
  await page.getByText('Sélectionner*').click();
  await page.getByTestId('option-Monsieur').getByText('Monsieur').click();

  const firstname = `Foo ${generateRandomString()}`;

  await page.getByTestId('nom').getByPlaceholder('Entrez votre texte*').fill('Playwright');
  await page.getByTestId('prenoms').getByPlaceholder('Entrez votre texte*').fill(firstname);
  await page.getByRole('button', { name: 'Confirmer' }).click();
  await expect(page.getByTestId('redaction-legal-record-header')).toContainText(firstname, { ignoreCase: true });
});

test('search existing record in contract', async ({ clients, page }) => {
  const redactionPage = new RedactionPage(page);
  const operation = await clients.operations.createVenteAncien();
  const contractId = await clients.contracts.createMandat({ operationId: operation.id });
  const firstname = `Foo ${generateRandomString()}`;

  await clients.records.createContact({ firstname: firstname, lastname: 'DUPOND' });

  await redactionPage.goTo({ contractId, operationId: operation.id });
  await page.getByText('Ajouter un vendeur').click();
  await page.getByText('Fiche existante').click();
  await page.getByPlaceholder('Rechercher une personne').fill(firstname + ' Dupond');
  await page
    .getByTestId('record-item')
    .filter({ hasText: firstname + ' Dupond' })
    .click();
  await expect(page.getByTestId('record-label-tile').filter({ hasText: firstname + ' Dupond' })).toBeVisible();
});

test('add existing record in contract from duplicate selection', async ({ clients, page }) => {
  const redactionPage = new RedactionPage(page);
  const operation = await clients.operations.createVenteAncien();
  const contractId = await clients.contracts.createMandat({ operationId: operation.id });

  const record = await clients.records.createContact({ firstname: 'Jean', lastname: 'DUPLICATE' });

  await redactionPage.goTo({ contractId, operationId: operation.id });
  await page.getByText('Ajouter un vendeur').click();
  await page.getByText('Nouvelle fiche').click();
  await page.getByText('Sélectionner*').click();
  await page.getByTestId('option-Personne Physique').getByText('Personne Physique').click();
  await page.getByText('Sélectionner*').click();
  await page.getByTestId('option-Monsieur').getByText('Monsieur').click();
  await page.getByTestId('nom').getByPlaceholder('Entrez votre texte*').click();
  await page.getByTestId('nom').getByPlaceholder('Entrez votre texte*').fill('Duplicate');
  await page.getByTestId('prenoms').getByPlaceholder('Entrez votre texte*').fill('Jean');
  await page.getByRole('button', { name: 'Confirmer' }).click();

  await expect(page.getByText('Doublon potentiel détecté')).toBeVisible();
  await page.getByText('Jean DUPLICATE').first().click();
  await expect(page.getByText('Jean DUPLICATE').first()).toBeVisible();

  await clients.records.deleteRecord(record.id);
});

test('repeat form in  contract', async ({ clients, page }) => {
  const redactionPage = new RedactionPage(page);
  const operation = await clients.operations.createVenteAncien();
  const contractId = await clients.contracts.createCompromis({ operationId: operation.id });
  await redactionPage.goTo({ contractId, operationId: operation.id });

  await redactionPage.openRecordTile('Informations sur le Financement');
  await page.getByTestId('apport').getByText('OUI').click();

  /**
   * Add first repeat question
   */

  await redactionPage.recordPageObject.addRepeatQuestion();

  /**
   * Repeat questions have dynamic ids, so we need to use a regex to find the element.
   */

  const responsePromise1 = page.waitForResponse((resp) => resp.url().includes('/records'));

  await page
    .getByTestId(/apport_personne$/)
    .getByPlaceholder('Entrez votre texte*')
    .fill('Foo');

  await responsePromise1;

  const responsePromise2 = page.waitForResponse((resp) => resp.url().includes('/records'));

  await page
    .getByTestId(/apport_montant$/)
    .getByPlaceholder('Valeur (€)*')
    .fill('50');

  await responsePromise2;

  await page.getByRole('button', { name: 'Confirmer' }).click();

  const firstRepeatQuestionLabel = 'Apport Foo 50€';

  await expect(page.getByText(firstRepeatQuestionLabel)).toBeVisible();
  await expect(page.getByText('50,00 €').first()).toBeVisible();

  /**
   * Add second repeat question
   */

  await redactionPage.recordPageObject.addRepeatQuestion();

  const responsePromise3 = page.waitForResponse((resp) => resp.url().includes('/records'));

  await page
    .getByTestId(/apport_personne$/)
    .getByPlaceholder('Entrez votre texte*')
    .fill('Bar');

  await responsePromise3;

  const responsePromise4 = page.waitForResponse((resp) => resp.url().includes('/records'));

  await page
    .getByTestId(/apport_montant$/)
    .getByPlaceholder('Valeur (€)*')
    .fill('100');

  await responsePromise4;

  await page.getByRole('button', { name: 'Confirmer' }).click();

  await expect(page.getByText('Apport Bar 100€')).toBeVisible();
  await expect(page.getByText('100,00 €')).toBeVisible();

  /**
   * Go to the repeat creation popin then cancel it
   */

  await redactionPage.recordPageObject.addRepeatQuestion();

  const responsePromise5 = page.waitForResponse((resp) => resp.url().includes('/records'));

  await page
    .getByTestId(/apport_personne$/)
    .getByPlaceholder('Entrez votre texte*')
    .fill('DRAFT');

  await responsePromise5;

  const responsePromise6 = page.waitForResponse((resp) => resp.url().includes('/records'));

  await page
    .getByTestId(/apport_montant$/)
    .getByPlaceholder('Valeur (€)*')
    .fill('999');

  await responsePromise6;

  await page.getByRole('button', { name: 'Précédent' }).nth(1).click();

  await expect(page.getByText('Apport DRAFT 999€')).toBeHidden();
  await expect(page.getByText('999,00 €')).toBeHidden();

  /**
   * Delete a repeat question
   */

  await page.getByTestId(`form-repetition-header-${firstRepeatQuestionLabel}`).getByRole('button').click();

  await expect(page.getByText(firstRepeatQuestionLabel)).toBeHidden();
  await expect(page.getByText('50,00 €')).toBeHidden();
});
