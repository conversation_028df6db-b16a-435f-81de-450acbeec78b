import { SignUpPage } from '../pages/sign-up.page';
import { test, expect } from '../shared/fixtures';
import { OperationsPage } from '../pages/operations.page';

const user: User = {
  email: `<EMAIL>`,
  password: '123456789',
  phone: '+33100000001'
};

test.beforeAll(async ({ adminClient }) => {
  await adminClient.deleteUser(user.email);
});

test('sign up user', async ({ page }) => {
  const signUpPage = new SignUpPage(page);
  const operations = new OperationsPage(page)

  await signUpPage.goTo();

  await signUpPage.signUp({
    email: user.email,
    firstname: 'Test',
    lastname: 'Random',
    password: user.password,
    phone: user.phone
  });

  // Check that unverified user is redirected to verification page
  await operations.goTo()
  await page.waitForURL('/verification');

  await signUpPage.fillReceivedCode('1234');

  await page.waitForURL('/organisation/taille');

  await expect(page.getByRole('heading', { name: 'Organisation' })).toBeVisible();

  // Check that verified user is redirected to operations page
  await page.goto('/verification');
  await page.waitForURL('/application/operations');
});

interface User {
  email: string;
  password: string;
  phone: string;
}
