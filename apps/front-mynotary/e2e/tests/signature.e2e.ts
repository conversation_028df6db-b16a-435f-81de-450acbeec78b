import { test, expect } from '../shared/fixtures';
import { SignaturesPage } from '../pages/signatures.page';
import { OperationDetailPage } from '../pages/operation-detail.page';
import { authUserStoragePath } from '../shared/utils';
import { FeatureType } from '@mynotary/crossplatform/features/api';

test.use({ storageState: authUserStoragePath });

/* Serial is needed because we are updating credit which is global to organization */
test.describe.configure({ mode: 'serial' });

test('sign an imported contract with credits', async ({ adminClient, clients, page }) => {
  const operation = await clients.operations.createVenteAncien();
  const operationDetailPage = new OperationDetailPage(page);
  const signaturesPage = new SignaturesPage(page);

  /* Add signature credit */
  await adminClient.setCredit({ quantity: 100, type: FeatureType.SIGNATURE_CREDITS });

  /* Create contract and validate */
  await operationDetailPage.goTo(operation.id);

  /* Create signature */
  await signaturesPage.createElectronicSignatureWithGroups();

  const groups = signaturesPage.getFollowUpGroups();

  await expect(groups).toHaveCount(2);
  await expect(groups.nth(1).getByText('Faire signer')).toBeDisabled();
});

test('sign an imported contract without credits', async ({ adminClient, clients, page }) => {
  test.slow();
  const operation = await clients.operations.createVenteAncien();
  const operationDetailPage = new OperationDetailPage(page);
  const signaturesPage = new SignaturesPage(page);

  /* Add signature credit */
  await adminClient.setCredit({ quantity: 0, type: FeatureType.SIGNATURE_CREDITS });

  /* Create contract and validate */
  await operationDetailPage.goTo(operation.id);

  /* Create signature and validate payment */
  await signaturesPage.createElectronicSignature();

  await page.getByRole('button', { name: 'Valider' }).click();
  await page.waitForResponse((resp) => resp.url().includes('/credit'));

  const signatory = signaturesPage.getSignatoryTile();
  await expect(signatory).toBeVisible();
});

test('sign an imported contract with file error', async ({ adminClient, clients, page }) => {
  const operation = await clients.operations.createVenteAncien();
  const operationDetailPage = new OperationDetailPage(page);
  const signaturesPage = new SignaturesPage(page);

  /* Add signature credit */
  await adminClient.setCredit({ quantity: 100, type: FeatureType.SIGNATURE_CREDITS });

  /* Create contract and validate */
  await operationDetailPage.goTo(operation.id);

  await signaturesPage.createSignatureWithPdfError();

  const signatory = signaturesPage.getSignatoryTile();
  await expect(signatory).toBeVisible();
});

test('sign a complete paper signature then go to the public page', async ({ adminClient, clients, page }) => {
  const operation = await clients.operations.createVenteAncien();
  const operationDetailPage = new OperationDetailPage(page);
  const signaturesPage = new SignaturesPage(page);

  /* Add signature credit */
  await adminClient.setCredit({ quantity: 100, type: FeatureType.SIGNATURE_CREDITS });

  /* Create contract and validate */
  await operationDetailPage.goTo(operation.id);

  /* Create signature */
  await signaturesPage.createCompletedSignature();

  const signatureId = await page.evaluate(() => document.location.pathname.split('/').at(-1));

  const token = await clients.tokens.createResourceToken({
    resourceId: signatureId ?? '',
    resourceType: 'SIGNATURE'
  });

  await signaturesPage.goToPublicPage(token.token, signatureId ?? '');
  const waitForDownload = page.waitForResponse((resp) => resp.url().includes('archives'));
  await page.getByRole('button', { name: 'Télécharger les documents' }).click();
  await waitForDownload;
  await expect(page.getByText('Documents téléchargés avec succès !')).toBeVisible();
});
