import { Module } from '@nestjs/common';
import { OpenapiValidatorModule } from '@mynotary/backend/shared/openapi-validator-infra';
import { TestController } from './test.controller';
import { join } from 'path';
import { provideAsyncTasksScope } from '@mynotary/backend/async-tasks/providers';
import { provideBillingsScope } from '@mynotary/backend/billings/providers';
import { provideCustomViewsScope } from '@mynotary/backend/custom-views/providers';
import { provideDataTableauScope } from '@mynotary/backend/data-tableau/providers';
import { provideEmailsScope } from '@mynotary/backend/emails/providers';
import { provideEventsScope } from '@mynotary/backend/events/providers';
import { provideExternalAppsScope } from '@mynotary/backend/external-apps/providers';
import { provideFeatureOrganizationsScope } from '@mynotary/backend/feature-organizations/providers';
import { provideFeaturesScope } from '@mynotary/backend/features/providers';
import { provideFilesScope } from '@mynotary/backend/files/providers';
import { provideLegalsScope } from '@mynotary/backend/legals/providers';
import { provideMembersScope } from '@mynotary/backend/members/providers';
import { provideOrganizationsScope } from '@mynotary/backend/organizations/providers';
import { provideRegisteredLettersScope } from '@mynotary/backend/registered-letters/providers';
import { provideRegistersScope } from '@mynotary/backend/registers/providers';
import { provideRolesScope } from '@mynotary/backend/roles/providers';
import { provideThemesScope } from '@mynotary/backend/themes/providers';
import { provideUnisScope } from '@mynotary/backend/unis/providers';
import { provideUsersScope } from '@mynotary/backend/users/providers';
import { ScheduleModule } from '@nestjs/schedule';

import {
  CouponsController,
  CreditHistoryController,
  CreditsController,
  ExternalSubscriptionsController,
  InvoiceProvidersController,
  PaymentsWebhooksController,
  SubscriptionsController,
  SubscriptionsUpdaterController,
  SubscriptionsWebhooksController
} from '@mynotary/backend/billings/feature';
import { CustomViewsController } from '@mynotary/backend/custom-views/feature';
import { TableauController } from '@mynotary/backend/data-tableau/feature';
import { AssociationsController, ExternalAppsController } from '@mynotary/backend/external-apps/feature';
import { FeaturesController } from '@mynotary/backend/features/feature';
import {
  AnnexedDocumentsController,
  ContractController,
  LegalBranchesController,
  LegalLinksController,
  LegalOperationCreationsController,
  LegalOperationInfosController,
  MissingLegalBranchesController,
  OperationDefaultAnswersController,
  OperationDuplicatesController,
  OperationReferencesController,
  OperationsController,
  RecordsController,
  TasksExpirationController
} from '@mynotary/backend/legals/feature';
import { MembersController } from '@mynotary/backend/members/feature';
import { OrganizationsController } from '@mynotary/backend/organizations/feature';
import {
  RegisteredLetterArchivesController,
  RegisteredLetterBatchesController,
  RegisteredLettersController,
  RegisteredLettersUpdaterController,
  RegisteredLetterWebhooksController
} from '@mynotary/backend/registered-letters/feature';
import {
  ManagementRegistersController,
  ReceivershipRegistersController,
  TransactionRegistersController
} from '@mynotary/backend/registers/feature';
import {
  PermissionsController,
  PermissionsGeneratorController,
  RoleDefaultPermissionsController,
  RolesController
} from '@mynotary/backend/roles/feature';
import { ThemesController } from '@mynotary/backend/themes/feature';
import { UnisController } from '@mynotary/backend/unis/feature';
import { UserEmailsController, UsersController } from '@mynotary/backend/users/feature';
import { provideOperationAccessScope } from '@mynotary/backend/operation-access/providers';
import { OperationAccessController } from '@mynotary/backend/operation-access/feature';
import { provideOperationInvitationsScope } from '@mynotary/backend/operation-invitations/providers';
import { provideOperationViewsScope } from '@mynotary/backend/operation-views/providers';
import { OperationviewsController } from '@mynotary/backend/operation-views/feature';
import { OrganizationDataTransfersController } from '@mynotary/backend/organization-data-transfers/feature';
import { provideOrganizationDataTransfersScope } from '@mynotary/backend/organization-data-transfers/providers';
import { provideContractValidatorsScope } from '@mynotary/backend/contract-validators/providers';
import { ContractValidatorsController } from '@mynotary/backend/contract-validators/feature';
import { ContractViewsController } from '@mynotary/backend/contract-views/feature';
import { provideContractViewsScope } from '@mynotary/backend/contract-views/providers';
import { DocumentRequestsController } from '@mynotary/backend/document-requests/feature';
import { provideDocumentRequestsScope } from '@mynotary/backend/document-requests/providers';
import { DriveFilesController, DriveFoldersController, DrivesController } from '@mynotary/backend/drives/feature';
import { provideDrivesScope } from '@mynotary/backend/drives/providers';
import { provideNotificationsScope } from '@mynotary/backend/notifications/providers';
import { NotificationsController } from '@mynotary/backend/notifications/feature';
import {
  InvoiceConfigsController,
  InvoiceEmailsController,
  InvoiceFilesController,
  InvoiceReportsController,
  InvoicesController
} from '@mynotary/backend/invoices/feature';
import { provideDataSyncScope } from '@mynotary/backend/data-sync/providers';
import { provideInvoicesScope } from '@mynotary/backend/invoices/providers';
import { provideDefaultRecordsScope } from '@mynotary/backend/default-records/providers';
import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { OrpiController } from '@mynotary/backend/orpi/feature';
import { provideOrpiScope } from '@mynotary/backend/orpi/providers';
import { provideLegacyJavaScope } from '@mynotary/backend/legacy-java/providers';
import { provideLearnWorldsScope } from '@mynotary/backend/learn-worlds/providers';
import { LearnWorldsController } from '@mynotary/backend/learn-worlds/feature';
import { provideOrganizationHoldingsScope } from '@mynotary/backend/organization-holdings/providers';
import { provideDataAnalyticsScope } from '@mynotary/backend/data-analytics/providers';
import { provideSecretsScope } from '@mynotary/backend/secrets/providers';
import {
  OperationInvitationController,
  OperationRolesController
} from '@mynotary/backend/operation-invitations/feature';
import { DataAnalyticsController } from '@mynotary/backend/data-analytics/feature';
import { providePdfScope } from '@mynotary/backend/pdf/providers';
import { LegalTemplatesController } from '@mynotary/backend/legal-templates/feature';
import { provideOperationAccessUpdateScope } from '@mynotary/backend/operation-access-update/providers';
import { OperationAccessUpdateController } from '@mynotary/backend/operation-access-update/feature';
import { ProgramController } from '@mynotary/backend/programs/feature';
import { provideProgramsScope } from '@mynotary/backend/programs/providers';
import { CoproprietesController } from '@mynotary/backend/coproprietes/feature';
import { provideCoproprietesScope } from '@mynotary/backend/coproprietes/providers';
import { provideLegalRecordExportsScope } from '@mynotary/backend/legal-record-exports/providers';
import { LegalRecordExportsController } from '@mynotary/backend/legal-record-exports/feature';
import { MemberSetupsController } from '@mynotary/backend/member-setups/feature';
import { provideMemberSetupsScope } from '@mynotary/backend/member-setups/providers';
import { OrganizationSetupsController } from '@mynotary/backend/organization-setups/feature';
import { provideOrganizationSetupsScope } from '@mynotary/backend/organization-setups/providers';
import { DefaultRecordsController } from '@mynotary/backend/default-records/feature';
import { GelAvoirsController } from '@mynotary/backend/gel-avoirs/feature';
import { provideGelAvoirsScope } from '@mynotary/backend/gel-avoirs/providers';
import { providePreEtatDatesScope } from '@mynotary/backend/pre-etat-dates/providers';
import { PreEtatDatesController, PreEtatDatesWebhookController } from '@mynotary/backend/pre-etat-dates/feature';
import { OrdersController } from '@mynotary/backend/orders/feature';
import { provideOrdersScope } from '@mynotary/backend/orders/providers';
import { provideAuthenticationsScope } from '@mynotary/backend/authentications/providers';
import { provideAuthorizationsScope } from '@mynotary/backend/authorizations/providers';
import { providePdfClientScope } from '@mynotary/backend/pdf-client/providers';
import { provideFilesClientScope } from '@mynotary/backend/files-client/providers';
import { provideInternalNotificationsScope } from '@mynotary/backend/internal-notifications/providers';
import { provideContractReviewsScope } from '@mynotary/backend/contract-reviews/providers';
import { provideContractValidationsScope } from '@mynotary/backend/contract-validations/providers';
import { ContractValidationsController } from '@mynotary/backend/contract-validations/feature';
import { ContractReviewsController } from '@mynotary/backend/contract-reviews/feature';

@Module({
  controllers: [
    AnnexedDocumentsController,
    AssociationsController,
    ContractController,
    ContractValidatorsController,
    ContractViewsController,
    CoproprietesController,
    CouponsController,
    CreditHistoryController,
    CreditsController,
    CustomViewsController,
    DataAnalyticsController,
    DefaultRecordsController,
    DocumentRequestsController,
    DriveFilesController,
    DriveFoldersController,
    DrivesController,
    ExternalAppsController,
    ExternalSubscriptionsController,
    FeaturesController,
    GelAvoirsController,
    InvoiceConfigsController,
    InvoiceEmailsController,
    InvoiceFilesController,
    InvoiceProvidersController,
    InvoiceReportsController,
    InvoicesController,
    LearnWorldsController,
    LegalBranchesController,
    LegalLinksController,
    LegalRecordExportsController,
    LegalOperationCreationsController,
    LegalOperationInfosController,
    LegalTemplatesController,
    ManagementRegistersController,
    MemberSetupsController,
    MembersController,
    MissingLegalBranchesController,
    NotificationsController,
    OperationAccessController,
    OperationAccessUpdateController,
    OperationDefaultAnswersController,
    OperationDuplicatesController,
    OperationInvitationController,
    OperationReferencesController,
    OperationRolesController,
    OperationsController,
    OperationviewsController,
    OrdersController,
    OrganizationDataTransfersController,
    OrganizationSetupsController,
    OrganizationsController,
    OrpiController,
    PaymentsWebhooksController,
    PermissionsController,
    PermissionsGeneratorController,
    PreEtatDatesController,
    PreEtatDatesWebhookController,
    ProgramController,
    ReceivershipRegistersController,
    RecordsController,
    RegisteredLetterArchivesController,
    RegisteredLetterBatchesController,
    RegisteredLetterWebhooksController,
    RegisteredLettersController,
    RegisteredLettersUpdaterController,
    RoleDefaultPermissionsController,
    RolesController,
    SubscriptionsController,
    SubscriptionsUpdaterController,
    SubscriptionsWebhooksController,
    TableauController,
    TasksExpirationController,
    TestController,
    ThemesController,
    TransactionRegistersController,
    UnisController,
    UserEmailsController,
    UsersController,
    ContractValidationsController,
    ContractReviewsController
  ],
  imports: [
    OpenapiValidatorModule.forRoot({
      apiSpecPath: join(__dirname, 'assets/api-mynotary.openapi.yaml')
    }),
    ScheduleModule.forRoot()
  ],
  providers: [
    ...provideContractReviewsScope(),
    ...provideContractValidationsScope(),
    ...provideAsyncTasksScope(),
    ...provideAuthenticationsScope(),
    ...provideAuthorizationsScope(),
    ...provideBillingsScope(),
    ...provideContractValidatorsScope(),
    ...provideContractViewsScope(),
    ...provideCoproprietesScope(),
    ...provideCustomViewsScope(),
    ...provideDataAnalyticsScope(),
    ...provideDataSyncScope(),
    ...provideDataTableauScope(),
    ...provideDefaultRecordsScope(),
    ...provideDocumentRequestsScope(),
    ...provideDrivesScope(),
    ...provideEmailsScope(),
    ...provideEventsScope(),
    ...provideExternalAppsScope(),
    ...provideFeatureOrganizationsScope(),
    ...provideFeaturesScope(),
    ...provideFilesScope(),
    ...provideFilesClientScope(),
    ...provideGelAvoirsScope(),
    ...provideInternalNotificationsScope(),
    ...provideInvoicesScope(),
    ...provideLearnWorldsScope(),
    ...provideLegacyJavaScope(),
    ...provideLegalRecordExportsScope(),
    ...provideLegalsScope(),
    ...provideMemberSetupsScope(),
    ...provideMembersScope(),
    ...provideNotificationsScope(),
    ...provideOperationAccessScope(),
    ...provideOperationAccessUpdateScope(),
    ...provideOperationInvitationsScope(),
    ...provideOperationViewsScope(),
    ...provideOrdersScope(),
    ...provideOrganizationDataTransfersScope(),
    ...provideOrganizationHoldingsScope(),
    ...provideOrganizationSetupsScope(),
    ...provideOrganizationsScope(),
    ...provideOrpiScope(),
    ...providePdfClientScope(),
    ...providePdfScope(),
    ...providePreEtatDatesScope(),
    ...provideProgramsScope(),
    ...provideRegisteredLettersScope(),
    ...provideRegistersScope(),
    ...provideRolesScope(),
    ...provideSecretsScope(),
    ...provideThemesScope(),
    ...provideUnisScope(),
    ...provideUsersScope(),
    PrismaService
  ]
})
export class AppModule {}
