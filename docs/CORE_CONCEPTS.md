# Core Concepts

This document provides an overview of the core concepts and architecture of our project.

It's really important to read this document before starting to work on the project. It will help you understand the
architecture and the rules we follow. It will also help you to understand the codebase and to write code that is
consistent with the rest of the project. It is important to undersand how to use these concepts but most importantantly
to understand why we use them.

Before reading this document, it is mandatory to read the following documentation:

1. [Nestjs First step](https://docs.nestjs.com/first-steps)]
2. [Nestjs Providers](https://docs.nestjs.com/providers)]
3. [NX Getting Started](https://nx.dev/getting-started/what-is-nx)
4. [NX Concepts](https://nx.dev/concepts/mental-model)
5. [Nx Enforce Module Boundaries (features)](https://nx.dev/features/enforce-module-boundaries)
6. [Nx Enforce Module Boundaries (recipes)](https://nx.dev/recipes/enforce-module-boundaries)

---

## Nx Libraries

Libraries are grouped by functional scope and can be reused across multiple applications.

Run `nx graph` to see a diagram of the projects libraries and apps.

To avoid excessive boilerplate and duplicate configuration files, libraries are implicit and are automatically created
based on the folder structure. For example, a library in the folder `libs/backend/foo/core` will generate the project
`backend-foo-core` in the Nx graph and you will be able to run target using the following command:

```shell
  nx run backend-foo-core:lint
```

The code for these plugins can be found in [Implicit libs](../tools/workspace-plugin/src/implicit-libs.ts).

## Nx Targets

Each project can be configured with its own targets (commands to execute such as lint, test, build...). To ensure
consistency and avoid boilerplate, targets are implicit and are automatically created based on the types of files
contained in the library. For example, a library containing a file with the extension `.spec.ts` will generate a target
`test` in the Nx graph.

The code for these plugins can be found in [Implicit targets](../tools/workspace-plugin/src/implicit-targets.ts)

---

## Scope Boundaries

We have invested significant effort in establishing strict rules for how different parts of our codebase interact with
each other. These boundaries between scopes serve several important purposes:

1. **Code Readability**: Well-separated scopes prevent "spaghetti code" where dependencies become tangled and difficult
   to follow. This makes the codebase easier to understand and maintain, especially for new team members.

2. **Efficient Development**: By leveraging NX's dependency graph, we can run tasks (tests, builds, linting) only for
   the code affected by changes. This significantly speeds up both local development and CI pipelines.

3. **Business Logic Isolation**: Our architecture isolates business logic from implementation details of external
   libraries and providers. This makes future changes and upgrades much easier, as we can swap out implementations
   without affecting our core business rules.

4. **Testability**: Clear boundaries make it easier to test components in isolation, using mocks or fake implementations
   for dependencies.

These boundaries are enforced through a combination of folder structure, TypeScript configuration, and ESLint rules.
Understanding and respecting these boundaries is essential for maintaining a clean, maintainable, and efficient
codebase.

To achieve this, we rely heavily on nx libraries and the `@nx/enforce-module-boundaries` ESLint rule to have clear
boundaries between each scope. This rule allows us to import only the projects that comply with the rules written in the
`eslintrc.json` file.

## MyNotary Boundaries Rules

All project must have 3 tags representing the platform, scope and type.

- **Platform** possible values are `frontend`, `crossplatform` and `backend`. `crossplatform` is mainly used to share
  some utils, enum, openapi specification and DTO

- **Scope** must describe the business domain eg: `roles`, `files`, `legals` (must be plural). In **rare** occurrence,
  when the whole team agrees that something doesn't belong to any specific domain, we can used the `shared` scope which
  is a scope that is usable by all scopes. New scopes must be created with the agreement of the whole team.

- **Type** is another dimension where we can split libraries depending on their responsabilities. Possible values are :
  api, app, authorization, core, feature, infra, openapi, providers, store, test, ui, util.

Note that the tags are automatically generated based on the folder structure.

### Type definition

#### Api

- **Platform**: backend, crossplatform and frontend
- **Description**: Communication between scopes is made through `api` libraries. For example, if a feature in the
  `users` scope needs to interact with the `organizations` scope, it would do so through the `OrganizationsApiService`.
  This ensures that domains remain decoupled and only expose what is necessary for other domains to use.
- **Can import**: api, core, util

#### App

- **Platform**: backend and frontend
- **Description**: Used for backend and frontend applications.
- **Can import**: core, infra, feature, providers, util, store, ui. Note that infra, util, store and ui are allowed
  because front-mynotary application is not completly split in libraries, when it will be split, this type will be
  removed and app should only import feature and providers.

#### Authorization

- **Platform**: backend
- **Description**: Logic about guards access to a resource (controller)
- **Can import**: api, core, util

#### Core

- **Platform**: backend and frontend
- **Description**: Core business logic, scope models, interfaces, and error definitions.
- **Can import**: api, core, util

#### Feature

- **Platform**: backend and frontend
- **Description**: Usually what is imported in the app. For backend, it's the controller. For frontend, it's the page
  components & smart components etc.
- **Backend feature can import**: api, authorization, core, openapi, util
- **Frontend feature can import**: api, core, store, ui, util

#### Infra

- **Platform**: backend and frontend
- **Description**: HTTP calls, queries to database, remote api, localstorage, file system etc. This usually contains the
  implementation of the services defined in the core layer.
- **Can import**: api, core, infra, openapi, providers, util

#### Openapi

- **Platform**: crossplatform
- **Description**: spec open api and DTO. Must be used in frontend infra and backend feature.
- **Can import**: nothing

#### Providers

- **Platform**: backend
- **Description**: Provide all services implementations for the scope.
- **Can import**: api, core, infra

#### Store

- **Platform**: frontend
- **Description**: State management only (selectors and slice)
- **Can import**: api, core, infra, openapi, store, util

#### Test

- **Platform**: backend
- **Description**: Tests have been moved to a separate type to avoid circular dependencies between scopes. Note that we
  should probably do the same for frontend when we will start testing feature with store.
- **Can import**: api, core, infra, providers, util

#### UI

- **Platform**: frontend
- **Description**: Small resusable components without any business logic. It's rarely nescessary to use this type.
- **Can import**: api, core, ui, util

#### Util

- **Platform**: backend, frontend and crossplatform
- **Description**: Utilities and helpers that can be used across multiple files. It should only be used in scope `shared`.
- **Can import**: api, core, util

---

## Dependency Injection

For backend applications, we use NestJS's built-in dependency injection system to separate implementation details tied to specific libraries from
our business logic (core). This approach also allows us to provide fake implementations for testing, which is
particularly useful when interacting with external APIs.

For frontend applications, we use a custom dependency injection system that is similar to NestJS's dependency injection
system. Implementation can be found in [libs/frontend/shared/injector-util](../libs/frontend/shared/injector-util).

Understanding this dependency injection system is crucial for working effectively on this project. While it may seem a
bit complex at first, after seeing a few examples, you'll notice that the pattern is consistent throughout the codebase.
Taking the time to grasp this concept will significantly improve your ability to develop and test features in our
architecture, as it's a fundamental aspect of how we separate concerns and ensure testability.

**Example of dependency injection to separate prisma from the business logic:**

```typescript
// In core layer
export abstract class UsersRepository {
  abstract findUser(email: string): Promise<User | null>;
  abstract createUser(user: UserCreateArgs): Promise<User>;
}

// In infra layer
@Injectable()
export class UsersRepositoryImpl implements UsersRepository {
  constructor(private readonly prisma: PrismaService) {}

  async findUser(email: string): Promise<User | null> {
    const user = await this.prisma.user.findUnique({ where: { email } });
    return user ? mapPrismaUserToScope(user) : null;
  }

  async createUser(args: UserCreateArgs): Promise<User> {
    const user = await this.prisma.user.create({ data: mapScopeUserToPrisma(args) });
    return mapPrismaUserToScope(user);
  }
}
```

Example of dependency injection to provide fake implementations for testing:

```typescript
// In core layer
export abstract class AuthenticationsProvider {
  abstract refreshToken(refreshToken: string): Promise<TokenInfo>;
}

// In test layer
export class AuthenticationsProviderFake implements AuthenticationsProvider {
  async refreshToken(args: string): Promise<TokenInfo> {
    return {
      email: '<EMAIL>',
      refreshToken: 'fake-refresh-token',
      token: 'fake-token',
      userId: '1'
    };
  }
}

await createTestingWideApp({
  bypassAuth: false,
  controller: AuthenticationsController,
  providers: [{ provide: AuthenticationsProvider, useClass: AuthenticationsProviderFake }]
});
```

---

# Id migration Strategy (number to string)

Historically, we used numeric ids for all our identifiers. However, we have decided to migrate to string ids for several
reasons:

- **Predictability**: Numeric ids can be easily predicted, which can lead to security issues. String ids are less
  predictable and therefore more secure.
- **Readability**: String ids are more readable in testing and debugging. Numeric ids can be difficult to read and
  understand, especially when dealing with large numbers.

Since we don't want to transition smoothly, we have decided to keep the numeric ids in the database and convert them to
string ids in the infra layer. This way, we can keep the numeric ids in the database and still use string ids in our core layer.

See [Zalando guidelines](https://opensource.zalando.com/restful-api-guidelines/#144) for more information.
