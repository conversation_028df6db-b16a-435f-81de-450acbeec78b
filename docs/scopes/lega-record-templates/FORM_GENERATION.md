# Form Generation

## Overview

To generate the form, we use a custom format referred to as **DW**. This format consists of three files:

### `form.dw`

This is a plain text file where each line can represent a category, a conditional question block, or a question. The
hierarchy of the form is determined by the indentation of the lines. Each line becomes a node in the form. Detailed
explanations of each node type will be provided in a later section.

### `library.json`

This file is used to add conditions and question types. Question types are primarily used to define multiple-choice
questions along with their possible options, without cluttering the `form.dw`. Detailed explanations of conditions will
be provided in a later section.

### `filters.json`

This file defines filters for each question type. These filters are used by frontend applications to conditionally
display questions based on the context.

---

These three files are used to generate a form in JSON format.

## Generation Steps

```mermaid
graph TD
  subgraph Step 1 Text to DwLine
    form_dw["form.dw"] -->|Parsed with regex| DwRegex["DwRegex"]
    DwRegex -->|Validated and converted| DwLine["DwLine"]
  end

  subgraph Step 2 DwLine to FormNode
    DwLine -->|Converted| FormNode["FormNode[]"]
    library_json["library.json"] -->|Provides question type and select choices | FormNode
    filters_json["filters.json"] -->|Provides filters for questions| FormNode
  end

  subgraph Step 3 Add Conditions
    library_json["library.json"] -->|Provides conditions | FinalForm["Final Form"]
    FormNode -->|Traverse and apply| FinalForm["Final Form"]
  end
```

The generation process is divided into three main steps:

### Step 1: Text to DwLine

The first step involves parsing the `form.dw` file, validating its content, and creating a model called `DwLine`. This
model represents the structure of the `form.dw` file. The objective of this step is to handle all text parsing with
regular expressions so that models can be used for the remainder of the generation process.

### Step 2: DwLine to FormNode

In the second step, the `DwLines` generated in Step 1 are processed to generate an array of `FormNode[]` while
preserving the hierarchy of the categories. This model represents the final structure of the form. The objective of this
step is to validate the different types of questions possible in the form and ensure that all required attributes are
present.

### Step 3: Adding Conditions

The third step involves adding conditions to the form. There are three types of conditions:

- Legal Operation Template Conditions: These verify the type of operation in which the form is being used.
- Legal Contract Template Conditions: These verify the type of contract in which the form is being used.
- Question Response Conditions: These check the answer to another question, using the QuestionCondition model to reference
this type of condition.

To evaluate a condition, it is necessary to ensure that all conditions of the parent categories or conditional blocks
are true, and that all questions' conditions are also true. During the form generation process, the form is
traversed, and conditions are merged so that both parent and indirect conditions are required for the condition of the
current question being processed.
