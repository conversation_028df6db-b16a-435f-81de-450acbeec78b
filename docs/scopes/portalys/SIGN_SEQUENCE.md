```mermaid
sequenceDiagram
  participant DXS
  participant queues
  participant Companion
  participant Firestore
  participant APITeleactes
  participant BFF
  
  BFF->>APITeleactes: PATCH status=TO_SIGN
  APITeleactes->>Firestore: POST /cardreader-sign/planete-demande-copie-document.1234
  Firestore->>Companion: event POST /cardreader-sign/planete-demande-copie-document.1234
  Companion->>APITeleactes: GET /planete-demande-copie-document/1234
  APITeleactes->>Companion: xml + sign params
  Companion->>queues: {xml + signed params} => /sign-in/planete-demande-copie-document.1234
  queues->>DXS: POST /cardreader-sign
  DXS->>queues: {signed-xml} => /sign-out/planete-demande-copie-document.1234
  queues->>APITeleactes: POST /planete-demande-copie-document/1234
  Note right of APITeleactes: APITeleactes will<br>update the status<br>to SIGNED, stores the<br>signed document in a bucket
  
```
