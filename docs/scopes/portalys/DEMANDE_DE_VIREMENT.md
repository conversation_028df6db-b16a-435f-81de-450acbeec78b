# Demande de virement

## Présentation

Lorsque le notaire effectue des demandes auprès de la DGFIP (ex: demande de copie de document), ces demandes sont payantes et nécessitent un virement
bancaire. Pour cela, le logiciel de reédaction d'acte (LRA) doit avertir le logiciel de comptabilité qui doit renvoyer
en retour une référence de virement bancaire. Ces communications se font de façon asynchrone.
La communication entre le LRA et le logiciel de comptabilité se fait via un dossier partagé sur le réseau de l'étude.
Une étude peut avoir plusieurs postes de travail donc il faut faire attention a ce que plusieurs postes de travail ne réagissent pas au même dépot de fichier.


## Flow d'interaction des applications

```mermaid
sequenceDiagram
    participant Portalys
    participant Firestore
    participant Companion
    participant Réseau_Etude
    participant Logiciel_Comptabilité

    Portalys->>Portalys: Envoie demande de copie de document
    Portalys->>Firestore: Ajoute un event
    Firestore->>Companion: Réceptionne l'event
    Companion->>Portalys: Récupère la data de la demande de virement
    Companion->>Réseau_Etude: Dépose demande de virement dans un fichier
    Réseau_Etude-->>Logiciel_Comptabilité: Consulte le fichier
    Logiciel_Comptabilité->>Réseau_Etude: Virement effectué
    Logiciel_Comptabilité->>Réseau_Etude: Dépose avis d'opéré
    Réseau_Etude-->>Companion: Avis d'opéré déposé
    Companion->>Portalys: Envoie l'avis d'opéré
```
