#!/bin/bash

# This script is used to build the contract stylesheet from the scss source.
# This stylesheet is used frontend to display the contract in the browser and it's sent to the backend to generate the PDF.
# To facilitate the extraction during the PDF generation, the stylesheet is exported as a TypeScript variable.

LOAD_PATH="--load-path=libs/frontend/assets/ui/"
SOURCE_FILE="libs/frontend/assets/ui/style/contract/index.scss"
OUTPUT_FILE="libs/frontend/assets/ui/style/contract/contract-stylesheet.css"

# --style=compressed: Sets the CSS output style to compressed (minified).
# --no-source-map: Disables the generation of source maps.
# --load-path: Defines the load path for SASS to search for partial files used with @use or @import.
pnpm sass --style=compressed --no-source-map $LOAD_PATH $SOURCE_FILE:$OUTPUT_FILE

TS_FILE="libs/frontend/themes/core/contract-style.ts"
CSS_CONTENT=$(<"$OUTPUT_FILE")


echo "/* This file is auto generated by tools/build-contract-stylesheet.sh. Do not edit manually */" > "$TS_FILE"
echo "" >> "$TS_FILE"
echo "export const contractStyle = \`$CSS_CONTENT\`;" >> "$TS_FILE"
