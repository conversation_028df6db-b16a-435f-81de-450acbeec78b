#!/usr/bin/env bash

DOCKER_CONTAINER_NAME=mynotary-db

echo "Deleting old data..."
rm -rf ../../tmp/db
sleep 3

echo "Starting new database..."
docker restart $DOCKER_CONTAINER_NAME
brew install coreutils
timeout 60s bash -c "until docker exec $DOCKER_CONTAINER_NAME pg_isready ; do sleep 5 ; done"

echo "Applying dump..."
sleep 3
docker exec -i $DOCKER_CONTAINER_NAME psql -U api -v -d mynotary < ./dump-prod.sql
docker exec -i $DOCKER_CONTAINER_NAME psql -U api -v -d mynotary < ./setup-static-files.sql
docker exec -i $DOCKER_CONTAINER_NAME psql -U api -v -d mynotary < ./setup-user.sql
docker exec -i $DOCKER_CONTAINER_NAME psql -U api -v -d mynotary < ./setup-document-request-template.sql
docker exec -i $DOCKER_CONTAINER_NAME psql -U api -v -d mynotary < ./activate-extensions.sql

echo "Next steps :"
echo "1) apply liquibase update: mvn resources:resources liquibase:update (java repository)"
echo "2) start all applications: pnpm serve-mn-front, pnpm serve-mn-backends"
echo "3) generate all permissions: http://localhost:9002/admin/actions/permissions"
echo "4) generate default roles: http://localhost:9002/admin/communaute/organisations/1/details/roles (click on 'Générer les permissions')"
