{"name": "mynotary", "version": "0.0.0", "license": "MIT", "engines": {"node": "20.10.0"}, "volta": {"node": "20.10.0"}, "pnpm": {"overrides": {"tree-sitter": "0.20.6"}}, "prisma": {"seed": "tsx libs/backend/shared/prisma-infra/testing-seed.ts"}, "scripts": {"build": "nx build", "codegen": "nx run-many --target codegen --parallel 10", "deploy-react-preprod": "nx run front-mynotary:build:preproduction && nx run front-mynotary:post-build && pnpm firebase deploy --only hosting:front-mynotary -P mynotary-preproduction -m \"$(git reflog -1 | sed 's/^.*: //')\"", "deploy-react-prod": "nx run front-mynotary:build:production && nx run front-mynotary:post-build && pnpm firebase deploy --only hosting:front-mynotary -P mynotary-production -m \"$(git reflog -1 | sed 's/^.*: //')\"", "e2e": "pnpm nx e2e front-mynotary --skip-nx-cache", "e2e-codegen": "pnpm playwright codegen http://localhost:9002", "e2e-ui": "pnpm nx e2e front-mynotary --ui --skip-nx-cache", "ghpr": "git push; gh pr create -f; gh pr merge -d -m --auto", "guards": "pnpm nx run-many -t generate-guards-goldens", "kaspr-create": "tsx libs/backend/scripting/core/exec/create-csv-from-kaspr-data.ts", "kaspr-fetch": "tsx libs/backend/scripting/core/exec/fetch-data-kaspr.ts", "lint": "nx run-many --target lint --max-warnings=0 --parallel 10", "lint-front": "nx run-many -t lint --exclude='*,!tag:platform:frontend'", "lint-back": "nx run-many -t lint --exclude='*,!tag:platform:backend'", "lint-crossplatform": "nx run-many -t lint --exclude='*,!tag:platform:crossplatform'", "nx-migrate": "pnpm nx migrate latest", "nx-run-migrations": "pnpm nx migrate --run-migrations", "postinstall": "husky install", "prepare": "husky", "prisma-add-migration": "tsx tools/prisma/add-migration.ts", "prisma-deploy-migrations": "./tools/prisma/deploy-migrations.sh", "prisma-generate-schema": "pnpm prisma generate --schema libs/backend/shared/prisma-infra/schema.prisma", "prisma-sync": " ./tools/prisma/prisma-sync-schema-with-db.sh", "reset": "nx reset && jest --clearCache && rm -Rf dist", "secrets": "tsx tools/add-secret-gcp.ts", "serve-mn-backends": "pnpm nx run-many --target=serve --projects=api-auth,api-mynotary,api-files --parallel=3", "serve-mn-front": "pnpm nx run-many --target=serve --projects=front-mynotary", "start": "nx serve", "template-compare": "tsx --project tools/legal-templates/tsconfig.json tools/legal-templates/compare-templates.ts", "legal-templates": "nx run tool-legal-templates:update-legal-templates", "check-guards": "tsx tools/check-guards.ts ./libs", "test": "jest --setupFiles dotenv/config --testPathIgnorePatterns=wide.spec.ts --testPathIgnorePatterns=scheduled.spec.ts --watch", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org mynotary --project front-mynotary dist && sentry-cli sourcemaps upload --org mynotary --project front-mynotary dist"}, "private": true, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "3.2.2", "@getbrevo/brevo": "^2.2.0", "@google-cloud/firestore": "^7.11.0", "@google-cloud/logging": "11.2.0", "@google-cloud/logging-winston": "^6.0.0", "@google-cloud/secret-manager": "^6.0.1", "@google-cloud/storage": "^7.16.0", "@google-cloud/tasks": "^6.1.0", "@googlemaps/js-api-loader": "^1.16.6", "@hotjar/browser": "^1.0.9", "@loadable/component": "^5.16.3", "@nestjs/common": "11.1.0", "@nestjs/core": "11.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "11.1.0", "@nestjs/schedule": "^6.0.0", "@pdf-lib/fontkit": "^1.1.1", "@prisma/client": "^6.8.2", "@radix-ui/react-popover": "^1.1.0", "@reduxjs/toolkit": "2.8.1", "@sentry/cli": "^2.39.1", "@sentry/react": "^9.1.0", "@sinonjs/fake-timers": "^14.0.0", "@slack/web-api": "^7.9.0", "@swc/helpers": "0.5.15", "@tanstack/react-query": "^5.77.1", "@tanstack/react-query-devtools": "^5.76.1", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-shell": "^2.2.1", "@types/express": "^5.0.1", "@udecode/cn": "48.0.3", "@udecode/plate": "^48.0.1", "@udecode/plate-alignment": "^48.0.0", "@udecode/plate-basic-marks": "^48.0.0", "@udecode/plate-break": "^48.0.0", "@udecode/plate-docx": "^48.0.0", "@udecode/plate-floating": "^48.0.0", "@udecode/plate-font": "^48.0.0", "@udecode/plate-heading": "^48.0.0", "@udecode/plate-indent": "^48.0.0", "@udecode/plate-link": "^48.0.0", "@udecode/plate-list": "^48.0.0", "@udecode/plate-media": "^48.0.0", "@udecode/plate-node-id": "48.0.0", "@udecode/plate-reset-node": "^48.0.0", "@udecode/plate-resizable": "^48.0.0", "@udecode/plate-select": "^48.0.0", "@udecode/plate-table": "^48.0.0", "@udecode/plate-trailing-block": "^48.0.0", "@udecode/react-utils": "^47.3.1", "archiver": "^7.0.1", "axios": "^1.9.0", "axios-retry": "^4.5.0", "bowser": "2.11.0", "chokidar": "^4.0.3", "class-variance-authority": "0.7.0", "classnames": "^2.5.1", "compressorjs": "^1.2.1", "core-js": "^3.42.0", "csv-parser": "^3.2.0", "date-fns": "^3.6.0", "decimal.js": "^10.5.0", "dotenv": "16.5.0", "dotenv-expand": "^12.0.0", "express": "^5.1.0", "express-openapi-validator": "^5.5.0", "fast-xml-parser": "^5.2.0", "firebase": "^11.8.1", "firebase-admin": "^12.7.0", "firebase-tools": "^14.4.0", "google-auth-library": "^9.15.1", "history": "5.3.0", "html-loader": "5.1.0", "html-webpack-plugin": "5.6.0", "ibantools": "^4.5.1", "is-hotkey": "0.2.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "koffi": "^2.11.0", "libphonenumber-js": "^1.12.4", "libxmljs": "^1.0.11", "lodash": "^4.17.21", "markdown-it": "14.1.0", "mime": "^3.0.0", "nest-winston": "^1.10.0", "nestjs-soap": "^3.0.2", "papaparse": "5.5.2", "passport": "^0.7.0", "passport-http-bearer": "^1.0.1", "pdf-lib": "^1.17.1", "prisma": "^6.8.2", "puppeteer": "24.9.0", "qrcode": "1.4.4", "react": "18.3.1", "react-color": "2.19.3", "react-day-picker": "8.10.0", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-dom": "18.3.1", "react-inlinesvg": "^4.2.0", "react-number-format": "4.9.4", "react-paginate": "8.3.0", "react-pdf": "8.0.2", "react-phone-number-input": "^3.4.0", "react-redux": "9.2.0", "react-rnd": "10.5.1", "react-router": "7.6.0", "react-router-dom": "7.6.0", "react-select": "3.2.0", "react-tooltip": "5.28.0", "react-use": "^17.6.0", "redux-thunk": "3.1.0", "reflect-metadata": "^0.2.2", "regenerator-runtime": "0.14.1", "rrweb": "1.1.3", "rxjs": "^7.8.1", "spark-md5": "^3.0.2", "stripe": "18.0.0", "swagger-ui-react": "5.12.2", "tslib": "^2.8.0", "type-fest": "4.11.0", "ua-parser-js": "2.0.0", "unorm": "1.6.0", "uuid": "11.1.0", "winston": "^3.17.0", "written-number": "^0.11.1"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-react": "^7.27.1", "@biomejs/biome": "1.9.4", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "19.8.0", "@inquirer/prompts": "^7.5.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "11.1.0", "@nx/devkit": "20.4.0", "@nx/eslint": "20.4.0", "@nx/eslint-plugin": "20.4.0", "@nx/jest": "20.4.0", "@nx/js": "20.4.0", "@nx/nest": "20.4.0", "@nx/node": "20.4.0", "@nx/playwright": "20.4.0", "@nx/plugin": "20.4.0", "@nx/react": "20.4.0", "@nx/rspack": "20.4.0", "@nx/web": "20.4.0", "@nx/webpack": "20.4.0", "@nx/workspace": "20.4.0", "@openapitools/openapi-generator-cli": "^2.20.0", "@playwright/test": "1.43.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.0", "@pulumi/cloudflare": "^6.1.0", "@pulumi/command": "^1.0.1", "@pulumi/gcp": "8.14.0", "@pulumi/pulumi": "3.146.0", "@rsdoctor/rspack-plugin": "^1.1.2", "@rspack/cli": "1.3.0", "@rspack/core": "1.3.0", "@rspack/dev-server": "1.1.0", "@rspack/plugin-minify": "^0.7.5", "@rspack/plugin-react-refresh": "^1.4.1", "@svgr/webpack": "8.1.0", "@swc-node/register": "1.10.9", "@swc/cli": "0.7.3", "@swc/core": "1.11.5", "@swc/jest": "0.2.37", "@tanstack/eslint-plugin-query": "^5.67.2", "@tauri-apps/cli": "^2.5.0", "@testing-library/react": "16.3.0", "@types/archiver": "^6.0.2", "@types/google.maps": "3.58.0", "@types/intercom-web": "^2.8.24", "@types/ip": "^1.1.3", "@types/is-hotkey": "0.1.10", "@types/jest": "29.5.12", "@types/jest-image-snapshot": "^6.4.0", "@types/jsonwebtoken": "9.0.6", "@types/loadable__component": "^5.13.9", "@types/lodash": "^4.17.0", "@types/markdown-it": "14.1.1", "@types/mime": "3.0.4", "@types/node": "~22.15.3", "@types/papaparse": "5.3.15", "@types/passport-http-bearer": "^1.0.41", "@types/qrcode": "1.3.5", "@types/react": "18.3.1", "@types/react-color": "3.0.12", "@types/react-dom": "18.3.0", "@types/react-select": "3.0.8", "@types/sinonjs__fake-timers": "8.1.5", "@types/spark-md5": "^3.0.4", "@types/supertest": "6.0.2", "@types/swagger-ui-react": "4.18.3", "@types/ua-parser-js": "^0.7.39", "@types/unorm": "^1.3.31", "@types/uuid": "10.0.0", "@types/webpack": "^5.28.5", "@typescript-eslint/eslint-plugin": "8.26.0", "@typescript-eslint/parser": "8.26.0", "babel-jest": "29.7.0", "enquirer": "^2.4.1", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-sort-destructure-keys": "^2.0.0", "eslint-plugin-sort-keys-fix": "^1.1.2", "eslint-plugin-typescript-sort-keys": "^3.3.0", "eslint-plugin-unused-imports": "3.2.0", "glob": "11.0.0", "husky": "9.1.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-environment-node": "29.7.0", "jest-html-loader": "^1.0.0", "jest-image-snapshot": "^6.5.0", "jest-openapi": "^0.14.2", "jest-transform-css": "^6.0.1", "jsonc-eslint-parser": "^2.4.0", "msw": "2.2.9", "node-object-hash": "^3.1.1", "nx": "20.4.0", "prettier": "3.5.0", "react-refresh": "^0.17.0", "sass": "1.77.6", "stylelint": "^16.15.0", "stylelint-config-clean-order": "^7.0.0", "stylelint-config-standard-scss": "^14.0.0", "supertest": "7.1.0", "swc-loader": "0.2.6", "ts-jest": "29.3.0", "ts-morph": "^26.0.0", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.1", "typescript": "5.7.2", "url-loader": "^4.1.1", "webpack-merge": "6.0.1"}}