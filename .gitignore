# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc

# dependencies
node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings
/clients

# System Files
.DS_Store
Thumbs.db

.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

/firebase-debug.log
/ui-debug.log


# config files we don't want to share
.env.local
.env.preproduction
.env.production
gcp-service-account.json
gcp-service-account-preprod.json
gcp-service-account-prod.json

# firebase hosting
.firebase

# prisma
generated-client

# Jest Snapshot testing
__tmp__
gcp-service-account-preprod-firebase.json

# Playwright auth files
apps/front-mynotary/e2e/.auth

.nx/cache
.nx/workspace-data

testing-schema.prisma

tools/cdc/synthesis-dtos
tools/cdc/lots.csv
playwright-report

# Sentry Config File
.sentryclirc


libs/crossplatform/legal-record-templates/core/records/**/form.json
