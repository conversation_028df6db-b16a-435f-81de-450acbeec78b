import { Injectable } from '@nestjs/common';
import { ContractValidatorsService, CreateContractValidatorsFeatureArgs } from '@mynotary/backend/contract-validators/core';

@Injectable()
export class ContractValidatorsApiService {
  constructor(private contractValidatorsService: ContractValidatorsService) {}

  async createContractValidatorsFeature(args: CreateContractValidatorsFeatureArgs): Promise<void> {
    await this.contractValidatorsService.createContractValidatorsFeature(args);
  }
}
