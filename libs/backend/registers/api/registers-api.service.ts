import { Injectable } from '@nestjs/common';
import {
  Entry,
  FindRegisterEntryArgs,
  RegisterEntriesService,
  UpdateRegisterEntryArgs
} from '@mynotary/backend/registers/core';

@Injectable()
export class RegistersApiService {
  constructor(private registerEntriesService: RegisterEntriesService) {}

  async findRegisterEntry(args: FindRegisterEntryArgs): Promise<Entry | null> {
    return this.registerEntriesService.findRegisterEntry(args);
  }

  async updateRegisterEntry(args: UpdateRegisterEntryArgs): Promise<void> {
    return this.registerEntriesService.updateRegisterEntry(args);
  }
}
