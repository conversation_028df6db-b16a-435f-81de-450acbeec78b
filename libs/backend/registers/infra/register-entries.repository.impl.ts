import { PrismaService, register_entry } from '@mynotary/backend/shared/prisma-infra';
import {
  Entry,
  RegisterEntriesRepository,
  RegisterEntryType,
  GetRegisterEntriesArgs,
  RegisterEntryUpdate,
  RegisterEntryStatus,
  TransactionRegisterEntry,
  ManagementRegisterEntry,
  ReceiverShipRegisterEntry,
  RegisterEntryCommon
} from '@mynotary/backend/registers/core';
import { NotFoundError, convertEnum, JSONValue } from '@mynotary/crossplatform/shared/util';
import { Injectable } from '@nestjs/common';

@Injectable()
export class RegisterEntriesRepositoryImpl implements RegisterEntriesRepository {
  constructor(private prisma: PrismaService) {}

  async getRegisterEntries(args: GetRegisterEntriesArgs): Promise<Entry[]> {
    const entries = await this.prisma.register_entry.findMany({
      select: SELECT_REGISTER_COLUMN,
      where: {
        contract_id: args.contractId != null ? parseInt(args.contractId) : undefined,
        feature_id: args?.registerId != null ? parseInt(args?.registerId) : undefined,
        status: args.status != null ? args.status : undefined,
        type: args.type != null ? convertCoreType(args.type) : undefined
      }
    });

    return entries.map((entry) => convertDbToCore(entry));
  }

  async findRegisterEntry(id: string): Promise<Entry | null> {
    const entry = await this.prisma.register_entry.findUnique({
      select: SELECT_REGISTER_COLUMN,
      where: { id: parseInt(id) }
    });

    return entry != null ? convertDbToCore(entry) : null;
  }

  async updateRegisterEntry(args: RegisterEntryUpdate): Promise<void> {
    const entry = await this.prisma.register_entry.findUnique({
      select: SELECT_REGISTER_COLUMN,
      where: { id: parseInt(args.id) }
    });

    if (entry == null) {
      throw new NotFoundError({ id: args.id, resource: 'RegisterEntry' });
    }

    const answer = entry.answer as JSONValue;

    await this.prisma.register_entry.update({
      data: {
        answer: {
          ...answer,
          observations_registre: {
            value: args.observations != null ? args.observations : answer?.['observations_registre']?.value
          }
        },
        status: args.status != null ? args.status : entry.status
      },
      where: { id: parseInt(args.id) }
    });
  }
}

const SELECT_REGISTER_COLUMN = {
  answer: true,
  contract_id: true,
  feature_id: true,
  id: true,
  status: true,
  type: true
};

export type EntryDb = Pick<register_entry, 'id' | 'feature_id' | 'type' | 'status' | 'contract_id' | 'answer'>;

type DbEntryType = 'TRANSACTION' | 'MANAGEMENT' | 'RECEIVERSHIP';

function convertCoreType(coreType: RegisterEntryType): DbEntryType {
  switch (coreType) {
    case RegisterEntryType.TRANSACTION:
      return 'TRANSACTION';
    case RegisterEntryType.MANAGEMENT:
      return 'MANAGEMENT';
    case RegisterEntryType.RECEIVERSHIP:
      return 'RECEIVERSHIP';
  }
}

function convertDbToCore(entry: EntryDb): Entry {
  const type = convertEnum(RegisterEntryType, entry.type);

  switch (type) {
    case RegisterEntryType.TRANSACTION:
      return convertToTransactionEntry(entry);
    case RegisterEntryType.MANAGEMENT:
      return convertToManagementEntry(entry);
    case RegisterEntryType.RECEIVERSHIP:
      return convertToReceivershipEntry(entry);
  }
}

function convertCommonEntry(entry: EntryDb): RegisterEntryCommon {
  return {
    contractId: entry.contract_id?.toString(),
    id: entry.id.toString(),
    observations: (entry.answer as JSONValue)?.observations_registre?.value ?? '',
    registerId: entry.feature_id.toString(),
    status: convertEnum(RegisterEntryStatus, entry.status)
  };
}

function convertToTransactionEntry(entry: EntryDb): TransactionRegisterEntry {
  return {
    ...convertCommonEntry(entry),
    type: RegisterEntryType.TRANSACTION
  };
}

function convertToManagementEntry(entry: EntryDb): ManagementRegisterEntry {
  return {
    ...convertCommonEntry(entry),
    type: RegisterEntryType.MANAGEMENT
  };
}

function convertToReceivershipEntry(entry: EntryDb): ReceiverShipRegisterEntry {
  return {
    ...convertCommonEntry(entry),
    type: RegisterEntryType.RECEIVERSHIP
  };
}
