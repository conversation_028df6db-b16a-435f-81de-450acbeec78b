import {
  ManagementRegistersRepository, ReceivershipRegistersRepository,
  RegisterEntriesRepository,
  TransactionRegistersRepository
} from '@mynotary/backend/registers/core';
import { ManagementRegistersRepositoryImpl } from './management-register.repository.impl';
import { ReceivershipRegistersRepositoryImpl } from './receivership-register.repository.impl';
import { RegisterEntriesRepositoryImpl } from './register-entries.repository.impl';
import { TransactionRegistersRepositoryImpl } from './transaction-register.repository.impl';

export function provideRegisterInfra() {
  return [
    { provide: ManagementRegistersRepository, useClass: ManagementRegistersRepositoryImpl },
    { provide: TransactionRegistersRepository, useClass: TransactionRegistersRepositoryImpl },
    { provide: ReceivershipRegistersRepository, useClass: ReceivershipRegistersRepositoryImpl },
    { provide: RegisterEntriesRepository, useClass: RegisterEntriesRepositoryImpl },
    { provide: RegisterEntriesRepository, useClass: RegisterEntriesRepositoryImpl }
  ];
}
