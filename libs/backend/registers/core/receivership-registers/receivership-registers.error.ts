import { InvalidInputError } from '@mynotary/crossplatform/shared/util';

export class ReceivershipRegisterStartedError extends InvalidInputError {
  override name = ReceivershipRegisterStartedError.name;

  constructor({ cause }: { cause?: unknown }) {
    super({
      cause,
      displayedMessage:
        'Impossible de mettre à jour la valeur initiale car le registre comporte déjà des numéros entrés.',
      message: "Can't update initial value if register has entries"
    });
  }
}
