import { Entry, RegisterEntryStatus, RegisterEntryType, RegisterEntryUpdate } from './register-entries';

export abstract class RegisterEntriesRepository {
  abstract getRegisterEntries(args: GetRegisterEntriesArgs): Promise<Entry[]>;

  abstract findRegisterEntry(id: string): Promise<Entry | null>;

  abstract updateRegisterEntry(args: RegisterEntryUpdate): Promise<void>;
}

export interface GetRegisterEntriesArgs {
  contractId?: string;
  registerId?: string;
  status?: RegisterEntryStatus;
  type?: RegisterEntryType;
}
