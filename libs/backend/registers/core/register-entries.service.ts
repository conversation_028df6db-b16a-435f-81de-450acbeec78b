import { Injectable } from '@nestjs/common';
import { RegisterEntriesRepository } from './register-entries.repository';
import { Entry, RegisterEntryStatus } from './register-entries';

@Injectable()
export class RegisterEntriesService {
  constructor(private registerEntriesRepository: RegisterEntriesRepository) {}

  async findRegisterEntry(args: FindRegisterEntryArgs): Promise<Entry | null> {
    const entries = await this.registerEntriesRepository.getRegisterEntries(args);

    if (entries.length === 0) {
      return null;
    }

    return entries[0];
  }

  async updateRegisterEntry(updateEntry: UpdateRegisterEntryArgs): Promise<void> {
    await this.registerEntriesRepository.updateRegisterEntry(updateEntry);
  }
}

export interface UpdateRegisterEntryArgs {
  id: string;
  observations?: string;
  status?: RegisterEntryStatus;
}

export interface FindRegisterEntryArgs {
  contractId: string;
  status: RegisterEntryStatus;
}
