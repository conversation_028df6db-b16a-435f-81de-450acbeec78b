export enum RegisterEntryType {
  MANAGEMENT = 'MANAGEMENT',
  RECEIVERSHIP = 'RECEIVERSHIP',
  TRANSACTION = 'TRANSACTION'
}

export enum RegisterEntryStatus {
  CLOSED = 'CLOSED',
  RESERVED = 'RESERVED',
  VALIDATED = 'VALIDATED'
}

export interface RegisterEntryCommon {
  contractId?: string;
  id: string;
  observations: string;
  registerId: string;
  status: RegisterEntryStatus;
}

export type TransactionRegisterEntry = RegisterEntryCommon & {
  type: RegisterEntryType.TRANSACTION;
};

export type ManagementRegisterEntry = RegisterEntryCommon & {
  type: RegisterEntryType.MANAGEMENT;
};

export type ReceiverShipRegisterEntry = RegisterEntryCommon & {
  type: RegisterEntryType.RECEIVERSHIP;
};

export type Entry = TransactionRegisterEntry | ManagementRegisterEntry | ReceiverShipRegisterEntry;

export interface RegisterEntryUpdate {
  id: string;
  observations?: string;
  status?: RegisterEntryStatus;
}
