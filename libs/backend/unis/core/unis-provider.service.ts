import { MnAddress } from '@mynotary/crossplatform/shared/util';

export abstract class UnisProvider {
  abstract findUser(email: string): Promise<UnisUser | null>;

  abstract getUserOrganizations(email: string): Promise<UnisOrganization[]>;
}

export interface UnisUser {
  email: string;
  firstname: string;
  id: string;
  lastname: string;
}

export interface UnisOrganization {
  address: MnAddress;
  id: string;
  name: string;
  siret: string;
}
