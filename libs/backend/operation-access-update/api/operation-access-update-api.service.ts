import { Injectable } from '@nestjs/common';
import {
  InitializeOperationAccessArgs,
  OperationAccessUpdateService
} from '@mynotary/backend/operation-access-update/core';

@Injectable()
export class OperationAccessUpdateApiService {
  constructor(private operationAccessUpdateService: OperationAccessUpdateService) {}

  async initializeOperationAccess(args: InitializeOperationAccessArgs) {
    return await this.operationAccessUpdateService.initializeOperationAccess(args);
  }
}
