import { Injectable } from '@nestjs/common';
import { CreditsRepository } from './credits.repository';
import { SubscriptionsProviderService } from '../subscriptions/subscriptions-provider.service';
import { SubscriptionsService } from '../subscriptions/subscriptions.service';
import { CreditHistoryType, CreditPackType, CreditType, NewCredit } from '@mynotary/crossplatform/billings/core';
import { CreditHistoriesService } from './credit-histories.service';
import { FeatureOrganizationsApiService, SubscriptionFeature } from '@mynotary/backend/feature-organizations/api';
import { Credit } from './credits';
import { FeatureType } from '@mynotary/crossplatform/features/api';

@Injectable()
export class CreditsService {
  constructor(
    private creditsRepository: CreditsRepository,
    private subscriptionsProviderService: SubscriptionsProviderService,
    private subscriptionsService: SubscriptionsService,
    private creditHistoriesService: CreditHistoriesService,
    private featureOrganizationService: FeatureOrganizationsApiService
  ) {}

  async getCredits(organizationId: string): Promise<Credit[]> {
    const features = await this.featureOrganizationService.getFeatures({ organizationId });

    const signatureCredits = await this.getCreditsByType(features, FeatureType.SIGNATURE_CREDITS);
    const advancedSignatureCredits = await this.getCreditsByType(features, FeatureType.ADVANCED_SIGNATURE_CREDITS);
    const registeredLetterCredits = await this.getCreditsByType(features, FeatureType.REGISTERED_LETTER_CREDITS);
    const preEtatDateCredits = await this.getCreditsByType(features, FeatureType.PRE_ETAT_DATE_CREDITS);
    const erpCredits = await this.getCreditsByType(features, FeatureType.ERP_CREDITS);

    return [advancedSignatureCredits, registeredLetterCredits, signatureCredits, preEtatDateCredits, erpCredits].filter(
      (credit): credit is Credit => credit != null
    );
  }

  async buyCredits(args: BuyCreditsArgs) {
    const subscription = await this.subscriptionsService.getOrganizationSubscription(args.organizationId);

    await this.subscriptionsProviderService.buyCredits({
      addToUnbilledCharges: args.addToUnbilledCharges,
      coupon: args.coupon,
      credits: args.credits,
      subscriptionId: subscription.id
    });

    for (const newCredit of args.credits) {
      const feature = await this.featureOrganizationService.findFeature({
        featureType: this.convertToFeatureType(newCredit.type),
        organizationId: args.organizationId
      });

      if (feature != null) {
        const credit = await this.creditsRepository.getCredit(feature.id);
        await this.updateCredit({
          id: feature.id,
          quantity: credit.quantity + newCredit.quantity,
          userId: args.userId
        });
      } else {
        const creditId = await this.creditsRepository.createCredits({
          organizationId: args.organizationId,
          quantity: newCredit.quantity,
          type: newCredit.type
        });

        await this.creditHistoriesService.createCreditHistory({
          creditId,
          creditType: this.convertToCreditPackType(newCredit.type),
          diff: newCredit.quantity,
          userId: args.userId
        });
      }
    }
  }

  async updateCredit({ id, quantity, userId }: UpdateCreditArgs): Promise<void> {
    const credit = await this.creditsRepository.getCredit(id);

    await this.creditHistoriesService.createCreditHistory({
      creditId: id,
      creditType: credit.type,
      diff: quantity - credit.quantity,
      userId: userId
    });

    await this.creditsRepository.updateCredit({ id, quantity, type: credit.type });
  }

  async useCredits({ creditHistoryType, diff, id, label, operationId, userId }: UseCreditsArgs): Promise<void> {
    if (diff === 0) {
      return;
    }

    const credit = await this.creditsRepository.getCredit(id);

    await this.creditHistoriesService.createCreditHistory({
      creditHistoryType,
      creditType: credit.type,
      diff,
      label,
      operationId,
      userId
    });

    await this.creditsRepository.updateCredit({ id, quantity: credit.quantity + diff, type: credit.type });
  }

  private async getCreditsByType(features: SubscriptionFeature[], featureType: FeatureType): Promise<Credit | null> {
    const creditFeature = features.find((feature) => feature.type === featureType);

    if (creditFeature == null) {
      return null;
    }

    return await this.creditsRepository.getCredit(creditFeature.id);
  }

  private convertToFeatureType(type: CreditType | CreditPackType): FeatureType {
    switch (type) {
      case CreditType.SIGNATURE_CREDIT_FREEMIUM:
      case CreditType.SIGNATURE_CREDIT:
      case CreditPackType.PACK_SIGNATURE_CREDITS:
        return FeatureType.SIGNATURE_CREDITS;
      case CreditType.SIGNATURE_ADVANCED_CREDIT:
      case CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS:
        return FeatureType.ADVANCED_SIGNATURE_CREDITS;
      case CreditType.REGISTERED_LETTER_CREDIT:
      case CreditPackType.PACK_REGISTERED_LETTER_CREDITS:
        return FeatureType.REGISTERED_LETTER_CREDITS;
      case CreditType.PRE_ETAT_DATE_CREDIT:
      case CreditPackType.PACK_PRE_ETAT_DATE_CREDITS:
        return FeatureType.PRE_ETAT_DATE_CREDITS;
      case CreditType.ERP_CREDIT:
      case CreditPackType.PACK_ERP_CREDITS:
        return FeatureType.ERP_CREDITS;
    }
  }

  private convertToCreditPackType(type: CreditType | CreditPackType): CreditPackType {
    switch (type) {
      case CreditType.SIGNATURE_CREDIT_FREEMIUM:
      case CreditType.SIGNATURE_CREDIT:
      case CreditPackType.PACK_SIGNATURE_CREDITS:
        return CreditPackType.PACK_SIGNATURE_CREDITS;
      case CreditType.SIGNATURE_ADVANCED_CREDIT:
      case CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS:
        return CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS;
      case CreditType.REGISTERED_LETTER_CREDIT:
      case CreditPackType.PACK_REGISTERED_LETTER_CREDITS:
        return CreditPackType.PACK_REGISTERED_LETTER_CREDITS;
      case CreditType.PRE_ETAT_DATE_CREDIT:
      case CreditPackType.PACK_PRE_ETAT_DATE_CREDITS:
        return CreditPackType.PACK_PRE_ETAT_DATE_CREDITS;
      case CreditType.ERP_CREDIT:
      case CreditPackType.PACK_ERP_CREDITS:
        return CreditPackType.PACK_ERP_CREDITS;
    }
  }
}

interface BuyCreditsArgs {
  addToUnbilledCharges: boolean;
  coupon?: string;
  credits: NewCredit[];
  organizationId: string;
  userId: string;
}

interface UpdateCreditArgs {
  id: string;
  quantity: number;
  userId: string;
}

export interface UseCreditsArgs {
  creditHistoryType?: CreditHistoryType;
  defaultOrganizationId?: string;
  diff: number;
  id: string;
  label?: string;
  operationId?: string;
  userId: string;
}
