import { CreditsRepository, CreateCreditsArgs, Credit, UpdateCreditArgs } from '@mynotary/backend/billings/core';
import { PrismaService, organization_feature } from '@mynotary/backend/shared/prisma-infra';
import { forEach, filter, find } from 'lodash';
import { JSONObject, convertEnum, NotFoundError } from '@mynotary/crossplatform/shared/util';
import { CreditType, Credits, CreditPackType } from '@mynotary/crossplatform/billings/core';
import { Injectable } from '@nestjs/common';
import { FeatureType } from '@mynotary/crossplatform/features/api';

@Injectable()
export class CreditsRepositoryImpl implements CreditsRepository {
  constructor(private prisma: PrismaService) {}

  async createCredits(args: CreateCreditsArgs): Promise<string> {
    const creditFeature = this.createCreditFeature({ quantity: args.quantity, type: args.type });
    const newFeature = await this.prisma.organization_feature.create({
      data: {
        enabled: true,
        feature_id: creditFeature.featureId,
        feature_state: creditFeature.featureState,
        organization_id: parseInt(args.organizationId)
      }
    });

    return newFeature.id.toString();
  }

  public async getCredits(subscriptionIds: string[]): Promise<Map<string, Credits>> {
    const organizations = await this.prisma.organization.findMany({
      select: { id: true, subscription_id: true },
      where: {
        subscription_id: { in: subscriptionIds.map((s) => parseInt(s)) }
      }
    });

    const orgaIds = organizations.map((organization) => organization.id);

    const features = await this.prisma.organization_feature.findMany({
      select: { feature_id: true, feature_state: true, id: true, organization_id: true },
      where: {
        enabled: true,
        feature_id: {
          in: [
            FeatureType.ADVANCED_SIGNATURE_CREDITS,
            FeatureType.REGISTERED_LETTER_CREDITS,
            FeatureType.SIGNATURE_CREDITS
          ]
        },
        organization_id: { in: orgaIds }
      }
    });

    const subscriptionCredits = new Map<string, Credits>();
    forEach(subscriptionIds, (subId) => {
      const subOrgas = filter(organizations, (orga) => orga.subscription_id === parseInt(subId));
      const subFeatures = filter(
        features,
        (feature) => find(subOrgas, (orga) => orga.id === feature.organization_id) != null
      );

      const credits = this.sumCredits(subFeatures);
      subscriptionCredits.set(subId.toString(), credits);
    });
    return subscriptionCredits;
  }

  async getCredit(creditId: string): Promise<Credit> {
    const feature: SelectedFeature | null = await this.prisma.organization_feature.findUnique({
      select: { feature_id: true, feature_state: true, id: true, organization_id: true },
      where: { id: parseInt(creditId) }
    });

    if (feature == null) {
      throw new NotFoundError({ id: creditId, resource: 'credits' });
    }

    return this.convertToCredit(feature);
  }

  async updateCredit(args: UpdateCreditArgs): Promise<void> {
    const feature = this.createCreditFeature(args);

    await this.prisma.organization_feature.update({
      data: { feature_state: feature.featureState },
      where: { id: parseInt(args.id) }
    });
  }

  private createCreditFeature({ quantity, type }: CreateCreditFeatureArgs): CreditFeature {
    switch (type) {
      case CreditPackType.PACK_REGISTERED_LETTER_CREDITS:
      case CreditType.REGISTERED_LETTER_CREDIT:
        return {
          featureId: FeatureType.REGISTERED_LETTER_CREDITS,
          featureState: { registeredLetterCredits: { number: quantity } },
          label: 'Crédits de lettre recommandée'
        };
      case CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS:
      case CreditType.SIGNATURE_ADVANCED_CREDIT:
        return {
          featureId: FeatureType.ADVANCED_SIGNATURE_CREDITS,
          featureState: { advancedSignatureCredits: { number: quantity } },
          label: 'Crédits de signature avancée'
        };
      case CreditPackType.PACK_SIGNATURE_CREDITS:
      case CreditType.SIGNATURE_CREDIT:
      case CreditType.SIGNATURE_CREDIT_FREEMIUM:
        return {
          featureId: FeatureType.SIGNATURE_CREDITS,
          featureState: { signatureCredits: { number: quantity } },
          label: 'Crédits de signature'
        };
      case CreditType.PRE_ETAT_DATE_CREDIT:
      case CreditPackType.PACK_PRE_ETAT_DATE_CREDITS:
        return {
          featureId: FeatureType.PRE_ETAT_DATE_CREDITS,
          featureState: { preEtatDateCredits: { number: quantity } },
          label: 'Crédits de pré-état daté'
        };
      case CreditType.ERP_CREDIT:
      case CreditPackType.PACK_ERP_CREDITS:
        return {
          featureId: FeatureType.ERP_CREDITS,
          featureState: { erpCredits: { number: quantity } },
          label: 'Crédits Erp'
        };
    }
  }

  private sumCredits(features: { feature_id: string; feature_state: unknown }[]): Credits {
    const credits: Credits = {
      advancedSignatureCredits: 0,
      erpCredits: 0,
      preEtatDateCredits: 0,
      registeredLetterCredits: 0,
      signatureCredits: 0
    };

    forEach(features, (feature) => {
      const state = feature.feature_state as JSONObject;
      const featureId = convertEnum(FeatureType, feature.feature_id);

      switch (featureId) {
        case FeatureType.ADVANCED_SIGNATURE_CREDITS:
          credits.advancedSignatureCredits += state?.['advancedSignatureCredits']?.number ?? 0;
          break;
        case FeatureType.REGISTERED_LETTER_CREDITS:
          credits.registeredLetterCredits += state?.['registeredLetterCredits']?.number ?? 0;
          break;
        case FeatureType.SIGNATURE_CREDITS:
          credits.signatureCredits += state?.['signatureCredits']?.number ?? 0;
          break;
        case FeatureType.PRE_ETAT_DATE_CREDITS:
          credits.preEtatDateCredits += state?.['preEtatDateCredits']?.number ?? 0;
          break;
        case FeatureType.ERP_CREDITS:
          credits.erpCredits += state?.['erpCredits']?.number ?? 0;
          break;
      }
    });

    return credits;
  }

  private convertToCredit(dbCredit: SelectedFeature): Credit {
    const creditType = this.convertToCreditPackType(dbCredit.feature_id as FeatureType);
    const featureState = dbCredit.feature_state as JSONObject;

    return {
      id: dbCredit.id.toString(),
      organizationId: dbCredit.organization_id.toString(),
      quantity: this.quantityFromCreditState(creditType, featureState),
      type: creditType
    };
  }

  private convertToCreditPackType(dbCreditType: FeatureType): CreditPackType {
    switch (dbCreditType) {
      case FeatureType.SIGNATURE_CREDITS:
        return CreditPackType.PACK_SIGNATURE_CREDITS;
      case FeatureType.ADVANCED_SIGNATURE_CREDITS:
        return CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS;
      case FeatureType.REGISTERED_LETTER_CREDITS:
        return CreditPackType.PACK_REGISTERED_LETTER_CREDITS;
      case FeatureType.PRE_ETAT_DATE_CREDITS:
        return CreditPackType.PACK_PRE_ETAT_DATE_CREDITS;
      case FeatureType.ERP_CREDITS:
        return CreditPackType.PACK_ERP_CREDITS;
      case FeatureType.CHAT_SUPPORT:
      case FeatureType.CONTRACT_BRANDING:
      case FeatureType.CO_BRANDING:
      case FeatureType.DATA_VISUALIZATION:
      case FeatureType.EMAIL_BRANDING:
      case FeatureType.EXTERNAL_PARTICIPANTS:
      case FeatureType.INTERCONNECTIONS:
      case FeatureType.INVOICES:
      case FeatureType.MANAGEMENT_REGISTER_ACCESS:
      case FeatureType.RECEIVERSHIP_REGISTER_ACCESS:
      case FeatureType.RECORD_SHARING:
      case FeatureType.ROLE_MANAGEMENT:
      case FeatureType.SMS_REGISTER_ACCESS:
      case FeatureType.TRANSACTION_REGISTER_ACCESS:
      case FeatureType.V3_ACCESS:
      case FeatureType.VALIDATION_CUSTOMIZATION:
      case FeatureType.CONTRACT_REVIEWS:
        throw new Error('Feature not supported');
    }
  }

  private quantityFromCreditState(creditType: CreditPackType, state: JSONObject): number {
    switch (creditType) {
      case CreditPackType.PACK_REGISTERED_LETTER_CREDITS:
        return state?.['registeredLetterCredits']?.number ?? 0;
      case CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS:
        return state?.['advancedSignatureCredits']?.number ?? 0;
      case CreditPackType.PACK_SIGNATURE_CREDITS:
        return state?.['signatureCredits']?.number ?? 0;
      case CreditPackType.PACK_PRE_ETAT_DATE_CREDITS:
        return state?.['preEtatDateCredits']?.number ?? 0;
      case CreditPackType.PACK_ERP_CREDITS:
        return state?.['erpCredits']?.number ?? 0;
    }
  }
}

interface CreditFeature {
  featureId:
    | FeatureType.ADVANCED_SIGNATURE_CREDITS
    | FeatureType.SIGNATURE_CREDITS
    | FeatureType.REGISTERED_LETTER_CREDITS
    | FeatureType.PRE_ETAT_DATE_CREDITS
    | FeatureType.ERP_CREDITS;
  featureState: CreditState;
  label: string;
}

type SelectedFeature = Pick<organization_feature, 'id' | 'organization_id' | 'feature_state' | 'feature_id'>;

interface CreateCreditFeatureArgs {
  quantity: number;
  type: CreditType | CreditPackType;
}

type CreditState =
  | {
      advancedSignatureCredits: { number: number };
    }
  | {
      registeredLetterCredits: { number: number };
    }
  | {
      signatureCredits: { number: number };
    }
  | {
      preEtatDateCredits: { number: number };
    }
  | {
      erpCredits: { number: number };
    };
