import { Controller, UseGuards, Get, Param } from '@nestjs/common';
import { CouponDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { CouponsService } from '@mynotary/backend/billings/core';
import { convertCouponToDto } from './billings.dto-converters';
import { queryResolver } from '@mynotary/backend/shared/auth-util';
import { IsSelf } from '@mynotary/backend/authorizations/api';

@Controller()
export class CouponsController {
  constructor(private couponService: CouponsService) {}

  @UseGuards(IsSelf({ userIdResolver: queryResolver('userId') }))
  @Get('/coupon-validation/:couponCode')
  async validateCoupon(@Param('couponCode') couponCode: string): Promise<CouponDto> {
    const coupon = await this.couponService.getCoupon({ couponCode: couponCode });
    return convertCouponToDto(coupon);
  }
}
