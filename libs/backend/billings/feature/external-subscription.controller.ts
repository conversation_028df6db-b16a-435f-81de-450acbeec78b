import { Controller, Param, UseGuards, Delete } from '@nestjs/common';
import { SubscriptionsProviderService } from '@mynotary/backend/billings/core';
import { IsAdmin } from '@mynotary/backend/authorizations/api';

@Controller()
export class ExternalSubscriptionsController {
  constructor(private subscriptionProvider: SubscriptionsProviderService) {}

  /**
   * This method is designed to cancel a subscription in Zoho. It should be utilized exclusively for test teardown purposes.
   *
   * @param subscriptionId - The unique identifier of the subscription to be canceled.
   * @returns A promise that resolves with no data upon successful cancellation.
   */
  @UseGuards(IsAdmin())
  @Delete('/external-subscriptions/:subscriptionId')
  async cancelExternalSubscription(@Param('subscriptionId') subscriptionId: string): Promise<void> {
    await this.subscriptionProvider.cancelSubscription(subscriptionId);
  }
}
