import { Injectable } from '@nestjs/common';
import {
  FeatureOrganizationsRepository,
  SubscriptionLight,
  SubscriptionFeature
} from './feature-organizations.repository';

import { FindPlanTypeArgs } from './feature-organizations.repository';
import { getPlanConfig } from '@mynotary/crossplatform/billings/api';
import { filter } from 'lodash';
import { NotFoundError } from '@mynotary/crossplatform/shared/util';
import { FeatureLight } from '@mynotary/backend/shared/features-util';
import { OrganizationHoldingsApiService, HoldingIds } from '@mynotary/backend/organization-holdings/api';
import { FeatureDuplicateError } from './feature-organizations.errors';
import { getFeatureTypesFromAddonTypes } from './feature-organizations.utils';
import { FeatureType } from '@mynotary/crossplatform/features/api';

@Injectable()
export class FeatureOrganizationsService {
  constructor(
    private featureOrganizationsRepository: FeatureOrganizationsRepository,
    private organizationHoldingsApiService: OrganizationHoldingsApiService
  ) {}

  /**
   * Get features from the target organization and its holdings.
   * If the same feature exists on both the target organization and its holdings, the feature from the target
   * organization is returned. For optional features, we filter the features based on the subscription.
   * If the subscription doesn't exist, we return all the features. This scenario should not happen very often and
   * it is mainly to handle cases directly configured from MyNotary admin
   * @param featureType
   * @param organizationId
   */
  async getFeatures({ featureType, organizationId }: GetFeaturesArgs): Promise<SubscriptionFeature[]> {
    const organizationIds = [organizationId];

    const holdings = await this.organizationHoldingsApiService.findHoldings(organizationId);

    if (holdings?.holdingId != null) {
      organizationIds.push(holdings.holdingId);
    }

    if (holdings?.majorHoldingId != null) {
      organizationIds.push(holdings.majorHoldingId);
    }

    let features = await this.featureOrganizationsRepository.findFeatures({ featureType, organizationIds });
    features = this.filterHoldingFeatures({ features, holdings, targetOrganizationId: organizationId });

    const subscription = await this.findSubscriptionLight({ organizationId });

    if (subscription == null) {
      return features;
    }

    return this.filterOptionalFeature({ features, subscription });
  }

  async findFeature({ featureType, organizationId }: GetFeatureArgs): Promise<SubscriptionFeature | null> {
    const features = await this.getFeatures({ featureType, organizationId });

    if (features.length > 1) {
      throw new FeatureDuplicateError({ featureType, organizationId });
    }

    return features[0] ?? null;
  }

  async getFeature({ featureType, organizationId }: GetFeatureArgs): Promise<SubscriptionFeature> {
    const feature = await this.findFeature({ featureType, organizationId });

    if (feature == null) {
      throw new NotFoundError({ id: `organizationId: ${organizationId}, type: ${featureType}`, resource: 'feature' });
    }

    return feature;
  }

  /**
   * Return the list of organization ids that have access to the feature.
   * Get sub organizations and their features then check that the feature retrieved is the same as the one we are updating.
   * We use featuresService.getFeatures to filter optional features.
   *
   * eg: if we update TRANSACTION_REGISTER_ACCESS feture on holding, we don't want to generate permissions
   * on sub organizations that have no access to the feature.
   */
  public async getFeatureOrganizations(feature: GetFeatureOrganizationsArgs) {
    const { organizationIds } = await this.featureOrganizationsRepository.getFeatureOrganizations(
      feature.organizationId
    );
    const orgaWithAccessToFeatures: string[] = [feature.organizationId];

    for (const organizationId of organizationIds) {
      const features = await this.getFeatures({
        featureType: feature.type,
        organizationId
      });

      if (features.length > 1) {
        throw new FeatureDuplicateError({ featureType: feature.type, organizationId: organizationId });
      }

      if (features[0]?.id === feature.id) {
        orgaWithAccessToFeatures.push(organizationId);
      }
    }

    return { organizationIds: orgaWithAccessToFeatures };
  }

  async findSubscriptionLight(args: FindPlanTypeArgs) {
    return await this.featureOrganizationsRepository.findSubscriptionLight(args);
  }

  /**
   * Filter optional features if the feature subscription is different from the target organization subscription.
   */
  private filterOptionalFeature({ features, subscription }: FilterOptionalFeatureArgs): FeatureLight[] {
    const addonsOptional = getPlanConfig(subscription.planType)?.addonsOptional;
    const optionalFeatures = getFeatureTypesFromAddonTypes(addonsOptional);

    return filter(features, (feature) => {
      const isOptional = optionalFeatures.includes(feature.type);
      return !isOptional || (isOptional && subscription.id === feature.subscriptionId);
    });
  }

  /**
   * Filter features from holding if they exists in the target organization
   */
  private filterHoldingFeatures({
    features,
    holdings,
    targetOrganizationId
  }: FilterHoldingFeaturesArgs): FeatureLight[] {
    const result: FeatureLight[] = [];
    const addedFeatures: Set<FeatureType> = new Set();

    for (const feature of features) {
      if (feature.organizationId === targetOrganizationId && !addedFeatures.has(feature.type)) {
        result.push(feature);
        addedFeatures.add(feature.type);
      }
    }

    for (const feature of features) {
      if (feature.organizationId === holdings?.holdingId && !addedFeatures.has(feature.type)) {
        result.push(feature);
        addedFeatures.add(feature.type);
      }
    }

    for (const feature of features) {
      if (feature.organizationId === holdings?.majorHoldingId && !addedFeatures.has(feature.type)) {
        result.push(feature);
        addedFeatures.add(feature.type);
      }
    }

    return result;
  }
}

export interface GetFeaturesArgs {
  featureType?: FeatureType;
  organizationId: string;
}

interface FilterOptionalFeatureArgs {
  features: SubscriptionFeature[];
  subscription: SubscriptionLight;
}

interface FilterHoldingFeaturesArgs {
  features: FeatureLight[];
  holdings: HoldingIds | null;
  targetOrganizationId: string;
}

export interface GetFeatureOrganizationsArgs {
  id: string;
  organizationId: string;
  type: FeatureType;
}

export interface GetFeatureArgs {
  featureType: FeatureType;
  organizationId: string;
}
