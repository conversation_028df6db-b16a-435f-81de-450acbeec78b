<?xml version="1.0" encoding="UTF-8"?>
<!-- Version 2.20 du 07/10/2015 - Modification du schéma de signature -->
<!-- Version 2.21 du 26/04/2016 - Modification du schéma pour les PVI/ Etat civil / Document d'arpentage -->
<!--                                          - Suprresion des composants non utilisés -->
<!--                                            Partie_Duplicable_PS_IMM_Type,Partie_Duplicable_IMM_Type, Partie_Duplicable_TAB_Type, Plus_Value_Brute_Type,Plus_Value_Brute_PS_Type,Plus_Value_Nette_Imposable_IMM_Type, Plus_Value_Nette_Imposable_IMM_PS_Type, Plus_Value_Nette_Imposable_TAB_Type,Associes_Presents_Type, Base_Taxable_TFCTC_Type,Cession_Par_Societe_Type,Cession_Par_Societe_PS_Type,Total_A_Payer_Type,Identification_Associes_Societe_Type,Impot_Societe_Non_Residente_IMM_Type,Impot_Societe_Non_Residente_TAB_Type, Liquidation_Droits_IMM_Type, Liquidation_Droits_IMM_PS_Type, Liquidation_Droits_TAB_Type, Plus_Value_Totale_Type, Plus_Value_Totale_PS_Type, Taxation_Pvi_Type -->
<xs:schema xmlns:ta="http://teleactes.real.not/v2009" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" targetNamespace="http://teleactes.real.not/v2009" elementFormDefault="qualified" attributeFormDefault="unqualified" version="2.21" xml:lang="fr">
	<xs:import namespace="http://www.w3.org/2000/09/xmldsig#" schemaLocation="xmldsig-core-schema.xsd"/>
	<xs:include schemaLocation="teleactes_v2009.xsd"/>
	<!--************** ELEMENTS *****************************-->
	<xs:element name="D-2048-IMM">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="synthese-2048-IMM" type="ta:Synthese_2048_IMM_Type"/>
				<xs:element name="depot-2048-IMM" type="ta:Depot_2048_IMM_Type" minOccurs="0" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<!--************** TYPES SIMPLES ************************-->
	<xs:simpleType name="Car_Majuscule_Type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="[A-Z]"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Ape_Type">
		<xs:annotation>
			<xs:documentation>4 caractères alphanumériques (3 chiffres et 1 lettre)</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[a-zA-z.0-9]{1,6}"/>
		</xs:restriction>
	</xs:simpleType>
	<!--************** TYPES ENUMERES *********************-->
	<xs:simpleType name="Enum_Nature_Immeuble_Depense">
		<xs:restriction base="xs:string">
			<xs:enumeration value="I"/>
			<xs:enumeration value="D"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Cedant_PVI">
		<xs:restriction base="xs:string">
			<xs:enumeration value="P">
				<xs:annotation>
					<xs:documentation>Personne Physique</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="M">
				<xs:annotation>
					<xs:documentation>Personne Morale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Type_Declaration_Type">
		<xs:annotation>
			<xs:documentation source="S. LANDEAU, le 09/03/2007" xml:lang="FR">Listing des 3 valeurs de déclaration possible</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="PVI"/>
			<xs:enumeration value="TFCTC"/>
			<xs:enumeration value="TCTNRC"/>
			<xs:enumeration value="PVI-TFCTC"/>
			<xs:enumeration value="PVI-TCTNRC"/>
			<xs:enumeration value="TFCTC-TCTNRC"/>
			<xs:enumeration value="PVI-TFCTC-TCTNRC"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Type_Mention_Legale">
		<xs:annotation>
			<xs:documentation source="S. LANDEAU, le 09/03/2007" xml:lang="fr">Listing des 2 valeurs possible de type de mention légale</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="PVI"/>
			<xs:enumeration value="TFCTC"/>
		</xs:restriction>
	</xs:simpleType>
	<!--************** TYPES COMPLEXES ******************-->
	<xs:complexType name="Adresse_Usager_Pvi_Type">
		<xs:attributeGroup ref="ta:Adresse_Usager_Pvi_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Adresse_Usager_Pvi_Type.att">
		<xs:attribute name="residence-batiment" type="ta:String1-32">
			<xs:annotation>
				<xs:documentation>Résidence/bâtiment</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="numero-libelle" type="ta:String1-32">
			<xs:annotation>
				<xs:documentation>N° et libellé de la voie</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="lieu-dit" type="ta:String1-32"/>
		<xs:attribute name="code-postal" type="ta:String1-5" use="required"/>
		<xs:attribute name="commune" type="ta:String1-26" use="required"/>
	</xs:attributeGroup>
	<xs:complexType name="Cedant_Type">
		<xs:annotation>
			<xs:documentation>Personne physique ou morale décrivant le disposant concerné</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="cedant-personne-physique" type="ta:Personne_Physique_Type" minOccurs="0" maxOccurs="2">
				<xs:annotation>
					<xs:documentation>Noms et prénoms ou forme NB : ce champ peut contenir plusieurs noms et prénoms de cédants (cas de la cession d'un bien par un couple marié).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="cedant-personne-morale" type="ta:Personne_Morale_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Dénomination =&gt; données à récupérer dans Acte de vente / Client pm</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="siren-ape" type="ta:Siren_Ape_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Personne physique ou morale décrivant le disposant concerné</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="associe-personne-morale" type="ta:Personne_Morale_Type" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="associe-personne-physique" type="ta:Personne_Physique_Type" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Complements_PVI_TFCTC_Type">
		<xs:sequence>
			<xs:element name="commun">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="date-acte" type="xs:date">
							<xs:annotation>
								<xs:documentation>Date de l'acte Cette date permet de contrôler la validité du sous schéma PVI utilisée</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="nature-cedant" type="ta:Enum_Nature_Cedant_PVI">
							<xs:annotation>
								<xs:documentation>Nature du cédant ; Personne physique (P) ou morale (M)</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="fondement-taxation" maxOccurs="unbounded">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="type-base-taxation">
										<xs:simpleType>
											<xs:restriction base="xs:string">
												<xs:enumeration value="base-ire"/>
												<xs:enumeration value="base-ps"/>
												<xs:enumeration value="associes-ste-partA-ir-eee"/>
												<xs:enumeration value="associes-ste-partB-ir-autre"/>
												<xs:enumeration value="associes-ste-partA-ps-eee"/>
												<xs:enumeration value="associes-ste-partB-ps-autre"/>
												<xs:enumeration value="base-1609noniesG"/>
												<xs:enumeration value="base-1609noniesG-associes"/>
												<xs:enumeration value="base-ste-nr"/>
												<xs:enumeration value="base1-tftct"/>
												<xs:enumeration value="base2-tftct"/>
												<xs:enumeration value="base5-tctnrc"/>
												<xs:enumeration value="base10-tctnrc"/>
											</xs:restriction>
										</xs:simpleType>
									</xs:element>
									<xs:element name="base-taxation" type="ta:Numerique10"/>
									<xs:element name="abattement-art-CGI" type="ta:Numerique10" minOccurs="0"/>
									<xs:element name="article-cgi-associe" type="ta:Article_CGI_Type"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="PVI" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="nombre-fractions" type="ta:Numerique2" minOccurs="0" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation>Si acquisition par fractions successives</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="code-pays">
							<xs:annotation>
								<xs:documentation>Code pays : Code pays de résidence du cédant. Ce champ est obligatoirement renseigné par les notaires via la table INSEE des Pays. Le code Pays de la partie Complément PVI (Cf.§3.3), est lui même alimenté simultanément afin d'assurer la concordance du code et du libellé.</xs:documentation>
							</xs:annotation>
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:pattern value="[0-9]{1,5}"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="TFCTC" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="code-departement-TFCTC" type="ta:Code_Departement_Type"/>
						<xs:element name="code-commune-TFCTC" type="ta:Code_Commune_Type"/>
						<xs:element name="date-effet-TFCTC" type="xs:date" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="TCTNRC" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="code-departement-TCTNRC" type="ta:Code_Departement_Type"/>
						<xs:element name="code-commune-TCTNRC" type="ta:Code_Commune_TOPAD_Type"/>
						<xs:element name="date-effet-TCTNRC" type="xs:date" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Correction_Applicable_Immeubles_Type">
		<xs:annotation>
			<xs:documentation>correction applicable au prix d'acquisition</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="correction-detail" type="ta:Correction_Type" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="correction-totale" type="ta:Numerique10" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total de la colonne E</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="total-col-F-reintegration-amortissements" type="ta:Numerique10" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Total de la colonne F réintégration amortissements</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Correction_Type">
		<xs:sequence>
			<xs:element name="nature-immeuble-depense" type="ta:Enum_Nature_Immeuble_Depense" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Nature immeuble ou dépense I: Immeuble; D Dépense</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="valeur-nette" type="ta:Numerique10" minOccurs="0">
				<xs:annotation>
					<xs:documentation>si I : Prix ou valeur nette si D : montant des dépenses afférentes au seul l'immeuble</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="nombre-annees" type="ta:Numerique3" minOccurs="0">
				<xs:annotation>
					<xs:documentation>nombre d'années entiéres de détention</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="taux-annuel" type="ta:Taux2nDec" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Taux par année entièrre de détention</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="taux-global" type="ta:Taux2nDec" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Taux global</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="montant-correction" type="ta:Numerique10" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Montant de la correction</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reintegration-amortissements" type="ta:Numerique10" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Réintégration amortissements</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Designation_Bien_Type">
		<xs:sequence>
			<xs:element name="nature" type="ta:XSString1-30" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Construction, terrain à bâtir, terre agricole, ...</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="date-constructibilite-terrain" type="xs:date" minOccurs="0">
				<xs:annotation>
					<xs:documentation source="S. LANDEAU, le 09/03/2007" xml:lang="fr">Ajout 2007M conformément à la nouvelle version du cahier des charges</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="superficie-cadastre" type="ta:Superficie_Cadastre_Type"/>
			<xs:element name="adresse" type="ta:Adresse_Type">
				<xs:annotation>
					<xs:documentation>Adresse de l'immeuble</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="hectares-cedes" type="ta:Numerique7" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Si peuplement forestier ?</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Depot_2048_IMM_Type">
		<xs:annotation>
			<xs:documentation>Le nombre de PVI dépend du nombre de cédants et d'immeubles et de la situation des couples "cédant/immeuble" eu égard à la déclaration de PVI (cf composition de la partie "synthèse PVI")</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="identifiant-declaration" type="ta:Numerique3"/>
			<xs:element name="formulaire-2048-IMM" type="ta:Formulaire_2048_IMM_Type"/>
			<xs:element name="complements-PVI-TFCTC-TCTNRC" type="ta:Complements_PVI_TFCTC_Type"/>
			<xs:element name="document-joint" type="xs:base64Binary"/>
			<xs:element ref="ds:Signature"/>
		</xs:sequence>
		<xs:attribute name="Id" type="xs:ID"/>
	</xs:complexType>
	<xs:attributeGroup name="Depot_Pvi_Type.att">
		<xs:attribute name="identifiant-declaration" type="ta:Numerique3" use="required">
			<xs:annotation>
				<xs:documentation>Numéro séquentiel attribué par l'émetteur, relatif au dossier à partir de 1</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:complexType name="Formulaire_2048_IMM_Type">
		<xs:sequence>
			<xs:element name="elements-communs">
				<xs:complexType>
					<xs:sequence>
						<xs:element ref="ta:millesime" minOccurs="0"/>
						<xs:element name="type-declaration" type="ta:Enum_Type_Declaration_Type"/>
						<xs:element name="redacteur-acte" type="ta:Redacteur_Pvi_Type"/>
						<xs:element name="cedant" type="ta:Cedant_Type">
							<xs:annotation>
								<xs:documentation>Personne physique ou morale décrivant le disposant concerné</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="representant-accredite" type="ta:Representant_Accredite_Type" minOccurs="0">
							<xs:annotation>
								<xs:documentation>Si le cédant est un non-résident ou un associé non-résident</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="bien-cede" type="ta:Designation_Bien_Type"/>
						<xs:element name="cession" type="ta:Rens_Cession_Type">
							<xs:annotation>
								<xs:documentation>Renseignements relatif à la cession</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="origine-propriete" type="ta:Origine_Propriete_Type"/>
						<xs:element name="attestation-signature" type="ta:String-1-300">
							<xs:annotation>
								<xs:documentation>Le notaire atteste que la déclaration de PVI a été bien signée par le cédant</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="millesime" type="ta:Millesime_Type"/>
	<xs:complexType name="Millesime_Type">
		<xs:sequence>
			<xs:element name="Mois" type="ta:Num2" minOccurs="0"/>
			<xs:element name="Annee" type="ta:Num4" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Mode_Acquisition_Type">
		<xs:sequence>
			<xs:element name="titre-onereux" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A titre onéreux</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="par-succession" type="xs:boolean" minOccurs="0"/>
			<xs:element name="par-donation" type="xs:boolean" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Origine_Propriete_Type">
		<xs:sequence>
			<xs:element name="date-acquisition" type="xs:date" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Date d'acquision du bien cédé (plusieurs dates possibles)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="date-debut-travaux" type="xs:date" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>pour une construction</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="date-acquisition-terrain" type="xs:date" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>si construction</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="mode-acquisition" type="ta:Mode_Acquisition_Type" maxOccurs="unbounded"/>
			<xs:element name="detenu-indivision" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Le bien cédé est-il détenu en indivision</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="taux-indivision" type="ta:Taux2nDec" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Pourcentage de détention en indivision</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Redacteur_Pvi_Type">
		<xs:sequence>
			<xs:element name="nom">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="60"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="adresse" type="ta:Adresse_Type"/>
			<xs:element name="crpcen" type="ta:Identifiant_Etude_Notariale_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Il correspond au code de l'étude notariale (code CRPCEN).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Rens_Cession_Type">
		<xs:annotation>
			<xs:documentation>Renseignements relatif à la cession</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="transfert-propriete" type="ta:Transfert_Propriete_Type">
				<xs:annotation>
					<xs:documentation>Renseignements relatif à la cession</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="acquereur-personne-physique" type="ta:Personne_Physique_Type" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Nom de l'acquéreur</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="acquereur-personne-morale" type="ta:Personne_Morale_Type" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Dénomination de l'acquereur</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="siren-ape" type="ta:Siren_Ape_Type" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Personne physique ou morale décrivant le disposant concerné</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Representant_Accredite_Type">
		<xs:annotation>
			<xs:documentation>Si le cédant est un non-résident ou un associé non-résident</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="representant" type="ta:Representant_Personne_Physique_Type">
				<xs:annotation>
					<xs:documentation>Noms et prénoms ou forme NB : ce champ peut contenir plusieurs noms et prénoms de cédants (cas de la cession d'un bien par un couple marié).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="qualite-representant" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Qualité du représentant Ex Gérant</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="60"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="representant-personne-morale" type="ta:Representant_Personne_Morale_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Dénomination raison sociale</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="lieu-signature" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="32"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="date-signature" type="xs:date" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Siren_Ape_Type">
		<xs:annotation>
			<xs:documentation>Personne physique ou morale décrivant le disposant concerné</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="immatriculation" type="ta:Numerique9" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Numéro siren ? donnée à récupérer dans personne morale / Immatriculation</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ape" type="ta:Code_Ape_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code APE (3 chiffres et 1 lettre)</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Situation_Pvi_Type">
		<xs:annotation>
			<xs:documentation>Cette partie permet de décliner la situation de chaque couple cédant / immeuble de l'acte de vente eu égard à sa situation par rapport à la déclaration de PVI (déclaration déposée ou non). Elle est obligatoirement présente dans la partie PVI annexée à l'acte de vente</xs:documentation>
			<xs:documentation source="S. LANDEAU, le 09/03/2007" xml:lang="fr">Le contenu de la mention légale a été basculée en séquence / élément, car le volume de données et de 300 caractères, il est difficilement envisageable de gérer cette information en attribut?</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="identifiant-personne" type="ta:Identifiant_Type">
				<xs:annotation>
					<xs:documentation>Lien avec une personne "disposant" de l'acte de vente telle que définie dans les données structurées de l'acte de vente.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="identifiant-immeuble" type="ta:Identifiant_Type">
				<xs:annotation>
					<xs:documentation>Lien avec un immeuble de l'acte de vente</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="identifiant-declaration" type="ta:Numerique3" minOccurs="0"/>
			<xs:element name="groupe-PVI">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="declaration-PVI" type="xs:boolean"/>
						<xs:element name="mention-legale-PVI" minOccurs="0">
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:minLength value="1"/>
									<xs:maxLength value="300"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="groupe-TFCTC">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="declaration-TFCTC" type="xs:boolean"/>
						<xs:element name="mention-legale-TFCTC" minOccurs="0">
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:minLength value="1"/>
									<xs:maxLength value="300"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="groupe-TCTNRC">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="declaration-TCTNRC" type="xs:boolean"/>
						<xs:element name="mention-legale-TCTNRC" minOccurs="0">
							<xs:simpleType>
								<xs:restriction base="xs:string">
									<xs:minLength value="1"/>
									<xs:maxLength value="300"/>
								</xs:restriction>
							</xs:simpleType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:attributeGroup name="Situation_Pvi_Type.att">
		<xs:attribute name="identifiant-personne" type="ta:Identifiant_Type" use="required">
			<xs:annotation>
				<xs:documentation>Lien avec une personne "disposant" de l'acte de vente telle que définie dans les données structurées de l'acte de vente</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="identifiant-immeuble" type="ta:Identifiant_Type" use="required">
			<xs:annotation>
				<xs:documentation>Lien avec un immeuble de l'acte de vente</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:complexType name="Superficie_Cadastre_Type">
		<xs:sequence>
			<xs:element name="volume" type="ta:Volume_Teleactes_Type" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Volume et références cadastrales</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="lot" type="ta:Lot_Copropriete_Type" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>lots RCP</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Synthese_2048_IMM_Type">
		<xs:sequence>
			<xs:element name="situation-PVI-TFCTC-TCTNRC" type="ta:Situation_Pvi_Type" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Cette partie permet de décliner la situation de chaque couple cédant/immeuble de l'acte de vente eu égard à sa situation par rapport à la déclaration de PVI (déclaration déposée ou non). Elle est obligatoirement présente dans la partie PVI annexée à l'acte de vente</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Transfert_Propriete_Type">
		<xs:annotation>
			<xs:documentation>Renseignements relatif à la cession</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="nature" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Nature du transfert de propriété</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="60"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="date" type="xs:date">
				<xs:annotation>
					<xs:documentation>Date du transfert de propriété =&gt; données à récupérer dans Acte de vente / Date de l'acte</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
