<?xml version="1.0" encoding="UTF-8"?>
<!-- Version 1.0 du 01/07/2021 - Ajout des composants spécifiques flux V2019 -->
<!-- Version 1.1 du 07/07/2021 -Migration des types d'actions dans composantq_generiques_2019 -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://label.flux.teleactes.copernic.finances.gouv.fr/v2009" xmlns:composants="http://teleactes.real.not/v2009" xmlns:composants2019="http://teleactes.real.not/v2009" targetNamespace="http://label.flux.teleactes.copernic.finances.gouv.fr/v2009" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.1">
	<xs:annotation>
		<xs:documentation source="Composant LABEL pour les  flux V2019" xml:lang="fr">Créé par L.BAILLET - ADSN.
		</xs:documentation>
	</xs:annotation>
	<xs:import namespace="http://teleactes.real.not/v2009" schemaLocation="composants_generiques_v2009.xsd"/>
	<xs:import namespace="http://teleactes.real.not/v2009" schemaLocation="composants_generiques_v2019.xsd"/>
	<xs:include schemaLocation="label_v2009.xsd"/>
	<xs:element name="label_2019" type="Label_2019_Type">
		<xs:annotation>
			<xs:documentation>Label V 1.0</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="Label_2019_Type">
		<xs:annotation>
			<xs:documentation>Type Label</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="code-systeme-emetteur" type="Code_Systeme_Emetteur_Type">
				<xs:annotation>
					<xs:documentation>Identifie le système émetteur</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code-type-usager" type="Code_Type_Usager_Type">
				<xs:annotation>
					<xs:documentation>Code du type de l'usager</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reference-externe-usager">
				<xs:annotation>
					<xs:documentation>Référence unique d'un usager</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="10"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="code-sages" type="composants:Code_CH_Type">
				<xs:annotation>
					<xs:documentation>Code sages de la CH</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reference-externe-dossier" type="Reference_Externe_Dossier_Type">
				<xs:annotation>
					<xs:documentation>N° dossier attribué par le logiciel du notaire</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code-type-dossier" type="Code_Type_Dossier_2019_Type">
				<xs:annotation>
					<xs:documentation>Code du type de dossier</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reference-dossier-systeme" type="Reference_Dossier_Systeme_Type">
				<xs:annotation>
					<xs:documentation>Référence (identifiant unique) du document pour l'émetteur.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="intitule-dossier" type="composants:XSString1-30"/>
			<xs:element name="code-type-flux">
				<xs:annotation>
					<xs:documentation>Code du type de flux du label</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="Code_Type_Flux_Type"/>
				</xs:simpleType>
			</xs:element>
			<xs:element name="reference-dossier" type="Num12_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Identifiant unique du dossier dans Télé@ctes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code-type-action" type="composants2019:Code_Type_Action_2019_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code type action</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="numero-action" type="Numerique2_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Numero Sequenciel action CH</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reference-action" type="composants:XSString1-30" minOccurs="0">
				<xs:annotation>
					<xs:documentation>réference usager de l'action CH</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="horodatage" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>Date et heure de constitution du flux</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ================================================================================ -->
	<!-- 	Simple Type : STRING                                                                  						-->
	<!-- ================================================================================ -->
	<xs:simpleType name="Code_Type_Dossier_2019_Type">
		<xs:annotation>
			<xs:documentation>Code du type dossier</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="W">
				<xs:annotation>
					<xs:documentation>Radiation 2019 </xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="S">
				<xs:annotation>
					<xs:documentation>Serviture 2019</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="B">
				<xs:annotation>
					<xs:documentation>Rgularisation ACTR 2019</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
