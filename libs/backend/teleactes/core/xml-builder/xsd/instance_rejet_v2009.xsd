<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSPY v2004 rel. 3 U (http://www.xmlspy.com) by <PERSON><PERSON><PERSON> (DIRECTION GENERALE DES IMPOTS) -->
<xs:schema xmlns:composants="http://teleactes.real.not/v2009" xmlns="http://instance_rejet.flux.teleactes.copernic.finances.gouv.fr/v2009" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://instance_rejet.flux.teleactes.copernic.finances.gouv.fr/v2009" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:annotation>
		<xs:documentation><![CDATA[Instance-rejet 1.11 (2017-10-03) Ajout du Sages dans les références de dépôt et d'enliassement]]></xs:documentation>
		<xs:documentation><![CDATA[Instance-rejet 1.8(2013-03-13)Ajout code VTAB]]></xs:documentation>
		<xs:documentation><![CDATA[Instance-rejet 1.9 (2016-03-24) Passage de la balise destinataire à 60 car]]></xs:documentation>
		<xs:documentation><![CDATA[Instance-rejet 1.10 (2017-01-25) Ajout code VTVA]]></xs:documentation>
		<xs:documentation><![CDATA[Instance-rejet 1.11 (2021-10-14) Création type pour Instance_Rejet_Type.att et V2019]]></xs:documentation>
		<xs:documentation><![CDATA[Instance-rejet 1.12 (2022-07-19) Ajout code nature HLES]]></xs:documentation>
	</xs:annotation>
	<xs:import namespace="http://teleactes.real.not/v2009" schemaLocation="composants_generiques_v2009.xsd"/>
	<xs:element name="instance-rejet" type="Instance_Rejet_Type">
		<xs:annotation>
			<xs:documentation>Version 1.12 (2022-07-19)</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="Instance_Rejet_Type">
		<xs:sequence>
			<xs:element name="formalite" type="Formalite_Type"/>
			<xs:element name="notification" type="Notification_Type"/>
		</xs:sequence>
		<xs:attributeGroup ref="Instance_Rejet_Type.att"/>
	</xs:complexType>
	<xs:complexType name="Formalite_Type">
		<xs:sequence>
			<xs:element name="nature-formalite">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="VENT"/>
						<xs:enumeration value="VIMR"/>
						<xs:enumeration value="VHAB"/>
						<xs:enumeration value="HCON"/>
						<xs:enumeration value="HCOR"/>
						<xs:enumeration value="PRPD"/>
						<xs:enumeration value="PVEN"/>
						<xs:enumeration value="AVHC"/>
						<xs:enumeration value="AVPD"/>
						<xs:enumeration value="RINS"/>
						<xs:enumeration value="PRIV"/>
						<xs:enumeration value="VEFA"/>
						<xs:enumeration value="ATTE"/>
						<xs:enumeration value="ATTR"/>
						<xs:enumeration value="ACTR"/>
						<xs:enumeration value="BORR"/>
						<xs:enumeration value="PRDI"/>
						<xs:enumeration value="SERV"/>
						<xs:enumeration value="VTAB"/>
						<xs:enumeration value="VTVA"/>
						<xs:enumeration value="HLES"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="reference-depot" type="Reference_Depot_Type"/>
			<xs:element name="date-depot" type="Date_Depot_Type"/>
			<xs:element name="reference-publication" type="Reference_Publication_Type"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Reference_Depot_Type">
		<xs:annotation>
			<xs:documentation>Type réference pour un depot: identifiant FIDJI</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="code-sages" type="composants:Code_Sages_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code Sages du service où la pièce a été enregistrée</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="annee" type="composants:Num4">
				<xs:annotation>
					<xs:documentation>Annee de la reference</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code">
				<xs:annotation>
					<xs:documentation>Code D pour un depot</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="D"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="sequence" type="composants:Numerique10">
				<xs:annotation>
					<xs:documentation>Sequence de la reference</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Reference_Publication_Type">
		<xs:sequence>
			<xs:element name="code-sages" type="composants:Code_Sages_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code Sages du service où la pièce a été enregistrée</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="annee" type="composants:Num4"/>
			<xs:element name="lettre-enliassement">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="D"/>
						<xs:enumeration value="P"/>
						<xs:enumeration value="V"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="sequence" type="composants:Numerique10">
				<xs:annotation>
					<xs:documentation>Sequence de la reference</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Date_Depot_Type">
		<xs:sequence>
			<xs:element name="lib-date-depot" type="composants:String-1-300"/>
			<xs:element name="date-du-depot" type="xs:date"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Notification_Type">
		<xs:sequence>
			<xs:element name="destinataire" type="composants:XSString1-60"/>
			<xs:element name="date-envoi" type="xs:date"/>
			<xs:element name="causes" maxOccurs="unbounded">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="100"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="indicateur-rejet">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="P"/>
						<xs:enumeration value="T"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:attributeGroup name="Instance_Rejet_Type.att">
		<xs:attribute name="type-reponse-ch" type="Mention_MR_Type" use="required"/>
	</xs:attributeGroup>
	<xs:simpleType name="Mention_MR_Type">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MR"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
