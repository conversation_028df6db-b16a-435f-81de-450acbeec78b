<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSPY v2004 rel. 3 U (http://www.xmlspy.com) by s<PERSON><PERSON> (DIRECTION GENERALE DES IMPOTS) -->
<xs:schema targetNamespace="http://impots_taxes_2048_imm.flux.teleactes.copernic.finances.gouv.fr/v2009" xmlns="http://impots_taxes_2048_imm.flux.teleactes.copernic.finances.gouv.fr/v2009" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:composants="http://teleactes.real.not/v2009" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:annotation>
		<xs:documentation><![CDATA[Impots et Taxe Forfaitaire PVI cession Immeuble  Version 1.5 (2017-10-03) Ajout du Sages dans les références de dépôt et d'enliassement]]></xs:documentation>
		<xs:documentation><![CDATA[Impots et Taxe Forfaitaire PVI cession Immeuble  Version 1.4 (2009-10-16) Y SAMOUILLAN]]></xs:documentation>
	</xs:annotation>
	<xs:import namespace="http://teleactes.real.not/v2009" schemaLocation="composants_generiques_v2009.xsd"/>
	<xs:element name="impots-taxes-2048-imm" type="Impots_Taxes_2048_Imm_Type">
		<xs:annotation>
			<xs:documentation>Impots et taxe fordfaitaire Version 1.5 (2017-10-03) </xs:documentation>
		</xs:annotation>
	</xs:element>
	<!-- ================================================================================ -->
	<!-- 	Complex Type :                                                                      						-->
	<!-- ================================================================================ -->
	<xs:complexType name="Detail_Droits_Type">
		<xs:sequence>
			<xs:element name="base-imposition" type="composants:Decimal11-2"/>
			<xs:element name="taux" type="Decimal2-2"/>
			<xs:element name="montant-par-taux" type="composants:Decimal11-2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Droits_Type">
		<xs:sequence>
			<xs:element name="detail-droits" type="Detail_Droits_Type" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="montant-total-droits" type="composants:Decimal11-2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Impots_Taxes_2048_Imm_Type">
		<xs:sequence>
			<xs:element name="information-formalite" type="Information_Formalite_Type"/>
			<xs:element name="reference-regularisation" type="Reference_Regularisation_Type" minOccurs="0"/>
			<xs:element name="information-droits-2048-imm" type="Information_Droits_2048_Imm_Type"/>
			<xs:element name="information-non-taxation" type="Information_Non_Taxation_Type" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="Impots_Taxes_2048_Imm_Type.att"/>
	</xs:complexType>
	<xs:complexType name="Information_Droits_2048_Imm_Type">
		<xs:sequence>
			<xs:element name="droits" type="Droits_Type"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Information_Formalite_Type">
		<xs:sequence>
			<xs:element name="date-depot" type="xs:date">
				<xs:annotation>
					<xs:documentation>Date de dépôt de la formalité</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="lieu-depot" type="Lieu_Depot_Type">
				<xs:annotation>
					<xs:documentation>Lieu de dépôt de la formalité</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reference-depot" type="Reference_Depot_Type">
				<xs:annotation>
					<xs:documentation>Type réference pour un depot: identifiant FIDJI</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reference-enliassement" type="Reference_Enliassement_Type">
				<xs:annotation>
					<xs:documentation>Type réference pour un enliassement: identifiant FIDJI</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Lieu_Depot_Type">
		<xs:annotation>
			<xs:documentation>Lieu de dépôt</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="nom-conservation" type="composants:String1-30"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Reference_Decision_Type">
		<xs:sequence>
			<xs:element name="code-sages" type="composants:Code_Sages_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code Sages du service où la pièce a été enregistrée</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="annee" type="composants:Num4">
				<xs:annotation>
					<xs:documentation>Année de la référence</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code">
				<xs:annotation>
					<xs:documentation>Code de la référence : D ou P</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="D"/>
						<xs:enumeration value="P"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="sequence" type="composants:Numerique10">
				<xs:annotation>
					<xs:documentation>Sequence de la reference</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Reference_Depot_Type">
		<xs:annotation>
			<xs:documentation>Type réference pour un depot: identifiant FIDJI</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="code-sages" type="composants:Code_Sages_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code Sages du service où la pièce a été enregistrée</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="annee" type="composants:Num4">
				<xs:annotation>
					<xs:documentation>Annee de la reference</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code">
				<xs:annotation>
					<xs:documentation>Code D pour un depot</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="D"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="sequence" type="composants:Numerique10">
				<xs:annotation>
					<xs:documentation>Sequence de la reference</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Reference_Enliassement_Type">
		<xs:annotation>
			<xs:documentation>Type réference pour un enliassement: identifiant FIDJI</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="code-sages" type="composants:Code_Sages_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code Sages du service où la pièce a été enregistrée</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="annee" type="composants:Num4">
				<xs:annotation>
					<xs:documentation>Annee de la reference</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code">
				<xs:annotation>
					<xs:documentation>P pour un acte de vente </xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="1"/>
						<xs:enumeration value="P"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="sequence" type="composants:Numerique10">
				<xs:annotation>
					<xs:documentation>Sequence de la reference</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Reference_Regularisation_Type">
		<xs:sequence>
			<xs:element name="nature">
				<xs:annotation>
					<xs:documentation>Nature de formalité de rejet ou de régularisation</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="ACTR"/>
						<xs:enumeration value="ATTR"/>
						<xs:enumeration value="REJI"/>
						<xs:enumeration value="REPO"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="lib-regul-rejet" type="String46_Type"/>
			<xs:element name="date-regul-rejet" type="xs:date"/>
			<xs:element name="reference-decision" type="Reference_Decision_Type"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Information_Non_Taxation_Type">
		<xs:sequence>
			<xs:element name="reference-externe-pvi" type="composants:Numerique3" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Numéro séquentiel de référence du dépôt  de la PVI dans le dossier Acte de Vente</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Reference_Type" abstract="true">
		<xs:annotation>
			<xs:documentation>Type réference : identifiant FIDJI</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="annee" type="composants:Num4">
				<xs:annotation>
					<xs:documentation>Annee de la reference</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code">
				<xs:annotation>
					<xs:documentation>Code FIDJI (U,D,P)</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="U"/>
						<xs:enumeration value="D"/>
						<xs:enumeration value="P"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="sequence" type="composants:Numerique5">
				<xs:annotation>
					<xs:documentation>Sequence de la reference</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:attributeGroup name="Impots_Taxes_2048_Imm_Type.att">
		<xs:attribute name="type-reponse-ch" use="required">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="MI"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<!-- ================================================================================ -->
	<!-- 	Simple Type : STRING                                                                  						-->
	<!-- ================================================================================ -->
	<xs:simpleType name="Decimal2-2">
		<xs:annotation>
			<xs:documentation>montant numérique avec 2 entiers, un point décimal et 2 décimales. </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:decimal">
			<xs:pattern value="[0-9]{1,2}\.[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String46_Type">
		<xs:restriction base="xs:string">
			<xs:maxLength value="46"/>
			<xs:minLength value="1"/>
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value=" ?[A-Za-z0-9&apos;@&amp;\.!&quot;#$\(\)\*,-/:;&gt;&lt;=\?\[\\\]^_\{\}\|£§¨°²~\+%€µâäàáãåèéêëìíîïôöòóõùúûüýçñÀÁÂÃÄÅÈÉÊËÌÍÎÏÒÓÔÕÖÙÚÛÜÝÇÑ]+[ A-Za-z0-9&apos;@&amp;\.!&quot;#$\(\)\*,-/:;&gt;&lt;=\?\[\\\]^_\{\}\|£§¨°²~\+%€µâäàáãåèéêëìíîïôöòóõùúûüýçñÿÀÁÂÃÄÅÈÉÊËÌÍÎÏÒÓÔÕÖÙÚÛÜÝÇÑŸ]*"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- ================================================================================ -->
	<!-- 	Simple Type : NUMERIQUE ET DECIMAL                                            						-->
	<!-- ================================================================================ -->
</xs:schema>
