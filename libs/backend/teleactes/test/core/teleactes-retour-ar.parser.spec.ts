import { TeleactesRetourARParser } from '@mynotary/backend/teleactes/core';

describe(TeleactesRetourARParser.name, () => {
  it('should parse an AR with only one anomaly', async () => {
    const parser = new TeleactesRetourARParser();
    const result = parser.parse(
      `<?xml version="1.0" encoding="utf-8"?> <flux xmlns="http://flux.teleactes.copernic.finances.gouv.fr/v2009"> <label xmlns="http://label.flux.teleactes.copernic.finances.gouv.fr/v2009"> <code-systeme-emetteur>P</code-systeme-emetteur> <code-type-usager>N</code-type-usager> <reference-externe-usager>099031</reference-externe-usager> <code-sages>7604P01</code-sages> <reference-externe-dossier>16547843</reference-externe-dossier> <code-type-dossier>H</code-type-dossier> <reference-dossier-systeme>099031PLYS2024060315553025H1</reference-dossier-systeme> <intitule-dossier>N° mandat / Adresse / C / Nom</intitule-dossier> <code-type-flux>RI</code-type-flux> <reference-dossier>012345678912</reference-dossier> <horodatage>2010-06-13T11:33:10.624+01:00</horodatage> </label> <ar-doss-inexp xsi:schemaLocation="http://ar_integration.flux.teleactes.copernic.finances.gouv.fr/v2009 ar_integration_v2009.xsd" xmlns="http://ar_integration.flux.teleactes.copernic.finances.gouv.fr/v2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><ano>Ano unique pour les gouverner tous</ano></ar-doss-inexp> </flux>`
    );
    expect(result).toBeDefined();
    expect(result).toEqual(['Ano unique pour les gouverner tous']);
  });

  it('should parse an AR with multiple anomalies', async () => {
    const parser = new TeleactesRetourARParser();
    const result = parser.parse(
      `<?xml version="1.0" encoding="utf-8"?> <flux xmlns="http://flux.teleactes.copernic.finances.gouv.fr/v2009"> <label xmlns="http://label.flux.teleactes.copernic.finances.gouv.fr/v2009"> <code-systeme-emetteur>P</code-systeme-emetteur> <code-type-usager>N</code-type-usager> <reference-externe-usager>099031</reference-externe-usager> <code-sages>7604P01</code-sages> <reference-externe-dossier>16547843</reference-externe-dossier> <code-type-dossier>H</code-type-dossier> <reference-dossier-systeme>099031PLYS2024060315553025H1</reference-dossier-systeme> <intitule-dossier>N° mandat / Adresse / C / Nom</intitule-dossier> <code-type-flux>RI</code-type-flux> <reference-dossier>012345678912</reference-dossier> <horodatage>2010-06-13T11:33:10.624+01:00</horodatage> </label> <ar-doss-inexp xsi:schemaLocation="http://ar_integration.flux.teleactes.copernic.finances.gouv.fr/v2009 ar_integration_v2009.xsd" xmlns="http://ar_integration.flux.teleactes.copernic.finances.gouv.fr/v2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><ano>RGANF0001 ça marche pas</ano><ano>RGANF0002 ça marche vraiment pas</ano></ar-doss-inexp> </flux>`
    );
    expect(result).toBeDefined();
    expect(result.length).toBe(2);
  });
});
