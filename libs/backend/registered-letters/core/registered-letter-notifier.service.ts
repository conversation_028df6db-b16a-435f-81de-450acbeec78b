import { Injectable } from '@nestjs/common';
import { RegisteredLetter, RegisteredLetterBatch } from './registered-letters';

import { EmailsApiService, EmailTemplateId, RegisteredLetterEmail } from '@mynotary/backend/emails/api';
import { UsersApiService } from '@mynotary/backend/users/api';
import { RegisteredLetterStatus } from '@mynotary/crossplatform/registered-letters/api';
import { LetterNotificationType, NotificationApiService } from '@mynotary/backend/notifications/api';
import { LegalsApiService } from '@mynotary/backend/legals/api';
import { EnvService } from '@mynotary/backend/secrets/api';
import { getRegisteredLetterBatchApplicationUrl } from './registered-letters.utils';

@Injectable()
export class RegisteredLetterNotifierService {
  public constructor(
    private emailsService: EmailsApiService,
    private userService: UsersApiService,
    private notificationApiService: NotificationApiService,
    private legalsApiService: LegalsApiService,
    private envService: EnvService
  ) {}

  async notifyRegisteredLetterUpdate({ updatedBatch, updatedLetter }: NotifyRegisteredLetterUpdateArgs): Promise<void> {
    const emailData = await this.getEmailData({ batch: updatedBatch, registeredLetter: updatedLetter });

    if (emailData != null) {
      const user = await this.userService.findUser({ id: updatedBatch.creatorId, type: 'by-id' });

      if (user != null) {
        await this.emailsService.sendEmail({ data: emailData, receiver: user.email });
      }
    }

    await this.createLetterNotification({ batch: updatedBatch, updatedLetter });
  }

  private async getEmailData({
    batch,
    registeredLetter
  }: {
    batch: RegisteredLetterBatch;
    registeredLetter: RegisteredLetter;
  }): Promise<RegisteredLetterEmail | null> {
    const operationLabel = await this.legalsApiService.getOperationLabel(batch.operationId);
    const commonFields = {
      contractId: batch.contractId,
      operationId: batch.operationId,
      operationLabel,
      organizationId: batch.organizationId,
      receiver: {
        civility: registeredLetter.receiver.civility,
        firstname: registeredLetter.receiver.firstname,
        lastname: registeredLetter.receiver.lastname
      },
      sender: {
        email: batch.creatorEmail,
        firstname: batch.creatorFirstname,
        lastname: batch.creatorLastname
      },
      url: getRegisteredLetterBatchApplicationUrl({
        appUrl: this.envService.url.mnAppUrl,
        operationId: batch.operationId,
        registeredLetterBatchId: batch.id
      })
    };

    switch (registeredLetter.status) {
      case RegisteredLetterStatus.ACCEPTED:
        return {
          ...commonFields,
          date: registeredLetter.acceptationTime,
          templateId: EmailTemplateId.REGISTER_LETTER_ACCEPTED
        };
      case RegisteredLetterStatus.AWAITING_RECIPIENT_UPDATE:
        return {
          ...commonFields,
          errorMessage:
            "Identité du destinataire (votre client s'est enregistré avec un prénom/nom différent de celui indiqué par vos soins)",
          templateId: EmailTemplateId.REGISTER_LETTER_ERROR
        };
      case RegisteredLetterStatus.BOUNCED:
        return {
          ...commonFields,
          errorMessage: 'Mail erroné',
          templateId: EmailTemplateId.REGISTER_LETTER_ERROR
        };
      case RegisteredLetterStatus.REFUSED:
        return {
          ...commonFields,
          date: registeredLetter.refusalTime,
          templateId: EmailTemplateId.REGISTER_LETTER_REFUSED
        };
      case RegisteredLetterStatus.EXPIRED:
        return {
          ...commonFields,
          date: registeredLetter.negligenceTime,
          templateId: EmailTemplateId.REGISTER_LETTER_NEGLIGENCE
        };
      default:
        return null;
    }
  }

  private async createLetterNotification({
    batch,
    updatedLetter
  }: {
    batch: RegisteredLetterBatch;
    updatedLetter: RegisteredLetter;
  }) {
    const notificationData = {
      batchId: batch.id,
      contractId: batch.contractId,
      creatorId: batch.creatorId,
      creatorName: `${batch.creatorFirstname} ${batch.creatorLastname}`,
      letterId: updatedLetter.id,
      operationId: batch.operationId,
      receiver: `${updatedLetter.receiver.firstname} ${updatedLetter.receiver.lastname}`,
      userIds: [batch.creatorId]
    };

    switch (updatedLetter.status) {
      case RegisteredLetterStatus.ACCEPTED:
        await this.notificationApiService.createNotification({
          ...notificationData,
          acceptationTime: updatedLetter.acceptationTime,
          type: LetterNotificationType.LETTER_ACCEPTED
        });
        break;
      case RegisteredLetterStatus.REFUSED:
        await this.notificationApiService.createNotification({
          ...notificationData,
          refusalTime: updatedLetter.refusalTime,
          type: LetterNotificationType.LETTER_REFUSED
        });
        break;
      case RegisteredLetterStatus.EXPIRED:
        await this.notificationApiService.createNotification({
          ...notificationData,
          negligenceTime: updatedLetter.negligenceTime,
          type: LetterNotificationType.LETTER_EXPIRED
        });
        break;
      case RegisteredLetterStatus.ERROR:
      case RegisteredLetterStatus.AWAITING_RECIPIENT_UPDATE:
      case RegisteredLetterStatus.BOUNCED:
      case RegisteredLetterStatus.INVALID_EMAIL:
        await this.notificationApiService.createNotification({
          ...notificationData,
          type: LetterNotificationType.LETTER_ERROR
        });
        break;
      case RegisteredLetterStatus.SENT:
        await this.notificationApiService.createNotification({
          ...notificationData,
          depositTime: updatedLetter.depositTime,
          type: LetterNotificationType.LETTER_SENT
        });
        break;
    }
  }
}

interface NotifyRegisteredLetterUpdateArgs {
  updatedBatch: RegisteredLetterBatch;
  updatedLetter: RegisteredLetter;
}
