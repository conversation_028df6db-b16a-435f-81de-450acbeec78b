import { Injectable } from '@nestjs/common';
import { Prisma, PrismaService } from '@mynotary/backend/shared/prisma-infra';
import {
  CustomView,
  CustomViewsRepository,
  CustomViewsFiltering,
  CustomViewsType,
  FavoriteCustomView,
  CustomViewNew,
  FilterType
} from '@mynotary/backend/custom-views/core';
import { JSONValue } from '@mynotary/crossplatform/shared/util';

@Injectable()
export class CustomViewsRepositoryImpl implements CustomViewsRepository {
  constructor(private prisma: PrismaService) {}

  async findFavoriteViews(userId: string): Promise<FavoriteCustomView[]> {
    const favoriteCustomViews = await this.prisma.custom_view_favorite.findMany({
      where: {
        user_id: parseInt(userId)
      }
    });

    return favoriteCustomViews.map((view) => ({
      customViewId: view.custom_view_id.toString()
    }));
  }

  async findCustomViews(filtering: CustomViewsFiltering): Promise<CustomView[]> {
    let customViews = [];

    const userId = parseInt(filtering.userId);
    const organizationId = parseInt(filtering?.organizationId ?? '');

    if (organizationId) {
      /**
       * Find custom view shared with every members
       */
      customViews = await this.prisma.custom_view.findMany({
        where: {
          OR: [
            {
              creator_user_id: userId,
              organization_id: organizationId
            },
            {
              is_public: true,
              organization_id: organizationId
            }
          ],
          ...(filtering?.type ? { type: filtering.type } : {})
        }
      });
    } else {
      customViews = await this.prisma.custom_view.findMany({
        where: {
          creator_user_id: userId,
          ...(filtering?.type ? { type: filtering.type } : {})
        }
      });
    }

    return customViews.map((view) => convertToCore(view));
  }

  async createCustomView(customViewNew: CustomViewNew): Promise<CustomView> {
    const customViewCreated = await this.prisma.custom_view.create({
      data: {
        creator_user_id: customViewNew.creatorUserId == null ? null : parseInt(customViewNew.creatorUserId),
        filters: createDefaultViewsDatas(customViewNew) as JSONValue,
        is_default: customViewNew.isDefault,
        is_public: customViewNew.isPublic,
        label: customViewNew.label,
        organization_id: parseInt(customViewNew.organizationId),
        type: customViewNew.type
      }
    });

    return convertToCore(customViewCreated);
  }
}

interface DbCustomView {
  creator_user_id: number | null;
  filters: Prisma.JsonValue;
  id: number;
  is_default: boolean;
  is_public: boolean;
  label: string;
  organization_id: number;
  type: string;
}

function convertToCore(view: DbCustomView): CustomView {
  return {
    creatorUserId: view?.creator_user_id?.toString(),
    filters: JSON.stringify(view.filters),
    id: view.id.toString(),
    isDefault: view?.is_default ?? undefined,
    isFavorite: false,
    isPublic: view?.is_public ?? undefined,
    label: view?.label ?? undefined,
    organizationId: view?.organization_id.toString(),
    type: (view?.type as CustomViewsType) ?? undefined
  };
}

function createDefaultViewsDatas(customViewNew: CustomViewNew): DbCustomViewFilters {
  const filters: DbCustomViewFilters = {};

  customViewNew.filters.forEach((filter) => {
    const dbFilter = DbValues[filter.id];
    filters[dbFilter.id] = dbFilter;
  });

  return filters;
}

interface DbValue {
  id: string;
  label: string;
  selected: boolean;
}

interface DbValues {
  [key: string]: DbValue;
}

interface DbFilter {
  id: string;
  label: string;
  values: DbValues;
}

interface DbCustomViewFilters {
  [key: string]: DbFilter;
}

const DbValues = {
  [FilterType.ARCHIVE_OPERATION]: {
    id: 'ARCHIVE_OPERATION',
    label: 'État',
    values: { ACTIVE: { id: 'ARCHIVED', label: 'Archivé', selected: true } }
  },
  [FilterType.LAST_ACCESS]: {
    id: 'LAST_ACCESS',
    label: 'Accès',
    values: { MONTH: { id: 'MONTH', label: '30 derniers jours', selected: true } }
  }
};
