import { Injectable } from '@nestjs/common';
import { RegistersApiService, RegisterEntryStatus } from '@mynotary/backend/registers/api';
import { Signature, SignatureActive } from './signatures';
import { fns } from '@mynotary/crossplatform/shared/dates-util';

@Injectable()
export class SignatureRegisterUpdaterService {
  constructor(private registersApiService: RegistersApiService) {}

  async activateRegisterEntry(signature: SignatureActive): Promise<void> {
    await this.updateRegisterEntry({
      date: signature.creationTime,
      observationPrefix: 'Date de lancement de la signature du mandat',
      signature
    });
  }

  async completeRegisterEntry(signature: SignatureActive): Promise<void> {
    await this.updateRegisterEntry({
      date: new Date().toISOString(),
      observationPrefix: 'Date de signature du mandat',
      signature,
      status: RegisterEntryStatus.VALIDATED
    });
  }

  async cancelRegisterEntry(signature: Signature): Promise<void> {
    await this.updateRegisterEntry({
      date: new Date().toISOString(),
      observationPrefix: "Date d'annulation de la signature du mandat",
      signature
    });
  }

  private async updateRegisterEntry({
    date,
    observationPrefix,
    signature,
    status
  }: UpdateRegisterEntryArgs): Promise<void> {
    const registerEntry = await this.registersApiService.findRegisterEntry({
      contractId: signature.contractId,
      status: RegisterEntryStatus.RESERVED
    });

    if (registerEntry == null) {
      return;
    }

    const formattedDate = fns.format(new Date(date ?? ''), 'dd/MM/yyyy');
    const newObservations = `${registerEntry.observations || ''}\n${observationPrefix} : ${formattedDate}\n`;

    await this.registersApiService.updateRegisterEntry({
      id: registerEntry.id,
      observations: newObservations,
      ...(status && { status })
    });
  }
}

interface UpdateRegisterEntryArgs {
  date: string;
  observationPrefix: string;
  signature: Signature;
  status?: RegisterEntryStatus;
}
