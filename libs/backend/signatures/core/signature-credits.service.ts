import { Injectable } from '@nestjs/common';
import { SignatureActive } from './signatures';
import { SignatureMethod, SignatureType } from '@mynotary/crossplatform/signatures/api';
import { filter } from 'lodash';
import { BillingsApiService } from '@mynotary/backend/billings/api';
import { LegalsApiService } from '@mynotary/backend/legals/api';
import { SignaturesRepository } from './signatures.repository';
import { CreditPackType } from '@mynotary/crossplatform/billings/api';
import { NotFoundError } from '@mynotary/crossplatform/shared/util';
import { CreditHistoryType } from '@mynotary/crossplatform/billings/api';

@Injectable()
export class SignatureCreditsService {
  constructor(
    private signatureRepository: SignaturesRepository,
    private billingsApiService: BillingsApiService,
    private legalsApiService: LegalsApiService
  ) {}

  public async getSignatureCredit({
    operationId,
    signatureLevel
  }: {
    operationId: string;
    signatureLevel: SignatureMethod;
  }) {
    const { organizationId } = await this.signatureRepository.getBillableOrganization(operationId);
    const credits = await this.billingsApiService.getCredits(organizationId);

    const creditsType = convertSignatureLevelToCredits(signatureLevel);

    const currentCredit = credits.find((credit) => credit.type === creditsType);

    if (currentCredit == null) {
      throw new NotFoundError({ id: creditsType, resource: 'credits' });
    }

    return currentCredit;
  }

  public async substractCredits(signature: SignatureActive) {
    if (signature.type !== SignatureType.ELECTRONIC) {
      return;
    }

    const currentCredit = await this.getSignatureCredit({
      operationId: signature.operationId,
      signatureLevel: signature.level
    });

    const label = await this.getCreditLabel(signature);

    const minusCredits = -signature.signatories.length;

    await this.billingsApiService.useCredits({
      defaultOrganizationId: signature.organizationId,
      diff: minusCredits,
      id: currentCredit.id,
      label,
      operationId: signature.operationId,
      userId: signature.creator.id
    });
  }

  public async refundUnusedCredits(signature: SignatureActive) {
    if (signature.type !== SignatureType.ELECTRONIC) {
      return;
    }

    const currentCredit = await this.getSignatureCredit({
      operationId: signature.operationId,
      signatureLevel: signature.level
    });

    const label = await this.getCreditLabel(signature);

    const refundCredits = filter(signature.signatories, (signatory) => signatory.signatureTime == null).length;

    await this.billingsApiService.useCredits({
      creditHistoryType: convertSignatureLevelToCancellationType(signature.level),
      defaultOrganizationId: signature.organizationId,
      diff: refundCredits,
      id: currentCredit.id,
      label,
      operationId: signature.operationId,
      userId: signature.creator.id
    });
  }

  public async getCreditLabel(signature: SignatureActive) {
    const operation = await this.legalsApiService.getOperation(signature.operationId);
    return `${operation.label} - ${signature.documentLabel}`;
  }
}

const convertSignatureLevelToCancellationType = (signatureLevel: SignatureMethod): CreditHistoryType => {
  switch (signatureLevel) {
    case SignatureMethod.SIMPLE:
      return CreditHistoryType.SIGNATURE_CREDIT_FROM_CANCELATION;
    case SignatureMethod.ADVANCED:
      return CreditHistoryType.ADVANCED_SIGNATURE_CREDIT_FROM_CANCELATION;
  }
};

const convertSignatureLevelToCredits = (signatureLevel: SignatureMethod): CreditPackType => {
  switch (signatureLevel) {
    case SignatureMethod.SIMPLE:
      return CreditPackType.PACK_SIGNATURE_CREDITS;
    case SignatureMethod.ADVANCED:
      return CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS;
  }
};
