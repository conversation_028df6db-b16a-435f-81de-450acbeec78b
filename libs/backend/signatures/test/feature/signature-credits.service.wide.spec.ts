import {
  SignatureMethod,
  SignaturePlace,
  SignatureProviderType,
  SignatureType
} from '@mynotary/crossplatform/signatures/api';
import { NotFoundError } from '@mynotary/crossplatform/shared/util';
import { provideSignaturesTest } from '../index';
import {
  SignatureActive,
  SignatureCreditsService,
  SignaturesRepository,
  SignatureStatus
} from '@mynotary/backend/signatures/core';
import { BillingsApiService, Credit } from '@mynotary/backend/billings/api';
import { CreditPackType } from '@mynotary/crossplatform/billings/api';
import { Operation, LegalsApiService } from '@mynotary/backend/legals/api';
import { createTestingWideApp } from '@mynotary/backend/shared/test';

describe(SignatureCreditsService.name, () => {
  describe('getSignatureCredit', () => {
    it('should return advanced credit for an signature with SignatureMethod.ADVANCED', async () => {
      const { billingsApiService, signatureCreditsService, signatureRepository } = await setup();

      const operationId = 'operationId';
      const signatureLevel = SignatureMethod.ADVANCED;
      const organizationId = 'organizationId';

      const credits: Credit[] = [
        { id: 'foo_id', organizationId, quantity: 10, type: CreditPackType.PACK_SIGNATURE_CREDITS },
        { id: 'foo_id_2', organizationId, quantity: 20, type: CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS }
      ];
      jest.spyOn(signatureRepository, 'getBillableOrganization').mockResolvedValue({ organizationId });
      jest.spyOn(billingsApiService, 'getCredits').mockResolvedValue(credits);

      const result = await signatureCreditsService.getSignatureCredit({ operationId, signatureLevel });

      expect(result).toEqual({
        id: 'foo_id_2',
        organizationId,
        quantity: 20,
        type: CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS
      });
    });

    it('should return basic credit for an signature with SignatureMethod.SIMPLE', async () => {
      const { billingsApiService, signatureCreditsService, signatureRepository } = await setup();

      const operationId = 'operationId';
      const signatureLevel = SignatureMethod.SIMPLE;
      const organizationId = 'organizationId';

      const credits: Credit[] = [
        { id: 'foo_id', organizationId, quantity: 10, type: CreditPackType.PACK_SIGNATURE_CREDITS },
        { id: 'foo_id_2', organizationId, quantity: 20, type: CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS }
      ];
      jest.spyOn(signatureRepository, 'getBillableOrganization').mockResolvedValue({ organizationId });
      jest.spyOn(billingsApiService, 'getCredits').mockResolvedValue(credits);

      const result = await signatureCreditsService.getSignatureCredit({ operationId, signatureLevel });

      expect(result).toEqual({
        id: 'foo_id',
        organizationId,
        quantity: 10,
        type: CreditPackType.PACK_SIGNATURE_CREDITS
      });
    });

    it('should throw a NOT FOUND error if the current credit is not found', async () => {
      const { billingsApiService, signatureCreditsService, signatureRepository } = await setup();

      const operationId = 'operationId';
      const signatureLevel = SignatureMethod.SIMPLE;
      const organizationId = 'organizationId';
      const credits = [
        { id: 'foo_id_2', organizationId, quantity: 20, type: CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS }
      ];

      jest.spyOn(signatureRepository, 'getBillableOrganization').mockResolvedValue({ organizationId });
      jest.spyOn(billingsApiService, 'getCredits').mockResolvedValue(credits);

      await expect(signatureCreditsService.getSignatureCredit({ operationId, signatureLevel })).rejects.toThrow(
        NotFoundError
      );
    });
  });

  describe('substractCredits', () => {
    it('should subtract credits for an electronic signature for each signatory', async () => {
      const { billingsApiService, legalsApiService, signatureCreditsService } = await setup();

      const organizationId = 'organizationId';
      const basicCredits = {
        id: 'foo_id_2',
        organizationId,
        quantity: 20,
        type: CreditPackType.PACK_SIGNATURE_CREDITS
      };
      jest.spyOn(signatureCreditsService, 'getSignatureCredit').mockResolvedValue(basicCredits);
      jest.spyOn(legalsApiService, 'getOperation').mockResolvedValue(mockOperation);
      jest.spyOn(billingsApiService, 'useCredits').mockResolvedValue();

      await signatureCreditsService.substractCredits(electronicSignature(organizationId));

      expect(signatureCreditsService.getSignatureCredit).toHaveBeenCalledWith({
        operationId: 'operationId',
        signatureLevel: SignatureMethod.SIMPLE
      });

      expect(billingsApiService.useCredits).toHaveBeenCalledWith({
        defaultOrganizationId: organizationId,
        diff: -2,
        id: 'foo_id_2',
        label: 'operationLabel - documentLabel',
        operationId: 'operationId',
        userId: 'userId'
      });
    });

    it('should not subtract credits for a non-electronic signature', async () => {
      const { billingsApiService, legalsApiService, signatureCreditsService } = await setup();

      jest.spyOn(signatureCreditsService, 'getSignatureCredit');
      jest.spyOn(legalsApiService, 'getOperation');
      jest.spyOn(billingsApiService, 'useCredits');

      await signatureCreditsService.substractCredits(paperSignature);

      expect(signatureCreditsService.getSignatureCredit).not.toHaveBeenCalled();
      expect(legalsApiService.getOperation).not.toHaveBeenCalled();
      expect(billingsApiService.useCredits).not.toHaveBeenCalled();
    });
  });

  describe('refundUnusedCredits', () => {
    it('should refund unused credits for an electronic signature', async () => {
      const { billingsApiService, legalsApiService, signatureCreditsService } = await setup();

      const organizationId = 'organizationId';
      const basicCredits = {
        id: 'foo_id_2',
        organizationId,
        quantity: 20,
        type: CreditPackType.PACK_SIGNATURE_CREDITS
      };

      jest.spyOn(signatureCreditsService, 'getSignatureCredit').mockResolvedValue(basicCredits);
      jest.spyOn(legalsApiService, 'getOperation').mockResolvedValue(mockOperation);
      jest.spyOn(billingsApiService, 'useCredits').mockResolvedValue();

      await signatureCreditsService.refundUnusedCredits(electronicSignature(organizationId));

      expect(signatureCreditsService.getSignatureCredit).toHaveBeenCalledWith({
        operationId: 'operationId',
        signatureLevel: SignatureMethod.SIMPLE
      });
      expect(billingsApiService.useCredits).toHaveBeenCalledWith({
        creditHistoryType: 'SIGNATURE_CREDIT_FROM_CANCELATION',
        defaultOrganizationId: organizationId,
        diff: 2,
        id: 'foo_id_2',
        label: 'operationLabel - documentLabel',
        operationId: 'operationId',
        userId: 'userId'
      });
    });

    it('should refund unused credits for an electronic signature only if signatory has not signed', async () => {
      const { billingsApiService, legalsApiService, signatureCreditsService } = await setup();

      const organizationId = 'organizationId';
      const basicCredits = {
        id: 'foo_id_2',
        organizationId,
        quantity: 20,
        type: CreditPackType.PACK_SIGNATURE_CREDITS
      };

      jest.spyOn(signatureCreditsService, 'getSignatureCredit').mockResolvedValue(basicCredits);
      jest.spyOn(legalsApiService, 'getOperation').mockResolvedValue(mockOperation);
      jest.spyOn(billingsApiService, 'useCredits').mockResolvedValue();

      const electronicSignatureWithSignatory = {
        ...electronicSignature(organizationId),
        signatories: [
          ...electronicSignature(organizationId).signatories,
          {
            consent: true,
            email: '<EMAIL>',
            firstname: 'Bob',
            id: 'signatory002',
            lastname: 'Johnson',
            order: 1,
            phone: '+123456789',
            signatureId: 'sig002',
            signaturePlace: SignaturePlace.ONLINE,
            signatureTime: '2024-06-05T12:00:00Z'
          }
        ]
      };

      await signatureCreditsService.refundUnusedCredits(electronicSignatureWithSignatory);

      expect(signatureCreditsService.getSignatureCredit).toHaveBeenCalledWith({
        operationId: 'operationId',
        signatureLevel: SignatureMethod.SIMPLE
      });
      expect(billingsApiService.useCredits).toHaveBeenCalledWith({
        creditHistoryType: 'SIGNATURE_CREDIT_FROM_CANCELATION',
        defaultOrganizationId: organizationId,
        diff: 2,
        id: 'foo_id_2',
        label: 'operationLabel - documentLabel',
        operationId: 'operationId',
        userId: 'userId'
      });
    });

    it('should not refund unused credits for a non-electronic signature', async () => {
      const { billingsApiService, legalsApiService, signatureCreditsService } = await setup();

      jest.spyOn(signatureCreditsService, 'getSignatureCredit');
      jest.spyOn(legalsApiService, 'getOperation');
      jest.spyOn(billingsApiService, 'useCredits');

      await signatureCreditsService.refundUnusedCredits(paperSignature);

      expect(signatureCreditsService.getSignatureCredit).not.toHaveBeenCalled();
      expect(legalsApiService.getOperation).not.toHaveBeenCalled();
      expect(billingsApiService.useCredits).not.toHaveBeenCalled();
    });
  });

  async function setup() {
    const { getService } = await createTestingWideApp({
      bypassAuth: true,
      providers: provideSignaturesTest()
    });

    const signatureCreditsService = getService(SignatureCreditsService);

    const signatureRepository = getService(SignaturesRepository);
    const billingsApiService = getService(BillingsApiService);
    const legalsApiService = getService(LegalsApiService);

    return { billingsApiService, legalsApiService, signatureCreditsService, signatureRepository };
  }
});

const fooCreator = {
  email: '<EMAIL>',
  firstname: 'Alice',
  id: 'userId',
  lastname: 'Brown',
  organizationAddress: '456 Elm St, City, Country',
  organizationName: 'Tech Corp',
  phone: '+987654321'
};

const electronicSignature: (organization?: string) => SignatureActive = (organization) => ({
  activatorUserId: 'activatorUserId',
  contractId: 'foo_contact',
  contractModelId: 'modelDEF',
  creationTime: '2024-06-05T12:00:00Z',
  creator: fooCreator,
  documentLabel: 'documentLabel',
  documentType: 'SERVICE_CONTRACT',
  documentTypeLabel: 'Contrat de prestation',
  files: [
    {
      isContract: true,
      order: 1,
      signatureId: 'sig002',
      toSignFileId: 'file002'
    }
  ],
  id: 'sig002',
  level: SignatureMethod.SIMPLE,
  operationId: 'operationId',
  operationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
  ordered: true,
  organizationId: organization ?? 'org002',
  providerId: 'provider002',
  providerType: SignatureProviderType.YOUSIGN_V3,
  shouldNotifyAfterSignature: true,
  signatories: [
    {
      consent: true,
      email: '<EMAIL>',
      firstname: 'Bob',
      id: 'signatory002',
      lastname: 'Johnson',
      order: 1,
      phone: '+123456789',
      signatureId: 'sig002',
      signaturePlace: SignaturePlace.ONLINE
    },
    {
      consent: true,
      email: '<EMAIL>',
      firstname: 'Jeanne',
      id: 'signatory002',
      lastname: 'Dark',
      order: 1,
      phone: '+123456789',
      signatureId: 'sig002',
      signaturePlace: SignaturePlace.ONLINE
    }
  ],
  status: SignatureStatus.SIGNED,
  subscribers: [],
  type: SignatureType.ELECTRONIC
});

const paperSignature: SignatureActive = {
  activatorUserId: 'user123',
  contractId: 'contract123',
  contractModelId: 'modelABC',
  creationTime: '2024-06-05T12:00:00Z',
  creator: fooCreator,
  documentLabel: 'Contrat de travail',
  documentType: 'EMPLOYMENT_CONTRACT',
  documentTypeLabel: 'Contrat de travail',
  files: [
    {
      isContract: true,
      order: 1,
      signatureId: 'sig001',
      toSignFileId: 'file001'
    }
  ],
  id: 'sig001',
  level: SignatureMethod.SIMPLE,
  operationId: 'operationId',
  operationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
  ordered: true,
  organizationId: 'org001',
  providerId: 'provider001',
  providerType: SignatureProviderType.MYNOTARY_PAPER,
  shouldNotifyAfterSignature: true,
  signatories: [
    {
      consent: true,
      email: '<EMAIL>',
      firstname: 'Jane',
      id: 'signatory001',
      lastname: 'Smith',
      order: 1,
      phone: '+987654321',
      signatureId: 'sig001',
      signaturePlace: SignaturePlace.PHYSICAL
    }
  ],
  status: SignatureStatus.PENDING,
  subscribers: [],
  type: SignatureType.PAPER
};

const mockOperation: Operation = {
  createdAt: '2024-01-01T00:00:00Z',
  creatorId: 'creator123',
  id: 'operation123',
  label: 'operationLabel',
  labelPattern: 'pattern123',
  organizationId: 'org123',
  parentId: null,
  tags: {},
  templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN'
};
