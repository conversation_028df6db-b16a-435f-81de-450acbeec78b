import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { Injectable } from '@nestjs/common';
import { SignatureAssociationType, SignatureProviderType, SignatureType } from '@mynotary/crossplatform/signatures/api';
import { AuthOrganizationSignature, AuthSignature, SignatureAuthorizationsRepository } from '@mynotary/backend/signatures/core';

@Injectable()
export class SignatureAuthorizationsRepositoryImpl implements SignatureAuthorizationsRepository {
  constructor(private prisma: PrismaService) {}

  async findAuthSignature(signatureId: string): Promise<AuthSignature | null> {
    const signature = await this.prisma.signature.findUnique({
      select: {
        creator_user_id: true,
        id: true,
        provider_type: true,
        signature_association_legal_component: {
          select: {
            legal_component: {
              select: {
                id: true
              }
            }
          }
        },
        token: true
      },
      where: {
        id: parseInt(signatureId)
      }
    });

    if (signature == null) {
      return null;
    }

    return this.toAuthSignature(signature);
  }

  async findAuthOrganizationSignature(signatureId: string): Promise<AuthOrganizationSignature | null> {
    const signature = await this.prisma.signature.findUnique({
      select: {
        association_type: true,
        creator_user_id: true,
        id: true,
        provider_type: true,
        signature_association_organization_document: {
          select: {
            organization: {
              select: {
                id: true
              }
            }
          }
        },
        token: true
      },
      where: {
        association_type: SignatureAssociationType.ORGANIZATION_DOCUMENT,
        id: parseInt(signatureId)
      }
    });

    if (signature == null) {
      return null;
    }

    return this.toAuthOrganizationSignature(signature);
  }

  async findAuthContractBySignatureId(signatureId: string): Promise<{ id: string } | null> {
    const legalComponentContract = await this.prisma.signature_association_legal_component.findFirst({
      select: {
        contract_id: true
      },
      where: {
        signature_id: parseInt(signatureId)
      }
    });

    if (legalComponentContract == null || legalComponentContract.contract_id == null) {
      return null;
    }

    return { id: legalComponentContract.contract_id.toString() };
  }

  private toAuthSignature(signature: AuthDbSignature): AuthSignature {
    return {
      id: signature.id.toString(),
      operationId: signature.signature_association_legal_component?.[0]?.legal_component.id.toString(),
      token: signature.token,
      type: this.computeSignatureType(signature.provider_type)
    };
  }

  private toAuthOrganizationSignature(signature: AuthDbOrganizationSignature): AuthOrganizationSignature {
    return {
      id: signature.id.toString(),
      organizationId: signature.signature_association_organization_document[0].organization.id.toString(),
      token: signature.token,
      type: this.computeSignatureType(signature.provider_type)
    };
  }

  private computeSignatureType(providerType: string): SignatureType {
    switch (providerType) {
      case SignatureProviderType.YOUSIGN_V2:
      case SignatureProviderType.YOUSIGN_V3:
        return SignatureType.ELECTRONIC;
      case SignatureProviderType.UNCERTIFIED:
        return SignatureType.UNCERTIFIED;
      case SignatureProviderType.MYNOTARY_PAPER:
        return SignatureType.PAPER;
      default:
        throw new Error(`Wrong signature type : ${providerType}`);
    }
  }
}

type AuthDbSignature = {
  creator_user_id: number | null;
  id: number;
  provider_type: string;
  signature_association_legal_component: { legal_component: { id: number } }[];
  token: string;
};

type AuthDbOrganizationSignature = {
  creator_user_id: number | null;
  id: number;
  provider_type: string;
  signature_association_organization_document: { organization: { id: number } }[];
  token: string;
};
