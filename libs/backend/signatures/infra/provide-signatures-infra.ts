import {
  ElectronicSignatureProvider,
  SignatureAuthorizationsRepository,
  SignatureFieldsPageGenerator,
  SignaturesRepository
} from '@mynotary/backend/signatures/core';
import { SignaturesRepositoryImpl } from './signatures.repository.impl';
import { SignatureFieldsPageGeneratorImpl } from './signature-fields-page-generator.impl';
import { YousignClient } from './yousign.client';
import { YousignClientImpl } from './yousign.client.impl';
import { ElectronicSignatureProviderImpl } from './electronic-signatures.provider.impl';
import { SignatureAuthorizationsRepositoryImpl } from './signature-authorizations.repository.impl';

export function provideSignaturesInfra() {
  return [
    { provide: SignaturesRepository, useClass: SignaturesRepositoryImpl },
    { provide: ElectronicSignatureProvider, useClass: ElectronicSignatureProviderImpl },
    { provide: YousignClient, useClass: YousignClientImpl },
    { provide: SignatureFieldsPageGenerator, useClass: SignatureFieldsPageGeneratorImpl },
    { provide: SignatureAuthorizationsRepository, useClass: SignatureAuthorizationsRepositoryImpl }
  ];
}
