import { convertToOperationHeadlineBff, convertToOperationListBff } from '@mynotary/backend/operations-bff/core';

describe('operations.converter', () => {
  it('should convert to headline', () => {
    const operation = {
      creationTime: new Date().getTime(),
      id: 1,
      label: 'operation label',
      links: [],
      organizationId: 1,
      template: {
        id: 'template-id',
        type: 'type'
      },
      type: 'OPERATION'
    };

    const result = convertToOperationHeadlineBff(operation);

    expect(result.id).toEqual('1');
    expect(result.label).toEqual('operation label');
    expect(result.type).toEqual('type');
  });

  it('should convert to a list of operation headers', () => {
    const apiComponentsResult = {
      components: [
        {
          creationTime: new Date().getTime(),
          id: 1,
          label: 'operation label',
          links: [],
          organizationId: 1,
          status: {
            id: 2,
            label: 'status'
          },
          template: {
            id: 'OPERATION__IMMOBILIER__POLYNESIE__VENTE'
          },
          type: 'OPERATION'
        }
      ],
      totalItems: 1
    };

    const result = convertToOperationListBff(apiComponentsResult);

    expect(result.totalItems).toEqual(1);
    const op = result.operations[0];
    expect(op.id).toEqual('1');
    expect(op.label).toEqual('operation label');
    expect(op.status).toEqual('status');
    expect(op.type).toEqual('Dossier de Vente - Polynésie');
  });
});
