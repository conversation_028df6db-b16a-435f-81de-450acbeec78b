import { Injectable } from '@nestjs/common';
import { Dictionary, Exception } from '@mynotary/crossplatform/shared/util';
import { isProductionProject } from '@mynotary/backend/shared/util';

@Injectable()
export class EnvService {
  public ar24;
  public database;
  public debug;
  public externalApi;
  public firebase;
  public learnWorlds;
  public mixPanel;
  public mynotary;
  public portalys;
  public storage;
  public tableau;
  public url;
  public yousign;
  public preEtatDates;
  public zoho;
  public stripe;
  public brevo;
  public gcpApplication;
  public slack;

  constructor(private secrets: Dictionary<string>) {
    this.ar24 = {
      senderUserId: this.getLoadedSecret('AR24_SENDER_USER_ID'),
      token: this.getLoadedSecret('AR24_TOKEN'),
      url: this.getLoadedSecret('AR24_URL')
    };
    this.database = {
      host: this.getOptionalEnv('DB_HOST'),
      name: this.getOptionalEnv('DB_NAME'),
      password: this.getLoadedSecret('DB_PASSWORD'),
      url: this.getOptionalEnv('DATABASE_URL'),
      user: this.getOptionalEnv('DB_USER')
    };
    this.gcpApplication = {
      apiKey: this.getLoadedSecret('GCP_APPLICATION_API_KEY')
    };
    this.debug = this.getOptionalEnv('DEBUG') === 'true';
    this.externalApi = {
      orpiContractProviderSecret: this.getLoadedSecret('ORPI_CONTRACT_PROVIDER_SECRET_KEY'),
      orpiTelemacToken: this.getLoadedSecret('ORPI_TELEMAC_TOKEN'),
      unisClientId: this.getLoadedSecret('UNIS_CLIENT_ID'),
      unisClientSecret: this.getLoadedSecret('UNIS_CLIENT_SECRET')
    };
    this.firebase = {
      apiKey: this.getLoadedSecret('FIREBASE_API_KEY'),
      credentialFile: this.getOptionalEnv('FIREBASE_CREDENTIAL_FILE'),
      projectId: this.getLoadedSecret('FIREBASE_PROJECT_ID')
    };
    this.learnWorlds = {
      clientId: this.getLoadedSecret('LEARN_WORLDS_CLIENT_ID'),
      clientSecret: this.getLoadedSecret('LEARN_WORLDS_CLIENT_SECRET')
    };
    this.mixPanel = {
      organizationTableId: this.getLoadedSecret('MX_ORGANIZATIONS_TABLE_ID'),
      projectId: this.getLoadedSecret('MX_PANEL_PROJECTID'),
      projectToken: this.getLoadedSecret('MX_PANEL_PROJECT_TOKEN'),
      serviceAccountId: this.getLoadedSecret('MX_PANEL_SERVICE_ACCOUNT_ID'),
      serviceAccountSecret: this.getLoadedSecret('MX_PANEL_SERVICE_ACCOUNT_SECRET')
    };
    this.mynotary = {
      domain: this.getLoadedSecret('DOMAIN_ENV'),
      gcpProject: this.getLoadedSecret('GCP_PROJECT'),
      legacyPublicKey: this.getLoadedSecret('LEGACY_PUBLIC_KEY'),
      mnApiKey: this.getLoadedSecret('MYNOTARY_API_KEY'),
      mnPrivateKey: this.getLoadedSecret('MYNOTARY_PRIVATE_KEY'),
      mnPublicKey: this.getLoadedSecret('MYNOTARY_PUBLIC_KEY')
    };
    this.portalys = {
      adsnApiUrl: this.getLoadedSecret('PORTALYS_URL_API_ADSN'),
      anfStockUrl: this.getLoadedSecret('PORTALYS_URL_ANF_STOCK'),
      anfUrl: this.getLoadedSecret('PORTALYS_URL_ANF'),
      comedecCommunesUrl: this.getLoadedSecret('PORTALYS_URL_COMEDEC_COMMUNES'),
      etatcivilApiUrl: this.getLoadedSecret('PORTALYS_URL_API_ETATCIVIL'),
      planeteAnonymousUrl: this.getLoadedSecret('PORTALYS_URL_PLANETE_ANONYME'),
      planeteId: this.getLoadedSecret('PORTALYS_PLANETE_ID'),
      planeteUrl: this.getLoadedSecret('PORTALYS_URL_PLANETE'),
      teleacteApiUrl: this.getLoadedSecret('PORTALYS_URL_API_TELEACTES'),
      teleactesReferentielsUrl: this.getLoadedSecret('PORTALYS_URL_TELEACTES_REFERENTIELS'),
      userId: this.getLoadedSecret('PORTALYS_USER_ID')
    };
    this.storage = {
      defaultBucket: this.getLoadedSecret('GCP_CLOUD_STORAGE_BUCKET'),
      temporaryBucket: this.getLoadedSecret('GCP_CLOUD_STORAGE_TEMPORARY_BUCKET'),
      trashBucket: this.getLoadedSecret('GCP_CLOUD_STORAGE_BUCKET_TRASH'),
      zipBucket: this.getLoadedSecret('GCP_CLOUD_STORAGE_BUCKET_ZIP')
    };
    this.tableau = {
      clientId: this.getLoadedSecret('TABLEAU_CLIENT_ID'),
      secretId: this.getLoadedSecret('TABLEAU_SECRET_ID'),
      secretKey: this.getLoadedSecret('TABLEAU_SECRET_KEY')
    };
    this.url = {
      apiEmailsUrl: this.getLoadedSecret('API_V2_EMAIL_URL'),
      apiFilesUrl: this.getLoadedSecret('API_FILES_V2_URL'),
      apiJavaUrl: this.getLoadedSecret('API_JAVA_URL'),
      apiMyNotaryUrl: this.getLoadedSecret('API_MYNOTARY_URL'),
      mnAppUrl: this.getLoadedSecret('MN_APP_URL')
    };
    this.yousign = {
      apiKey: this.getLoadedSecret('YOUSIGN_API_KEY')
    };
    this.zoho = {
      clientId: this.getLoadedSecret('ZOHO_CLIENT_ID'),
      clientSecret: this.getLoadedSecret('ZOHO_CLIENT_SECRET'),
      organizationId: this.getLoadedSecret('ZOHO_ORGANIZATION_ID'),
      productV4: this.getLoadedSecret('ZOHO_PRODUCT_V4'),
      tva0: this.getLoadedSecret('ZOHO_TVA_GUYANE'),
      tva20: this.getLoadedSecret('ZOHO_TVA_20'),
      tvaDomTom: this.getLoadedSecret('ZOHO_TVA_DOM_TOM')
    };
    this.stripe = {
      apiKey: this.getLoadedSecret('STRIPE_API_KEY'),
      webhookSecret: this.getLoadedSecret('STRIPE_WEBHOOK_SECRET')
    };
    this.preEtatDates = {
      apiKey: this.getLoadedSecret('PRE_ETAT_DATES_API_KEY')
    };
    this.brevo = {
      apiKey: this.getLoadedSecret('BREVO_API_KEY')
    };
    this.slack = {
      token: this.getLoadedSecret('SLACK_TOKEN')
    };
  }

  public isTestEnv(): boolean {
    return process.env.NODE_ENV === 'test';
  }

  private getLoadedSecret(envKey: string): string {
    const value = this.secrets[envKey];
    const isProd = isProductionProject();

    if (!isProd) {
      return this.getOptionalEnv(envKey) ?? '';
    }

    if (!value) {
      throw new Exception(`Environment key: ${envKey} was not found`);
    }
    return value;
  }

  private getOptionalEnv(envKey: string): string | undefined {
    return process.env[envKey];
  }
}
