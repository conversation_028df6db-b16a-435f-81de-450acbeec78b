import { Injectable } from '@nestjs/common';
import { Secrets<PERSON>rovider } from '@mynotary/backend/secrets/core';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { Dictionary, Exception } from '@mynotary/crossplatform/shared/util';

@Injectable()
export class SecretsProviderImpl implements SecretsProvider {
  async getAllSecrets() {
    const client = this.getClient();
    const [secrets] = await client.listSecrets({
      parent: `projects/${await client.getProjectId()}`
    });

    const secretData: Dictionary<string> = {};
    for (const secret of secrets) {
      if (secret.name == null) {
        throw new Exception('Secret name is undefined');
      }
      const [version] = await client.accessSecretVersion({
        name: `${secret.name}/versions/latest`
      });

      const payload = version.payload?.data?.toString();

      if (payload == null) {
        throw new Exception(`Secret ${secret.name} is undefined`);
      }

      /** the initial secret name is in the format projects/{project}/secrets/{secret-name} */
      const keyName = secret.name.split('/').pop();

      if (keyName == null) {
        throw new Exception('Secret name is undefined');
      }

      secretData[keyName] = payload;
    }

    return secretData;
  }

  private getClient() {
    let options;
    const credentialsFilePath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
    if (credentialsFilePath) {
      options = {
        keyFilename: credentialsFilePath
      };
    }
    return new SecretManagerServiceClient(options);
  }
}
