import { UserEventsBffController } from '@mynotary/backend/user-events-bff/feature';
import { provideUserEventsBffTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { UserEvent } from '@mynotary/crossplatform/api-adsn/api';
import { LocalqueuesEnum } from '@mynotary/crossplatform/localqueues/api';

describe(UserEventsBffController.name, () => {
  it('should send user events', async () => {
    const { client } = await setup();
    const event: UserEvent = {
      payload: { hello: 'world' },
      queue: LocalqueuesEnum.LINK_REAL_CARD,
      userId: '1'
    };
    const response = await client.post('/user-event').send(event);

    expect(response.status).toBe(201);
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,

      controller: UserEventsBffController,
      providers: provideUserEventsBffTest()
    });

    const testingRepos = getService(TestingRepositories);

    return {
      client,
      testingRepos
    };
  }
});
