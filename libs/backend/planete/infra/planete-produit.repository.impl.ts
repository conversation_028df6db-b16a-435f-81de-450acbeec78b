import { Injectable } from '@nestjs/common';
import { FindPlaneteProduitArgs, PlaneteProduitRepository } from '@mynotary/backend/planete/core';
import { planete_produit, PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { convertEnum, Exception } from '@mynotary/crossplatform/shared/util';
import { PlaneteProduit, ProduitsPlanete } from '@mynotary/crossplatform/api-adsn/api';

@Injectable()
export class PlaneteProduitRepositoryImpl implements PlaneteProduitRepository {
  constructor(private prismaService: PrismaService) {}

  async find(args: FindPlaneteProduitArgs): Promise<PlaneteProduit[]> {
    try {
      const produits = await this.prismaService.planete_produit.findMany({
        where: {
          dossier_id: args.dossierId,
          id: args.id
        }
      });
      return produits.map(this.fromDb);
    } catch (e) {
      throw new Exception(`Error fetching produits`, { cause: e });
    }
  }

  private fromDb(produitDb: planete_produit): PlaneteProduit {
    if (typeof produitDb.contents !== 'object') {
      produitDb.contents = { value: produitDb.contents };
    }
    return {
      contents: produitDb.contents ?? {},
      dossierId: produitDb.dossier_id,
      id: produitDb.id,
      label: produitDb.label,
      status: produitDb.status,
      type: convertEnum(ProduitsPlanete, produitDb.type)
    };
  }
}
