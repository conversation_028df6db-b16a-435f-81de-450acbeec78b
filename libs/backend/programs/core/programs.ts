import { AnswerDict } from '@mynotary/crossplatform/records/api';

import { ReservationStatus } from '@mynotary/crossplatform/programs/core';
import { LegalRecordTemplateId } from '@mynotary/crossplatform/legal-templates/api';

export interface LotReservation {
  legalRecordId: string;
  programId: string;
  reservationStatus: ReservationStatus;
}

export interface ProgramLotUpdate {
  answer: AnswerDict;
  id: string;
  parentExternalId?: string;
}

export interface ProgramLotCreate {
  answer: AnswerDict;
  legalRecordTemplateId: LegalRecordTemplateId;
  parentExternalId?: string;
}

export type ProgramLotUpsert = ProgramLotUpdate | ProgramLotCreate;

export function isProgramLotUpdate(lot: ProgramLotUpsert): lot is ProgramLotUpdate {
  return (lot as ProgramLotUpdate).id !== undefined;
}
