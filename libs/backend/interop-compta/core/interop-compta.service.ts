import { Injectable, Logger } from '@nestjs/common';
import { InteropDelegateRepository } from './interop-delegate.repository';
import { InteropDelegateEventProvider } from './interop-delegate.event.provider';
import { InteropDdvEventProvider } from './interop-ddv.event.provider';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PlDossierFull, VirementStatus } from '@mynotary/crossplatform/api-teleactes/api';
import { InteropDdvFluxProvider } from './interop-ddv-flux.provider';
import { InteropAoParser } from './interop-ao.parser';
import { negociateVersion } from './negociate-version';
import { LegalsApiService } from '@mynotary/backend/legals/api';
import { assertNotNull, convertEnum, Exception, NotFoundError } from '@mynotary/crossplatform/shared/util';
import {
  InteropDdvData,
  InteropDdvFluxBuilderVersion,
  InteropDelegate,
  InteropDelegateStatus,
  InteropDelegateVersion,
  InteropSubPathsV3,
  InteropSubPathsV4,
  PlaneteHistoriqueServer,
  PlaneteHistoriqueType,
  PlanetePjRecueType,
  PlaneteStatus
} from '@mynotary/crossplatform/api-adsn/api';
import { InteropSyncArgs, SendInteropAoErrArgs } from '@mynotary/backend/adsn-client/api';
import { InteropSendDdvArgs } from '@mynotary/backend/teleactes-client/core';
import { getParametresEtude, PlaneteApiService } from '@mynotary/backend/planete/api';
import { OrganizationsApiService } from '@mynotary/backend/organizations/api';
import { OrganizationType } from '@mynotary/crossplatform/organizations/api';

@Injectable()
export class InteropComptaService {
  private readonly logger = new Logger(InteropComptaService.name);
  constructor(
    private planeteApiService: PlaneteApiService,
    private delegateRepository: InteropDelegateRepository,
    private delegateEventProvider: InteropDelegateEventProvider,
    private ddvEventProvider: InteropDdvEventProvider,
    private interopDdvFluxProvider: InteropDdvFluxProvider,
    private interopAoParser: InteropAoParser,
    private operationsApiService: LegalsApiService,
    private organizationsApiService: OrganizationsApiService
  ) {}

  async getDelegate(crpcen: string) {
    let delegate = await this.delegateRepository.get(crpcen);
    if (!delegate) {
      /* values below are default ones. They will be overridden after protocol version negotiation */
      const organization = await this.organizationsApiService.getOrganization({ uniqIdentifier: crpcen });
      if (!organization || organization.type !== OrganizationType.PORTALYS_NOTARY_OFFICE) {
        throw new NotFoundError({ id: crpcen, resource: 'organization with CRPCEN' });
      }
      delegate = {
        ...this.defaultDelegate,
        activeVersion: organization.interopVersion
          ? convertEnum(InteropDelegateVersion, organization.interopVersion)
          : InteropDelegateVersion.V4,
        appId: organization.interopAppId ?? 1
      };
    }
    if (delegate.delegateStatus === InteropDelegateStatus.ELECTION_PENDING) {
      this.logger.log(`New delegate elected for CRPCEN ${crpcen}`);
      delegate.delegateStatus = InteropDelegateStatus.ONLINE;
      await this.delegateRepository.set({ crpcen, delegate: { ...delegate } });
      delegate.actAsDelegate = true;
      /* in case we triggered the event to get a new delegate, cancel it now that we have one */
      await this.delegateEventProvider.cancel(crpcen);
    }
    return delegate;
  }

  @Cron(CronExpression.EVERY_10_MINUTES)
  async verifyDelegates() {
    const crpcenList = await this.delegateRepository.getAllKeys();
    for (const crpcen of crpcenList) {
      this.logger.log(`Checking delegate for CRPCEN ${crpcen}`);
      await this.ddvEventProvider.dispatch({ crpcen, id: 'ping' });
      /* give the Companion delegate 10s to respond */
      await new Promise((resolve) => setTimeout(resolve, 10000));
      const docExists = await this.ddvEventProvider.check({ crpcen, id: 'ping' });
      if (docExists) {
        const currentDelegate = await this.delegateRepository.get(crpcen);
        assertNotNull(currentDelegate, 'current delegate');
        await this.delegateRepository.set({
          crpcen,
          delegate: { ...currentDelegate, delegateStatus: InteropDelegateStatus.ELECTION_PENDING }
        });
        this.logger.log(`Delegate for CRPCEN ${crpcen} is offline, starting new elections`);
        /* The Companion delegate did not handle the dummy file on time, we suppose it's offline. Start new elections! */
        /* It's possible no-one answers to this call. In that case nothing happens until next call to verifyDelegates() */
        await this.delegateEventProvider.dispatch(crpcen);
        await this.ddvEventProvider.cancel({ crpcen, id: 'ping' }); /* don't await this one, we're not in a hurry */
      } else {
        this.logger.log(`Delegate for CRPCEN ${crpcen} is online`);
      }
    }
  }

  async sync({ crpcen, flags }: InteropSyncArgs) {
    let delegateInfo = await this.delegateRepository.get(crpcen);
    if (!delegateInfo) {
      const organization = await this.organizationsApiService.getOrganization({ uniqIdentifier: crpcen });
      if (!organization || organization.type !== OrganizationType.PORTALYS_NOTARY_OFFICE) {
        throw new NotFoundError({ id: crpcen, resource: 'organization with CRPCEN' });
      }
      delegateInfo = {
        ...this.defaultDelegate,
        activeVersion: organization.interopVersion
          ? convertEnum(InteropDelegateVersion, organization.interopVersion)
          : InteropDelegateVersion.V4,
        appId: organization.interopAppId ?? 1
      };
    }
    delegateInfo = negociateVersion({ delegateInfo, flags });
    /* save it for future reference */
    await this.delegateRepository.set({ crpcen, delegate: delegateInfo });
    /* return updated info to the delegate Companion, so it knows what folders should be watched */
    return delegateInfo;
  }

  private readonly defaultDelegate: InteropDelegate = {
    activeVersion: InteropDelegateVersion.V4,
    appId: 1,
    delegateStatus: InteropDelegateStatus.ELECTION_PENDING,
    interopPath: 'X:',
    subPaths: InteropSubPathsV4,
    syncFlags: []
  };

  async sendDdv({ crpcen, id }: InteropSendDdvArgs) {
    await this.ddvEventProvider.dispatch({ crpcen, id: id });
  }

  async getDdv(id: string): Promise<InteropDdvData> {
    // get virement data from id
    const virement = await this.planeteApiService.getPlaneteVirement(id);
    // get MN operation data
    if (!virement.operationId) {
      throw new Exception(`No operationId found in virement ${id}`);
    }
    const organization = await this.organizationsApiService.getOrganization({ operationId: virement.operationId });
    const dossier = await this.operationsApiService.getOperation(virement.operationId);
    const operation: PlDossierFull = {
      id: dossier.id,
      immeubles: [],
      intervenants: [],
      label: dossier.label
    };
    // get organization parameters
    const parametres = getParametresEtude(organization);
    // generate XML
    const xml = this.interopDdvFluxProvider.buildXml({
      operation,
      parametres,
      virement
    });
    // generate filename
    const filename = this.interopDdvFluxProvider.buildFileName({
      operation,
      parametres,
      virement
    });

    try {
      // save XML as PJ in Planete history
      if (virement.dossierPlaneteId) {
        const histories = await this.planeteApiService.findPlaneteHistorique({
          dossierId: virement.dossierPlaneteId,
          type: PlaneteHistoriqueType.TO_PAY
        });
        const lastHistory = histories.reduce((acc, curr) => (acc.eventTime > curr.eventTime ? acc : curr));
        await this.planeteApiService.createPlanetePjRecue({
          content: xml,
          dossierId: virement.dossierPlaneteId,
          historiqueId: lastHistory.id,
          label: 'Demande de virement',
          receivedTime: new Date(),
          type: PlanetePjRecueType.XML
        });
      }
    } catch (e) {
      // no history found
      this.logger.error(`History not found: ${JSON.stringify(e)}`);
    }

    return {
      content: xml,
      filename,
      subfolder:
        parametres.interopVersion === InteropDdvFluxBuilderVersion.V4 ? InteropSubPathsV4.ddv : InteropSubPathsV3.ddv
    };
  }

  async addInteropAo({ content, id }: SendInteropAoErrArgs) {
    const virement = await this.planeteApiService.getPlaneteVirement(id);
    // extract data
    const ao = this.interopAoParser.parse(content);
    // create history and store content as PJ
    if (virement.dossierPlaneteId) {
      const history = await this.planeteApiService.createPlaneteHistorique({
        descriptionObj: { message: ao.message },
        dossierId: virement.dossierPlaneteId,
        eventTime: new Date(),
        originServer: PlaneteHistoriqueServer.COMPTA,
        type: ao.etatOk ? PlaneteHistoriqueType.PAID : PlaneteHistoriqueType.PAYMENT_REFUSED
      });
      await this.planeteApiService.createPlanetePjRecue({
        content,
        dossierId: virement.dossierPlaneteId,
        historiqueId: history,
        label: "Avis d'opéré " + (ao.etatOk ? 'Positif' : 'Négatif'),
        receivedTime: new Date(),
        type: ao.etatOk ? PlanetePjRecueType.AOP : PlanetePjRecueType.AON
      });
    }
    // update VirementType
    if (ao.etatOk) {
      await this.planeteApiService.updatePlaneteVirement({
        ...virement,
        compteClientOffice: ao.compteClientOffice,
        date: ao.date,
        label: ao.libelle,
        montant: ao.montant,
        numero: ao.numero,
        status: VirementStatus.PAID
      });
    } else {
      await this.planeteApiService.updatePlaneteVirement({
        ...virement,
        status: VirementStatus.REFUSED
      });
    }
    // update PlaneteDossier status
    if (virement.dossierPlaneteId) {
      const oldDossier = await this.planeteApiService.getPlaneteDossier(virement.dossierPlaneteId);
      await this.planeteApiService.updatePlaneteDossier({
        ...oldDossier,
        status: ao.etatOk ? PlaneteStatus.PAID : PlaneteStatus.PAYMENT_REFUSED
      });
    }
  }
}
