import { Controller, Post, UseGuards } from '@nestjs/common';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { FeatureOrganizationsApiService } from '@mynotary/backend/feature-organizations/api';
import { HasFeatureAccess } from '@mynotary/backend/features/authorization';

import { provideFeaturesTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { FeatureType } from '@mynotary/crossplatform/features/api';

describe(HasFeatureAccess.name, () => {
  it('should return 200 if user has access to targeted feature', async () => {
    const { client, configureOwnedFeature } = await setup();

    const { featureId, organizationId } = await configureOwnedFeature();

    const response = await client.post(`/features/${featureId}`).send({ organizationId });

    expect(response.statusCode).toBe(201);
  });

  it("should reject user if user isn't member of the feature's organization", async () => {
    const { client, configureOwnedFeature } = await setup();
    const feat1 = await configureOwnedFeature();
    const feat2 = await configureOwnedFeature();

    const response = await client.post(`/features/${feat2.featureId}`).send({ organizationId: feat1.organizationId });

    expect(response.statusCode).toBe(403);
  });

  it("should reject user if user isn't logged in", async () => {
    const { client, configureOwnedFeature, setCurrentUser } = await setup();
    const { featureId, organizationId } = await configureOwnedFeature();

    setCurrentUser(null);
    const response = await client.post(`/features/${featureId}`).send({ organizationId });

    expect(response.statusCode).toBe(403);
  });

  it("should reject user if user doesn't have permission", async () => {
    const { client, configureOwnedFeature } = await setup();

    const { featureId, organizationId } = await configureOwnedFeature({ hasPerm: false });

    const response = await client.post(`/features/${featureId}`).send({ organizationId });

    expect(response.statusCode).toBe(403);
  });

  async function setup() {
    const { client, getService, setCurrentUser } = await createTestingWideApp({
      controller: TestController,
      providers: [...provideFeaturesTest()]
    });

    const testingRepos = getService(TestingRepositories);
    const orga = await testingRepos.organizations.createOrganization({ name: 'Agence 1' });
    const featureOrganization = getService(FeatureOrganizationsApiService);
    const featureOrganizationApiSpy = jest.spyOn(featureOrganization, 'getFeatures');

    const configureOwnedFeature = async (args: { hasPerm: boolean } = { hasPerm: true }) => {
      const member = await testingRepos.createMember({
        permissions: args.hasPerm
          ? [{ entityType: EntityType.ORGANIZATION, permissionType: PermissionType.UPDATE_ORGANIZATION_INFORMATIONS }]
          : []
      });
      setCurrentUser({ userId: member.userId });
      const feature = await testingRepos.features.createFeature({
        organizationId: member.organizationId,
        type: FeatureType.CONTRACT_BRANDING
      });
      featureOrganizationApiSpy.mockResolvedValue([
        { id: feature.id, organizationId: member.organizationId, type: FeatureType.CONTRACT_BRANDING }
      ]);
      return { featureId: feature.id, organizationId: member.organizationId };
    };

    return {
      client,
      configureOwnedFeature,
      orga,
      setCurrentUser,
      testingRepos
    };
  }
});

@Controller()
class TestController {
  @UseGuards(
    HasFeatureAccess({
      featureType: FeatureType.CONTRACT_BRANDING,
      permission: { entityType: EntityType.ORGANIZATION, type: PermissionType.UPDATE_ORGANIZATION_INFORMATIONS }
    })
  )
  @Post('/features/:id')
  getValue() {
    return '👌';
  }
}
