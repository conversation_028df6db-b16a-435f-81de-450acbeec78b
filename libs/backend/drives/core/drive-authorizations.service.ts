import { Injectable } from '@nestjs/common';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { AuthorizationsApiService } from '@mynotary/backend/authorizations/api';
import { AuthDriveFile, DriveFilesRepository } from './drive-files.repository';

@Injectable()
export class DriveAuthorizationsService {
  constructor(
    private driveFilesRepository: DriveFilesRepository,
    private authorizationsApiService: AuthorizationsApiService
  ) {}

  async findAuthDriveFile(driveFileId: string): Promise<AuthDriveFile | null> {
    return await this.driveFilesRepository.findAuthDriveFile(driveFileId);
  }

  async hasDriveAccessPermission({ operationId, permission, userId }: HasDriveAccessPermissionArgs): Promise<boolean> {
    const roleId = await this.authorizationsApiService.getOperationRoleId(operationId, userId);

    if (roleId == null) {
      return false;
    }

    return await this.authorizationsApiService.hasTargetPermission({
      permission: {
        entityType: EntityType.DOCUMENT,
        type: permission
      },
      roleId
    });
  }
}

interface HasDriveAccessPermissionArgs {
  operationId: string;
  permission: PermissionType;
  userId: string;
}
