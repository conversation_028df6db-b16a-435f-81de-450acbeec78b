import {
  AuthJavaLegacyTokenInfo,
  AuthLogAsTokenInfo,
  AuthorizationArgs,
  AuthResourceTokenInfo,
  AuthenticationInfo,
  AuthUserTokenInfo
} from '@mynotary/backend/shared/auth-util';
import { CanActivate, ExecutionContext } from '@nestjs/common';

export abstract class AuthorizationBase implements CanActivate {
  canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();

    const authorizationRequest: AuthorizationArgs = {
      body: request.body,

      headers: request.headers,

      ip: request.ip || request.connection.remoteAddress,

      legacyAppInfo: isLegacyApp(request.user) ? request.user : null,

      params: request.params,

      /* We want to limit the type and avoid dirty complex query params. */
      queryParams: request.query as Record<string, string>,
      resourceInfo: isResourceToken(request.user) ? request.user : null,
      userInfo: isUserToken(request.user) ? request.user : null
    };

    return this.isAuthorized(authorizationRequest);
  }

  abstract isAuthorized(authorizationRequest: AuthorizationArgs): boolean | Promise<boolean>;
}

function isLegacyApp(userInfo?: AuthenticationInfo): userInfo is AuthJavaLegacyTokenInfo {
  return userInfo?.type === 'legacy-app';
}

function isResourceToken(userInfo?: AuthenticationInfo): userInfo is AuthResourceTokenInfo {
  return userInfo?.type === 'resource-token';
}

function isUserToken(userInfo?: AuthenticationInfo): userInfo is AuthUserTokenInfo | AuthLogAsTokenInfo {
  return userInfo?.type === 'log-as' || userInfo?.type === 'user';
}
