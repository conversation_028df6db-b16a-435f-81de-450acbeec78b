import { Injectable, Type, UnprocessableEntityException } from '@nestjs/common';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { AuthorizationArgs, pathResolver } from '@mynotary/backend/shared/auth-util';
import { AuthorizationOperationsService, AuthorizationsService } from '@mynotary/backend/authorizations/core';
import { AuthorizationBase } from './authorization-base';

interface HasOperationAccessOptions {
  operationIdResolver?: (args: AuthorizationArgs) => string | null;
  permissionType?: PermissionType;
}

export function HasOperationAccess(options?: HasOperationAccessOptions): Type<AuthorizationBase> {
  const operationIdResolver = options?.operationIdResolver ?? pathResolver('operationId');
  @Injectable()
  class HasOperationAccess extends AuthorizationBase {
    constructor(
      private authOperationService: AuthorizationOperationsService,
      private authorizationsService: AuthorizationsService
    ) {
      super();
    }

    async isAuthorized(authorizationRequest: AuthorizationArgs) {
      const userId = authorizationRequest.userInfo?.userId;
      const operationId = operationIdResolver(authorizationRequest);
      const isUserVerified = await this.authorizationsService.isUserVerified(userId);

      if (userId == null || !isUserVerified) {
        return false;
      }

      if (operationId == null) {
        throw new UnprocessableEntityException('operationId is required');
      }

      return await this.authOperationService.hasOperationAccess({
        operationId,
        permissionType: options?.permissionType,
        userId
      });
    }
  }

  return HasOperationAccess;
}
