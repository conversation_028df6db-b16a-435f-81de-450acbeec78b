import { InvoiceEmailsController } from '@mynotary/backend/invoices/feature';
import { InvoiceFilesProvider } from '@mynotary/backend/invoices/core';
import { EmailsApiService } from '@mynotary/backend/emails/api';
import { InvoiceFileNewDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { InvoiceStatus } from '@mynotary/crossplatform/invoices/core';
import { provideInvoicesTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { FeatureType } from '@mynotary/crossplatform/features/api';

describe(InvoiceEmailsController.name, () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should deny sending email if invoice has the status CANCELLED', async () => {
    const { client, feature, organization, testingRepos, user } = await setup();

    const config = await testingRepos.invoices.createInvoiceConfig({
      featureId: feature.id,
      organizationId: organization.id
    });

    const invoice = await testingRepos.invoices.createInvoice({
      configId: config.id,
      featureId: feature.id,
      organizationId: organization.id,
      status: InvoiceStatus.CANCELLED,
      userId: user.id
    });

    const response = await client.post(`/invoice-emails`).send({
      invoiceId: invoice.id,
      message: 'Message',
      recipients: ['<EMAIL>', '<EMAIL>'],
      userId: user.id
    } satisfies InvoiceFileNewDto);

    expect(response.status).toBe(400);
  });

  it('should send invoice to the target users', async () => {
    const { client, emailServiceSpy, feature, fileProviderSpy, organization, testingRepos, user } = await setup();

    const config = await testingRepos.invoices.createInvoiceConfig({
      featureId: feature.id,
      organizationId: organization.id
    });

    const invoice = await testingRepos.invoices.createInvoice({
      configId: config.id,
      featureId: feature.id,
      organizationId: organization.id,
      userId: user.id
    });

    const response = await client.post(`/invoice-emails`).send({
      invoiceId: invoice.id,
      message: 'Message',
      recipients: ['<EMAIL>', '<EMAIL>'],
      userId: user.id
    } satisfies InvoiceFileNewDto);

    expect(response.status).toBe(201);
    expect(fileProviderSpy).toHaveBeenCalledTimes(1);
    expect(emailServiceSpy).toHaveBeenCalledTimes(2);
    expect(fileProviderSpy).toHaveBeenCalledWith(
      expect.objectContaining({ invoice: expect.objectContaining({ id: invoice.id }) })
    );
    expect(emailServiceSpy).toHaveBeenNthCalledWith(1, {
      data: {
        invoiceBuffer: Buffer.from('random text'),
        invoiceId: invoice.id,
        invoiceLabel: 'INV-12345',
        message: 'Message',
        organizationId: organization.id,
        sender: {
          email: `user-${user.id}@mynotary.fr`,
          firstname: 'Foo',
          lastname: 'BAR',
          organizationName: 'MyNotary',
          phone: ''
        },
        templateId: 'INVOICE_CREATION'
      },
      receiver: '<EMAIL>'
    });
    expect(emailServiceSpy).toHaveBeenNthCalledWith(2, {
      data: {
        invoiceBuffer: Buffer.from('random text'),
        invoiceId: invoice.id,
        invoiceLabel: 'INV-12345',
        message: 'Message',
        organizationId: organization.id,
        sender: {
          email: `user-${user.id}@mynotary.fr`,
          firstname: 'Foo',
          lastname: 'BAR',
          organizationName: 'MyNotary',
          phone: ''
        },
        templateId: 'INVOICE_CREATION'
      },
      receiver: '<EMAIL>'
    });
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,

      controller: InvoiceEmailsController,
      providers: provideInvoicesTest()
    });

    const testingRepos = getService(TestingRepositories);

    const invoiceFileProvider = getService(InvoiceFilesProvider);
    const emailApiService = getService(EmailsApiService);

    const user = await testingRepos.users.createUser({ verified: true });
    const organization = await testingRepos.organizations.createOrganization({ name: 'MyNotary' });

    const fileProviderSpy = jest
      .spyOn(invoiceFileProvider, 'createInvoiceFile')
      .mockResolvedValue({ invoiceBuffer: Buffer.from('random text') });

    const emailServiceSpy = jest.spyOn(emailApiService, 'sendEmail').mockResolvedValue();

    const feature = await testingRepos.features.createFeature({
      organizationId: organization.id,
      type: FeatureType.INVOICES
    });

    return {
      client,
      emailServiceSpy,
      feature,
      fileProviderSpy,
      organization,
      testingRepos,
      user
    };
  }
});
