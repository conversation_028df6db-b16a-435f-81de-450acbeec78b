import { InvoiceConfigField } from '@mynotary/crossplatform/invoices/core';
import { InvoiceConfigExtended, InvoiceConfigUpdateCurrentNumber } from './invoices';

export abstract class InvoiceConfigsRepository {
  abstract getInvoiceConfig(featureId: string): Promise<InvoiceConfigExtended[]>;

  abstract createInvoiceConfig(args: CreateInvoiceConfigArgs): Promise<InvoiceConfigExtended>;

  abstract updateInvoiceConfig(args: UpdateInvoiceConfigArgs): Promise<InvoiceConfigExtended>;

  abstract updateInvoiceConfigCurrentNumber(args: InvoiceConfigUpdateCurrentNumber): Promise<InvoiceConfigExtended>;

  abstract isConfigLinkedToInvoices(configId: string): Promise<boolean>;

  abstract getInvoiceConfigStartNumber(configId: string): Promise<number>;
}

export interface UpdateInvoiceConfigArgs {
  config: InvoiceConfigField;
  currentNumber?: number;
  id: string;
}

export type CreateInvoiceConfigArgs = {
  featureId: string;
  organizationId: string;
  userId: string;
} & InvoiceConfigField;
