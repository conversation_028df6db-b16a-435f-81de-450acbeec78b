import { Body, Controller, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { InvoicesService } from '@mynotary/backend/invoices/core';
import {
  AddressDto,
  ChargeHonoraireTypeDto,
  InvoiceDto,
  InvoiceFieldsUpdateDto,
  InvoiceListDto,
  InvoiceNewDto,
  InvoiceStatusDto,
  InvoiceTypeDto
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { convertEnum, convertOptionalEnum, MnAddress } from '@mynotary/crossplatform/shared/util';
import {
  ChargeHonoraireType,
  Invoice,
  InvoiceNew,
  InvoiceStatus,
  InvoiceType,
  UpdateInvoice
} from '@mynotary/crossplatform/invoices/core';
import { HasInvoicesAccess, HasInvoiceUpdateAccess } from '@mynotary/backend/invoices/authorization';
import { bodyResolver, queryResolver } from '@mynotary/backend/shared/auth-util';
import { AdminType } from '@mynotary/crossplatform/shared/users-core';
import { OrGuard } from '@mynotary/backend/authorizations/api';
import { IsAdmin } from '@mynotary/backend/authorizations/api';

@Controller()
export class InvoicesController {
  constructor(private invoicesService: InvoicesService) {}

  @UseGuards(
    OrGuard([
      [
        HasInvoicesAccess({
          organizationIdResolver: queryResolver('organizationId'),
          userIdResolver: queryResolver('userId')
        })
      ],
      [IsAdmin({ minimumAdminLevel: AdminType.SUPPORT })]
    ])
  )
  @Get('/invoices')
  async getInvoices(
    @Query()
    query: {
      endTime?: string;
      organizationId: string;
      search?: string[];
      startTime?: string;
      status?: string[];
      types?: string[];
      userId: string;
    }
  ): Promise<InvoiceListDto> {
    const invoices = await this.invoicesService.getInvoices({
      filters: {
        endTime: query.endTime ? new Date(parseInt(query.endTime)).toISOString() : undefined,
        search: query.search,
        startTime: query.startTime ? new Date(parseInt(query.startTime)).toISOString() : undefined,
        status: query.status?.map((status) => convertEnum(InvoiceStatus, status)),
        types: query.types?.map((type) => convertEnum(InvoiceType, type))
      },
      organizationId: query.organizationId,
      userId: query.userId
    });

    return { items: invoices.map(convertToInvoiceDto) };
  }

  @UseGuards(HasInvoicesAccess({ organizationIdResolver: bodyResolver('organizationId') }))
  @Post('/invoices')
  async createInvoices(@Body() body: InvoiceNewDto): Promise<InvoiceDto> {
    const invoiceNew = convertDtoToInvoiceNew(body);
    const createdInvoice = await this.invoicesService.createInvoice(invoiceNew);
    return convertToInvoiceDto(createdInvoice);
  }

  @UseGuards(HasInvoiceUpdateAccess({ organizationIdResolver: queryResolver('organizationId') }))
  @Put('/invoices/:invoiceId')
  async updateInvoice(
    @Param('invoiceId') invoiceId: string,
    @Body() body: InvoiceFieldsUpdateDto
  ): Promise<InvoiceDto> {
    const invoice = convertInvoiceDtoToUpdatedInvoice(body);
    const updatedInvoice = await this.invoicesService.updateInvoice({
      data: invoice,
      id: invoiceId
    });
    return convertToInvoiceDto(updatedInvoice);
  }
}

function convertDtoToInvoiceNew(dto: InvoiceNewDto): InvoiceNew {
  return {
    chargeHonoraire: convertOptionalEnum(ChargeHonoraireType, dto.chargeHonoraire),
    creationTime: dto.creationTime,
    discounts: dto.discounts,
    expirationTime: dto.expirationTime,
    items: dto.items,
    negociatorEmail: dto.negociatorEmail,
    negociatorFirstname: dto.negociatorFirstname,
    negociatorId: dto.negociatorId,
    negociatorLastname: dto.negociatorLastname,
    operationAddress: convertDtoToAddress(dto.operationAddress),
    operationInfo: dto.operationInfo,
    organizationId: dto.organizationId,
    registerEntryId: dto.registerEntryId,
    registerEntryNumber: dto.type != InvoiceTypeDto.OTHER ? dto.registerEntryNumber : undefined,
    type: convertEnum(InvoiceType, dto.type),
    userId: dto.userId
  };
}

function convertDtoToAddress(address: AddressDto): MnAddress {
  return {
    ...address,
    location:
      address.location != null ? { lat: address.location.lat ?? -1, lng: address.location.lng ?? -1 } : undefined
  };
}

function convertToInvoiceDto(invoice: Invoice): InvoiceDto {
  return {
    apeCode: invoice.apeCode,
    bank: invoice.bank,
    bankCode: invoice.bankCode,
    beneficiaryName: invoice.beneficiaryName,
    bic: invoice.bic,
    cancellationInformation: invoice.cancellationInformation,
    cciCity: invoice.cciCity,
    chargeHonoraire: convertOptionalEnum(ChargeHonoraireTypeDto, invoice.chargeHonoraire),
    companyAddress: invoice.companyAddress,
    companyName: invoice.companyName,
    companyType: invoice.companyType,
    creationTime: invoice.creationTime,
    discounts: invoice.discounts.map((discount) => ({
      amount: discount.amount,
      description: discount.description,
      label: discount.label
    })),
    expirationTime: invoice.expirationTime,
    featureId: invoice.featureId,
    iban: invoice.iban,
    id: invoice.id,
    insuranceAddress: invoice.insuranceAddress,
    insuranceName: invoice.insuranceName,
    invoiceLabel: invoice.invoiceLabel,
    items: invoice.items.map((item) => ({
      amount: item.amount,
      description: item.description,
      label: item.label,
      tva: item.tva
    })),
    legalWarning: invoice.legalWarning,
    negociatorEmail: invoice.negociatorEmail,
    negociatorFirstname: invoice.negociatorFirstname,
    negociatorId: invoice.negociatorId,
    negociatorLastname: invoice.negociatorLastname,
    operationAddress: invoice.operationAddress,
    operationInfo: invoice.operationInfo,
    organizationId: invoice.organizationId,
    paymentTime: invoice.paymentTime,
    registerEntryId: invoice.registerEntryId,
    registerEntryNumber: invoice.registerEntryNumber,
    shareCapital: invoice.shareCapital,
    siren: invoice.siren,
    status: convertEnum(InvoiceStatusDto, invoice.status),
    transactionCardNumber: invoice.transactionCardNumber,
    tva: invoice.tva,
    tvaNumber: invoice.tvaNumber,
    type: convertEnum(InvoiceTypeDto, invoice.type)
  };
}

function convertInvoiceDtoToUpdatedInvoice(dto: InvoiceFieldsUpdateDto): UpdateInvoice {
  return {
    cancellationInformation: dto.cancellationInformation,
    chargeHonoraire: convertOptionalEnum(ChargeHonoraireType, dto.chargeHonoraire),
    creationTime: dto.creationTime,
    discounts: dto.discounts,
    expirationTime: dto.expirationTime,
    items: dto.items,
    negociatorEmail: dto.negociatorEmail,
    negociatorFirstname: dto.negociatorFirstname,
    negociatorId: dto.negociatorId,
    negociatorLastname: dto.negociatorLastname,
    operationAddress: convertDtoToAddress(dto.operationAddress),
    operationInfo: dto.operationInfo,
    paymentTime: dto.paymentTime,
    registerEntryId: dto.registerEntryId,
    registerEntryNumber: dto.registerEntryNumber,
    status: convertEnum(InvoiceStatus, dto.status),
    type: convertEnum(InvoiceType, dto.type)
  };
}
