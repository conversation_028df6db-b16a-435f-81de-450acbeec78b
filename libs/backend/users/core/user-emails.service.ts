import { Injectable } from '@nestjs/common';
import { UsersRepository } from './users.repository';
import { InvalidEmailsError } from './users.error';
import { isValidEmail } from '@mynotary/crossplatform/shared/util';
import { DefaultRecordsApiService } from '@mynotary/backend/default-records/api';
import { LegalsApiService } from '@mynotary/backend/legals/api';
import { forEach, groupBy, some } from 'lodash';
import { User } from '@mynotary/crossplatform/shared/users-core';
import { AuthenticationsProvider } from '@mynotary/backend/authentications/api';
import { isValidEmailDomain } from '@mynotary/backend/shared/util';

@Injectable()
export class UserEmailsService {
  constructor(
    private usersRepository: UsersRepository,
    private defaultRecordsApiService: DefaultRecordsApiService,
    private legalsApiService: LegalsApiService,
    private authenticationsProvider: AuthenticationsProvider
  ) {}

  /**
   * Update users from their old email to a new email. Used when a list of users need to change their email, usually
   * due to a domain change.
   *
   * First, it validates the provided email pairs and users.
   * Then performs the following steps for each user:
   * 1. Updates the user's default records with the new email (only if it matches the old email).
   * 2. Updates the user's email in the authentication service (Firebase).
   * 3. Updates the user's email in the users repository (Postgres).
   */
  async updateUserEmails(args: UpdateUserEmails): Promise<UpdateUserEmailsRes[]> {
    const results: UpdateUserEmailsRes[] = [];
    const oldEmails = args.emailPairs.map((pair) => pair.oldEmail);
    const users = await this.usersRepository.getUsers({ emails: oldEmails });

    await isValidEmailDomain(args.emailPairs.map((pair) => pair.newEmail));

    await this.validateEmails({ emailPairs: args.emailPairs, users });
    const oldEmailsGrouped = groupBy(args.emailPairs, (emailPair) => emailPair.oldEmail);

    for (const user of users) {
      const newEmail = oldEmailsGrouped[user.email][0].newEmail;
      const oldEmail = user.email;

      try {
        await this.updateUserDefaultRecords({ newEmail, user });
        await this.authenticationsProvider.updateAuthUser({ email: newEmail, userId: user.id });
        await this.usersRepository.updateUser({ email: newEmail, id: user.id });
        results.push({ newEmail, oldEmail });
      } catch (error) {
        results.push({
          error: error instanceof Error ? error.message : JSON.stringify(error),
          newEmail,
          oldEmail
        });
      }
    }
    return results;
  }

  /**
   * Validates the provided email pairs and users.
   *
   * This method performs the following checks:
   * 1. Each email must be valid.
   * 2. Ensures no duplicate old or new emails within the provided email pairs.
   * 3. All old emails must correspond to an existing user.
   * 4. None of the new emails should already exist.
   *
   * If any of these checks fail, an `InvalidEmailsError` is thrown with the list of invalid emails.
   *
   * @param args - The arguments containing email pairs and users to validate.
   * @throws {InvalidEmailsError} - If any of the validation checks fail.
   */
  private async validateEmails(args: { emailPairs: UpdateUserEmails['emailPairs']; users: User[] }): Promise<void> {
    const invalidEmails: string[] = [];
    const newEmails = args.emailPairs.map((pair) => pair.newEmail);
    const newUsers = await this.usersRepository.getUsers({ emails: newEmails });

    const oldEmailsGrouped = groupBy(args.emailPairs, (emailPair) => emailPair.oldEmail);
    const newEmailsGrouped = groupBy(args.emailPairs, (emailPair) => emailPair.newEmail);

    args.emailPairs.forEach((pair) => {
      if (!isValidEmail(pair.oldEmail)) {
        invalidEmails.push(pair.oldEmail);
      }
      if (!isValidEmail(pair.newEmail)) {
        invalidEmails.push(pair.newEmail);
      }

      if (oldEmailsGrouped[pair.oldEmail].length > 1) {
        invalidEmails.push(pair.oldEmail);
      }

      if (newEmailsGrouped[pair.newEmail].length > 1) {
        invalidEmails.push(pair.newEmail);
      }

      if (!some(args.users, (user) => user.email === pair.oldEmail)) {
        invalidEmails.push(pair.oldEmail);
      }
    });

    forEach(newUsers, (newUser) => invalidEmails.push(newUser.email));

    if (invalidEmails.length > 0) {
      throw new InvalidEmailsError(invalidEmails);
    }
  }

  private async updateUserDefaultRecords(args: { newEmail: string; user: User }): Promise<void> {
    const defaultRecords = await this.defaultRecordsApiService.getDefaultRecords({
      legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES',
      userId: args.user.id
    });

    for (const defaultRecord of defaultRecords) {
      const legalRecord = await this.legalsApiService.getLegalRecord(defaultRecord.recordId);

      if (legalRecord.answer.email.value === args.user.email) {
        await this.legalsApiService.updateRecord({
          answer: {
            email: { value: args.newEmail }
          },
          recordId: legalRecord.id
        });
      }
    }
  }
}

interface UpdateUserEmails {
  emailPairs: { newEmail: string; oldEmail: string }[];
}

interface UpdateUserEmailsRes {
  error?: string;
  newEmail: string;
  oldEmail: string;
}
