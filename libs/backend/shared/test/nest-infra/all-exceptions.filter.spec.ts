import { Test } from '@nestjs/testing';
import { APP_FILTER } from '@nestjs/core';
import { AllExceptionsFilter } from '@mynotary/backend/shared/nest-infra';
import { Controller, ForbiddenException, Get } from '@nestjs/common';
import { Exception, InvalidInputError, NotFoundError } from '@mynotary/crossplatform/shared/util';
import supertest from 'supertest';

describe(AllExceptionsFilter.name, () => {
  it('should respond with 500 and generic message when Exception is thrown', async () => {
    const { client } = await createApp();

    const response = await client.get('/exception');

    expect(response.statusCode).toEqual(500);
    expect(response.body).toEqual({
      title: 'Internal server error',
      type: 'InternalServerError'
    });
  });

  it('should respond with 500 and generic message when Error is thrown', async () => {
    const { client } = await createApp();

    const response = await client.get('/custom-error');

    expect(response.statusCode).toEqual(500);
    expect(response.body).toEqual({
      title: 'Internal server error',
      /* We want to make sure that the error type is not forwarded.*/
      type: 'InternalServerError'
    });
  });

  it('should respond with 404 and message when NotFoundError is thrown', async () => {
    const { client } = await createApp();

    const response = await client.get('/not-found');

    expect(response.statusCode).toEqual(404);
    expect(response.body).toEqual({
      title: 'user with id "1" not found',
      type: 'NotFoundError'
    });
  });

  it('should respond with 400, the title and the provided message when InvalidInputError is thrown', async () => {
    const { client } = await createApp();

    const response = await client.get('/invalid-input');

    expect(response.statusCode).toEqual(400);
    expect(response.body).toEqual({
      displayedMessage: `L'adresse email "<EMAIL>" est déjà utilisée.`,
      title: 'Email already used',
      type: 'DuplicateEmailError'
    });
  });

  it('should forward Nest errors properly', async () => {
    const { client } = await createApp();

    const response = await client.get('/forbidden');

    expect(response.statusCode).toEqual(403);
    expect(response.body).toEqual({
      title: 'This is forbidden',
      type: 'ForbiddenException'
    });
  });
});

async function createApp() {
  const moduleRef = await Test.createTestingModule({
    controllers: [TestController],
    providers: [{ provide: APP_FILTER, useClass: AllExceptionsFilter }]
  }).compile();

  const app = await moduleRef.createNestApplication().init();
  const client = supertest(app.getHttpServer());
  return { client };
}

@Controller()
class TestController {
  @Get('/custom-error')
  getCustomError() {
    throw new MyCustomError();
  }

  @Get('/exception')
  getException() {
    throw new Exception('Something impossible happened');
  }

  @Get('/forbidden')
  getForbidden() {
    throw new ForbiddenException('This is forbidden');
  }

  @Get('/invalid-input')
  getInvalidInput() {
    throw new DuplicateEmailError('<EMAIL>');
  }

  @Get('/not-found')
  getNotFound() {
    throw new NotFoundError({ id: '1', resource: 'user' });
  }
}

class DuplicateEmailError extends InvalidInputError {
  override name = DuplicateEmailError.name;
  constructor(email: string) {
    super({
      displayedMessage: `L'adresse email "${email}" est déjà utilisée.`,
      message: 'Email already used'
    });
  }
}

class MyCustomError extends Error {
  override name = MyCustomError.name;
  constructor() {
    super('My custom error.');
  }
}
