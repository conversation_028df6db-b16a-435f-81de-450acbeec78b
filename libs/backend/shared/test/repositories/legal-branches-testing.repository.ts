import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { LegalLinksTestingRepository } from './legal-links-testing.repository';
import {
  LegalLinkTemplateId,
  LegalOperationTemplateId,
  LegalRecordTemplateId
} from '@mynotary/crossplatform/legal-templates/api';
import { AnswerDict } from '@mynotary/crossplatform/records/api';

export class LegalBranchesTestingRepository {
  constructor(private prisma: PrismaService) {}

  legalLinksRepository: LegalLinksTestingRepository = new LegalLinksTestingRepository(this.prisma);

  async createLegalBranch({
    fromLegalId,
    legalLinkTemplateId,
    organizationId,
    recordId,
    specificContractId,
    toId,
    type,
    userId
  }: {
    fromLegalId: string;
    legalLinkTemplateId?: LegalLinkTemplateId;
    organizationId: string;
    recordId?: string;
    specificContractId?: string;
    toId?: string;
    type: string;
    userId: string;
  }) {
    const link = await this.legalLinksRepository.createLegalLink({
      legalLinkTemplateId: legalLinkTemplateId,
      organizationId,
      recordId,
      userId
    });

    const branch = await this.prisma.legal_component_branch.create({
      data: {
        from_id: parseInt(fromLegalId),
        link_id: parseInt(link.id),
        specific_contract_id: specificContractId ? parseInt(specificContractId) : undefined,
        to_id: toId ? parseInt(toId) : undefined,
        type
      },
      select: { id: true }
    });

    return {
      fromLegalId,
      id: branch.id.toString(),
      linkId: link.id,
      toId
    };
  }

  async findLegalBranch({ branchId }: { branchId: string }) {
    const branch = await this.prisma.legal_component_branch.findUnique({
      select: {
        id: true,
        to_id: true
      },
      where: { id: parseInt(branchId) }
    });

    return {
      id: branch?.id.toString(),
      toId: branch?.to_id?.toString()
    };
  }

  async getLegalBranches({ fromLegalId }: { fromLegalId: string }): Promise<TestingBranch[]> {
    const branches = await this.prisma.legal_component_branch.findMany({
      select: {
        id: true,
        legal_component_legal_component_branch_to_idTolegal_component: {
          select: {
            legal_component_record: {
              select: {
                answer: true
              }
            },
            template_id_str: true
          }
        },
        legal_component_link: {
          select: {
            legal_component: {
              select: {
                id: true,
                template_id_str: true
              }
            }
          }
        },
        specific_contract_id: true,
        to_id: true,
        type: true
      },
      where: { from_id: parseInt(fromLegalId) }
    });

    return branches.map((branch) => ({
      fromLegalId: fromLegalId,
      id: branch.id.toString(),
      legalLinkId: branch.legal_component_link.legal_component.id.toString(),
      legalLinkTemplateId: branch.legal_component_link.legal_component.template_id_str as LegalLinkTemplateId,
      specificContractId: branch.specific_contract_id?.toString(),
      toLegalId: branch.to_id?.toString(),
      toLegalRecordAnswer: branch.legal_component_legal_component_branch_to_idTolegal_component?.legal_component_record
        ?.answer.answer as AnswerDict,
      toLegalTemplateId: branch.legal_component_legal_component_branch_to_idTolegal_component?.template_id_str as
        | LegalRecordTemplateId
        | LegalOperationTemplateId,
      type: branch.type
    }));
  }

  async deleteLegalBranch(id: string) {
    await this.prisma.legal_component_branch.deleteMany({
      where: { id: parseInt(id) }
    });
  }
}

interface TestingBranch {
  fromLegalId: string;
  id: string;
  legalLinkId: string;
  legalLinkTemplateId: LegalLinkTemplateId;
  specificContractId?: string;
  toLegalId?: string;
  toLegalRecordAnswer?: AnswerDict;
  toLegalTemplateId?: LegalRecordTemplateId | LegalOperationTemplateId;
  type: string;
}
