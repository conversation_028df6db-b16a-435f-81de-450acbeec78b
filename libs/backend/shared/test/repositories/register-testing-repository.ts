import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { RegisterEntryStatus } from '@mynotary/backend/registers/api';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';

export class RegisterTestingRepository {
  constructor(private prisma: PrismaService) {}

  async createRegisterEntry(args: CreateRegisterEntryArgs) {
    const entry = await this.prisma.register_entry.create({
      data: {
        answer: {},
        contract_id: parseInt(args.contractId),
        creator_user_id: parseInt(args.userId),
        feature_id: parseInt(args.featureId),
        organization_id: parseInt(args.organizationId),
        status: args.status,
        type: 'TRANSACTION'
      }
    });

    return {
      id: entry.id
    };
  }

  async getRegisterEntry(id: string) {
    const entry = await this.prisma.register_entry.findUnique({
      where: { id: parseInt(id) }
    });
    assertNotNull(entry, 'regsiter entry');

    return { status: entry.status };
  }
}

interface CreateRegisterEntryArgs {
  contractId: string;
  featureId: string;
  organizationId: string;
  status: RegisterEntryStatus;
  userId: string;
}
