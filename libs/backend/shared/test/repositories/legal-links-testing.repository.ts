import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { LegalLinkTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { TemplatesTestingRepository } from './templates-testing.repository';

export class LegalLinksTestingRepository {
  constructor(private prisma: PrismaService) {}

  templatesTestingRepository: TemplatesTestingRepository = new TemplatesTestingRepository(this.prisma);

  async createLegalLink({
    legalLinkTemplateId,
    organizationId,
    recordId,
    userId
  }: {
    legalLinkTemplateId?: LegalLinkTemplateId;
    organizationId: string;
    recordId?: string;
    userId: string;
  }) {
    const templateId = legalLinkTemplateId ?? 'LINK__SITUATION_MARITALE__MARIAGE';
    const template = await this.templatesTestingRepository.createTemplate(templateId);
    const { id } = await this.prisma.legal_component.create({
      data: {
        creator_user_id: userId ? parseInt(userId) : undefined,
        organization_id: parseInt(organizationId),
        template_id: template.idNum,
        template_id_str: templateId
      },
      select: { id: true }
    });

    const link = await this.prisma.legal_component_link.create({
      data: {
        legal_component_id: id,
        record_id: recordId ? parseInt(recordId) : undefined
      },
      select: { legal_component_id: true }
    });

    return {
      id: link.legal_component_id.toString()
    };
  }

  async findLegalLink({ linkId }: { linkId: string }) {
    const link = await this.prisma.legal_component_link.findUnique({
      select: {
        legal_component_id: true
      },
      where: { legal_component_id: parseInt(linkId) }
    });

    return {
      id: link?.legal_component_id.toString()
    };
  }
}
