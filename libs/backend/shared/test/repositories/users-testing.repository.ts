import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { randomUUID } from 'crypto';

type AdminType = 'COLLABORATORS' | 'DEV' | 'SUPPORT';

export class UsersTestingRepository {
  constructor(private prisma: PrismaService) {}

  async createUser({
    adminType,
    authToken,
    email,
    firstname,
    isAdmin = false,
    lastname,
    verified = true
  }: {
    adminType?: AdminType;
    authToken?: string;
    email?: string;
    firstname?: string;
    isAdmin?: boolean;
    lastname?: string;
    verified?: boolean;
  } = {}) {
    const uuid = randomUUID();

    const user = await this.prisma.user.create({
      data: {
        admin_type: adminType,
        authentication_token: authToken,
        civility: 'MAN',
        email: email ?? `${uuid}@mynotary.fr`,
        firstname: firstname ?? 'Foo',
        is_admin: isAdmin,
        lastname: lastname ?? 'BAR',
        verified
      },
      select: {
        email: true,
        firstname: true,
        id: true,
        lastname: true
      }
    });

    const updatedEmail = await this.prisma.user.update({
      data: { email: `user-${user.id}@mynotary.fr` },
      select: {
        email: true,
        id: true
      },
      where: { id: user.id }
    });

    return {
      email: updatedEmail.email,
      firstname: user.firstname,
      id: user.id.toString(),
      lastname: user.lastname
    };
  }
  /**
   *
   * create a non mynotary user
   */
  async createExternalUser({ adminType, verified = true }: { adminType?: AdminType; verified?: boolean } = {}) {
    const uuid = randomUUID();

    const user = await this.prisma.user.create({
      data: {
        admin_type: adminType,
        civility: 'MAN',
        email: `${uuid}@test.fr`,
        firstname: 'Foo',
        is_admin: false,
        lastname: 'BAR',
        verified
      },
      select: {
        email: true,
        firstname: true,
        id: true,
        lastname: true
      }
    });

    const updatedEmail = await this.prisma.user.update({
      data: { email: `user-${user.id}@test.fr` },
      select: {
        email: true,
        id: true
      },
      where: { id: user.id }
    });

    return {
      email: updatedEmail.email,
      firstname: user.firstname,
      id: user.id.toString(),
      lastname: user.lastname
    };
  }

  async deleteUser(id: string) {
    return await this.prisma.user.deleteMany({
      where: { id: parseInt(id) }
    });
  }

  async getUser(id: string) {
    const user = await this.prisma.user.findUnique({
      select: {
        email: true,
        id: true,
        last_authentication_time: true
      },
      where: { id: parseInt(id) }
    });

    if (!user) {
      throw new Error('User not found');
    }

    return {
      email: user.email,
      lastAuthenticationTime: user?.last_authentication_time,
      userId: user.id.toString()
    };
  }
}
