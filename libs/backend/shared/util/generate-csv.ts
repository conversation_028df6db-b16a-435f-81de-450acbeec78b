import { map } from 'lodash';

export function generateCSV<T>({
  headers,
  rows,
  separator = ';'
}: {
  headers: { id: keyof T; label: string }[];
  rows: T[];
  separator?: string;
}): string {
  const csv = map(headers, (header) => header.label).join(separator) + '\n';
  const rowsCSV = map(rows, (row) =>
    map(headers, (header) => {
      return `${row[header.id]}`;
    }).join(separator)
  ).join('\n');

  return csv + rowsCSV;
}
