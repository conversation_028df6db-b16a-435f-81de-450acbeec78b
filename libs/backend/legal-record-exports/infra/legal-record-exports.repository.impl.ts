import { answer, PrismaService, legal_component } from '@mynotary/backend/shared/prisma-infra';
import { Injectable } from '@nestjs/common';
import { LegalRecordExportsRepository, FindLegalRecordExportsArgs } from '@mynotary/backend/legal-record-exports/core';
import { assertIsAnswer, LegalRecord } from '@mynotary/crossplatform/records/api';
import { LegalRecordTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { TypeFiche } from '@mynotary/crossplatform/legal-record-exports/core';
import { assertNotNull, Exception } from '@mynotary/crossplatform/shared/util';

@Injectable()
export class LegalRecordExportsRepositoryImpl implements LegalRecordExportsRepository {
  constructor(private prisma: PrismaService) {}

  async getRecordsPerYear(args: FindLegalRecordExportsArgs): Promise<LegalRecord[]> {
    const legalTemplates = this.getLegalRecordTemplates(args.type);

    const legalRecords: DbRecord[] = await this.prisma.legal_component_record.findMany({
      select: {
        answer: {
          select: {
            answer: true,
            id: true
          }
        },
        answer_id: true,
        legal_component: {
          select: {
            creation_time: true,
            id: true,
            organization_id: true,
            template_id_str: true,
            user: {
              select: {
                firstname: true,
                id: true,
                lastname: true
              }
            }
          }
        }
      },
      where: {
        legal_component: {
          creation_time: {
            gte: args.startDate,
            lte: args.endDate
          },
          deleted: false,
          organization_id: parseInt(args.organizationId),
          template_id_str: {
            in: legalTemplates
          }
        }
      }
    });

    return legalRecords.map((dbRecord) => this.dbRecordToRecord(dbRecord));
  }

  private getLegalRecordTemplates(type: TypeFiche): LegalRecordTemplateId[] {
    switch (type) {
      case TypeFiche.PERSONNE_MORALE:
        return personneMoraleLegalRecordTemplates;
      case TypeFiche.PERSONNE_PHYSIQUE:
        return personnePhysiqueLegalRecordTemplates;
      case TypeFiche.BIEN:
        return bienLegalRecordTemplates;
      default:
        throw new Exception(`Unknown type: ${type}`);
    }
  }

  private dbRecordToRecord(dbRecord: DbRecord): LegalRecord {
    const answer = dbRecord.answer?.answer;
    const user = dbRecord.legal_component.user;
    const id = dbRecord.legal_component.id;

    assertNotNull(answer, `Answer is null for record ${id}`);
    assertNotNull(user, `User is null for record ${id}`);
    assertNotNull(dbRecord.legal_component.organization_id, `Organization id is null for record ${id}`);

    assertIsAnswer(answer);

    return {
      answer,
      answerId: dbRecord.answer_id.toString() ?? '',
      createdAt: dbRecord.legal_component.creation_time.toISOString(),
      creator: {
        firstname: user.firstname.toString(),
        id: user.id.toString(),
        lastname: user.lastname.toString()
      },
      id: id.toString(),
      organizationId: dbRecord.legal_component.organization_id.toString(),
      templateId: dbRecord.legal_component.template_id_str as LegalRecordTemplateId
    };
  }
}

type DbRecord = {
  answer: { answer: answer['answer'] } | null;
  answer_id: number;
  legal_component: {
    creation_time: legal_component['creation_time'];
    id: legal_component['id'];
    organization_id: legal_component['organization_id'];
    template_id_str: legal_component['template_id_str'];
    user: DbRecordCreator | null;
  };
};

type DbRecordCreator = { firstname: string; id: number; lastname: string };

const personneMoraleLegalRecordTemplates: LegalRecordTemplateId[] = ['RECORD__PERSONNE__MORALE'];

const personnePhysiqueLegalRecordTemplates: LegalRecordTemplateId[] = [
  'RECORD__PERSONNE__PHYSIQUE',
  'RECORD__PERSONNE__FISICA'
];

const bienLegalRecordTemplates: LegalRecordTemplateId[] = [
  'RECORD__BIEN__INDIVIDUEL_HABITATION',
  'RECORD__BIEN__INDIVIDUEL_HORS_HABITATION',
  'RECORD__BIEN__LOT_HABITATION',
  'RECORD__BIEN__LOT_HORS_HABITATION',
  'RECORD__BIEN__MONOPROPRIETE_HABITATION',
  'RECORD__BIEN__MONOPROPRIETE_HORS_HABITATION',
  'RECORD__BIEN__TERRAIN_CONSTRUCTIBLE',
  'RECORD__BIEN__TERRAIN_NON_CONSTRUCTIBLE',
  'RECORD__STRUCTURE__COPROPRIETE',
  'RECORD__STRUCTURE__ENSEMBLE_IMMOBILIER',
  'RECORD__STRUCTURE__LOTISSEMENT',
  'RECORD__STRUCTURE__VOLUME'
];
