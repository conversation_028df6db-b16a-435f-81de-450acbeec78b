import { Injectable } from '@nestjs/common';
import { LocalqueuesRepository } from './localqueues.repository';
import {
  LocalqueuesEnum,
  pushMessageArgs,
  WatchFolderArgs,
  watchQueueArgs
} from '@mynotary/crossplatform/localqueues/api';

@Injectable()
export class LocalqueuesService {
  constructor(private localqueuesRepository: LocalqueuesRepository) {}

  async pushMessage(args: pushMessageArgs) {
    await this.localqueuesRepository.pushMessage(args);
  }

  async watchQueue<T>(args: watchQueueArgs<T>) {
    return await this.localqueuesRepository.watchQueue<T>(args);
  }

  async watchFolder<T>(args: WatchFolderArgs<T>) {
    return await this.localqueuesRepository.watchFolder<T>(args);
  }

  async unwatch(watcherNumber: number) {
    await this.localqueuesRepository.unwatch(watcherNumber);
  }

  async unwatchAll() {
    await this.localqueuesRepository.unwatchAll();
  }

  async replayQueue(queue: LocalqueuesEnum) {
    await this.localqueuesRepository.replayQueue(queue);
  }
}
