import { LocalqueuesRepository } from '@mynotary/backend/localqueues/core';
import { Test, TestingModule } from '@nestjs/testing';
import path from 'path';
import fs from 'fs';
import { LocalqueuesEnum } from '@mynotary/crossplatform/localqueues/api';
import { provideLocalqueuesTest } from '../index';

describe('LocalQueuesRepositoryImpl', () => {
  let localqueuesRepository: LocalqueuesRepository;
  type MessageType = { message: string };
  const WAIT_STEP_MS = 10;
  const waitFileEvent = (ms = WAIT_STEP_MS) => new Promise((resolve) => setTimeout(resolve, ms));

  /**
   * Simple test case for watchQueue/pushMessage
   */
  it('should watch a simple queue', async () => {
    await setup();
    let received = false;
    await localqueuesRepository.watchQueue<MessageType>({
      callback: async (__id, message) => {
        if (message.message === 'hello') {
          received = true;
        }
        return true;
      },
      queue: LocalqueuesEnum.CARDREADER_SIGN_IN
    });

    await localqueuesRepository.pushMessage({
      id: '1',
      message: { message: 'hello' },
      queue: LocalqueuesEnum.CARDREADER_SIGN_IN
    });

    let wait = 0;
    while (!received && wait < 5000) {
      await waitFileEvent(WAIT_STEP_MS);
      wait += WAIT_STEP_MS;
    }
    expect(received).toBe(true);
  });

  /**
   * Test case for forwardToQueue
   */
  it('should forward to the next queue', async () => {
    await setup();
    let received = '';
    await localqueuesRepository.watchQueue<MessageType>({
      callback: async (__id, message) => {
        received = message.message;
        return true;
      },
      queue: LocalqueuesEnum.CARDREADER_SIGN_OUT
    });

    await localqueuesRepository.watchQueue<MessageType>({
      callback: async (__id, message) => {
        message.message += ', world';
        return message;
      },
      forwardToQueue: LocalqueuesEnum.CARDREADER_SIGN_OUT,
      queue: LocalqueuesEnum.CARDREADER_SIGN_IN
    });

    await localqueuesRepository.pushMessage({
      id: '2',
      message: { message: 'hello' },
      queue: LocalqueuesEnum.CARDREADER_SIGN_IN
    });
    let wait = 0;
    while (received === '' && wait < 5000) {
      await waitFileEvent(WAIT_STEP_MS);
      wait += WAIT_STEP_MS;
    }
    expect(received).toBe('hello, world');
  });

  /**
   * tests the replayQueue feature
   */
  it('should replay the event if file is touched', async () => {
    await setup();
    let nbTimes = 0;
    await localqueuesRepository.watchQueue<MessageType>({
      callback: async (__id, message) => {
        if (message.message === 'hello') {
          nbTimes++;
        }
        return nbTimes > 1; // delete the message after 2 times
      },
      queue: LocalqueuesEnum.CARDREADER_SIGN_IN
    });

    await localqueuesRepository.pushMessage({
      id: '1',
      message: { message: 'hello' },
      queue: LocalqueuesEnum.CARDREADER_SIGN_IN
    });
    let wait = 0;
    while (nbTimes === 0 && wait < 5000) {
      await waitFileEvent(WAIT_STEP_MS);
      wait += WAIT_STEP_MS;
    }

    expect(nbTimes).toBe(1);
    await localqueuesRepository.replayQueue(LocalqueuesEnum.CARDREADER_SIGN_IN);
    wait = 0;
    while (nbTimes === 1 && wait < 5000) {
      await waitFileEvent(WAIT_STEP_MS);
      wait += WAIT_STEP_MS;
    }
    expect(nbTimes).toBe(2);
  });

  afterEach(async () => {
    await localqueuesRepository.unwatchAll();
  });

  afterAll(async () => {
    const queueFolder = path.join(process.cwd(), 'queues');
    fs.rm(queueFolder, { force: true, recursive: true }, () => {
      /* ignore */
    });
  });

  async function setup() {
    const module: TestingModule = await Test.createTestingModule({
      providers: [...provideLocalqueuesTest()]
    }).compile();
    localqueuesRepository = module.get<LocalqueuesRepository>(LocalqueuesRepository);
  }
});
