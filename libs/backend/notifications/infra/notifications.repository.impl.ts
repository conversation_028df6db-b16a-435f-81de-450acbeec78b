import { PrismaService, notification, user_notification } from '@mynotary/backend/shared/prisma-infra';
import { Injectable } from '@nestjs/common';
import {
  NotificationsRepository,
  GetNotificationsArgs,
  UnseenNotificationCount,
  GetUnseenNotificationsArgs,
  UpdateUserNotificationArgs,
  UpdateUserNotificationRes,
  NotificationNew,
  TaskNotificationNew,
  SignatureNotificationNew,
  LetterNotificationNew,
  AuthNotification
} from '@mynotary/backend/notifications/core';
import { Exception, JSONValue, convertEnum } from '@mynotary/crossplatform/shared/util';
import {
  SignatureNotificationType,
  TaskNotification,
  BaseNotification,
  SignatureNotification,
  UserNotification,
  TaskNotificationType,
  LetterNotification,
  LetterNotificationType,
  NotificationType,
  OrderNotificationType,
  OrderNotification
} from '@mynotary/crossplatform/notifications/core';

@Injectable()
export class NotificationsRepositoryImpl implements NotificationsRepository {
  constructor(private prisma: PrismaService) {}

  async getNotifications(args: GetNotificationsArgs): Promise<UserNotification[]> {
    const notifications = await this.prisma.user_notification.findMany({
      orderBy: {
        notification: {
          creation_time: 'desc'
        }
      },
      select: SELECTED_USER_NOTIFICATION,
      take: 100,
      where: {
        archived: args.archived != null ? args.archived : undefined,
        seen: args.unseen != null ? !args.unseen : undefined,
        user_id: args.userId != null ? parseInt(args.userId) : undefined
      }
    });

    return notifications.map((notification) => convertDbToNotification(notification));
  }

  async createNotifications(args: NotificationNew): Promise<void> {
    const notificationData = await this.prisma.notification.create({
      data: createDbNotificationData(args)
    });

    await this.createUserNotifications({ notificationId: notificationData.id, userIds: args.userIds });
  }

  async updateNotification(args: UpdateUserNotificationArgs): Promise<UpdateUserNotificationRes> {
    const res = await this.prisma.user_notification.update({
      data: { archived: args.archived, seen: args.seen },
      select: {
        user_id: true
      },
      where: { id: parseInt(args.id) }
    });

    return {
      userId: res.user_id.toString()
    };
  }

  async getUnseenNotifications(args: GetUnseenNotificationsArgs): Promise<UnseenNotificationCount> {
    const count = await this.prisma.user_notification.count({
      where: {
        archived: false,
        seen: false,
        user_id: parseInt(args.userId)
      }
    });

    return {
      count
    };
  }

  async markNotificationAsSeen(userId: string): Promise<void> {
    await this.prisma.user_notification.updateMany({
      data: { seen: true },
      where: { user_id: parseInt(userId) }
    });
  }

  async findAuthNotification(id: string): Promise<AuthNotification | null> {
    const notification = await this.prisma.user_notification.findUnique({
      select: {
        id: true,
        user_id: true
      },
      where: {
        id: parseInt(id)
      }
    });

    if (notification == null) {
      return null;
    }

    return {
      id: notification.id.toString(),
      userId: notification.user_id?.toString()
    };
  }

  private async createUserNotifications({ notificationId, userIds }: UserNotificationsNew): Promise<void> {
    const userNotificationsData = userIds.map((userId) => ({
      archived: false,
      notification_id: notificationId,
      seen: false,
      user_id: parseInt(userId)
    }));

    await this.prisma.user_notification.createMany({
      data: userNotificationsData
    });
  }
}

const SELECTED_USER_NOTIFICATION = {
  archived: true,
  id: true,
  notification: {
    select: {
      contract_id: true,
      creation_time: true,
      data: true,
      legal_component_operation: {
        select: {
          label: true,
          parent_operation_id: true
        }
      },
      operation_contract: {
        select: {
          label: true
        }
      },
      operation_id: true,
      type: true
    }
  },
  seen: true,
  user_id: true
};

type BaseDbNotification = Pick<user_notification, 'user_id' | 'id' | 'archived' | 'seen'> & {
  notification: Pick<notification, 'data' | 'creation_time' | 'operation_id' | 'contract_id' | 'type'>;
};

type DbNotification = BaseDbNotification & {
  notification: {
    legal_component_operation: { label: string | null; parent_operation_id: number | null } | null;
    operation_contract: { label: string } | null;
  };
};

interface UserNotificationsNew {
  notificationId: number;
  userIds: string[];
}

interface DBTaskNotificationData {
  creatorFirstname: string;
  creatorId: number;
  creatorLastname: string;
  expirationDate?: string;
  receivers?: string[];
  taskId: number;
  title: string;
}

function convertDbToNotification(dbNotification: DbNotification): UserNotification {
  switch (dbNotification.notification.type as NotificationType) {
    case TaskNotificationType.TASK_CUSTOM_COMPLETED:
    case TaskNotificationType.TASK_CUSTOM_CREATED:
    case TaskNotificationType.TASK_CUSTOM_EXPIRED:
    case TaskNotificationType.TASK_CUSTOM_REMINDER:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_COMPLETED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_CREATED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_EXPIRED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_REMINDER:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_COMPLETED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_CREATED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_EXPIRED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_REMINDER:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_COMPLETED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_CREATED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_EXPIRED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_REMINDER:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_COMPLETED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_CREATED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_EXPIRED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_REMINDER:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_COMPLETED:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_CREATED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_COMPLETED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_CREATED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_EXPIRED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_REMINDER:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_COMPLETED:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_CREATED:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_CANCELED:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_CANCELED:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_COMPLETED:
      return convertTaskNotification(dbNotification);
    case SignatureNotificationType.SIGNATURE_SIGNATORY_SIGNED:
    case SignatureNotificationType.SIGNATURE_EXPIRED:
    case SignatureNotificationType.SIGNATURE_CANCELED:
    case SignatureNotificationType.SIGNATURE_COMPLETED:
    case SignatureNotificationType.SIGNATURE_SIGNATORY_ERROR:
      return convertSignatureNotification(dbNotification);
    case LetterNotificationType.LETTER_ACCEPTED:
    case LetterNotificationType.LETTER_ERROR:
    case LetterNotificationType.LETTER_EXPIRED:
    case LetterNotificationType.LETTER_REFUSED:
    case LetterNotificationType.LETTER_SENT:
    case LetterNotificationType.BATCH_ACCEPTED:
      return convertLetterNotification(dbNotification);
    case OrderNotificationType.ORDER_COMPLETED:
    case OrderNotificationType.ORDER_PAYED:
      return convertOrderNotification(dbNotification);
  }
}

function convertTaskNotification(dbNotification: DbNotification): TaskNotification {
  if (dbNotification.notification.operation_id == null) {
    throw new Exception('Operation should not be null');
  }

  const data = dbNotification.notification.data as JSONValue as DBTaskNotificationData;

  return {
    ...convertDbCommonFields(dbNotification),
    data: {
      ...convertDbDataCommonFields(dbNotification),
      creatorFirstname: data.creatorFirstname,
      creatorId: data.creatorId.toString(),
      creatorLastname: data.creatorLastname,
      expirationDate: data.expirationDate,
      receivers: data.receivers,
      title: data.title
    },
    type: convertEnum(TaskNotificationType, dbNotification.notification.type)
  };
}

function convertSignatureNotification(dbNotification: DbNotification): SignatureNotification {
  return {
    ...convertDbCommonFields(dbNotification),
    data: {
      ...convertDbDataCommonFields(dbNotification),
      ...(dbNotification.notification.data as JSONValue)
    },
    type: convertEnum(SignatureNotificationType, dbNotification.notification.type)
  };
}

function convertLetterNotification(dbNotification: DbNotification): LetterNotification {
  return {
    ...convertDbCommonFields(dbNotification),
    data: {
      ...convertDbDataCommonFields(dbNotification),
      ...(dbNotification.notification.data as JSONValue)
    },
    type: convertEnum(LetterNotificationType, dbNotification.notification.type)
  };
}

function convertOrderNotification(dbNotification: DbNotification): OrderNotification {
  return {
    ...convertDbCommonFields(dbNotification),
    data: {
      ...convertDbDataCommonFields(dbNotification),
      ...(dbNotification.notification.data as JSONValue)
    },
    type: convertEnum(OrderNotificationType, dbNotification.notification.type)
  };
}

function convertDbCommonFields(dbNotification: DbNotification): BaseNotification {
  return {
    archived: dbNotification.archived,
    creationDate: dbNotification.notification.creation_time.toISOString(),
    id: dbNotification.id.toString(),
    seen: dbNotification.seen,
    userId: dbNotification.user_id.toString()
  };
}

function convertDbDataCommonFields(dbNotification: DbNotification) {
  if (dbNotification.notification.operation_id == null) {
    throw new Exception('Operation should not be null');
  }

  return {
    contractId: dbNotification.notification.contract_id?.toString(),
    contractLabel: dbNotification.notification.operation_contract?.label?.toString(),
    operationId: dbNotification.notification.operation_id.toString(),
    operationLabel: dbNotification.notification.legal_component_operation?.label?.toString() ?? '',
    parentOperationId: dbNotification.notification.legal_component_operation?.parent_operation_id?.toString()
  };
}

interface DBSignatureSignatorySignedNotificationData {
  signatoryFullName: string;
  signatoryId: string;
  signatureId: string;
  signatureTime: string;
}

interface DBSignatureSignatorySignedNotificationData {
  signatoryFullName: string;
  signatoryId: string;
  signatureId: string;
  signatureTime: string;
}

interface DBSignatureExpiredNotificationData {
  expirationTime: string;
  signatureId: string;
}

interface DBSignatureCanceledNotificationData {
  cancelationTime: string;
  signatureId: string;
}

interface DBSignatureCompletedNotificationData {
  signatureId: string;
  signatureTime: string;
}

interface DBSignatureSignatoryErrorNotificationData {
  cancelationTime: string;
  newSignatureId: string;
  reason: string;
  signatureId: string;
}

interface DBLetterBaseNotificationData {
  batchId: string;
  creatorId: number;
  creatorName: string;
}

interface DBLetterBatchAcceptedNotificationData extends DBLetterBaseNotificationData {
  acceptationTime: string;
}

interface DBLetterAcceptedNotificationData extends DBLetterBaseNotificationData {
  acceptationTime: string;
  letterId: string;
  receiver: string;
}

interface DBLetterErrorNotificationData extends DBLetterBaseNotificationData {
  letterId: string;
  receiver: string;
}

interface DBLetterSentNotificationData extends DBLetterBaseNotificationData {
  depositTime: string;
  letterId: string;
  receiver: string;
}

interface DBLetterNegligenceNotificationData extends DBLetterBaseNotificationData {
  letterId: string;
  negligenceTime: string;
  receiver: string;
}

interface DBLetterRefusalNotificationData extends DBLetterBaseNotificationData {
  letterId: string;
  receiver: string;
  refusalTime: string;
}

interface DBOrderBaseNotificationData {
  documentName: string;
  orderId: string;
}

interface DBOrderCompletedNotificationData extends DBOrderBaseNotificationData {
  completionTime: string;
}

interface DBOrderPayedNotificationData extends DBOrderBaseNotificationData {
  paymentTime: string;
}

const notificationDataBase = (notification: NotificationNew) => ({
  creation_time: new Date(),
  operation_id: parseInt(notification.operationId),
  type: notification.type
});

const notificationContractDataBase = (
  notification: TaskNotificationNew | SignatureNotificationNew | LetterNotificationNew
) => ({
  contract_id: notification.contractId != null ? parseInt(notification.contractId) : undefined,
  ...notificationDataBase(notification)
});

function createDbNotificationData(notification: NotificationNew) {
  switch (notification.type) {
    case TaskNotificationType.TASK_CUSTOM_COMPLETED:
    case TaskNotificationType.TASK_CUSTOM_CREATED:
    case TaskNotificationType.TASK_CUSTOM_EXPIRED:
    case TaskNotificationType.TASK_CUSTOM_REMINDER:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_COMPLETED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_CREATED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_EXPIRED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_REMINDER:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_COMPLETED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_CREATED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_EXPIRED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_REMINDER:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_COMPLETED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_CREATED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_EXPIRED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_REMINDER:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_COMPLETED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_CREATED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_EXPIRED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_REMINDER:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_COMPLETED:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_CREATED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_COMPLETED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_CREATED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_EXPIRED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_REMINDER:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_CANCELED:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_COMPLETED:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_CANCELED:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_CREATED:
      return {
        ...notificationContractDataBase(notification),
        data: {
          creatorFirstname: notification.creatorFirstname,
          creatorId: parseInt(notification.creatorId),
          creatorLastname: notification.creatorLastname,
          expirationDate: notification.expirationDate,
          receivers: notification.receivers,
          taskId: parseInt(notification.taskId),
          title: notification.title
        } satisfies DBTaskNotificationData
      };
    case SignatureNotificationType.SIGNATURE_SIGNATORY_SIGNED:
      return {
        ...notificationContractDataBase(notification),
        data: {
          signatoryFullName: notification.signatoryFullName,
          signatoryId: notification.signatoryId,
          signatureId: notification.signatureId,
          signatureTime: notification.signatureTime
        } satisfies DBSignatureSignatorySignedNotificationData
      };
    case SignatureNotificationType.SIGNATURE_EXPIRED:
      return {
        ...notificationContractDataBase(notification),
        data: {
          expirationTime: notification.expirationTime,
          signatureId: notification.signatureId
        } satisfies DBSignatureExpiredNotificationData
      };
    case SignatureNotificationType.SIGNATURE_CANCELED:
      return {
        ...notificationContractDataBase(notification),
        data: {
          cancelationTime: notification.cancelationTime,
          signatureId: notification.signatureId
        } satisfies DBSignatureCanceledNotificationData
      };

    case SignatureNotificationType.SIGNATURE_COMPLETED:
      return {
        ...notificationContractDataBase(notification),
        data: {
          signatureId: notification.signatureId,
          signatureTime: notification.signatureTime
        } satisfies DBSignatureCompletedNotificationData
      };
    case SignatureNotificationType.SIGNATURE_SIGNATORY_ERROR:
      return {
        ...notificationContractDataBase(notification),
        data: {
          cancelationTime: notification.cancelationTime,
          newSignatureId: notification.newSignatureId,
          reason: notification.reason,
          signatureId: notification.signatureId
        } satisfies DBSignatureSignatoryErrorNotificationData
      };
    case LetterNotificationType.BATCH_ACCEPTED:
      return {
        ...notificationContractDataBase(notification),
        data: {
          acceptationTime: notification.acceptationTime,
          batchId: notification.batchId,
          creatorId: parseInt(notification.creatorId),
          creatorName: notification.creatorName
        } satisfies DBLetterBatchAcceptedNotificationData
      };
    case LetterNotificationType.LETTER_ACCEPTED:
      return {
        ...notificationContractDataBase(notification),
        data: {
          acceptationTime: notification.acceptationTime,
          batchId: notification.batchId,
          creatorId: parseInt(notification.creatorId),
          creatorName: notification.creatorName,
          letterId: notification.letterId,
          receiver: notification.receiver
        } satisfies DBLetterAcceptedNotificationData
      };
    case LetterNotificationType.LETTER_ERROR:
      return {
        ...notificationContractDataBase(notification),
        data: {
          batchId: notification.batchId,
          creatorId: parseInt(notification.creatorId),
          creatorName: notification.creatorName,
          letterId: notification.letterId,
          receiver: notification.receiver
        } satisfies DBLetterErrorNotificationData
      };
    case LetterNotificationType.LETTER_EXPIRED:
      return {
        ...notificationContractDataBase(notification),
        data: {
          batchId: notification.batchId,
          creatorId: parseInt(notification.creatorId),
          creatorName: notification.creatorName,
          letterId: notification.letterId,
          negligenceTime: notification.negligenceTime,
          receiver: notification.receiver
        } satisfies DBLetterNegligenceNotificationData
      };
    case LetterNotificationType.LETTER_REFUSED:
      return {
        ...notificationContractDataBase(notification),
        data: {
          batchId: notification.batchId,
          creatorId: parseInt(notification.creatorId),
          creatorName: notification.creatorName,
          letterId: notification.letterId,
          receiver: notification.receiver,
          refusalTime: notification.refusalTime
        } satisfies DBLetterRefusalNotificationData
      };
    case LetterNotificationType.LETTER_SENT:
      return {
        ...notificationContractDataBase(notification),
        data: {
          batchId: notification.batchId,
          creatorId: parseInt(notification.creatorId),
          creatorName: notification.creatorName,
          depositTime: notification.depositTime,
          letterId: notification.letterId,
          receiver: notification.receiver
        } satisfies DBLetterSentNotificationData
      };
    case OrderNotificationType.ORDER_COMPLETED:
      return {
        ...notificationDataBase(notification),
        data: {
          completionTime: notification.completionTime,
          documentName: notification.documentName,
          orderId: notification.orderId
        } satisfies DBOrderCompletedNotificationData
      };
    case OrderNotificationType.ORDER_PAYED:
      return {
        ...notificationDataBase(notification),
        data: {
          documentName: notification.documentName,
          orderId: notification.orderId,
          paymentTime: notification.paymentTime
        } satisfies DBOrderPayedNotificationData
      };
  }
}
