import { Injectable } from '@nestjs/common';
import { ComedecCommuneRepository, FindComedecCommuneArgs } from '@mynotary/backend/referentiels/core';
import { ComedecCommune } from '@mynotary/crossplatform/api-adsn/api';
import { Prisma, PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { XMLParser } from 'fast-xml-parser';
import { Exception } from '@mynotary/crossplatform/shared/util';

@Injectable()
export class ComedecCommuneRepositoryImpl extends ComedecCommuneRepository {
  constructor(private prismaService: PrismaService) {
    super();
  }

  async findComedecCommune({ codeInsee, nom }: FindComedecCommuneArgs): Promise<ComedecCommune[]> {
    try {
      const communesDb = await this.prismaService.ref_comedec_commune.findMany({
        orderBy: { code_insee: 'asc' },
        where: {
          OR: [
            {
              ancien_nom: { contains: nom, mode: 'insensitive' }
            },
            {
              nom: { contains: nom, mode: 'insensitive' }
            }
          ],
          code_insee: codeInsee
        }
      });
      return communesDb.map((commune) => ({
        ancienNom: commune.ancien_nom ?? undefined,
        codeDepartement: commune.code_departement,
        codeInsee: commune.code_insee,
        id: commune.id,
        nom: commune.nom,
        serviceVAD: !!commune.service_vad,
        serviceVAM: !!commune.service_vam,
        serviceVAN: !!commune.service_van
      }));
    } catch (e) {
      throw new Exception('Error fetching comedec commune', { cause: e });
    }
  }

  async updateComedecCommune(update: string): Promise<void> {
    try {
      const listeCommunes = this.parseComedecCommune(update);
      const transaction: Prisma.PrismaPromise<unknown>[] = [];
      transaction.push(this.prismaService.ref_comedec_commune.deleteMany());
      transaction.push(
        this.prismaService.ref_comedec_commune.createMany({
          data: listeCommunes.map((commune) => ({
            ancien_nom: commune.ancienNom,
            code_departement: commune.codeDepartement,
            code_insee: commune.codeInsee,
            nom: commune.nom,
            service_vad: commune.serviceVAD,
            service_vam: commune.serviceVAM,
            service_van: commune.serviceVAN
          }))
        })
      );
      await this.prismaService.$transaction(transaction);
    } catch (e) {
      throw new Exception('Error updating comedec commune', { cause: e });
    }
  }

  parseComedecCommune(update: string, skipLengthCheck?: boolean): ComedecCommune[] {
    const parser = new XMLParser({
      isArray: (tagName) => tagName === 'service',
      parseTagValue: false,
      removeNSPrefix: true
    });
    const jsonObj = parser.parse(update);
    const rootXml = jsonObj['Envelope']['Body']['reponse_liste_communes_abonnees'];
    const communesXml = rootXml['communes']['commune'];
    const codeRetour = rootXml['code_retour'];
    if (codeRetour !== '2') {
      throw new Exception('Error in Referentiel Comedec : code_retour is ' + codeRetour);
    }
    const tailleListe = Number(rootXml['taille_liste']);

    const communes: ComedecCommune[] = communesXml.map((communeXml: ComedecCommuneXml) => ({
      ancienNom: communeXml.ancien_nom_commune_enrichi,
      codeDepartement: communeXml.departement,
      codeInsee: communeXml.insee,
      nom: communeXml.nom_commune_enrichi,
      serviceVAD: communeXml.services.service.includes('NOT:VAD'),
      serviceVAM: communeXml.services.service.includes('NOT:VAM'),
      serviceVAN: communeXml.services.service.includes('NOT:VAN')
    }));

    if (communes.length !== tailleListe) {
      throw new Exception(
        'Referentiel Comedec : taille_liste is ' +
          tailleListe +
          ' but ' +
          communes.length +
          ' communes found. Corrupted file ?'
      );
    }
    if (!skipLengthCheck && communes.length < 500) {
      throw new Exception('Too few communes in Referentiel Comedec. Bad update ?');
    }
    return communes;
  }
}

type ComedecCommuneXml = {
  ancien_nom_commune_enrichi: string;
  departement: string;
  insee: string;
  nom_commune_enrichi: string;
  services: { service: string[] };
};
