import { Injectable } from '@nestjs/common';
import { HoldingIds, OrganizationHoldingsService } from '@mynotary/backend/organization-holdings/core';

@Injectable()
export class OrganizationHoldingsApiService {
  constructor(private organizationHoldingsService: OrganizationHoldingsService) {}

  async findHoldings(organizationId: string): Promise<HoldingIds | null> {
    return this.organizationHoldingsService.findHoldings(organizationId);
  }

  async findSubOrganizationIds(organizationId: string): Promise<string[]> {
    return this.organizationHoldingsService.findSubOrganizationIds(organizationId);
  }
}
