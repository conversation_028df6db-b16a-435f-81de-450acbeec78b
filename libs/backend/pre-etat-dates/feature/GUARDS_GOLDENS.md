# Guards Goldens

  > ⚠ This file is auto-generated. Do not edit by hand.
## pre-etat-dates-webhook.controller.ts
```ts
@UseGuards(
    OrGuard([
      [IsApplication({ applicationName: ApplicationName.SMARTBOOSTER, headerName: 'api-token' })],
      [IsGcpApplication()]
    ])
  )
@Post('/pre-etat-dates-webhook')

```
## pre-etat-dates.controller.ts
```ts
@UseGuards(IsGcpApplication())
@Post('/pre-etat-dates')

```
