import { Body, Controller, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { RolesBffService } from '@mynotary/backend/roles-bff/core';
import { queryResolver } from '@mynotary/backend/shared/auth-util';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { RoleBff } from '@mynotary/crossplatform/bff-portalys/api';
import { HasRoleAccess } from '@mynotary/backend/roles/api';
import {
  HasOrganizationAccess,
  HasOrganizationPermission,
  IsAdmin,
  OrGuard
} from '@mynotary/backend/authorizations/api';

@Controller()
export class RolesBffController {
  constructor(private rolesBffService: RolesBffService) {}

  @UseGuards(IsAdmin())
  @Get('/permissions')
  async getAvailablePermissions(@Query('organizationId') organizationId: string) {
    return await this.rolesBffService.getAvailablePermissions(organizationId);
  }

  @UseGuards(IsAdmin())
  @Post('/role-default-permissions')
  async generateDefaultPermissionsOrganization(@Body('organizationId') organizationId: string) {
    return await this.rolesBffService.generateDefaultPermissionsOrganization(organizationId);
  }

  @UseGuards(
    OrGuard([[HasOrganizationAccess({ organizationIdResolver: queryResolver('organizationId') })], [IsAdmin()]])
  )
  @Get('/roles')
  async getRoles(@Query('organizationId') organizationId: string) {
    return await this.rolesBffService.getRoles(organizationId);
  }

  @UseGuards(
    OrGuard([[HasOrganizationAccess({ organizationIdResolver: queryResolver('organizationId') })], [IsAdmin()]])
  )
  @Get('/roles/:id')
  async getRoleById(@Param('id') id: string) {
    return await this.rolesBffService.getRoleById(id);
  }

  @UseGuards(OrGuard([[IsAdmin()], [HasOrganizationPermission(PermissionType.UDPATE_ORGANIZATION_ROLES)]]))
  @Post('/roles')
  async createRole(@Body() role: RoleBff) {
    return await this.rolesBffService.createRole(role);
  }

  @UseGuards(OrGuard([[IsAdmin()], [HasRoleAccess({ permission: PermissionType.UDPATE_ORGANIZATION_ROLES })]]))
  @Put('/roles/:id')
  async updateRole(@Param('id') id: string, @Body() role: RoleBff) {
    role.id = id;
    return await this.rolesBffService.updateRole(role);
  }
}
