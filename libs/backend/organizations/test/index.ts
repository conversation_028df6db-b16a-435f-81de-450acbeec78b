import { provideOrganizationsScope } from '@mynotary/backend/organizations/providers';
import { provideRolesTest } from '@mynotary/backend/roles/test';
import { provideFeatureOrganizationsTest } from '@mynotary/backend/feature-organizations/test';
import { provideFeaturesTest } from '@mynotary/backend/features/test';
import { provideExternalAppsTest } from '@mynotary/backend/external-apps/test';
import { provideCustomViewsTest } from '@mynotary/backend/custom-views/test';
import { provideAuthorizationsTest } from '@mynotary/backend/authorizations/test';

export const provideOrganizationsTest = () => {
  return [
    ...provideAuthorizationsTest(),
    ...provideOrganizationsScope(),
    ...provideRolesTest(),
    ...provideFeatureOrganizationsTest(),
    ...provideFeaturesTest(),
    ...provideExternalAppsTest(),
    ...provideCustomViewsTest()
  ];
};
