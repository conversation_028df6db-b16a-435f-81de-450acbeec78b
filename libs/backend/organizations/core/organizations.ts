import { NotaryOfficeType, OrganizationType } from '@mynotary/crossplatform/organizations/api';
import { MnAddress, StringDate } from '@mynotary/crossplatform/shared/util';

export type Organization = MnOrganization | PlOrganization;

type CommonOrganization = {
  address: MnAddress;
  adminRoleId: string | null;
  apiKey: string;
  creationTime: StringDate;
  creatorUserId: string | null;
  email: string | null;
  hasHoldingSubscription: boolean;
  hubspotId: string | null;
  id: string;
  name: string;
  rib: string | null;
  subscriptionId: string | null;
  type: OrganizationType;
  uniqueIdentifier: string;
};

export type MnOrganization = CommonOrganization & {
  type:
    | OrganizationType.AGENCY
    | OrganizationType.AGENCY_NETWORK
    | OrganizationType.DEVELOPER
    | OrganizationType.HOUSE_BUILDER
    | OrganizationType.MARKETER
    | OrganizationType.NEGOCIATOR_NETWORK
    | OrganizationType.NOTARY_OFFICE
    | OrganizationType.PUBLIC_HOUSER;
};

export type PlOrganization = CommonOrganization &
  PlOrganizationSpecificFields & {
    type: OrganizationType.PORTALYS_NOTARY_OFFICE;
  };

export type PlOrganizationSpecificFields = {
  antsRecoveryDelay: number | null;
  crpcen: string | null;
  email: string | null;
  fax: string | null;
  iban: Iban | null;
  interopAppId: number | null;
  interopVersion: string | null;
  notaryOfficeType: NotaryOfficeType | null;
  phone: string | null;
};

export interface OrganizationCreateCommon {
  address: MnAddress;
  creatorUserId?: string;
  hubspotId?: string;
  name: string;
  parentOrganizationId?: string;
  ribFileId?: string;
  subscriptionId?: number;
}

type OrganizationNotaryOfficeNew = {
  crpcen: string;
  type: OrganizationType.NOTARY_OFFICE;
} & OrganizationCreateCommon;

export type OrganizationPortalysNew = {
  crpcen: string;
  type: OrganizationType.PORTALYS_NOTARY_OFFICE;
} & OrganizationCreateCommon &
  PlOrganizationSpecificFields;

type OrganizationOtherNew = {
  siren: string;
  type:
    | OrganizationType.AGENCY_NETWORK
    | OrganizationType.DEVELOPER
    | OrganizationType.HOUSE_BUILDER
    | OrganizationType.NEGOCIATOR_NETWORK
    | OrganizationType.PUBLIC_HOUSER
    | OrganizationType.MARKETER
    | OrganizationType.AGENCY;
} & OrganizationCreateCommon;

export type OrganizationNew = OrganizationNotaryOfficeNew | OrganizationOtherNew | OrganizationPortalysNew;

export type OrganizationUpdate = {
  address?: MnAddress;
  antsRecoveryDelay?: number | null;
  crpcen?: string;
  email?: string | null;
  fax?: string | null;
  hubspotId?: string | null;
  iban?: Iban | null;
  id?: string;
  interopAppId?: number;
  interopVersion?: string;
  name?: string;
  notaryOfficeType?: NotaryOfficeType;
  phone?: string | null;
  rib?: string | null;
  siren?: string;
  subscriptionId?: string | null;
  uniqueIdentifier?: string;
};

type Iban = {
  bban: string;
  bic: string;
  cle: string;
  domiciliation: string;
  guichet?: string | null;
  pays: string;
  titulaire?: string | null;
};

export function isNotaryOffice(args: OrganizationNew): args is OrganizationNotaryOfficeNew {
  return (
    (args.type === OrganizationType.NOTARY_OFFICE || args.type === OrganizationType.PORTALYS_NOTARY_OFFICE) &&
    args.crpcen != null
  );
}

export function isOtherOrganization(args: OrganizationNew): args is OrganizationOtherNew {
  const maybeOtherOrganization = args as OrganizationOtherNew;
  return (
    [
      OrganizationType.AGENCY_NETWORK,
      OrganizationType.DEVELOPER,
      OrganizationType.HOUSE_BUILDER,
      OrganizationType.NEGOCIATOR_NETWORK,
      OrganizationType.PUBLIC_HOUSER,
      OrganizationType.MARKETER,
      OrganizationType.AGENCY
    ].includes(maybeOtherOrganization.type) && maybeOtherOrganization?.siren != null
  );
}
