import { AssociationsRepository, ExternalAppsRepository } from '@mynotary/backend/external-apps/core';
import { AssociationsRepositoryImpl } from './associations.repository.impl';
import { ExternalAppsRepositoryImpl } from './external-apps.repository.impl';

export const provideExternalAppsInfra = () => {
  return [
    { provide: AssociationsRepository, useClass: AssociationsRepositoryImpl },
    { provide: ExternalAppsRepository, useClass: ExternalAppsRepositoryImpl }
  ];
};
