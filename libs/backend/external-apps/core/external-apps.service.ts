import { Injectable } from '@nestjs/common';
import { ExternalAppsRepository } from './external-apps.repository';

@Injectable()
export class ExternalAppsService {
  constructor(private externalAppsRepository: ExternalAppsRepository) {}

  async deleteApplicationInformations(organizationId: string): Promise<void> {
    await this.externalAppsRepository.deleteApplicationInformations(organizationId);
  }
}
