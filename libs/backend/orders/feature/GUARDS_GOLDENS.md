# Guards Goldens

  > ⚠ This file is auto-generated. Do not edit by hand.
## orders.controller.ts
```ts
@UseGuards(HasOrdersAccess({ organizationIdResolver: queryResolver('organizationId') }))
@Get('/orders')

@UseGuards(
    HasOrderNewAccess({
      destinationLegalRecordIdResolver: bodyResolver('destinationLegalRecordId'),
      operationIdResolver: bodyResolver('operationId'),
      organizationIdResolver: bodyResolver('organizationId')
    })
  )
@Post('/orders')

@UseGuards(HasOrderAccess({ orderIdResolver: pathResolver('id') }))
@Delete('/orders/:id')

```
