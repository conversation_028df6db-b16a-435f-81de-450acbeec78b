import { createTestingWideApp, TestingPermission, TestingRepositories } from '@mynotary/backend/shared/test';
import { Controller, Get, UseGuards } from '@nestjs/common';
import { HasOrderAccess } from '@mynotary/backend/orders/authorization';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';

import { pathResolver } from '@mynotary/backend/shared/auth-util';

import { OrderType } from '@mynotary/crossplatform/orders/core';
import { AuthorizationsApiService } from '@mynotary/backend/authorizations/api';
import { provideOrdersTest } from '../index';

describe(HasOrderAccess.name, () => {
  it('should deny access when the user is a member but not the creator and does not have permissions', async () => {
    const { client, organizationId, testingRepositories, userId } = await setupWithAuthenticatedUser();

    const anotherUser = await testingRepositories.addMember({ organizationId });

    const operation = await testingRepositories.operations.createVenteAncien({ organizationId, userId });

    const order = await testingRepositories.orders.createOrder({
      operationId: operation.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId: anotherUser.userId
    });

    const response = await client.get(`/orders/${order.id}`).send();

    expect(response.statusCode).toBe(403);
  });

  it('should allow access when user is the creator of the order', async () => {
    const { client, organizationId, testingRepositories, userId } = await setupWithAuthenticatedUser();

    const operation = await testingRepositories.operations.createVenteAncien({ organizationId, userId });

    const order = await testingRepositories.orders.createOrder({
      operationId: operation.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });

    const response = await client.get(`/orders/${order.id}`).send({});

    expect(response.statusCode).toBe(200);
  });

  it('should allow access when user is a member with appropriate permissions', async () => {
    const { client, organizationId, testingRepositories, userId } = await setupWithAuthenticatedUser([
      { entityType: EntityType.ORGANIZATION, permissionType: PermissionType.READ_ORGANIZATION_ORDER }
    ]);

    const anotherUser = await testingRepositories.addMember({ organizationId });

    const operation = await testingRepositories.operations.createVenteAncien({ organizationId, userId });

    const order = await testingRepositories.orders.createOrder({
      operationId: operation.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId: anotherUser.userId
    });

    const response = await client.get(`/orders/${order.id}`).send();

    expect(response.statusCode).toBe(200);
  });

  async function setupWithAuthenticatedUser(permissions: TestingPermission[] = []) {
    const utils = await setup(permissions);

    utils.setCurrentUser({ userId: utils.userId });

    return utils;
  }

  async function setup(permissions: TestingPermission[]) {
    const { client, getService, setCurrentUser } = await createTestingWideApp({
      controller: TestController,
      providers: provideOrdersTest()
    });
    const testingRepositories = getService(TestingRepositories);
    const { organizationId, userId } = await testingRepositories.createMember({ permissions });

    return {
      authorizationsApiService: getService(AuthorizationsApiService),
      client,
      getService,
      organizationId,
      setCurrentUser,
      testingRepositories,
      userId
    };
  }
});

@Controller()
class TestController {
  @UseGuards(HasOrderAccess({ orderIdResolver: pathResolver('orderId') }))
  @Get('/orders/:orderId')
  postValue() {
    return '👌';
  }
}
