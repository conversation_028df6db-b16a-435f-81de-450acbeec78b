import { DemandeCopieDocumentFluxProvider } from '@mynotary/backend/demande-copie-document/core';
import {
  DemandeCopieDocument,
  DemandeCopieDocumentType,
  ParametresEtudeType,
  TeleactesDossierType,
  UsagerType,
  VirementStatus
} from '@mynotary/crossplatform/api-teleactes/api';
import { PlaneteApiService } from '@mynotary/backend/planete/api';
import { Operation } from '@mynotary/backend/legals/api';
import {
  InteropDdvFluxBuilderVersion,
  InteropStructureDestinataireType,
  InteropTransactionLibelleType
} from '@mynotary/crossplatform/api-adsn/api';
import { provideDemandeCopieDocumentTest } from '../index';
import { createTestingWideApp } from '@mynotary/backend/shared/test';

describe('DemandeCopieDocumentFluxProviderImpl', () => {
  it('should generate xml', async () => {
    const { planeteApiService, provider } = await setup();
    const parametresEtude: ParametresEtudeType = {
      adresse: {
        codePostal: '97100',
        commune: 'Basse-Terre',
        email: '<EMAIL>',
        fax: '**********',
        lieuDit: 'Moitou',
        numeroEtVoie: '1 rue des fleurs',
        residenceEtBatiment: 'Résidence des Canards',
        telephone: '**********'
      },
      crpcen: '099031',
      iban: {
        bban: '30004000031234567890143', // yes that's a real IBAN... Courtesy of Copilot !!!
        bic: 'BNPAFRPPXXX',
        cle: '76',
        domiciliation: 'BNP PARIBAS',
        pays: 'FR'
      },
      interopAppId: 1,
      interopVersion: InteropDdvFluxBuilderVersion.V4,
      nom: 'SCP BOYER SALETES & ASSOCIES',
      type: UsagerType.SOCIETE_CIVILE_PROFESSIONNELLE
    };

    const demande: DemandeCopieDocument = {
      date: new Date(),
      id: '1234',
      idFluxPlanete: planeteApiService.generateReferenceDossierSysteme({
        crpcen: parametresEtude.crpcen,
        typeDossierTeleactes: TeleactesDossierType.DEMANDE_RENSEIGNEMENT
      }),
      referenceType2: {
        date: new Date('1989-06-16'),
        numero: 4265,
        sages: '5914P03',
        volume: 2011
      },
      sagesEnvoi: '5914P03',
      type: DemandeCopieDocumentType.PUBLICATION_EDD,
      userId: '1',
      virement: {
        compteClientOffice: '9900886',
        date: new Date('2024-02-06'),
        destinataireType: InteropStructureDestinataireType.SPF,
        label: 'Vente X à Y demande document',
        montant: 13.37,
        numero: '1234567',
        status: VirementStatus.PAID,
        typeTransaction: InteropTransactionLibelleType.REQUI
      }
    };
    let xml = provider.buildXml({
      dossier: {
        id: '1234',
        label: 'Dossier 1234'
      } as Operation,
      parametres: parametresEtude,
      produitTeleactes: demande
    });
    expect(xml).toBeDefined();
    xml = xml.replace(
      /reference-dossier-systeme>[A-Z0-9].*?</,
      'reference-dossier-systeme>099031PLYS2024010100000001H1<'
    );
    xml = xml.replace(/horodatage>[^<]*?</, 'horodatage>2024-01-01T00:00:00.000Z<');
    expect(xml).toMatchSnapshot();
  });

  async function setup() {
    const { getService } = await createTestingWideApp({
      providers: provideDemandeCopieDocumentTest()
    });

    return {
      planeteApiService: getService<PlaneteApiService>(PlaneteApiService),
      provider: getService<DemandeCopieDocumentFluxProvider>(DemandeCopieDocumentFluxProvider)
    };
  }
});
