import { Civility } from '@mynotary/crossplatform/shared/users-core';
import { MnAddress } from '@mynotary/crossplatform/shared/util';

export interface OrpiAgency {
  address: MnAddress;
  answers: {
    adresse: Mn<PERSON>ddress;
    agence_adresse: Mn<PERSON>ddress;
    agence_assurance_location_rcp_adresse?: MnAddress;
    agence_assurance_location_rcp_nom?: string;
    agence_assurance_location_rcp_police?: string;
    agence_assurance_rcp_adresse?: MnAddress;
    agence_assurance_rcp_nom?: string;
    agence_assurance_rcp_police?: string;
    agence_assurance_syndic_rcp_adresse?: MnAddress;
    agence_assurance_syndic_rcp_nom?: string;
    agence_assurance_syndic_rcp_police?: string;
    agence_carte_cci: string;
    agence_carte_cci_departement: string;
    agence_carte_cci_statut: 'oui' | 'non';
    agence_carte_date?: number;
    agence_carte_gestion: 'oui' | 'non';
    agence_carte_location_cci?: string;
    agence_carte_location_cci_departement?: string;
    agence_carte_location_date?: number;
    agence_carte_location_numero?: string;
    agence_carte_numero: string;
    agence_carte_numero_gestion?: string;
    agence_carte_prefecture: string;
    agence_carte_syndic_cci?: string;
    agence_email: string;
    agence_etablissement_bancaire: 'oui' | 'non';
    agence_etablissement_bancaire_adresse?: MnAddress;
    agence_etablissement_bancaire_nom?: string;
    agence_etablissement_bancaire_numero?: string;
    agence_fonds_clients_statut: 'oui' | 'non';
    agence_fonds_clients_statut_gestion: 'oui' | 'non';
    agence_fonds_garantie_adresse?: MnAddress;
    agence_fonds_garantie_location_adresse?: MnAddress;
    agence_fonds_garantie_location_montant?: number;
    agence_fonds_garantie_montant?: number;
    agence_fonds_garantie_nom?: string;
    agence_fonds_garantie_syndic_adresse?: MnAddress;
    agence_fonds_garantie_syndic_montant?: number;
    agence_fonds_garantie_syndic_nom?: string;
    agence_fonds_garantie_syndic_numero?: string;
    agence_garantie_financiere_adresse?: MnAddress;
    agence_garantie_financiere_nom?: string;
    agence_garantie_financiere_numero?: string;
    agence_garantie_financiere_syndic_montant?: number;
    agence_garantie_syndic_financiere_adresse?: MnAddress;
    agence_garantie_syndic_financiere_nom?: string;
    agence_liens_capitalistiques: 'oui' | 'non';
    agence_nom: string;
    agence_orias: 'oui' | 'non';
    agence_orias_numero?: string;
    agence_telephone: string;
    capital: number;
    mediateur_adresse: MnAddress;
    mediateur_nom: string;
    mediateur_site: string;
    mediateur_site_statut: 'oui';
    mediation: 'oui' | 'non';
    numero_tva: string;
    personne_morale_denomination: string;
    personne_morale_denomination_commerciale: string;
    personne_morale_forme_sociale: string;
    personne_morale_forme_sociale_autre?: string;
    rcs_numero: string;
    rcs_ville: string;
    representant_civilite: 'femme' | 'homme';
    representant_nom: string;
    representant_prenom: string;
    siren: string;
    ville_immatriculation: string;
  };
  code: string;
  email: string;
  id: number;
  idSweepBright: string;
  legalRepresentatives: LegalRepresentative[];
  members: OrpiMember[];
  name: string;
  phone: string;
  siren: string;
  status: 'active' | 'inactive';
}

export interface OrpiMember {
  contractType: OrpiContractType;
  email: string | null;
  emailOrpi: string;
  function: string | null;
  id: number;
  role: OrpiMemberRole;
  // Registre Spécial des Agents Commerciaux
  rsac?: string;
}

export interface LegalRepresentative {
  civility: Civility;
  email: string;
  firstname: string;
  lastname: string;
}

export type OrpiContractType = 'representant' | 'agent_co_salarie' | 'agent_co_independannt';

export type OrpiMemberRole = 'admin' | 'responsable' | 'collaborateur';

export interface OrpiUser {
  civility: Civility;
  email: string;
  firstname: string;
  id: number;
  lastname: string;
  phone?: string;
}
