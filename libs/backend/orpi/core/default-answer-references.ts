import { LegalOperationTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { AnswerDict } from '@mynotary/crossplatform/records/api';

interface OperationReferenceValue {
  answers: {
    answer: AnswerDict;
    type: string;
  }[];
  operationTemplate: LegalOperationTemplateId;
}

export const defaultAnswerReferences: OperationReferenceValue[] = [
  {
    answers: [
      {
        answer: {
          clause_particuliere: { value: 'non' },
          honoraires_etat_des_lieux_statut: { value: 'oui' },
          honoraires_prestation_etat_des_lieux_bailleur_automatique: { value: 'oui' },
          honoraires_prestation_etat_des_lieux_locataire_automatique: { value: 'oui' },
          honoraires_prestation_redaction_bailleur_automatique: { value: 'oui' },
          honoraires_prestation_redaction_locataire_automatique: { value: 'oui' },
          mandant_execution: { value: 'oui' },
          mandant_retractation: { value: 'oui' },
          mandant_statut: { value: 'oui' },
          mandat_duree: { value: 24 },
          mandat_duree_recondution: { value: 24 },
          mandat_duree_recondution_totale: { value: 240 },
          mandat_frequence: { value: 'systematique' },
          mandat_gestion_calcul_honoraires: { value: 'honoraires_gestion_pourcentage' },
          mandat_gestion_duree: { value: 'mandat_gestion_duree_mois' },
          mandat_gestion_honoraires_versement: { value: 'honoraires_gestion_versement_prelevement' },
          mandat_gestion_location_statut: { value: 'oui' },
          mandat_gestion_rapport: { value: 'trimestre' },
          mandat_gestion_travaux: { value: 'mandat_gestion_travaux_plafonds' },
          mandat_gestion_travaux_plafond: { value: 150 },
          mandat_honoraires_mention: { value: 'modalite_mention_honoraires_location_fixes' },
          mandat_reconduction: { value: 'oui' },
          mandat_type: { value: 'gestion' },
          signature_electronique_mandat: { value: 'oui' }
        },
        type: 'MANDAT'
      },
      {
        answer: {
          charges_liste_statut: { value: 'oui' },
          charges_modalite: { value: 'provision' },
          clause_particuliere: { value: 'non' },
          depot_garantie: { value: 'oui' },
          depot_garantie_automatique: { value: 'oui' },
          depot_garantie_depositaire: { value: 'depot_agence_gestion' },
          depot_garantie_versement: { value: 'oui' },
          destination: { value: 'habitation' },
          diagnostic_resultats_statut: { value: 'oui' },
          etat_des_lieux: { value: 'non' },
          loyer_modalite_paiement: { value: ['virement'] },
          loyer_modalite_paiement_creancier: { value: 'agence_gestion' },
          loyer_modalite_paiement_date_mensualite: { value: '1' },
          loyer_modalite_paiement_frequence: { value: 'oui' },
          loyer_revision: { value: 'oui' },
          mandat_gestion_conditions: { value: 'oui' },
          signature_electronique: { value: 'oui' }
        },
        type: 'LOCATION'
      }
    ],
    operationTemplate: 'OPERATION__IMMOBILIER__LOCATION'
  },
  {
    answers: [],
    operationTemplate: 'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL'
  },
  {
    answers: [
      {
        answer: {
          delegation_mandat: { value: 'oui' },
          mandant_execution: { value: 'oui' },
          mandant_retractation: { value: 'oui' },
          mandant_statut: { value: 'professionnelles' },
          mandat_actions_commerciales: {
            value: [
              'mandat_actions_dossier',
              'mandat_actions_photo',
              'mandat_actions_vitrine',
              'mandat_actions_site_agence',
              'mandat_actions_site_speciaux'
            ]
          },
          mandat_cadastre: { value: 'oui' },
          mandat_duree: { value: 3 },
          mandat_duree_recondution: { value: 3 },
          mandat_duree_recondution_totale: { value: 12 },
          mandat_frequence: { value: 'systematique' },
          mandat_pouvoir: { value: 'non' },
          mandat_recommande_electronique: { value: 'oui' },
          mandat_reconduction: { value: 'oui' },
          mandat_signature_electronique: { value: 'oui' },
          mandat_tapuscrit: { value: 'non' },
          mandat_vente_calcul: { value: 'recherche_pourcentage' }
        },
        type: 'MANDAT'
      }
    ],
    operationTemplate: 'OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL'
  },
  {
    answers: [],
    operationTemplate: 'OPERATION__IMMOBILIER__VENTE_VIAGER'
  },
  {
    answers: [],
    operationTemplate: 'OPERATION__ORGANISATION_INTERNE'
  },
  {
    answers: [
      {
        answer: {
          clause_particuliere: { value: 'non' },
          mandant_dip_integre_statut: { value: 'oui' },
          mandant_execution: { value: 'oui' },
          mandant_retractation: { value: 'oui' },
          mandant_statut: { value: 'personnelles' },
          mandat_anglais: { value: 'non' },
          mandat_honoraires_charge: { value: null },
          mandat_signature_electronique: { value: 'oui' },
          mandat_vente_calcul: { value: 'recherche_pourcentage' },
          orpi_accompagnement: { value: ['seul_interlocuteur', 'statistique', 'espace_client', 'newsletter'] },
          orpi_accompagnement_comptes_rendus: { value: 'visite' },
          orpi_accompagnement_telephone_email: { value: 'telephone' },
          orpi_assainissement: { value: 'oui' },
          orpi_avant_notaire: {
            value: ['information', 'compromis', 'signature', 'concierge', 'assistance', 'suivi', 'suivi_notaire']
          },
          orpi_bien_occupe: { value: 'non' },
          orpi_communication: { value: ['fichier', 'vitrine'] },
          orpi_constitution: { value: ['document_orpi', 'remise_liste', 'remise_fiche'] },
          orpi_ddt_mandant: { value: 'oui' },
          orpi_estimation: { value: ['recueil', 'valeur', 'explication', 'dossier'] },
          orpi_irrevocabilite_defaut_duree: { value: ['trois_mois'] },
          orpi_recherche: { value: ['telephone', 'entretien', 'verification', 'information', 'fiche'] },
          orpi_reduction_honoraires: { value: 'oui' },
          orpi_signature_notaire: { value: ['date', 'presence', 'satisfaction'] },
          orpi_valeur: { value: ['photo', 'publicite', 'presentation'] },
          orpi_visibilite: { value: ['orpi', 'agence', 'pack_orpi', 'orpi_around'] }
        },
        type: 'MANDAT'
      },
      {
        answer: {
          clause_particuliere: { value: 'non' },
          compromis_acquereur_pro: { value: 'personnelles' },
          compromis_recommande_electronique: { value: 'oui' },
          compromis_signature_electronique: { value: 'oui' },
          coordonnees: { value: 'oui' },
          depot_garantie_statut: { value: null },
          diagnostic_resultats_statut: { value: 'non' }
        },
        type: 'CONDITIONS_GENERALES'
      }
    ],
    operationTemplate: 'OPERATION__ORPI__IMMOBILIER__VENTE'
  },
  {
    answers: [],
    operationTemplate: 'OPERATION__RECRUTEMENT__MYNOTARY__AGENT'
  },
  {
    answers: [],
    operationTemplate: 'OPERATION__SYNDIC__GENERAL'
  }
];
