import { OperationReferencesController } from '@mynotary/backend/legals/feature';

import { OperationReferenceDto } from '@mynotary/crossplatform/api-mynotary/openapi';

import { provideLegalsTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';

describe(OperationReferencesController.name, () => {
  it('should get a list of organization-references', async () => {
    const { client, member, testingRepos } = await setup();

    const op = await testingRepos.operations.createOperation({
      isOperationReference: true,
      organizationId: member.organizationId,
      templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN'
    });

    await testingRepos.operations.createOperation({
      organizationId: '-1',
      templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN'
    });

    const response = await client
      .get(`/organizations/${member.organizationId}/operation-references`)
      .query({ operationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN' })
      .send();

    expect(response.statusCode).toBe(200);
    expect(response.body.items.length).toBe(1);

    expect(response.body).toMatchObject({
      items: [
        {
          operationId: op.id,
          operationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
          organizationId: member.organizationId
        } satisfies OperationReferenceDto
      ]
    });
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,

      controller: OperationReferencesController,
      providers: provideLegalsTest()
    });

    const testingRepos = getService(TestingRepositories);

    const member = await testingRepos.createMember();

    return {
      client,
      member,
      testingRepos
    };
  }
});
