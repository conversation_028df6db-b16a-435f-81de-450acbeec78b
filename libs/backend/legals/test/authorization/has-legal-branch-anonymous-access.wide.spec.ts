import { Controller, Get, UseGuards } from '@nestjs/common';
import { HasLegalBranchAnonymousAccess } from '@mynotary/backend/legals/authorization';
import { ResourceTokenType } from '@mynotary/crossplatform/authorizations/api';
import { pathResolver } from '@mynotary/backend/shared/auth-util';
import { provideLegalsTest } from '../index';
import { DecodedMnToken } from '@mynotary/backend/authentications/api';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';

describe(HasLegalBranchAnonymousAccess.name, () => {
  it('should allow access if token match legal branch from id', async () => {
    const { branch, client, setValidToken } = await setup();

    setValidToken(branch.fromLegalId);

    const response = await client.get(`/branches/${branch.id}`).send();

    expect(response.statusCode).toBe(200);
  });

  it('should allow access if token match legal branch to id', async () => {
    const { branch, client, setValidToken } = await setup();

    setValidToken(branch.toId);

    const response = await client.get(`/branches/${branch.id}`).send();

    expect(response.statusCode).toBe(200);
  });

  it('should deny access if token is not valid', async () => {
    const { branch, client, setValidToken } = await setup();

    setValidToken();
    const response = await client.get(`/branches/${branch.id}`).send();

    expect(response.statusCode).toBe(403);
  });

  it(`should deny access when record don't match branch legal records`, async () => {
    const { branch, client, member, setValidToken, testingRepos } = await setup();

    const record = await testingRepos.records.createRecord({
      organizationId: member.organizationId,
      userId: member.userId
    });

    setValidToken(record.id);

    const response = await client.get(`/branches/${branch.id}`).send();

    expect(response.statusCode).toBe(403);
  });

  async function setup() {
    const { client, getService, setResourceToken } = await createTestingWideApp({
      bypassAuth: false,
      controller: TestController,
      providers: provideLegalsTest()
    });
    const testingRepos = getService(TestingRepositories);

    const member = await testingRepos.createMember({ permissions: [] });

    const record = await testingRepos.records.createRecord({
      organizationId: member.organizationId,
      userId: member.userId
    });

    const branch = await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: record.id,
      organizationId: member.organizationId,
      toId: record.id,
      type: 'EPOUX',
      userId: member.userId
    });

    return {
      branch,
      client,
      member,
      record,
      setValidToken: (resourceId?: string) => {
        const token: DecodedMnToken = {
          creationTime: new Date(),
          expirationTime: new Date(),
          id: 'valid_token',
          issuer: 'issuer',
          resourceId: resourceId ?? '',
          resourceType: ResourceTokenType.LEGAL_RECORD
        };
        setResourceToken(token);
      },
      testingRepos: testingRepos
    };
  }
});

@Controller()
class TestController {
  @UseGuards(
    HasLegalBranchAnonymousAccess({
      branchIdResolver: pathResolver('branchId')
    })
  )
  @Get('/branches/:branchId')
  getValue() {
    return '👌';
  }
}
