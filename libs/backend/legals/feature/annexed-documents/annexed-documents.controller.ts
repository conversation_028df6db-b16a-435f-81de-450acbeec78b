import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import {
  AnnexedDocumentDto,
  AnnexedDocumentListDto,
  AnnexedDocumentNewDto,
  AnnexedDocumentStateDto
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { AnnexedDocument, AnnexedDocumentsService, AnnexedDocumentState } from '@mynotary/backend/legals/core';
import { convertEnum } from '@mynotary/crossplatform/shared/util';
import { bodyResolver, queryResolver } from '@mynotary/backend/shared/auth-util';
import { HasContractPermission } from '@mynotary/backend/legals/authorization';
import { HasOperationAccess } from '@mynotary/backend/authorizations/api';

@Controller()
export class AnnexedDocumentsController {
  constructor(private annexedDocumentsService: AnnexedDocumentsService) {}

  @UseGuards(
    HasContractPermission(PermissionType.UPDATE_CONTRACT, {
      contractIdResolver: bodyResolver('contractId')
    })
  )
  @Post('/annexed-documents')
  async upsertAnnexedDocument(@Body() body: AnnexedDocumentNewDto): Promise<AnnexedDocumentDto> {
    const annexedDocument = await this.annexedDocumentsService.upsertAnnexedDocument({
      ...body,
      state: convertEnum(AnnexedDocumentState, body.state)
    });

    return {
      ...convertToDto(annexedDocument),
      operationId: body.operationId,
      state: convertEnum(AnnexedDocumentStateDto, annexedDocument.state)
    };
  }

  @UseGuards(
    HasOperationAccess({
      operationIdResolver: queryResolver('operationId')
    })
  )
  @Get('/annexed-documents')
  async getAnnexedDocuments(@Query('operationId') operationId: string): Promise<AnnexedDocumentListDto> {
    const annexedDocuments = await this.annexedDocumentsService.getAnnexedDocuments(operationId);

    return {
      items: annexedDocuments.map((annexedDocument) => convertToDto(annexedDocument))
    };
  }
}

const convertToDto = (annexedDocument: AnnexedDocument) => ({
  contractId: annexedDocument.contractId,
  documentId: annexedDocument.documentId,
  id: annexedDocument.id,
  operationId: annexedDocument.operationId,
  recordId: annexedDocument.recordId,
  state: convertEnum(AnnexedDocumentStateDto, annexedDocument.state)
});
