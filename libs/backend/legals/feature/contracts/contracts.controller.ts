import { Body, Controller, Param, Post, Put, UseGuards } from '@nestjs/common';
import { ContractDuplicatesService, ContractsService } from '@mynotary/backend/legals/core';
import {
  ContractArchiveDto,
  ContractNewDto,
  ContractResponseDto,
  ContractStatusDto,
  UpdateContractStatusDto,
  ContractDuplicateNewDto
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { convertEnum } from '@mynotary/crossplatform/shared/util';
import { ContractStatus } from '@mynotary/crossplatform/legals/core';
import { IsAdmin, IsGcpApplication, IsUserVerified, OrGuard } from '@mynotary/backend/authorizations/api';
import { HasContractNewAccess, HasContractPermission } from '@mynotary/backend/legals/authorization';
import { LegalContractTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { HasContractAccess } from '@mynotary/backend/legals/authorization';

@Controller()
export class ContractController {
  constructor(
    private contractService: ContractsService,
    private duplicateContractService: ContractDuplicatesService
  ) {}

  @UseGuards(IsUserVerified, HasContractPermission(PermissionType.UPDATE_CONTRACT))
  @Put('/contracts/:contractId/archive')
  async archiveContract(@Param('contractId') contractId: string, @Body() body: ContractArchiveDto): Promise<void> {
    await this.contractService.archiveContract({ archived: body.archived, id: contractId });
  }

  @UseGuards(IsAdmin())
  @Put('/admin/contracts-status/:contractId')
  async updateAdminContractStatus(
    @Param('contractId') contractId: string,
    @Body() body: UpdateContractStatusDto
  ): Promise<void> {
    await this.contractService.updateContract({
      id: contractId,
      status: convertEnum(ContractStatus, body.status)
    });
  }

  @UseGuards(OrGuard([[IsGcpApplication()], [HasContractNewAccess()]]))
  @Post('/contracts')
  async createContract(@Body() body: ContractNewDto): Promise<ContractResponseDto> {
    const { contract, legalData } = await this.contractService.createContractWithDefaultValue({
      label: body.label,
      legalContractTemplateId: body.legalContractTemplateId as LegalContractTemplateId,
      legalOperationId: body.legalOperationId,
      userId: body.userId
    });

    return {
      contract: {
        ...contract,
        status: convertEnum(ContractStatusDto, contract.status)
      },
      legalData
    };
  }

  @Post('/contract-duplicates')
  @UseGuards(HasContractAccess())
  async duplicateContract(@Body() body: ContractDuplicateNewDto): Promise<ContractResponseDto> {
    const result = await this.duplicateContractService.duplicateContractWithData({
      contractId: body.contractId,
      userId: body.userId
    });

    return {
      contract: {
        ...result.contract,
        status: convertEnum(ContractStatusDto, result.contract.status)
      },
      legalData: result.legalData
    };
  }
}
