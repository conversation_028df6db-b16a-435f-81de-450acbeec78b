import { Injectable, Type } from '@nestjs/common';
import { AuthorizationArgs, bodyResolver } from '@mynotary/backend/shared/auth-util';
import { AuthorizationBase, AuthorizationsApiService } from '@mynotary/backend/authorizations/api';

interface HasContractAccessOptions {
  contractIdResolver?: (args: AuthorizationArgs) => string | null;
}

export function HasContractAccess(options?: HasContractAccessOptions): Type<AuthorizationBase> {
  const contractIdResolver = options?.contractIdResolver ?? bodyResolver('contractId');

  @Injectable()
  class HasContractAccess extends AuthorizationBase {
    constructor(private authorizationsApiService: AuthorizationsApiService) {
      super();
    }

    async isAuthorized(authorizationRequest: AuthorizationArgs): Promise<boolean> {
      const userId = authorizationRequest.userInfo?.userId;
      const contractId = contractIdResolver(authorizationRequest);

      if (!contractId || !userId) {
        return false;
      }

      const isUserVerified = await this.authorizationsApiService.isUserVerified(userId);

      if (!isUserVerified) {
        return false;
      }

      return await this.authorizationsApiService.hasContractAccess({ contractId, userId });
    }
  }

  return HasContractAccess;
}
