import { Injectable, Type, UnprocessableEntityException } from '@nestjs/common';
import { AuthorizationArgs, pathResolver } from '@mynotary/backend/shared/auth-util';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { AuthorizationBase, AuthorizationsApiService } from '@mynotary/backend/authorizations/api';
import { LegalRecordAuthorizationsRepository } from '@mynotary/backend/legals/core';

interface HasLegalRecordAccessArgs {
  legalRecordIdResolver?: (args: AuthorizationArgs) => string | null;
  permissionType?: PermissionType;
}

export function HasLegalRecordAccess(args?: HasLegalRecordAccessArgs): Type<AuthorizationBase> {
  const recordIdResolver = args?.legalRecordIdResolver ?? pathResolver('recordId');
  @Injectable()
  class HasLegalRecordAccess extends AuthorizationBase {
    constructor(
      private authLegalRecordsRepository: LegalRecordAuthorizationsRepository,
      private authorizationsApiService: AuthorizationsApiService
    ) {
      super();
    }

    async isAuthorized(authorizationRequest: AuthorizationArgs) {
      const userId = authorizationRequest.userInfo?.userId;
      const recordId = recordIdResolver(authorizationRequest);
      const isUserVerified = await this.authorizationsApiService.isUserVerified(userId);

      if (userId == null || !isUserVerified) {
        return false;
      }

      if (recordId == null) {
        throw new UnprocessableEntityException('recordId is required');
      }

      return await this.authLegalRecordsRepository.hasLegalRecordAccess({
        permissionType: args?.permissionType,
        recordId,
        userId
      });
    }
  }

  return HasLegalRecordAccess;
}
