import { Operation, OperationTags } from './operations';

export abstract class OperationsRepository {
  abstract getOperation(operationId: string): Promise<Operation>;
  abstract findUserOperations(args: FindUserOperationsArgs): Promise<Operation[]>;
  abstract updateOperation(args: UpdateOperationArgs): Promise<Operation>;
  abstract getOperationLabel(operationId: string): Promise<string>;
}

export interface FindUserOperationsArgs {
  limit: number;
  userId: string;
}

export type UpdateOperationArgs = {
  id: string;
  label?: string;
  organizationId?: string;
  parentOperationId?: string;
  tags?: OperationTags;
};
