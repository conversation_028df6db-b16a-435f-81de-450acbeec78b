import { LegalOperationTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { RequiredLegalLink } from '@mynotary/crossplatform/legal-operation-templates/api';
import { OperationDefaultAnswer } from '../operation-default-answers';

export abstract class LegalOperationCreationsRepository {
  abstract createLegalOperation(args: CreateLegalOperationArgs): Promise<LegalOperationLight>;

  /**
   * Returns the legal link of the parent operation that should hold the sub operation.
   */
  abstract getSubOperationLink(args: GetVenteLegalLinkArgs): Promise<VenteLegalLink>;

  /**
   * Mark an operation as deleted. Currently only used when an error occurs during the creation.
   */
  abstract softDeleteLegalOperation(legalOperationId: string): Promise<void>;
}

export interface CreateLegalOperationArgs {
  defaultAnswers: OperationDefaultAnswer[];
  isOperationReference?: boolean;
  label?: string;
  labelPattern?: string;
  legalOperationTemplateId: LegalOperationTemplateId;
  organizationId: string;
  requiredLegalLinks: RequiredLegalLink[];
  userId: string;
}

export interface GetVenteLegalLinkArgs {
  parentOperationId: string;
  type: 'VENTE';
}

export interface LegalOperationLight {
  id: string;
  legalOperationTemplateId: LegalOperationTemplateId;
  organizationId: string;
  userId: string;
}

export interface VenteLegalLink {
  id: string;
}
