import { Injectable } from '@nestjs/common';
import { GetLegalDataArgs, LegalsRepository } from './legals.repository';

@Injectable()
export class LegalsService {
  constructor(private legalsRepository: LegalsRepository) {}

  async getLegal(id: string) {
    return this.legalsRepository.getLegal(id);
  }

  async getLegalData(args: GetLegalDataArgs) {
    return this.legalsRepository.getLegalData(args);
  }
}
