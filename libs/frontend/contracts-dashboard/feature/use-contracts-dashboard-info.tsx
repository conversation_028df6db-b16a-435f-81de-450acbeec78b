import { ContractsAssociation } from '@mynotary/frontend/contracts-dashboard/core';
import { ContractLegacy } from '@mynotary/frontend/legals/api';
import { selectOperationStatus } from '@mynotary/frontend/operation-status/api';
import { format } from 'date-fns';
import { useSelector } from 'react-redux';
import { getLegalOperationTemplate } from '@mynotary/crossplatform/legal-operation-templates/api';
import { isProgramConfig } from '@mynotary/crossplatform/legal-templates/api';

interface GetContractDashboardInfoProps {
  association: ContractsAssociation;
  contract: ContractLegacy;
}

export const useContractsDashboardInfo = ({ association, contract }: GetContractDashboardInfoProps) => {
  const status = useSelector(selectOperationStatus(contract.operation.statusId ?? -1));
  const parentOperationStatus = useSelector(selectOperationStatus(contract.operation.parentOperationStatusId ?? -1));
  const legalOperationTemplate = getLegalOperationTemplate(contract.operation.template.id);

  return {
    creationTime: format(contract.creationTime, 'dd/MM/yyyy'),
    firstname: association.user.firstname,
    isSubOperation: !isProgramConfig(legalOperationTemplate?.config)
      ? legalOperationTemplate?.config.isSubOperation
      : false,
    label: contract.label,
    lastname: association.user.lastname,
    legalOperationTemplate,
    operationLabel: contract.operation.label,
    operationStatus: status?.label ?? '-',
    parentOperationStatus: parentOperationStatus?.label ?? '-',
    profilePictureFileId: association.user.profilePictureFileId,
    signatoriesCount:
      association.signatoriesCount > 0 ? `${association.totalSignatories}/${association.signatoriesCount}` : '-',
    signatureCreationTime: association.lastSignature?.creationTime
      ? format(association.lastSignature.creationTime, 'dd/MM/yyyy')
      : '-',
    signatureSignatureTIme: association.lastSignature?.signatureTime
      ? format(association.lastSignature.signatureTime, 'dd/MM/yyyy')
      : '-',
    url: '/assets/images/pictos/icon/file-text-light.svg'
  };
};
