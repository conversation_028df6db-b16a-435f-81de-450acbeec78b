import './contracts-dashboard-tile.scss';
import { selectContractAssociationFeature } from '@mynotary/frontend/contracts-dashboard/store';
import { ContractLegacy, ContractStatusTag } from '@mynotary/frontend/legals/api';
import { ContactAvatar, MnSvg, MnTooltip, TableLayout } from '@mynotary/frontend/shared/ui';
import { ReactElement, MouseEvent, useState } from 'react';
import { useSelector } from 'react-redux';
import { ContractDashboardActionsList } from '../contracts-actions/contract-dashboard-actions-list';
import { useHandleRedirectionContractDashboard } from '../use-contract-dashboard-redirection';
import { useContractsDashboardInfo } from '../use-contracts-dashboard-info';
import { getPublicFile } from '@mynotary/frontend/files/api';

interface ContractsDashboardTileProps {
  contract: ContractLegacy;
}

export const ContractsDashboardTile = ({ contract }: ContractsDashboardTileProps): ReactElement => {
  const [isHovered, setIsHovered] = useState(false);
  const contractsAssociation = useSelector(selectContractAssociationFeature);
  const association = contractsAssociation?.[contract.id];
  const {
    creationTime,
    firstname,
    isSubOperation,
    label,
    lastname,
    operationLabel,
    operationStatus,
    parentOperationStatus,
    profilePictureFileId,
    signatoriesCount,
    signatureCreationTime,
    signatureSignatureTIme,
    url
  } = useContractsDashboardInfo({ association, contract });

  const { handleContractDashboardRedirection } = useHandleRedirectionContractDashboard({
    association,
    contract
  });

  return (
    <TableLayout.Row
      className='contracts-dashboard-tile'
      onClick={(e: MouseEvent) => handleContractDashboardRedirection(e)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {isHovered && (
        <div className='cdt-hover'>
          <div>
            <MnSvg path={url} variant='green300' />
          </div>
          <div className='cdt-hover-label'>Ouvrir mon contrat</div>
          <MnSvg path='/assets/images/pictos/icon/arrow-right-light.svg' size='medium' variant='green300' />
          <div className='cdt-hover-description'>
            {label}
            <span> - Créé par {`${firstname} ${lastname}`}</span>
          </div>
        </div>
      )}
      <div className='cdt-info'>
        <div>
          <MnSvg path={url} variant='black' />
        </div>
        <div className='cdt-info-container'>
          <div className='cdt-info-label'>{label}</div>
          <div>{operationLabel}</div>
        </div>
      </div>
      <div className='cdt-container'>
        <div className='cdt-row-info'>
          <div className='cdt-row-label'>Statut du contrat:</div>
          <ContractStatusTag className='cdt-status-renderer' contractId={contract.id} />
        </div>
        <div className='cdt-row-info'>
          <div className='cdt-row-label'>Statut du dossier :</div>
          <div className='cdt-status'>{operationStatus}</div>
        </div>
        {isSubOperation && (
          <div className='cdt-row-info'>
            <div className='cdt-row-label'>Statut du programme:</div>
            <div className='cdt-status'>{parentOperationStatus}</div>
          </div>
        )}
      </div>
      <div className='cdt-info-container'>
        <div className='cdt-row-info'>
          <div className='cdt-row-label'>Lancée:</div>
          <div>{signatureCreationTime}</div>
        </div>
        <div className='cdt-row-info'>
          <div className='cdt-row-label'>Signée:</div>
          <div>{signatureSignatureTIme}</div>
        </div>
        <div className='cdt-row-info'>
          <div className='cdt-row-label'>{`Signataire(s):`}</div>
          <div>{signatoriesCount}</div>
        </div>
      </div>

      <div className='cdt-avatar'>
        <MnTooltip content={`${firstname} ${lastname} \n Le: ${creationTime}`}>
          <ContactAvatar
            firstname={firstname}
            lastname={lastname}
            photoUrl={profilePictureFileId ? getPublicFile(profilePictureFileId) : undefined}
          />
        </MnTooltip>
      </div>
      <div>
        <ContractDashboardActionsList association={association} contract={contract} />
      </div>
    </TableLayout.Row>
  );
};
