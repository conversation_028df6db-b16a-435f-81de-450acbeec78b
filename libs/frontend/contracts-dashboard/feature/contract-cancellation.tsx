import React, { ReactElement } from 'react';
import { MnButton } from '@mynotary/frontend/shared/ui';
import { MnPopinActions } from '@mynotary/frontend/shared/ui';

interface ContractCancellationProps {
  onClose: () => void;
}

const ContractCancellation = ({ onClose }: ContractCancellationProps): ReactElement => {
  return (
    <MnPopinActions
      buttonElements={[<MnButton label='Terminer' onClick={() => onClose()} shape='squircle' variant='primary' />]}
      handleClosePopin={onClose}
    >
      <div>
        <h4>Oups.. Vous ne pouvez pas supprimer ce contrat</h4>
        <p>
          Des signatures ou des recommandés sont actuellement en cours, ou ont été effectuées sur ce contrat. Par
          conséquent, vous êtes légalement tenu de le conserver.
        </p>
      </div>
    </MnPopinActions>
  );
};

export { ContractCancellation };
