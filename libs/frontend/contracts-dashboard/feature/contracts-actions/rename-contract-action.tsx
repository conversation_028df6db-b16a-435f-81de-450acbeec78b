import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { ContractLegacy, selectContractPermission } from '@mynotary/frontend/legals/api';
import { ActionIcon, MnSidePopin } from '@mynotary/frontend/shared/ui';
import { ReactElement, useState } from 'react';
import { useSelector } from 'react-redux';
import { MnContractRename } from '../contract-rename/contract-rename';

interface RenameContractActionProps {
  contract: ContractLegacy;
  onFinish?: () => void;
}

export const RenameContractAction = ({ contract, onFinish }: RenameContractActionProps): ReactElement | null => {
  const [isOpen, setIsOpen] = useState(false);
  const canRenamePermission = useSelector(selectContractPermission(PermissionType.UPDATE_CONTRACT, contract.id));

  if (!canRenamePermission) {
    return null;
  }

  return (
    <>
      <ActionIcon icon={'/assets/images/pictos/icon/edit-2.svg'} label={'Renommer'} onClick={() => setIsOpen(true)} />
      <MnSidePopin isOpen={isOpen} onClose={() => setIsOpen(false)}>
        <MnContractRename
          contract={contract}
          onContractRenamed={() => {
            setIsOpen(false);
            onFinish?.();
          }}
        />
      </MnSidePopin>
    </>
  );
};
