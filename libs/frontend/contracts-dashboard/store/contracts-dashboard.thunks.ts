import { FilesClient } from '@mynotary/crossplatform/files-client/api';
import { ContractLegacy, createContractFolder } from '@mynotary/frontend/legals/api';
import { createRegisterLetterFolder } from '@mynotary/frontend/registered-letters/api';
import {
  getRegisteredLetterBatches,
  selectRegisteredLetterByContractId
} from '@mynotary/frontend/registered-letters/api';
import { inject } from '@mynotary/frontend/shared/injector-util';
import { AppAsyncThunk } from '@mynotary/frontend/shared/redux-util';
import { createSignatureFolder } from '@mynotary/frontend/signatures/api';
import { getSignatures, selectCurrentSignatureByContractId } from '@mynotary/frontend/signatures/api';

export const downloadImportedContractFiles =
  (contractId: number, fetchOperationData?: boolean): AppAsyncThunk<{ id: string }> =>
  async (dispatch, getState) => {
    const filesClient = inject(FilesClient);
    const contract: ContractLegacy = getState().contractsById[contractId];
    const operationId = contract.operation.id;

    // Entities are fetched if download is trigger from applications routes
    if (fetchOperationData) {
      await Promise.all([dispatch(getSignatures(operationId)), dispatch(getRegisteredLetterBatches(operationId))]);
    }

    const signature = selectCurrentSignatureByContractId(contractId)(getState());
    const notif = selectRegisteredLetterByContractId(contractId)(getState());

    if (signature) {
      return await filesClient.createArchive(createSignatureFolder(signature));
    }
    if (notif && !notif.draft) {
      return await filesClient.createArchive(createRegisterLetterFolder(notif));
    }
    try {
      const context = contract.creationContext != null ? JSON.parse(contract.creationContext) : {};
      const folder = createContractFolder(contract, context.files);
      return await filesClient.createArchive(folder);
    } catch (e) {
      console.error(e);
      return Promise.reject(e);
    }
  };
