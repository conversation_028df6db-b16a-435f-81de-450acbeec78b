import {
  Filters,
  FiltersContract<PERSON><PERSON>,
  Filters<PERSON><PERSON>s,
  isArchiveContractFilter,
  isContractSignatureTypeFilter,
  isDate<PERSON>ilter,
  isLastAccess,
  isOriginContractFilter,
  isSearchFilter,
  isStatusContractFilter,
  isTypeContractFilter,
  isStatusOperationFilter,
  isStatusProgramFilter
} from '@mynotary/frontend/dashboard-filters/api';
import { ContractsWithAssociationPagination } from '@mynotary/frontend/contracts-dashboard/core';
import { AppAsyncThunk } from '@mynotary/frontend/shared/redux-util';
import { filter, forEach, isEmpty, map } from 'lodash';
import { ApiHelpers } from '@mynotary/frontend/shared/axios-util';
import { setContractsAssociation } from './contracts-association.slice';
import { setContracts } from '@mynotary/frontend/legals/api';
import { Pageable } from '@mynotary/frontend/shared/util';

export const getContractsWithAssociation =
  (filters?: Filters, pageable?: Pageable): AppAsyncThunk<ContractsWithAssociationPagination> =>
  async (dispatch) => {
    const filtering: FiltersContractApi = {};

    forEach(filters, (f) => {
      if (isOriginContractFilter(f)) {
        filtering[FiltersKeys.ORIGIN] = map(
          filter(f?.values, (v) => v.selected),
          (value) => value.id
        );
      }
      if (isStatusContractFilter(f)) {
        filtering[FiltersKeys.STATUS] = map(
          filter(f?.values, (v) => v.selected),
          (value) => value.id
        );
      }
      if (isTypeContractFilter(f)) {
        let types: { modelId: string; templateId: string }[] = [];
        forEach(f.values, (value) => {
          const selectedTypes = filter(value.values, (v) => v.selected);
          types = [
            ...types,
            ...map(selectedTypes, (type) => {
              // use templateId to target a specific IMPORT model inside an operation
              const splittedIds = type.id.split('-');
              return { modelId: splittedIds?.[1], templateId: splittedIds?.[0] };
            })
          ];
        });
        filtering[FiltersKeys.CONTRACTS_TYPES] = types;
      }

      if (isDateFilter(f)) {
        filtering[FiltersKeys.CREATION_TIME] = {
          after: f.children[FiltersKeys.CREATION_TIME].value?.to?.setHours(23, 59, 59),
          before: f.children[FiltersKeys.CREATION_TIME].value?.from?.setHours(0, 0, 1)
        };
        filtering[FiltersKeys.SIGNATURE_CREATION_TIME] = {
          after: f.children[FiltersKeys.SIGNATURE_CREATION_TIME]?.value?.to?.setHours(23, 59, 59),
          before: f.children[FiltersKeys.SIGNATURE_CREATION_TIME]?.value?.from?.setHours(0, 0, 1)
        };
        filtering[FiltersKeys.SIGNATURE_TIME] = {
          after: f.children[FiltersKeys.SIGNATURE_TIME]?.value?.to?.setHours(23, 59, 59),
          before: f.children[FiltersKeys.SIGNATURE_TIME]?.value?.from?.setHours(0, 0, 1)
        };
      }

      if (isSearchFilter(f)) {
        filtering[FiltersKeys.SEARCH] = f.value?.trim().split(' ');
      }

      if (isStatusOperationFilter(f)) {
        filtering[FiltersKeys.STATUS_OPERATION] = map(
          filter(f?.values, (v) => v.selected),
          (value) => value.id
        );
      }
      if (isStatusProgramFilter(f)) {
        filtering[FiltersKeys.STATUS_PROGRAM] = map(
          filter(f?.values, (v) => v.selected),
          (value) => value.id
        );
      }

      if (isLastAccess(f)) {
        filtering[FiltersKeys.LAST_ACCESS] = map(
          filter(f?.values, (v) => v.selected),
          (value) => value.id
        );
      }

      if (isContractSignatureTypeFilter(f)) {
        filtering[FiltersKeys.CONTRACT_SIGNATURE_TYPE] = map(
          filter(f?.values, (v) => v.selected),
          (value) => value.id
        );
      }

      if (isArchiveContractFilter(f)) {
        filtering[FiltersKeys.ARCHIVE_CONTRACT] = map(
          filter(f?.values, (v) => v.selected),
          (value) => value.id
        );
      }
    });

    if (!filtering.ARCHIVE_CONTRACT || isEmpty(filtering?.ARCHIVE_CONTRACT)) {
      filtering.ARCHIVE_CONTRACT = ['ACTIVE'];
    }

    const { data } = await ApiHelpers.post<ContractsWithAssociationPagination>(`/contracts`, {
      filtering,
      page: pageable?.page ?? 0,
      pageSize: pageable?.pageSize ?? 20
    });

    dispatch(
      setContractsAssociation(
        map(data.data, (item) => ({
          contractId: item.operationContract.id,
          lastNotification: item?.notification,
          lastSignature: item?.signature,
          signatoriesCount: item?.signatoriesCount,
          totalSignatories: item?.totalSignatories,
          user: item?.user
        }))
      )
    );

    dispatch(setContracts(map(data.data, (item) => item.operationContract)));

    return data;
  };
