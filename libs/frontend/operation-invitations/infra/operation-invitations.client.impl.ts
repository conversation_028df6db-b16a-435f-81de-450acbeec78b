import {
  OperationInvitationCreateDto,
  OperationInvitationDto,
  OperationInvitationRoleDto,
  OperationInvitationUpdateDto,
} from '@mynotary/crossplatform/api-mynotary/openapi';
import {
  GetOperationRolesArgs,
  OperationInvitation,
  OperationInvitationNew,
  UpdateOperationInvitationArgs,
  OperationInvitationsClient
} from '@mynotary/frontend/operation-invitations/core';
import { createAxiosInstance } from '@mynotary/frontend/shared/axios-util';
import { environment } from '@mynotary/frontend/shared/environments-util';

export class OperationInvitationsClientImpl implements OperationInvitationsClient {
  mainApiClient = createAxiosInstance({ baseURL: environment.apiMyNotaryUrl });

  async getOperationRoles(args: GetOperationRolesArgs): Promise<OperationInvitationRoleDto[]> {
    const { data } = await this.mainApiClient.get<OperationInvitationRoleDto[]>(`/operation-roles`, {
      params: { email: args.email, operationId: args.operationId.toString(), userId: args.userId.toString() }
    });

    return data;
  }

  async createOperationInvitation(args: OperationInvitationNew): Promise<OperationInvitation> {
    const { data } = await this.mainApiClient.post<OperationInvitationDto>(`/operation-invitations`, {
      email: args.email,
      emailContent: args.emailContent,
      operationId: args.operationId,
      roleId: args.roleId,
      userId: args.userId.toString()
    } satisfies OperationInvitationCreateDto);

    return this.toOperationInvitation(data);
  }

  async updateOperationInvitation(args: UpdateOperationInvitationArgs): Promise<OperationInvitation> {
    const { data } = await this.mainApiClient.put(`/operation-invitations/${args.id}`, {
      emailContent: args.emailContent,
      roleId: args.roleId,
      userId: args.userId.toString()
    } satisfies OperationInvitationUpdateDto);

    return this.toOperationInvitation(data);
  }

  async deleteOperationInvitation(id: string): Promise<void> {
    await this.mainApiClient.delete(`/operation-invitations/${id}`);
  }

  private toOperationInvitation(data: OperationInvitationDto): OperationInvitation {
    return {
      ...data,
      creationTime: new Date(data.creationTime).getTime(),
      creatorUserId: parseInt(data.creatorUserId),
      id: data.id,
      operationId: parseInt(data.operationId),
      user: data.user
    }
  }
}
