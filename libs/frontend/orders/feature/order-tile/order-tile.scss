@use 'style/mixins/typography' as *;
@use 'style/variables/colors' as *;

.order-tile {
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: space-between;

  width: 100%;
  padding: 16px 24px;

  .ot-line {
    display: flex;
    gap: 8px;
    align-items: center;
    height: fit-content;
  }

  .ot-operation {
    min-width: fit-content;
  }

  .ot-tag {
    max-width: none;
  }

  .ot-weight {
    font-weight: $medium;
  }

  .ot-underline {
    text-decoration: underline;

    &:hover {
      cursor: pointer;
      color: var(--primary);
    }
  }

  .ot-actions {
    width: fit-content;
  }

  .ot-title {
    font-weight: $semi-bold;
  }

  .ot-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .ot-header {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
  }
}

.ot-popin-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.ot-popin-primary {
  color: var(--primary);
}

.ot-actions-list {
  .ot-no-actions {
    display: none;
    padding: 8px 16px;
    color: $gray700;

    &:only-child {
      display: block;
    }
  }
}
