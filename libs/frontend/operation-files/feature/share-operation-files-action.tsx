import { MnTooltip, ActionIcon } from '@mynotary/frontend/shared/ui';
import React from 'react';
import { openPopin, PopinType } from '@mynotary/frontend/popins/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { useSelector } from 'react-redux';
import { selectPermission } from '@mynotary/frontend/roles/api';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';

interface ShareOperationFilesActionProps {
  onFinish?: () => void;
  operationId: number;
}

export const ShareOperationFilesAction = ({ onFinish, operationId }: ShareOperationFilesActionProps) => {
  const dispatch = useAsyncDispatch();
  const canShareDocument = useSelector(
    selectPermission(PermissionType.CREATE_TASK_SHARE_DOCUMENTS, EntityType.DOCUMENT)
  );

  const handleClick = () => {
    dispatch(openPopin({ operationId, type: PopinType.OPERATION_FILES, variant: 'SHARE' }));
    onFinish?.();
  };

  return (
    <MnTooltip
      content='Vous ne disposez pas des droits nécéssaires pour effectuer cette action, merci de vous rapprocher de votre responsable.'
      disabled={canShareDocument}
    >
      <ActionIcon
        disabled={!canShareDocument}
        icon={'/assets/images/pictos/icon/share2-light.svg'}
        label={'Partager des fichiers'}
        onClick={handleClick}
        testId='operation-document-share'
      />
    </MnTooltip>
  );
};
