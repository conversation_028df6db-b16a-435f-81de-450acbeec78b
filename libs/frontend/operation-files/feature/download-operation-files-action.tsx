import { ActionIcon } from '@mynotary/frontend/shared/ui';
import React from 'react';
import { PopinType, openPopin } from '@mynotary/frontend/popins/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';

interface DownloadOperationFilesActionProps {
  operationId: number;
}

export const DownloadOperationFilesAction = ({ operationId }: DownloadOperationFilesActionProps) => {
  const dispatch = useAsyncDispatch();

  const handleClick = () => {
    dispatch(openPopin({ operationId, type: PopinType.OPERATION_FILES, variant: 'DOWNLOAD' }));
  };

  return (
    <ActionIcon
      icon={'/assets/images/pictos/icon/download-light.svg'}
      label={'Télécharger des fichiers'}
      onClick={handleClick}
      testId='operation-document-download'
    />
  );
};
