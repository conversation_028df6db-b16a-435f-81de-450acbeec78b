import './record-creation-siren.scss';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { ReactElement, useState } from 'react';
import {
  createValue,
  FindRecordDuplicatesArgs,
  getRecordDuplicates,
  Record,
  RecordDuplicateSelection,
  selectOperation
} from '@mynotary/frontend/legals/api';
import { pick } from 'lodash';
import { SirenForm } from '../../sirenForm/sirenForm';
import { FooterActionBtnPopin, MnLoaderPopin } from '@mynotary/frontend/shared/ui';
import { NewSnackbarMessage, setSnackbarMessage } from '@mynotary/frontend/snackbars/api';
import { ManualRecordCreation } from '../../manualRecordCreation/manualRecordCreation';
import { classNames, slowDown } from '@mynotary/frontend/shared/util';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { selectCurrentOperationId } from '@mynotary/frontend/current-operation/api';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';
import { CompaniesClient, CompaniesList, Company, convertCompanyToRecordNew } from '@mynotary/frontend/companies/api';
import { RecordCreationComponentProps } from '../../legal-record-creations';

type Step = 'loading' | 'duplicateSelection' | 'sirenForm' | 'manualCreation' | 'personneMoralSelection';

const RecordCreationSiren = ({
  className,
  onValidate,
  template,
  templateSelector
}: RecordCreationComponentProps): ReactElement => {
  const personneMoraleClient = useService(CompaniesClient);
  const dispatch = useAsyncDispatch();
  const operationId = useSelector(selectCurrentOperationId);
  const operationOrganizationId = useSelector(selectOperation(operationId))?.organizationId;

  const [step, setStep] = useState<Step>('sirenForm');
  const [siren, setSiren] = useState<string>();
  const [duplicateRecordIds, setDuplicateRecordIds] = useState<number[]>([]);
  const [personMorales, setPersonMorales] = useState<Company[]>([]);

  const currentOrganization = useSelector(selectCurrentOrganization);
  const currentOrganizationId = currentOrganization?.id ?? operationOrganizationId;
  const currentUser = useSelector(selectCurrentUser);

  const searchExternalPersonneMoral = async (siren: string) => {
    setStep('loading');

    try {
      const personneMoraleList = await slowDown(personneMoraleClient.findCompanies(siren));

      if (personneMoraleList != null) {
        setPersonMorales(personneMoraleList);

        if (personneMoraleList.length >= 1) {
          dispatch(setSnackbarMessage(successApiMessage));
        }

        setStep('personneMoralSelection');
      } else {
        setStep('manualCreation');
      }
    } catch (e) {
      dispatch(setSnackbarMessage(notFoundMessage));
      setStep('manualCreation');
      console.error(e);
    }
  };

  const searchDuplicate = async (siren: string, template: LegalRecordTemplate) => {
    if (currentOrganizationId == null) {
      return [];
    }
    const duplicateFiltering = convertToDuplicateFiltering(siren, template);
    return dispatch(getRecordDuplicates(duplicateFiltering.answer, duplicateFiltering.templateId));
  };

  const handleSubmitSiren = async (submitedSiren: string) => {
    setStep('loading');
    setSiren(submitedSiren);

    const duplicateRecords = await searchDuplicate(submitedSiren, template);

    if (duplicateRecords.length > 0) {
      setStep('duplicateSelection');
      setDuplicateRecordIds(duplicateRecords.map((record) => record.id));
    }

    if (duplicateRecords.length === 0) {
      searchExternalPersonneMoral(submitedSiren);
    }
  };

  const handleCancelDuplicateSelection = async () => {
    if (siren == null) {
      return;
    }
    await searchExternalPersonneMoral(siren);
  };

  const handleSelectExistingItem = (record: Record) => {
    onValidate(record);
  };

  const handleClickPersonneMorale = async (personneMorale: Company) => {
    if (currentUser == null) {
      return;
    }
    const newRecord = convertCompanyToRecordNew(personneMorale, template, currentOrganizationId, currentUser.id);

    await onValidate(newRecord);
    dispatch(setSnackbarMessage(successValidationMessage));
  };

  const handleNotFound = () => {
    setStep('manualCreation');
  };

  const handleValidateManualAnswer = async (answer: AnswerDict) => {
    if (currentOrganizationId == null || currentUser == null) {
      return;
    }

    const recordNew = {
      answer: answer,
      creatorId: currentUser.id,
      organizationId: currentOrganizationId,
      templateId: template.id
    };

    onValidate(recordNew);
  };

  return (
    <div className={classNames('record-creation-siren', className)}>
      {step === 'sirenForm' && (
        <>
          {templateSelector}
          <SirenForm defaultValue={siren} onValidate={handleSubmitSiren} />
        </>
      )}
      {step === 'duplicateSelection' && (
        <RecordDuplicateSelection
          duplicateIds={duplicateRecordIds}
          onCancel={handleCancelDuplicateSelection}
          onSelect={handleSelectExistingItem}
          types={template?.specificTypes}
        />
      )}
      {step === 'personneMoralSelection' && (
        <>
          <SirenForm defaultValue={siren} onValidate={handleSubmitSiren} />
          <CompaniesList companies={personMorales} onClick={handleClickPersonneMorale} />
          <FooterActionBtnPopin onClick={handleNotFound} types={template?.specificTypes} />
        </>
      )}
      {step === 'manualCreation' && (
        <ManualRecordCreation
          defaultAnswer={createAnswerWithSiren(siren)}
          onClosePopin={() => setStep('sirenForm')}
          onValidate={handleValidateManualAnswer}
          template={template}
        />
      )}
      <MnLoaderPopin
        gifSrc='/assets/images/gif/siren.gif'
        isVisible={step === 'loading'}
        title='Notre robot récupère automatiquement les informations pour vous!'
      />
    </div>
  );
};

export { RecordCreationSiren };

const convertToDuplicateFiltering = (siren: string, template: LegalRecordTemplate): FindRecordDuplicatesArgs => {
  const answer = createAnswerWithSiren(siren);
  return {
    answer: pick(answer, template.config.duplicate ?? []),
    templateId: template.id
  };
};

const createAnswerWithSiren = (siren?: string): AnswerDict => {
  return { siren: createValue(siren) };
};

const successApiMessage: NewSnackbarMessage = {
  options: {
    color: 'success',
    description:
      'Confirmez la recherche ou cliquez sur “Je ne trouve pas mon résultat” pour entrer les données manuellement.',
    title: `Le robot a trouvé des entités correspondantes au N°SIREN`
  },
  templateId: 'TEMPORARY'
};

const successValidationMessage: NewSnackbarMessage = {
  options: {
    color: 'success',
    title: `L’entité correspondante au N° SIREN a été créée`
  },
  templateId: 'TEMPORARY'
};

const notFoundMessage: NewSnackbarMessage = {
  options: {
    color: 'warning',
    description: 'Veuillez saisir manuellement les données.',
    title: `Aucun résultat trouvé pour le N°SIREN`
  },
  templateId: 'TEMPORARY'
};
