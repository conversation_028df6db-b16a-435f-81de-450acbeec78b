import './recordCreationDefault.scss';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { classNames } from '@mynotary/frontend/shared/util';
import {
  FindRecordDuplicatesArgs,
  getRecordDuplicates,
  Record,
  RecordDuplicateSelection,
  selectCurrentOperation
} from '@mynotary/frontend/legals/api';
import React, { ReactElement, useState } from 'react';
import { pick } from 'lodash';
import { MnLoaderPopin } from '@mynotary/frontend/shared/ui';
import { ManualRecordCreation } from '../../manualRecordCreation/manualRecordCreation';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';
import { setSuccessMessage } from '@mynotary/frontend/snackbars/api';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { RecordCreationComponentProps } from '../../legal-record-creations';

type RecordCreationStep = 'form' | 'loading' | 'duplicate';

const RecordCreationDefault = ({
  className,
  defaultCreationAnswer,
  filteredRecordIds,
  onClosePopin,
  onValidate,
  template,
  templateSelector
}: RecordCreationComponentProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const [answer, setAnswer] = useState<AnswerDict>(defaultCreationAnswer ?? {});
  const [duplicateRecordIds, setDuplicateRecordIds] = useState<number[]>([]);
  const [step, setStep] = useState<RecordCreationStep>('form');

  const currentOrganization = useSelector(selectCurrentOrganization);
  const currentOperation = useSelector(selectCurrentOperation);
  const currentUser = useSelector(selectCurrentUser);

  const createRecordAndValidate = async (answer: AnswerDict) => {
    const organizationId = currentOrganization?.id ?? currentOperation?.organizationId;
    const creatorId = currentUser?.id ?? currentOperation?.creatorUser?.id;
    assertNotNull(organizationId, 'Organization is required to create record');
    assertNotNull(creatorId, 'Creator is required to create)');

    await onValidate({
      answer: answer,
      creatorId,
      organizationId,
      templateId: template.id
    });

    dispatch(setSuccessMessage('Fiche créée avec succès !'));
  };

  const handleClickConfirm = async (answer: AnswerDict) => {
    setStep('loading');
    setAnswer(answer);

    try {
      let duplicateRecords: Record[] = [];

      if (currentOrganization) {
        const duplicateFiltering = convertToRecordDuplicateFiltering(answer, template);
        duplicateRecords = await dispatch(
          getRecordDuplicates(duplicateFiltering.answer, duplicateFiltering.templateId, filteredRecordIds)
        );
      }

      if (duplicateRecords.length > 0) {
        setStep('duplicate');
        setDuplicateRecordIds(duplicateRecords.map((record) => record.id));
      } else {
        await createRecordAndValidate(answer);
      }
    } catch (e) {
      console.error(e);
      setStep('form');
    }
  };

  const handleCancelDuplicateSelection = async () => {
    await createRecordAndValidate(answer);
  };

  const handleSelectExistingItem = (record: Record) => {
    onValidate(record);
  };

  return (
    <div className={classNames('record-creation-default', className)}>
      {step === 'form' && (
        <ManualRecordCreation
          defaultAnswer={defaultCreationAnswer}
          onClosePopin={onClosePopin}
          onValidate={handleClickConfirm}
          template={template}
          templateSelector={templateSelector}
        />
      )}
      {step === 'loading' && <MnLoaderPopin isVisible={step === 'loading'} />}
      {step === 'duplicate' && (
        <RecordDuplicateSelection
          duplicateIds={duplicateRecordIds}
          onCancel={handleCancelDuplicateSelection}
          onSelect={handleSelectExistingItem}
          types={template?.specificTypes}
        />
      )}
    </div>
  );
};

export { RecordCreationDefault };

export const convertToRecordDuplicateFiltering = (
  answer: AnswerDict,
  template: LegalRecordTemplate
): FindRecordDuplicatesArgs => {
  return {
    answer: pick(answer, template.config.duplicate ?? []),
    templateId: template.id
  };
};
