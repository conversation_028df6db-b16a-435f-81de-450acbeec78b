import './operationGroupRecordMobileTile.scss';
import { ActionPopoverOverlay, MnSvg } from '@mynotary/frontend/shared/ui';
import { useRef, useState } from 'react';
import { MnRenamePopin, OrderedFile, putRenameFile } from '@mynotary/frontend/files/api';
import { FileInfo } from '@mynotary/crossplatform/files-client/api';
import { getNameWithExtension } from '@mynotary/crossplatform/files/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { useLongPress } from '@mynotary/frontend/shared/util';

interface OperationGroupRecordMobileTileProps {
  editable: boolean;
  file: OrderedFile;
  handleFileDeletion: (file: FileInfo) => void;
  handleSelectedFile: (file?: FileInfo) => void;
}

const OperationGroupRecordMobileTile = ({
  editable,
  file,
  handleFileDeletion,
  handleSelectedFile
}: OperationGroupRecordMobileTileProps) => {
  const dispatch = useAsyncDispatch();
  const [isRenamePopinOpen, setIsRenamePopinOpen] = useState(false);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  const { extension, name } = getNameWithExtension({ name: file.name, type: file.contentType });

  const handleFileRenaming = (newLabel: string): void => {
    if (newLabel && newLabel !== name) {
      dispatch(putRenameFile(file.id, `${newLabel}.${extension}`));
    }
  };

  const actions = [
    {
      icon: '/assets/images/pictos/icon/edit-2-light.svg',
      id: 'rename',
      label: 'Renommer',
      onClick: () => setIsRenamePopinOpen(true)
    },
    {
      icon: '/assets/images/pictos/icon/trash-light.svg',
      id: 'delete',
      label: 'Supprimer',
      onClick: () => handleFileDeletion(file)
    }
  ];

  const longPressEvent = useLongPress(
    {
      onClick: editable ? () => handleSelectedFile(file) : undefined,
      onLongPress: editable ? () => setIsPopoverOpen(true) : undefined
    },
    { delay: 500 }
  );

  return (
    <div>
      <div className='operation-group-record-mobile-tile' key={file.id} ref={ref} {...longPressEvent}>
        <div className='ogrmt-icon'>
          <MnSvg mode='normal' path={`/assets/images/pictos/filled/icon-docs-${extension}.svg`} />
        </div>
        <div className='ogrmt-label'> {file.name}</div>
      </div>

      {isRenamePopinOpen && (
        <MnRenamePopin
          defaultValue={name}
          editable={editable}
          isOpen={isRenamePopinOpen}
          needFilenameValidation={true}
          onClose={() => setIsRenamePopinOpen(false)}
          onValidate={handleFileRenaming}
          title='Renommer le fichier'
        />
      )}
      {isPopoverOpen && (
        <ActionPopoverOverlay actions={actions} hideOverlay={() => setIsPopoverOpen(false)} parentRef={ref} />
      )}
    </div>
  );
};

export { OperationGroupRecordMobileTile };
