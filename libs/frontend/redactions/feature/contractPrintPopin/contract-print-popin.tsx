import './contract-print-popin.scss';
import { ReactElement, ReactNode, useMemo, useState } from 'react';
import { downloadContract, downloadContractWithAnnexes } from '../contract-download.thunks';
import { RedactionContract } from '../redaction-contract/redaction-contract';
import { useSelector } from 'react-redux';
import { selectAreClausesLoaded } from '@mynotary/frontend/legals/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { isBrowser, MnProps, useGlobalLoader, useResponsive } from '@mynotary/frontend/shared/util';
import {
  FloatingButton,
  MnButtonController,
  MnMainContextBar,
  MnMultiLayerOverlay,
  MnSvg,
  MnZoom,
  PopoverActionList,
  SheetContent,
  SheetHeader,
  SheetPopin
} from '@mynotary/frontend/shared/ui';
import { setErrorMessage } from '@mynotary/frontend/snackbars/api';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { FilesService, FileViewer } from '@mynotary/frontend/files/api';
import { useRedactionContractDataFetching } from '../use-redaction-contract-data-fetching';

interface ContractPrintPopinProps extends MnProps {
  contractId: number;
  fetchOperationData?: boolean;
  onClose: () => void;
  variant: 'VIEW_ONLY' | 'DOWNLOAD' | 'HIDDEN';
}

export const ContractPrintPopin = ({
  contractId,
  fetchOperationData,
  onClose,
  variant
}: ContractPrintPopinProps): ReactElement => {
  const [zoom, setZoom] = useState<number>(100);

  const { contract, theme } = useRedactionContractDataFetching({ contractId });

  const actionsToolbar = useMemo(
    () => [
      {
        checkCondition: () => !isBrowser('Firefox'),
        component: <MnZoom defaultValue={zoom} min={10} onChange={setZoom} unit={10} />,
        divider: true
      }
    ],
    [zoom]
  );

  /**
   * Can be used for downloading contract with generateContractPdf when we need a contract outside of redaction
   */
  if (variant === 'HIDDEN') {
    return (
      <div className='contract-print-mobile-hidden'>
        <RedactionContract contractId={contractId} readonly={true} />
      </div>
    );
  }

  const hasDownload = variant === 'DOWNLOAD';

  return (
    <SheetPopin isOpened={true} onClose={onClose} size='large'>
      <SheetHeader onClose={onClose}>
        <MnSvg path='/assets/images/pictos/icon/file.svg' variant='gray700' />
      </SheetHeader>
      <SheetContent
        actions={
          hasDownload ? (
            <ContractPrintPopinActions contractId={contractId} fetchOperationData={fetchOperationData} />
          ) : undefined
        }
      >
        {contract && contract.contractFileId && <FileViewer fileId={contract.contractFileId} />}
        {theme && contract.contractFileId == null && (
          <RedactionContract
            contractId={contractId}
            readonly={true}
            toolbar={<MnMainContextBar actions={actionsToolbar} />}
            zoom={zoom}
          />
        )}
        {hasDownload && (
          <ContractPrintMobileActions>
            <ContractPrintPopinActions contractId={contractId} fetchOperationData={fetchOperationData} />
          </ContractPrintMobileActions>
        )}
      </SheetContent>
    </SheetPopin>
  );
};

const ContractPrintPopinActions = ({
  contractId,
  fetchOperationData
}: {
  contractId: number;
  fetchOperationData?: boolean;
}): ReactElement => {
  const dispatch = useAsyncDispatch();
  const filesService = useService(FilesService);
  const areClausesLoaded = useSelector(selectAreClausesLoaded);
  const [setIsLoading, isLoading] = useGlobalLoader(false);

  const handleDownloadContract = async (withAnnexes: boolean) => {
    setIsLoading(true);
    try {
      const file = await dispatch(
        withAnnexes
          ? downloadContractWithAnnexes(contractId, fetchOperationData)
          : downloadContract(contractId, fetchOperationData)
      );
      await filesService.downloadFile(file);
    } catch {
      dispatch(setErrorMessage('Une erreur inconnue empêche le téléchargement'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='contract-print-popin-actions'>
      <MnButtonController
        bolded={true}
        className='cppa-button'
        disabled={isLoading || !areClausesLoaded}
        label='Télécharger le contrat'
        leftIcon='/assets/images/pictos/icon/download-light.svg'
        onClick={() => handleDownloadContract(false)}
        underlined={false}
      />

      <MnButtonController
        bolded={true}
        className='cppa-button'
        disabled={isLoading || !areClausesLoaded}
        label='Télécharger le contrat et annexes'
        leftIcon='/assets/images/pictos/icon/download-light.svg'
        onClick={() => handleDownloadContract(true)}
        underlined={false}
      />
    </div>
  );
};

const ContractPrintMobileActions = ({ children }: { children: ReactNode }) => {
  const [opened, setOpened] = useState(false);

  const isTablet = useResponsive('TABLET');

  if (isTablet) {
    return null;
  }

  return (
    <>
      <FloatingButton icon={'/assets/images/pictos/icon/plus-light.svg'} onClick={() => setOpened(true)} />
      {opened && (
        <MnMultiLayerOverlay onClose={() => setOpened(false)}>
          <PopoverActionList className='contract-print-mobile-actions'>{children}</PopoverActionList>
        </MnMultiLayerOverlay>
      )}
    </>
  );
};
