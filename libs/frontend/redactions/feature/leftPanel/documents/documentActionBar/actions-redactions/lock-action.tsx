import { ActionIcon } from '@mynotary/frontend/shared/ui';
import { OperationFolderSelectableItem, OperationFolderSelectableItemDocument } from '../../../../documentHelpers';
import { ContractLegacy } from '@mynotary/frontend/legals/api';
import { Dictionary } from '@mynotary/crossplatform/shared/util';
import { useDocumentActions } from '../../../../useDocumentAction';
import { useDocumentsSelectedPermissions } from './use-documents-selected-permissions';

interface LockActionProps {
  contract: ContractLegacy;
  documents: OperationFolderSelectableItemDocument[];
  onActionClick: () => void;
  operationId: number;
  selectableItems: OperationFolderSelectableItem[];
  selectedItemIds: Dictionary<boolean>;
}

export const LockAction = ({
  contract,
  onActionClick,
  operationId,
  selectableItems,
  selectedItemIds
}: LockActionProps) => {
  const { canLockIds } = useDocumentsSelectedPermissions({
    contractId: contract.id,
    operationId,
    selectableItems,
    selectedItemIds
  });

  const { handleLockDocument } = useDocumentActions({
    contract,
    operationId,
    selectableItems,
    selectedItemIds
  });

  const handleLockActionClick = () => {
    handleLockDocument(true);
    onActionClick();
  };

  if (!canLockIds) {
    return null;
  }

  return (
    <div className='download-action'>
      <ActionIcon
        icon='/assets/images/pictos/icon/lock.svg'
        label={'Vérifiée(s) et non modifiable(s)'}
        onClick={() => handleLockActionClick()}
        testId={'lock'}
      />
    </div>
  );
};
