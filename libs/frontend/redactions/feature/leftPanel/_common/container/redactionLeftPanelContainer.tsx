import './redactionLeftPanelContainer.scss';
import React, { ReactElement, ReactNode } from 'react';
import { MnProps, classNames } from '@mynotary/frontend/shared/util';

interface RedactionLeftPanelContainerProps extends MnProps {
  children: ReactNode;
}

const RedactionLeftPanelContainer = ({ children, className }: RedactionLeftPanelContainerProps): ReactElement => {
  return <div className={classNames('redaction-left-panel-container', className)}>{children}</div>;
};

export { RedactionLeftPanelContainer };
