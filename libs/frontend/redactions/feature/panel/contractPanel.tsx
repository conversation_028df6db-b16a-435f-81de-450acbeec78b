import './contractPanel.scss';
import { MnMainContextBar, MnZoom } from '@mynotary/frontend/shared/ui';
import { Panel } from './panel';
import { useMemo, useState } from 'react';
import { ContractHeader } from '../contractHeader/contractHeader';
import { useSelector } from 'react-redux';
import { ContractVisibility } from '../contractVisibility/contractVisibility';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { DownloadContract } from '../actions/downloadContract/downloadContract';
import { selectContract, selectContractPermission } from '@mynotary/frontend/legals/api';
import { isBrowser, useResponsive } from '@mynotary/frontend/shared/util';
import { DependenciesPopin } from '../dependencyPopin/dependenciesPopin';
import { RedactionContract } from '../redaction-contract/redaction-contract';
import { plateFixedToolbarDomId } from '@mynotary/frontend/text-editor/api';
import { selectEditingPermission } from '@mynotary/frontend/redactions/store';
import { EditingPermission } from '@mynotary/frontend/redactions/core';

interface ContractPanelProps {
  contractId: number;
  operationId: number;
}

export const ContractPanel = ({ contractId, operationId }: ContractPanelProps) => {
  const [zoom, setZoom] = useState(100);
  const contract = useSelector(selectContract(contractId));
  const canSeeContract = useSelector(selectContractPermission(PermissionType.READ_CONTRACT, contractId));
  const editingPermission = useSelector(selectEditingPermission(contractId));
  const isResponsive = useResponsive('SMALL_DESKTOP');

  const actions = useMemo(
    () => [
      {
        checkCondition: () => !isResponsive && !isBrowser('Firefox'),
        component: <MnZoom defaultValue={100} max={130} min={80} onChange={setZoom} withLabel={false} />,
        divider: true
      },
      {
        checkCondition: () => canSeeContract,
        component: <DownloadContract contractId={contractId} operationId={operationId} />
      }
    ],
    [canSeeContract, contractId, isResponsive, operationId]
  );

  const secondaryActions = useMemo(
    () => [
      {
        checkCondition: () => editingPermission !== EditingPermission.READ_ONLY,
        component: <div id={plateFixedToolbarDomId} />
      }
    ],
    [editingPermission]
  );

  if (!contract) {
    return null;
  }

  if (!contract?.contractFileId && !canSeeContract) {
    return <ContractVisibility />;
  }

  return (
    <Panel className='contract-panel'>
      {isResponsive && <ContractHeader contractId={contractId} operationId={operationId} />}
      <div className='cp-redaction-container'>
        <DependenciesPopin contractId={contractId} operationId={operationId} />
        <RedactionContract
          contractId={contractId}
          toolbar={
            !isResponsive ? (
              <MnMainContextBar actions={actions} className='cp-context-bar' secondaryActions={secondaryActions} />
            ) : undefined
          }
          zoom={zoom}
        />
      </div>
    </Panel>
  );
};
