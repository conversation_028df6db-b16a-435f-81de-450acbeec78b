import { ImagePlugin, PlateEditor, TElement } from '@mynotary/frontend/text-editor/api';
import { last, size } from 'lodash';
import { TableOfContentElement } from './plugins/tableOfContentPlugin';
import { DividerElement } from './plugins/dividerPlugin';
import { ClauseElement } from './plugins/clause/clause-plugin';
import { FlyLeafElement } from './plugins/flyLeaf/flyLeafPlugin';
import { LineBreakElement } from './plugins/lineBreakPlugin';
import { PageBreakElement } from './plugins/pageBreakPlugin';

export type RedactionEditorElement =
  | ClauseElement
  | TableOfContentElement
  | FlyLeafElement
  | LineBreakElement
  | PageBreakElement
  | DividerElement;

/**
 * ClauseContentElement represents the content of a ClauseEditableElement, ClauseNonEditableElement or ClauseDetailsElement.

 * It can be any of the following types:
 * - HeadingElement
 * - ImageElement
 * - LinkElement
 * - ParagraphElement
 * - ListElement
 */
export type ClauseContentElement = {} & TElement;

export const lastOperationIsResize = (editor: PlateEditor): boolean => {
  const lastOperation = last(editor.operations);
  return lastOperation?.type === 'set_node' && lastOperation?.newProperties?.['colSizes'] != null;
};

export const lastOperationIsSetSelection = (editor: PlateEditor): boolean => {
  const lastOperation = last(editor.operations);
  return size(editor.operations) === 1 && lastOperation?.type === 'set_selection';
};

export const lastOperationIsInsertImage = (editor: PlateEditor): boolean => {
  const lastOperation = last(editor.operations);
  return lastOperation?.type === 'insert_node' && lastOperation?.node?.type === ImagePlugin.key;
};

export const extractClauseIndexFromLastOperation = (editor: PlateEditor) => {
  const lastOperation = last(editor.operations);
  if (lastOperation != null && Array.isArray(lastOperation.path)) {
    const clauseIndex: number = lastOperation.path[0];
    if (editor.children[clauseIndex]) {
      return clauseIndex;
    }
  }
  return null;
};
