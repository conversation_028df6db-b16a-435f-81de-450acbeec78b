import { Descendant, isElement, PlateEditor } from '@mynotary/frontend/text-editor/api';
import { JeffersonFlatNode, OrganizationClause } from '@mynotary/frontend/legals/api';
import { forEach } from 'lodash';
import { createClauseElement } from './plugins/clause/create-clause-element';


/**
 * @see REDACTIONS.md/OrganizationClause
 */
export const createOrganizationClause = (args: {
  editor: PlateEditor;
  frameNode: JeffersonFlatNode;
  organizationClause: OrganizationClause;
}) => {
  const children = JSON.parse(args.organizationClause.content);
  const traverse = (node: Descendant) => {
    if (!isElement(node)) {
      return;
    }

    if ('mappingStackPath' in node) {
      node['mappingStackPath'] = args.frameNode.mappingStackPath;
    }

    forEach(node.children, (n) => traverse(n));
  };

  forEach(children, (child) => traverse(child));

  return createClauseElement({ children, editor: args.editor, frameNode: args.frameNode });
};
