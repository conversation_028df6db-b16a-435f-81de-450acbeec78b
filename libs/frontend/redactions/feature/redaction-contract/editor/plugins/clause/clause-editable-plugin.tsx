import { createPlatePlugin, PlateElement, TElement } from '@mynotary/frontend/text-editor/api';
import React from 'react';
import { ClauseContentElement } from '../../redaction-editor';

export const ClauseEditablePlugin = createPlatePlugin({
  key: 'ClauseEditable',
  node: {
    component: ({ children, ...props }) => {
      return <PlateElement {...props}>{children}</PlateElement>;
    },
    isElement: true,
    type: 'ClauseEditable'
  }
});

export interface ClauseEditableElement extends TElement {
  children: ClauseContentElement[];
  type: 'ClauseEditable';
}
