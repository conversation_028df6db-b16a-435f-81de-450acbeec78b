import { sortBy } from 'lodash';
import { ClausePlugin } from './editor/plugins/clause/clause-plugin';

const sortMutationByPriority = (mutation: MutationRecord) => {
  if (mutation.type === 'attributes' && mutation.attributeName === 'data-slate-value') {
    return 1;
  } else if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
    return 2;
  } else if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
    return 3;
  }
  return 4;
};

export const scrollOnContractChange = (mutationList: MutationRecord[], targetElement: Element) => {
  if (targetElement.contains(document.activeElement)) {
    return;
  }

  let element: HTMLElement | null = null;
  const mutations = sortBy(mutationList, sortMutationByPriority);

  const removeBackgroundColor = () => {
    if (element != null) {
      element.style.backgroundColor = '';
    }
  };

  for (const mutation of mutations) {
    if (
      mutation.type === 'attributes' &&
      mutation.attributeName === 'data-slate-value' &&
      isScrollableHtmlElement(mutation.target)
    ) {
      element = mutation.target.parentElement;
    } else if (mutation.type === 'childList' && isScrollableHtmlElement(mutation.addedNodes[0])) {
      element = mutation.addedNodes[0];
    } else if (mutation.type === 'childList' && isScrollableHtmlElement(mutation.previousSibling)) {
      element = mutation.previousSibling;
    }

    if (element != null) {
      if (element.attributes.getNamedItem('data-slate-type')?.value === ClausePlugin.key) {
        element.style.backgroundColor = '#FFF3CC';
      }

      /**
       * Prevent scroll when drag handles on table elements are hovered in contract
       */
      if ((mutation.target as HTMLElement).contentEditable === 'false' && element.children.length === 0) {
        return;
      }
      setTimeout(removeBackgroundColor, 2000);
      element.scrollIntoView({ block: 'center' });
      break;
    }
  }
};

function isScrollableHtmlElement(element?: Node | null): element is HTMLElement {
  return typeof (element as HTMLElement)?.scrollIntoView === 'function';
}
