import './documentFileCount.scss';
import { MnTag, ContractStatusTagEnum } from '@mynotary/frontend/shared/ui';
import { pluralize, classNames } from '@mynotary/frontend/shared/util';

interface MnDocumentFileCountProps {
  documentCount: number;
  isDisabled: boolean;
}

const MnDocumentFileCount = ({ documentCount, isDisabled }: MnDocumentFileCountProps) => {
  return (
    <MnTag
      className={classNames('mn-document-file-count', { isEmpty: documentCount === 0 })}
      label={`${documentCount} ${pluralize('fichier', documentCount)} `}
      variant={isDisabled ? ContractStatusTagEnum.DISABLED : ContractStatusTagEnum.WAITING}
    />
  );
};

export { MnDocumentFileCount };
