import './floatingPopinPagination.scss';
import React, { ReactElement, useState } from 'react';
import { findIndex } from 'lodash';
import { MnButtonIcon } from '@mynotary/frontend/shared/ui';
import { FileInfo } from '@mynotary/crossplatform/files-client/api';

interface MnFloatingPopinPaginationProps {
  files: FileInfo[];
  handleSelectedFiles: (selectedFile: FileInfo) => void;
  selectedFile?: FileInfo;
}

const MnFloatingPopinPagination = ({
  files,
  handleSelectedFiles,
  selectedFile
}: MnFloatingPopinPaginationProps): ReactElement => {
  const [index, setIndex] = useState<number>(1);

  const handleNavigation = (direction: string): void | null => {
    const indexFiles = findIndex(files, (file) => selectedFile?.id === file.id);

    if (direction === 'next') {
      if (index >= files.length) return null;
      handleSelectedFiles(files[indexFiles + 1]);
      setIndex(index + 1);
    } else {
      if (index <= 1) return null;
      handleSelectedFiles(files[indexFiles - 1]);
      setIndex(index - 1);
    }
  };

  return (
    <div className='floating-popin-footer'>
      <MnButtonIcon
        className='fpf-icon-arrow'
        onClick={() => handleNavigation('back')}
        path='/assets/images/pictos/icon/chevron-left.svg'
        size='small'
        variant='primary'
      />
      <div className='fpf-selection'>
        <span>{index}</span>
        <span>/</span>
        <span>{files.length}</span>
      </div>
      <MnButtonIcon
        className='fpf-icon-arrow fpf-icon-arrow-reversed'
        onClick={() => handleNavigation('next')}
        path='/assets/images/pictos/icon/chevron-left.svg'
        size='small'
        variant='primary'
      />
    </div>
  );
};

export { MnFloatingPopinPagination };
