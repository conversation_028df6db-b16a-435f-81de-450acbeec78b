import styles from './operation-headline.module.scss';

import React from 'react';
import { HeaderNavigationItem, MnCircularProgressionBar, MnSvg, NavBarItem } from '@mynotary/frontend/shared/ui';
import { useParams } from 'react-router-dom';
import { useQueryOperation } from '@mynotary/frontend/operations-portalys/core';
import { plRoutePaths, reverse } from '@mynotary/frontend/routes/api';
import { useSelector } from 'react-redux';
import { selectCurrentMemberOrganizationId } from '@mynotary/frontend/user-session/api';
import { selectCurrentOperationOrganizationId } from '@mynotary/frontend/legals/api';

export const OperationHeadline = () => {
  const { id: operationId } = useParams();
  const currentOrganizationId = useSelector(selectCurrentMemberOrganizationId);
  const operationOrganizationId = useSelector(selectCurrentOperationOrganizationId);
  const organizationId = operationOrganizationId
    ? operationOrganizationId.toString()
    : currentOrganizationId
      ? currentOrganizationId.toString()
      : '';

  const operationQuery = useQueryOperation({ operationId, organizationId });

  const params = useParams();

  const menuItems: NavBarItem[] = [
    {
      id: 'general',
      label: 'Général',
      link: {
        href: '/operations',
        relativePath: '/operations'
      }
    },
    {
      id: 'demandes',
      label: 'Demandes',
      link: {
        href: '/operations',
        relativePath: '/operations'
      },
      selected: true
    },
    {
      id: 'stockage',
      label: 'Stockage',
      link: {
        href: '/operations',
        relativePath: '/operations'
      }
    },
    {
      id: 'actes',
      label: 'Actes',
      link: {
        href: '/operations',
        relativePath: '/operations'
      }
    },
    {
      id: 'notes',
      label: 'Notes',
      link: {
        href: '/operations',
        relativePath: '/operations'
      }
    },
    {
      id: 'contrats',
      label: 'Contrats',
      link: {
        href: reverse(plRoutePaths.operation.contrats.path, params),
        relativePath: plRoutePaths.operation.contrats.relativePath
      }
    }
  ];

  return (
    <div className={styles.headline}>
      <div className={styles.infos}>
        <HeadIcon />
        <HeadTitle label={operationQuery.data.label} type={operationQuery.data.type} />
        <HeadTasks nbTasks={operationQuery.data.nbTasks} />
        <HeadProgress progress={operationQuery.data.progress} />
      </div>
      <div className={styles.tabMenu}>
        {menuItems.map((item) => {
          return <HeaderNavigationItem item={item} key={item.id} />;
        })}
      </div>
    </div>
  );
};

const HeadIcon = () => {
  return (
    <div className={styles.icon}>
      <MnSvg mode={'normal'} path={'/assets/images/pictos/icon/folder-light.svg'} size={'medium'} variant={'black'} />
    </div>
  );
};

interface HeadTitleProps {
  label: string;
  type: string;
}

const HeadTitle = ({ label, type }: HeadTitleProps) => {
  return (
    <div className={styles.title}>
      Dossier {type}
      <br />
      {label}
    </div>
  );
};

interface HeadTasksProps {
  nbTasks: number;
}

const HeadTasks = ({ nbTasks }: HeadTasksProps) => {
  return (
    <div className={styles.infoblock}>
      <div className={styles.infoblockTitle}>
        Tâches
        <br />
        <span className={styles.infoblockSubtitle}>du dossier</span>
      </div>
      <div className={styles.infoblockContent}>
        <div className={styles.task}>
          <MnSvg
            className={styles.taskIcon}
            mode={'normal'}
            path={'/assets/images/pictos/icon/task.svg'}
            size={'medium'}
            variant={'primary'}
          />
          {nbTasks}
        </div>
      </div>
    </div>
  );
};

interface HeadProgressProps {
  progress: number;
}

const HeadProgress = ({ progress }: HeadProgressProps) => {
  return (
    <div className={styles.infoblock}>
      <div className={styles.infoblockTitle}>
        % Avancée
        <br />
        <span className={styles.infoblockSubtitle}>du dossier</span>
      </div>
      <div className={styles.infoblockContent}>
        <MnCircularProgressionBar size={40} strokeWidth={4} value={progress} />
      </div>
    </div>
  );
};
