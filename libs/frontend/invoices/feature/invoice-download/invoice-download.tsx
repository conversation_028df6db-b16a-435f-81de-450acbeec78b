import { useService } from '@mynotary/frontend/shared/injector-util';
import { InvoicesClient } from '@mynotary/frontend/invoices/core';
import { downloadBlob, useGlobalLoaderClass } from '@mynotary/frontend/shared/util';
import { useSelector } from 'react-redux';
import { selectCurrentMemberOrganizationId, selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { ActionIcon } from '@mynotary/frontend/shared/ui';
import React from 'react';
import { selectInvoice } from '@mynotary/frontend/invoices/store';

interface InvoiceDownloadProps {
  invoiceId: string;
  onFinish?: () => void;
}

export const InvoiceDownload = ({ invoiceId, onFinish }: InvoiceDownloadProps) => {
  const invoicesClient = useService(InvoicesClient);

  const user = useSelector(selectCurrentUser);
  const organizationId = useSelector(selectCurrentMemberOrganizationId);
  const invoice = useSelector(selectInvoice(invoiceId));

  const { displayGlobalLoader, hideGlobalLoader, isGlobalLoaderVisible } = useGlobalLoaderClass();

  const handleDownloadInvoice = async () => {
    if (user == null || organizationId == null) {
      throw new Error('User or organization not found');
    }

    displayGlobalLoader();

    try {
      const blob = await invoicesClient.downloadInvoice({ invoiceId, organizationId, userId: user.id });

      const fileName = `${invoice?.invoiceLabel}.pdf`;

      downloadBlob(blob, fileName);
    } finally {
      hideGlobalLoader();
    }

    onFinish?.();
  };

  return (
    <ActionIcon
      disabled={isGlobalLoaderVisible}
      icon={'/assets/images/pictos/icon/download-light.svg'}
      label={'Télécharger'}
      onClick={handleDownloadInvoice}
    />
  );
};
