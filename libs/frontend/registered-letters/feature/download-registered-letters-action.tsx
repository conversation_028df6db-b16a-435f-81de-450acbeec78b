import { FilesClient } from '@mynotary/crossplatform/files-client/api';
import { FilesService } from '@mynotary/frontend/files/api';
import { createRegisterLetterFolder, RegisteredLetterBatch } from '@mynotary/frontend/registered-letters/core';
import { useService, inject } from '@mynotary/frontend/shared/injector-util';
import { ActionIcon } from '@mynotary/frontend/shared/ui';
import { useGlobalLoader } from '@mynotary/frontend/shared/util';

interface DownloadRegisteredLettersActionProps {
  batch: RegisteredLetterBatch;
  onFinish?: () => void;
}

export const DownloadRegisteredLettersAction = ({ batch, onFinish }: DownloadRegisteredLettersActionProps) => {
  const filesService = useService(FilesService);
  const [setIsLoading] = useGlobalLoader(false);
  const filesClient = inject(FilesClient);

  const downloadProofFile = async () => {
    try {
      setIsLoading(true);
      const file = await filesClient.createArchive(createRegisterLetterFolder(batch));
      await filesService.downloadFile(file);
    } finally {
      setIsLoading(false);
      onFinish?.();
    }
  };

  return (
    <ActionIcon
      icon='/assets/images/pictos/icon/download-light.svg'
      label='Télécharger dossier de preuves'
      onClick={downloadProofFile}
    />
  );
};
