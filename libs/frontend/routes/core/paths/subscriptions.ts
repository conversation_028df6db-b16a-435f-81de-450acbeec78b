const base = '/abonnements';

export const subscriptions = {
  path: base,
  relativePath: `${base}/*`,
  subscriptionAddons: {
    path: `${base}/options`,
    relativePath: '/options'
  },
  subscriptionCart: {
    path: `${base}/panier`,
    relativePath: '/panier'
  },
  subscriptionCompany: {
    path: `${base}/informations-société`,
    relativePath: '/informations-société'
  },
  subscriptionManager: {
    path: `${base}/informations-responsable`,
    relativePath: '/informations-responsable'
  },
  subscriptionPlan: {
    path: `${base}/offres`,
    relativePath: '/offres'
  },
  /**
   * If subscriptionResult is changed here, it must be changed in subscriptions-provider.service.impl.ts
   */
  subscriptionResult: {
    path: `${base}/resultat`,
    relativePath: '/resultat'
  }
};
