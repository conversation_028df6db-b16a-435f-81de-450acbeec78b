import styles from './dash-inquiries.module.scss';
import { MnCard, MnSearchInput, MnSelect, MnSvg, Tag } from '@mynotary/frontend/shared/ui';
import React, { useState } from 'react';
import classNames from 'classnames';
import { PlInquiryList } from '@mynotary/frontend/inquiries/api';
import { useSelector } from 'react-redux';
import { selectCurrentMemberOrganizationId } from '@mynotary/frontend/user-session/api';
import { useQueryInquiriesByStatus } from '@mynotary/frontend/dashboards-portalys/core';
import { InquiryType } from '@mynotary/crossplatform/bff-portalys/api';
import { values } from 'lodash';

export function DashInquiries() {
  const organizationId = useSelector(selectCurrentMemberOrganizationId);

  const queryStatuses = useQueryInquiriesByStatus(organizationId);

  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedType, setSelectedType] = useState<InquiryType | undefined>();
  const [searchText, setSearchText] = useState<string | undefined>();
  const typeOptions = values(InquiryType).map((option: InquiryType) => ({ id: option }));

  return (
    <div className={styles.dashInquiries}>
      <div className={styles.statusSelectionBar}>
        <h4>Suivi des demandes</h4>
        {queryStatuses.isPending && <div>Changement en cours...</div>}
        {queryStatuses.isError && <div>Erreur {queryStatuses.error.message}</div>}
        {queryStatuses.isSuccess && (
          <div className={styles.statuses}>
            {queryStatuses.data.map((status) => (
              <div
                className={classNames(styles.status, status.id === selectedStatus && styles.selected)}
                key={status.id}
                onClick={() => setSelectedStatus(status.id)}
              >
                <div className={styles.statusLabel}>
                  {status.color && <Tag color={status.color} label={'\u00A0'} shape={'rounded'} />}
                  {!status.color && (
                    <MnSvg path={'/assets/images/pictos/icon/grid.svg'} size={'small'} variant={'black'} />
                  )}
                  {status.label}
                </div>
                <div className={styles.statusCount}>{status.count}</div>
              </div>
            ))}
          </div>
        )}
      </div>
      <div className={styles.inquiriesBar}>
        <MnCard className={styles.filtersBar}>
          <MnSelect
            className={styles.selectFilter}
            getOptionLabel={(opt) => opt.id}
            onChange={(opt) => setSelectedType(opt?.id)}
            options={typeOptions}
            placeholder={'Type'}
            required={false}
            value={typeOptions.find((opt) => opt.id === selectedType)}
          />
          <MnSearchInput
            debounceTime={250}
            onChange={(search) => setSearchText(search)}
            placeholder={'Rechercher un nom un id...'}
            variant={'tile'}
          />
        </MnCard>
        <div className={styles.inquiries}>
          {organizationId && (
            <PlInquiryList
              organizationId={organizationId?.toString()}
              search={searchText}
              status={selectedStatus !== 'all' ? selectedStatus : undefined}
              type={selectedType}
            />
          )}
        </div>
      </div>
    </div>
  );
}
