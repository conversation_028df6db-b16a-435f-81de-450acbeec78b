import './csv-legal-records-importer.scss';
import { assertNotNull, Dictionary } from '@mynotary/crossplatform/shared/util';
import React, { ReactElement, useState } from 'react';
import { useSelector } from 'react-redux';
import { find, forEach, isEmpty, map } from 'lodash';
import { MnButton } from '@mynotary/frontend/shared/ui';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { AppAsyncThunk, useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { MnProps, useGlobalLoaderClass } from '@mynotary/frontend/shared/util';
import { postRecord, selectCurrentOperationOrganizationId } from '@mynotary/frontend/legals/api';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';
import { getLegalRecordTemplate } from '@mynotary/crossplatform/legal-record-templates/api';
import { setErrorMessage } from '@mynotary/frontend/snackbars/api';
import { CsvLegalRecord, CsvLegalRecordNew, getCsvColumnDescriptor } from '@mynotary/frontend/csv-legal-records/core';
import { CsvImporter } from '@mynotary/frontend/csv/api';
import {
  CsvLegalRecordConfigColumn,
  CSVLegalRecordError,
  findCsvLegalRecordConfig,
  getCsvLegalRecordErrors,
  mapCsvLegalRecordsToAnswers
} from '@mynotary/crossplatform/csv-legal-records/core';

interface RecordCsvImporterProps extends MnProps {
  columns: CsvLegalRecordConfigColumn[];
  onRecordsImported: (csvRecords: CsvLegalRecord[]) => void;
  template: LegalRecordTemplate;
}

export const CsvLegalRecordsImporter = ({
  columns,
  onRecordsImported,
  template
}: RecordCsvImporterProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const { displayGlobalLoader, hideGlobalLoader } = useGlobalLoaderClass();
  const [errors, setErrors] = useState<CSVLegalRecordError[]>();

  const currentOperationOrganizationId = useSelector(selectCurrentOperationOrganizationId);
  const currentOrganization = useSelector(selectCurrentOrganization);
  const currentUser = useSelector(selectCurrentUser);
  const organizationId = currentOperationOrganizationId ?? currentOrganization?.id;

  const descriptors = map(columns, (column) => getCsvColumnDescriptor({ column, legalRecordTemplateId: template.id }));

  const csvLegalRecordConfig = findCsvLegalRecordConfig(template.id);

  /**
   * Check that all columns in the CSV file are defined in the batch import configuration
   * Loop through each cell to check if the value match the type set in the batch import config.
   */
  const isValidCsvValues = (rows: Dictionary<string>[]) => {
    const errors = getCsvLegalRecordErrors({ columns, legalRecordTemplateId: template.id, rows });
    if (!isEmpty(errors)) {
      setErrors(errors);
      hideGlobalLoader();
      return false;
    }
    return true;
  };

  /**
   * When records are grouped, we need to find the template to use for the current row.
   * Eg: lot habitation and lot hors habitation can be imported in the same csv and we use the column 'habitation' to
   * determine which template to use.
   */
  const getCsvLegalRecordTemplate = (row: Dictionary<string>) => {
    const habitationColumn = find(columns, (column) => column.columnId === 'habitation');

    if (habitationColumn && habitationColumn.type === 'LEGAL_TEMPLATE_TYPE') {
      const choiceRowValue: string = row[habitationColumn.columnId]; // oui ou non
      const targetTemplateId = habitationColumn.choice[choiceRowValue as 'oui' | 'non'];
      const templateRecord: LegalRecordTemplate = getLegalRecordTemplate(targetTemplateId);
      assertNotNull(templateRecord, `Template record is missing ${targetTemplateId}`);
      return templateRecord;
    }

    return template;
  };

  /**
   * Loop through each cell and create the record answer formatted depending on the column type.
   */
  const convertRowsToCsvRecordNews = (rows: Dictionary<string>[]): CsvLegalRecordNew[] => {
    assertNotNull(organizationId, 'Current organization is missing');
    assertNotNull(currentUser, 'Current user is missing');

    const newRecords: CsvLegalRecordNew[] = [];
    const csvLegalRecordAnswers = mapCsvLegalRecordsToAnswers({
      columns,
      legalRecordTemplateId: template.id,
      rows
    });
    forEach(csvLegalRecordAnswers, (legalRecordAnswers) => {
      const targetTemplate = getCsvLegalRecordTemplate(legalRecordAnswers.csvData);

      newRecords.push({
        csvData: legalRecordAnswers.csvData,
        newRecord: {
          answer: legalRecordAnswers.answer,
          creatorId: currentUser.id,
          organizationId,
          templateId: targetTemplate.id
        }
      });
    });
    return newRecords;
  };

  const handleImport = async (rows: Dictionary<string>[]) => {
    displayGlobalLoader();

    if (!isValidCsvValues(rows)) {
      return;
    }
    const newRecords: CsvLegalRecordNew[] = convertRowsToCsvRecordNews(rows);

    try {
      // TODO: move all action in callback
      const values = await dispatch(createCsvRecords(newRecords));
      onRecordsImported(values);
    } catch (err) {
      console.error(err);
      dispatch(
        setErrorMessage(
          'Une erreur inconnue est survenue lors de la création des fiches. Veuillez contacter le support.'
        )
      );
    } finally {
      hideGlobalLoader();
    }
  };

  return (
    <div className='record-csv-importer'>
      <h4 className='rci-title'>{errors ? 'Erreurs' : 'Import CSV'}</h4>
      {!errors && (
        <CsvImporter
          className='rci-importer'
          csvFileName={csvLegalRecordConfig?.modelName}
          descriptors={descriptors}
          onImport={handleImport}
        />
      )}
      {errors && (
        <div className='rci-error-table'>
          {map(errors, (error, index) => (
            <div className='rci-error-row' key={index}>
              <div className='rci-error-row-header'>{error.header}</div>
              <div className='rci-error-row-message'>{error.message}</div>
            </div>
          ))}
          <MnButton className='rci-error-re-import' label='Ré-importer' onClick={() => setErrors(undefined)} />
        </div>
      )}
    </div>
  );
};

const createCsvRecords =
  (records: CsvLegalRecordNew[]): AppAsyncThunk<CsvLegalRecord[]> =>
  async (dispatch) => {
    const csvRecords: CsvLegalRecord[] = [];
    for (const csvRecord of records) {
      const legalComponent = await dispatch(postRecord(csvRecord.newRecord));
      csvRecords.push({ csvData: csvRecord.csvData, record: legalComponent });
    }
    return csvRecords;
  };
