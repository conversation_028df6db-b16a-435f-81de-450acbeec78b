export {
  Filters,
  FiltersContractApi,
  FiltersKeys,
  isArchiveContractFilter,
  isContractSignatureTypeFilter,
  isDateFilter,
  isLastAccess,
  isOriginContractFilter,
  isSearchFilter,
  isStatusContractFilter,
  isTypeContractFilter,
  isStatusOperationFilter,
  isStatusProgramFilter,
  FiltersOperationApi
} from '@mynotary/frontend/dashboard-filters/core';

export { MnFilters, useFilterLoading, FiltersUpdateButton } from '@mynotary/frontend/dashboard-filters/feature';
