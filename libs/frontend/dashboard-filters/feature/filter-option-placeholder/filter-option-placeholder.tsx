import './filter-option-placeholder.scss';
import React, { ReactElement } from 'react';
import { MnSvg } from '@mynotary/frontend/shared/ui';

const FilterOptionPlaceHolder = (): ReactElement => {
  return (
    <div className='filter-option-placeholder'>
      <div>Sélectionner une option</div>
      <MnSvg
        className='fop-chevron'
        path='/assets/images/pictos/icon/chevron-down-light.svg'
        size='medium'
        variant='gray500'
      />
    </div>
  );
};

export { FilterOptionPlaceHolder };
