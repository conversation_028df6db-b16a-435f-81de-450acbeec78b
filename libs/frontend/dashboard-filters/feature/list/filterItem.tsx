import './filterItem.scss';
import { Dictionary } from '@mynotary/crossplatform/shared/util';
import React, { ReactElement, useState } from 'react';
import { formatDateLabel, classNames } from '@mynotary/frontend/shared/util';
import { forEach, map, reduce } from 'lodash';
import { FilterDropdownItems } from '../dropdown/filterDropdownItems/filterDropdownItems';
import { FilterDropDown, MnDropdown, MnSearchInput } from '@mynotary/frontend/shared/ui';
import { MnTooltip } from '@mynotary/frontend/shared/ui';
import { MnButtonIcon, MnDropDownTileHeader } from '@mynotary/frontend/shared/ui';
import { MnFilterDateContainer } from '../date/filterDateContainer';
import {
  DateFilter,
  Filters,
  FiltersDropdown,
  FiltersKeys,
  FiltersValue,
  isArchiveContractFilter,
  isArchiveOperationFilter,
  isBillingLicenceFilter,
  isBillingStatusFilter,
  isBlankFilter,
  isContractSignatureTypeFilter,
  isDateFilter,
  isInvoiceStatus,
  isInvoiceType,
  isLastAccess,
  isOriginContractFilter,
  isOriginOperationFilter,
  isPlanTypeFilter,
  isSearchFilter,
  isStatusContractFilter,
  isStatusOperationFilter,
  isStatusProgramFilter,
  isTemplateOperationFilter,
  isTypeContractFilter,
  isTypeRecordFilter
} from '@mynotary/frontend/dashboard-filters/core';
import { FilterOptionPlaceHolder } from '../filter-option-placeholder/filter-option-placeholder';

interface MnFilterItemProps {
  availableFiltersLabels?: FiltersDropdown;
  filter: Filters[keyof Filters];
  filterIndex?: number;
  isPersonalView?: boolean;
  onHandleAddFilter?: (filterId: keyof Filters, index: number) => void;
  onHandleFilterUpdate: (mustForceReload?: boolean, newFilters?: Filters, reset?: true) => void;
  onHandleRemoveFilter?: (indexRemovedElement: number) => void;
}

/**
 * @deprecated see filter-items.tsx
 */
export const MnFilterItem = ({
  availableFiltersLabels,
  filter,
  filterIndex,
  isPersonalView,
  onHandleAddFilter,
  onHandleFilterUpdate,
  onHandleRemoveFilter
}: MnFilterItemProps): ReactElement => {
  const [isDropdownActive, setIsDropdownActive] = useState<boolean>();
  const [isHeaderOpened, setIsHeaderOpened] = useState<boolean>();

  const handleClose = (forceUpdate: boolean): void => {
    setIsDropdownActive(false);

    if (!isPersonalView) {
      onHandleFilterUpdate(forceUpdate);
    }
  };

  const getLabelDropdownItems: <Id>(values: Dictionary<FiltersValue<Id>>) => string = (values) => {
    const selectedLabels: string[] = [];

    const handleSelectedLabels: <Id>(values: Dictionary<FiltersValue<Id>>) => void = (values): void => {
      forEach(values, (value) => {
        if (value.values) {
          handleSelectedLabels(value.values);
        } else if (value.selected) {
          selectedLabels.push(value.label);
        }
      });
    };

    handleSelectedLabels(values);

    return reduce(selectedLabels, (acc, label) => (acc.length > 0 ? `${acc}; ${label}` : `${label}`), '');
  };

  const getLabelDate = (filter: DateFilter): string => {
    const selectedLabels: string[] = [];

    forEach(filter.children, (filterDate) => {
      const from = new Date(filterDate?.value?.from ?? '')?.setHours(23, 59, 59);
      const to = filterDate?.value?.to;

      if (filterDate?.label) {
        selectedLabels.push(formatDateLabel(filterDate?.label, from, to));
      }
    });

    return reduce(selectedLabels, (acc, label) => (acc.length > 0 ? `${acc}; ${label}` : `${label}`), '');
  };

  const handleHeaderDropDown = (filterId: FiltersKeys, filterIndex: number): void => {
    setIsHeaderOpened(false);
    onHandleAddFilter?.(filterId as keyof Filters, filterIndex);
    setIsDropdownActive(true);
  };

  const isCheckboxFilter = (filter?: { id: string }): filter is CheckboxFilter => {
    return (
      isOriginContractFilter(filter) ||
      isOriginOperationFilter(filter) ||
      isLastAccess(filter) ||
      isStatusContractFilter(filter) ||
      isInvoiceStatus(filter) ||
      isInvoiceType(filter) ||
      isArchiveOperationFilter(filter) ||
      isArchiveContractFilter(filter) ||
      isStatusOperationFilter(filter) ||
      isStatusProgramFilter(filter) ||
      isTemplateOperationFilter(filter) ||
      isTypeRecordFilter(filter) ||
      isTypeContractFilter(filter) ||
      isBillingStatusFilter(filter) ||
      isBillingLicenceFilter(filter) ||
      isPlanTypeFilter(filter) ||
      isContractSignatureTypeFilter(filter)
    );
  };

  return (
    <>
      {filter && typeof filterIndex === 'number' && isPersonalView && !!availableFiltersLabels && (
        <MnDropdown
          defaultIsOpened={isHeaderOpened}
          handleDefault={(isVisible) => setIsHeaderOpened(isVisible)}
          header={() => (
            <MnDropDownTileHeader
              className='mn-filter-item-view-dropdown-filter'
              label={filter.label}
              path='/assets/images/pictos/icon/chevron-down-light.svg'
            />
          )}
          onPrevious={() => setIsHeaderOpened(false)}
        >
          <div className='mn-filter-item-view-list'>
            {map(availableFiltersLabels, (filter, index) => (
              <div
                className='mn-filter-item-view-list-item'
                key={index}
                onClick={() => handleHeaderDropDown(filter.id, filterIndex)}
              >
                {filter.label}
              </div>
            ))}
          </div>
        </MnDropdown>
      )}

      {isCheckboxFilter(filter) && (
        <MnTooltip
          className={classNames('mn-filter-item-view-tooltip', { view: isPersonalView })}
          content={isPersonalView ? getLabelDropdownItems<string>(filter.values) : filter.label}
        >
          <MnDropdown
            className='mn-filter-item-view-dropdown'
            defaultIsOpened={isDropdownActive}
            handleDefault={(isVisible) => (isVisible ? setIsDropdownActive(isVisible) : handleClose(!isPersonalView))}
            header={(isVisible) => (
              <FilterDropDown
                className={classNames('mn-filter-item-header', { view: isPersonalView })}
                label={isPersonalView ? getLabelDropdownItems(filter.values) : filter.label}
                onClose={(forceUpdate) => isVisible && handleClose(forceUpdate)}
              />
            )}
            onPrevious={isPersonalView ? () => setIsDropdownActive(false) : undefined}
          >
            <FilterDropdownItems<string>
              id={filter.id}
              onChange={filter.onChange}
              onClose={handleClose}
              onHandleFilterUpdate={onHandleFilterUpdate}
              values={filter.values}
            />
          </MnDropdown>
        </MnTooltip>
      )}

      {isDateFilter(filter) && (
        <MnTooltip
          className={classNames('mn-filter-item-view-tooltip', { view: isPersonalView })}
          content={isPersonalView ? getLabelDate(filter) : filter.label}
        >
          <MnDropdown
            className='mn-filter-item-view-dropdown'
            defaultIsOpened={isDropdownActive}
            handleDefault={(isVisible) => (isVisible ? setIsDropdownActive(isVisible) : handleClose(!isPersonalView))}
            header={(isVisible) => (
              <FilterDropDown
                className={classNames('mn-filter-item-header', { view: isPersonalView })}
                label={isPersonalView ? getLabelDate(filter) : filter.label}
                onClose={(forceUpdate) => isVisible && handleClose(forceUpdate)}
              />
            )}
            onPrevious={isPersonalView ? () => setIsDropdownActive(false) : undefined}
          >
            <MnFilterDateContainer filter={filter} />
          </MnDropdown>
        </MnTooltip>
      )}

      {isSearchFilter(filter) && (
        <MnSearchInput
          onChange={filter.onChange}
          onComplete={handleClose}
          placeholder={filter.label}
          value={filter.value}
        />
      )}

      {isBlankFilter(filter) && isPersonalView && <FilterOptionPlaceHolder />}
      {isPersonalView && typeof filterIndex === 'number' && (
        <MnButtonIcon
          className='mn-cvfl-cross'
          onClick={() => onHandleRemoveFilter?.(filterIndex)}
          path='/assets/images/pictos/icon/cross-light.svg'
          size='small'
          variant='gray500'
        />
      )}
    </>
  );
};

type CheckboxFilter = {
  id: string;
  label: string;
  onChange: (id: string, selected: boolean) => void;
  values: Record<string, FiltersValue<string>>;
};
