import React, { ReactElement } from 'react';
import { forEach, map } from 'lodash';
import {
  ArchiveOperationFilter,
  BillingLicenceFilter,
  Filters,
  OriginContractFilter,
  OriginOperationFilter,
  PlanTypeFilter,
  StatusContractFilter,
  StatusOperationFilter,
  StatusProgramFilter,
  TemplateOperationFilter,
  TypeContractFilter,
  TypeRecordFilter
} from '@mynotary/frontend/dashboard-filters/core';
import { FilterTag } from './tag';

interface CheckboxTagProps {
  filter:
    | ArchiveOperationFilter
    | OriginOperationFilter
    | OriginContractFilter
    | StatusContractFilter
    | TypeContractFilter
    | StatusOperationFilter
    | StatusProgramFilter
    | TemplateOperationFilter
    | BillingLicenceFilter
    | PlanTypeFilter
    | TypeRecordFilter;
  onHandleFilterUpdate: (mustForceReload?: boolean, newFilters?: Filters, reset?: true) => void;
}

const CheckboxTag = ({ filter, onHandleFilterUpdate }: CheckboxTagProps): ReactElement => {
  const values: {
    id: string | number;
    label: string;
  }[] = [];

  forEach(filter.values, (value) => {
    if (value.values) {
      forEach(value.values, (v2) => {
        if (v2.selected) {
          values.push({ id: v2.id, label: v2.label });
        }
      });
    } else if (value.selected) {
      values.push({ id: value.id, label: value.label });
    }
  });

  return (
    <>
      {map(values, (value) => {
        return (
          <FilterTag
            key={value.id}
            label={value.label}
            onDelete={() => filter.onChange(value.id as never, false)}
            onHandleFilterUpdate={onHandleFilterUpdate}
          />
        );
      })}
    </>
  );
};

export { CheckboxTag };
