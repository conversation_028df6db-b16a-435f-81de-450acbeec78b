import { Dictionary } from '@mynotary/crossplatform/shared/util';
import { DateRange } from 'react-day-picker';
import { SignatureType } from '@mynotary/crossplatform/signatures/api';
import { PlanType, SubscriptionStatus } from '@mynotary/crossplatform/billings/api';
import { filter, find, forEach, isEmpty, map } from 'lodash';
import { InvoiceStatus, InvoiceType } from '@mynotary/crossplatform/invoices/api';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';

export type FiltersValue<Id> = {
  id: Id;
  isDefault?: boolean;
  label: string;
  selected: boolean;
  values?: Dictionary<FiltersValue<Id>>;
};

/**
 * TODO: when FiltersKeys are moved to new domain, update archiveOperationTemplate.tsx
 */

export enum FiltersKeys {
  ARCHIVE_CONTRACT = 'ARCHIVE_CONTRACT',
  ARCHIVE_OPERATION = 'ARCHIVE_OPERATION',
  BILLING_LICENCE = 'BILLING_LICENCE',
  BILLING_STATUS = 'BILLING_STATUS',
  BLANK = 'BLANK',
  CONTRACTS_TYPES = 'CONTRACTS_TYPES',
  CONTRACT_SIGNATURE_TYPE = 'CONTRACT_SIGNATURE_TYPE',
  CREATION_TIME = 'CREATION_TIME',
  DATE = 'DATE',
  EXACT_SPECIFIC_TYPES = 'EXACT_SPECIFIC_TYPES',
  INCLUDE_IDS = 'INCLUDE_IDS',
  INVOICE_STATUS = 'INVOICE_STATUS',
  INVOICE_TYPES = 'INVOICE_TYPES',
  LAST_ACCESS = 'LAST_ACCESS',
  ORIGIN = 'ORIGIN',
  ORIGIN_OPERATION = 'ORIGIN_OPERATION',
  PLAN_TYPE = 'PLAN_TYPE',
  SEARCH = 'SEARCH',
  SIGNATURE_CREATION_TIME = 'SIGNATURE_CREATION_TIME',
  SIGNATURE_TIME = 'SIGNATURE_TIME',
  STATUS = 'STATUS',
  STATUS_OPERATION = 'STATUS_OPERATION',
  STATUS_OPERATION_UNDEFINED = 'STATUS_OPERATION_UNDEFINED',
  STATUS_PROGRAM = 'STATUS_PROGRAM',
  TEMPLATE_IDS = 'TEMPLATE_IDS'
}

export type FiltersValues = Array<
  | DateFilter
  | ArchiveContractFilter
  | OriginOperationFilter
  | StatusOperationFilter
  | StatusProgramFilter
  | TemplateOperationFilter
  | ArchiveOperationFilter
  | OriginContractFilter
  | StatusContractFilter
  | TypeContractFilter
  | TypeRecordFilter
  | CreationTimeFilter
  | SignatureCreationTimeFilter
  | SignatureTimeContractFilter
  | BillingLicenceFilter
  | BillingStatusFilter
  | PlanTypeFilter
  | SearchFilter
  | BlankFilter
  | LastAccessFilter
  | ContractSignatureTypeFilter
  | StatusInvoiceFilter
  | TypesInvoiceFilter
  | undefined
>;
export type FiltersDropdown = {
  id: FiltersKeys;
  label: string;
}[];
export type Filters = {
  [FiltersKeys.DATE]?: DateFilter;
  [FiltersKeys.INVOICE_STATUS]?: StatusInvoiceFilter;
  [FiltersKeys.INVOICE_TYPES]?: TypesInvoiceFilter;

  [FiltersKeys.ORIGIN_OPERATION]?: OriginOperationFilter;
  [FiltersKeys.STATUS_OPERATION]?: StatusOperationFilter;
  [FiltersKeys.STATUS_PROGRAM]?: StatusProgramFilter;
  [FiltersKeys.TEMPLATE_IDS]?: TemplateOperationFilter;
  [FiltersKeys.ARCHIVE_OPERATION]?: ArchiveOperationFilter;

  [FiltersKeys.ARCHIVE_CONTRACT]?: ArchiveContractFilter;

  [FiltersKeys.ORIGIN]?: OriginContractFilter;
  [FiltersKeys.STATUS]?: StatusContractFilter;
  [FiltersKeys.CONTRACTS_TYPES]?: TypeContractFilter;
  [FiltersKeys.CONTRACT_SIGNATURE_TYPE]?: ContractSignatureTypeFilter;

  [FiltersKeys.EXACT_SPECIFIC_TYPES]?: TypeRecordFilter;

  [FiltersKeys.CREATION_TIME]?: CreationTimeFilter;
  [FiltersKeys.SIGNATURE_CREATION_TIME]?: SignatureCreationTimeFilter;
  [FiltersKeys.SIGNATURE_TIME]?: SignatureTimeContractFilter;

  [FiltersKeys.BILLING_LICENCE]?: BillingLicenceFilter;
  [FiltersKeys.BILLING_STATUS]?: BillingStatusFilter;
  [FiltersKeys.PLAN_TYPE]?: PlanTypeFilter;

  [FiltersKeys.SEARCH]?: SearchFilter;

  [FiltersKeys.BLANK]?: BlankFilter;
  [FiltersKeys.LAST_ACCESS]?: LastAccessFilter;
};
export type BlankFilter = {
  id: FiltersKeys.BLANK;
  label: string;
};

/**
 * TODO: when ArchiveFilterId are moved to new domain, update archiveOperationTemplate.tsx
 */

export enum ArchiveFilterId {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED'
}

export type ArchiveOperationFilter = {
  id: FiltersKeys.ARCHIVE_OPERATION;
  label: string;
  onChange: (id: ArchiveFilterId, selected: boolean) => void;
  values: Dictionary<FiltersValue<ArchiveFilterId>>;
};

export enum OriginFilterId {
  ORGANIZATION = 'ORGANIZATION',
  OWN = 'OWN'
}

export enum LastAccessFilterId {
  MONTH = 'MONTH'
}

export type OriginOperationFilter = {
  id: FiltersKeys.ORIGIN_OPERATION;
  label: string;
  onChange: (id: OriginFilterId, selected: boolean) => void;
  values: Record<OriginFilterId, FiltersValue<OriginFilterId>>;
};
export type StatusOperationFilter = {
  id: FiltersKeys.STATUS_OPERATION;
  label: string;
  onChange: (id: number, selected: boolean) => void;
  values: Dictionary<FiltersValue<number>>;
};

export type StatusProgramFilter = {
  id: FiltersKeys.STATUS_PROGRAM;
  label: string;
  onChange: (id: number, selected: boolean) => void;
  values: Dictionary<FiltersValue<number>>;
};
export type TemplateOperationFilter = {
  id: FiltersKeys.TEMPLATE_IDS;
  label: string;
  onChange: (id: number, selected: boolean) => void;
  values: Dictionary<FiltersValue<number>>;
};
export type LastAccessFilter = {
  id: FiltersKeys.LAST_ACCESS;
  label: string;
  onChange: (id: LastAccessFilterId, selected: boolean) => void;
  values: Record<LastAccessFilterId, FiltersValue<LastAccessFilterId>>;
};

export interface FiltersOperationApi {
  [FiltersKeys.ORIGIN_OPERATION]?: string[];
  [FiltersKeys.STATUS_OPERATION]?: number[];
  [FiltersKeys.TEMPLATE_IDS]?: number[];
  [FiltersKeys.INCLUDE_IDS]?: number[];
  [FiltersKeys.CREATION_TIME]?: { after?: number; before?: number };
  [FiltersKeys.SEARCH]?: string[];
  [FiltersKeys.ARCHIVE_OPERATION]?: string[];
  [FiltersKeys.LAST_ACCESS]?: string[];
}

export type OriginContractFilter = {
  id: FiltersKeys.ORIGIN;
  label: string;
  onChange: (id: OriginFilterId, selected: boolean) => void;
  values: Record<OriginFilterId, FiltersValue<OriginFilterId>>;
};

export type StatusContractFilter = {
  id: FiltersKeys.STATUS;
  label: string;
  onChange: (id: ContractStatus, selected: boolean) => void;
  values: Dictionary<FiltersValue<ContractStatus>>;
};

export type StatusInvoiceFilter = {
  id: FiltersKeys.INVOICE_STATUS;
  label: string;
  onChange: (id: InvoiceStatus, selected: boolean) => void;
  values: Dictionary<FiltersValue<InvoiceStatus>>;
};

export type TypesInvoiceFilter = {
  id: FiltersKeys.INVOICE_TYPES;
  label: string;
  onChange: (id: InvoiceType, selected: boolean) => void;
  values: Dictionary<FiltersValue<InvoiceType>>;
};

export type TypeContractFilter = {
  id: FiltersKeys.CONTRACTS_TYPES;
  label: string;
  onChange: (id: string, selected: boolean) => void;
  values: Dictionary<FiltersValue<string>>;
};

export type ContractSignatureTypeFilter = {
  id: FiltersKeys.CONTRACT_SIGNATURE_TYPE;
  label: string;
  onChange: (id: SignatureType, selected: boolean) => void;
  values: Record<SignatureType, FiltersValue<SignatureType>>;
};

export type ArchiveContractFilter = {
  id: FiltersKeys.ARCHIVE_CONTRACT;
  label: string;
  onChange: (id: ArchiveFilterId, selected: boolean) => void;
  values: Dictionary<FiltersValue<ArchiveFilterId>>;
};

export interface FiltersContractApi {
  [FiltersKeys.ORIGIN]?: string[];
  [FiltersKeys.STATUS]?: string[];
  [FiltersKeys.STATUS_OPERATION]?: number[];
  [FiltersKeys.STATUS_PROGRAM]?: number[];
  [FiltersKeys.CONTRACTS_TYPES]?: { modelId: string; templateId: string }[];
  [FiltersKeys.CREATION_TIME]?: { after?: number; before?: number };
  [FiltersKeys.SIGNATURE_CREATION_TIME]?: { after?: number; before?: number };
  [FiltersKeys.SIGNATURE_TIME]?: { after?: number; before?: number };
  [FiltersKeys.SEARCH]?: string[];
  [FiltersKeys.ARCHIVE_CONTRACT]?: string[];
  [FiltersKeys.LAST_ACCESS]?: string[];
  [FiltersKeys.CONTRACT_SIGNATURE_TYPE]?: string[];
}

export type TypeRecordFilter = {
  id: FiltersKeys.EXACT_SPECIFIC_TYPES;
  label: string;
  onChange: (id: number, selected: boolean) => void;
  values: Dictionary<FiltersValue<string>>;
};

export type DateFilter = {
  children: {
    [FiltersKeys.CREATION_TIME]: CreationTimeFilter;
    [FiltersKeys.SIGNATURE_CREATION_TIME]?: SignatureCreationTimeFilter;
    [FiltersKeys.SIGNATURE_TIME]?: SignatureTimeContractFilter;
  };
  id: FiltersKeys.DATE;
  label: string;
};

export type CreationTimeFilter = {
  id: FiltersKeys.CREATION_TIME;
  label: string;
  onChange: (range: DateRange) => void;
  value: DateRange;
};
export type SignatureCreationTimeFilter = {
  id: FiltersKeys.SIGNATURE_CREATION_TIME;
  label: string;
  onChange: (range: DateRange) => void;
  value: DateRange;
};
export type SignatureTimeContractFilter = {
  id: FiltersKeys.SIGNATURE_TIME;
  label: string;
  onChange: (range: DateRange) => void;
  value: DateRange;
};

export type BillingLicenceFilter = {
  id: FiltersKeys.BILLING_LICENCE;
  label: string;
  onChange: (id: string, selected: boolean) => void;
  values: Dictionary<FiltersValue<string>>;
};

export type BillingStatusFilter = {
  id: FiltersKeys.BILLING_STATUS;
  label: string;
  onChange: (id: string, selected: boolean) => void;
  values: Dictionary<FiltersValue<SubscriptionStatus>>;
};

export type PlanTypeFilter = {
  id: FiltersKeys.PLAN_TYPE;
  label: string;
  onChange: (id: string, selected: boolean) => void;
  values: Dictionary<FiltersValue<PlanType>>;
};

export type SearchFilter = {
  id: FiltersKeys.SEARCH;
  label: string;
  onChange: (value?: string) => void;
  value?: string;
};

export function isOriginContractFilter(value?: { id: string }): value is OriginContractFilter {
  return value?.id === FiltersKeys.ORIGIN;
}

export function isStatusContractFilter(value?: { id: string }): value is StatusContractFilter {
  return value?.id === FiltersKeys.STATUS;
}

export function isTypeContractFilter(value?: { id: string }): value is TypeContractFilter {
  return value?.id === FiltersKeys.CONTRACTS_TYPES;
}

export function isDateFilter(value?: { id: string }): value is DateFilter {
  return value?.id === FiltersKeys.DATE;
}

export function isBillingLicenceFilter(value?: { id: string }): value is BillingLicenceFilter {
  return value?.id === FiltersKeys.BILLING_LICENCE;
}

export function isBillingStatusFilter(value?: { id: string }): value is BillingStatusFilter {
  return value?.id === FiltersKeys.BILLING_STATUS;
}

export function isPlanTypeFilter(value?: { id: string }): value is PlanTypeFilter {
  return value?.id === FiltersKeys.PLAN_TYPE;
}

export function isContractSignatureTypeFilter(value?: { id: string }): value is ContractSignatureTypeFilter {
  return value?.id === FiltersKeys.CONTRACT_SIGNATURE_TYPE;
}

export function isArchiveContractFilter(value?: { id: string }): value is ArchiveContractFilter {
  return value?.id === FiltersKeys.ARCHIVE_CONTRACT;
}

export function isArchiveOperationFilter(value?: { id: string }): value is ArchiveOperationFilter {
  return value?.id === FiltersKeys.ARCHIVE_OPERATION;
}

export function isOriginOperationFilter(value?: { id: string }): value is OriginOperationFilter {
  return value?.id === FiltersKeys.ORIGIN_OPERATION;
}

export function isStatusOperationFilter(value?: { id: string }): value is StatusOperationFilter {
  return value?.id === FiltersKeys.STATUS_OPERATION;
}

export function isStatusProgramFilter(value?: { id: string }): value is StatusProgramFilter {
  return value?.id === FiltersKeys.STATUS_PROGRAM;
}

export function isTemplateOperationFilter(value?: { id: string }): value is TemplateOperationFilter {
  return value?.id === FiltersKeys.TEMPLATE_IDS;
}

export function isLastAccess(value?: { id: string }): value is LastAccessFilter {
  return value?.id === FiltersKeys.LAST_ACCESS;
}

export function isInvoiceStatus(value?: { id: string }): value is StatusInvoiceFilter {
  return value?.id === FiltersKeys.INVOICE_STATUS;
}

export function isInvoiceType(value?: { id: string }): value is TypesInvoiceFilter {
  return value?.id === FiltersKeys.INVOICE_TYPES;
}

// RECORD
export function isTypeRecordFilter(value?: { id: string }): value is TypeRecordFilter {
  return value?.id === FiltersKeys.EXACT_SPECIFIC_TYPES;
}

export function isSearchFilter(value?: { id: string }): value is SearchFilter {
  return value?.id === FiltersKeys.SEARCH;
}

export function isBlankFilter(value?: { id: string }): value is BlankFilter {
  return value?.id === FiltersKeys.BLANK;
}

export function isSimpleFilter(value?: { id: string }): value is OriginContractFilter {
  return (
    isOriginContractFilter(value) ||
    isStatusContractFilter(value) ||
    isStatusProgramFilter(value) ||
    isTypeContractFilter(value) ||
    isOriginOperationFilter(value) ||
    isStatusOperationFilter(value) ||
    isTemplateOperationFilter(value) ||
    isTypeRecordFilter(value) ||
    isBillingLicenceFilter(value) ||
    isBillingStatusFilter(value) ||
    isArchiveOperationFilter(value) ||
    isArchiveContractFilter(value) ||
    isPlanTypeFilter(value) ||
    isLastAccess(value) ||
    isContractSignatureTypeFilter(value) ||
    isInvoiceStatus(value) ||
    isInvoiceType(value)
  );
}

export const getFilterOperationApi = (filters: Filters): FiltersOperationApi => {
  const filtering: FiltersOperationApi = {};

  forEach(filters, (f) => {
    if (isOriginOperationFilter(f)) {
      filtering[FiltersKeys.ORIGIN_OPERATION] = map(
        filter(f?.values, (v) => v.selected),
        (value) => value.id
      );
    }
    if (isStatusOperationFilter(f)) {
      filtering[FiltersKeys.STATUS_OPERATION] = map(
        filter(f?.values, (v) => v.selected),
        (value) => value.id
      );
    }
    if (isTemplateOperationFilter(f)) {
      filtering[FiltersKeys.TEMPLATE_IDS] = map(
        filter(f?.values, (v) => v.selected),
        (value) => value.id
      );
    }

    if (isArchiveOperationFilter(f)) {
      filtering[FiltersKeys.ARCHIVE_OPERATION] = map(
        filter(f?.values, (v) => v.selected),
        (value) => value.id
      );
    }

    if (isDateFilter(f)) {
      filtering[FiltersKeys.CREATION_TIME] = {
        after: f.children[FiltersKeys.CREATION_TIME].value?.to?.setHours(23, 59, 59),
        before: f.children[FiltersKeys.CREATION_TIME].value?.from?.setHours(0, 0, 1)
      };
    }
    if (isSearchFilter(f)) {
      filtering[FiltersKeys.SEARCH] = f.value?.trim().split(' ');
    }

    if (isLastAccess(f)) {
      filtering[FiltersKeys.LAST_ACCESS] = map(
        filter(f?.values, (v) => v.selected),
        (value) => value.id
      );
    }
  });

  if (!filtering.ARCHIVE_OPERATION || isEmpty(filtering?.ARCHIVE_OPERATION)) {
    filtering.ARCHIVE_OPERATION = ['ACTIVE'];
  }

  return filtering;
};

export const getFiltersCount = (filters: Filters) => {
  let count = 0;
  const hasValue = (filter: Filters[keyof Filters]): void => {
    if (isDateFilter(filter)) {
      const hasDateValue = find(filter.children, (child) => !!child?.value?.from || !!child?.value?.to);

      if (hasDateValue) {
        count++;
      }
    } else if (isSearchFilter(filter)) {
      if (filter.value) {
        count++;
      }
    } else if (isSimpleFilter(filter)) {
      if (filter.values) {
        const hasCheckboxValue = find(filter.values, (value) => {
          if (value.values) {
            return !!find(value.values, (value) => value.selected);
          } else {
            return value.selected;
          }
        });

        if (hasCheckboxValue) {
          count++;
        }
      }
    }
  };
  forEach(filters, (filter) => {
    hasValue(filter);
  });

  return count;
};
