import {
  ExternalAppsClient,
  CreateAssociationArgs,
  ApiApplicationsResponse
} from '@mynotary/frontend/external-apps/core';
import { createAxiosInstance, ApiHelpers } from '@mynotary/frontend/shared/axios-util';
import { environment } from '@mynotary/frontend/shared/environments-util';
import {
  OrganizationAssociationDto,
  OrganizationAssociationTypeDto
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { convertEnum } from '@mynotary/crossplatform/shared/util';

export class ExternalAppsClientImpl implements ExternalAppsClient {
  mainApiClient = createAxiosInstance({ baseURL: environment.apiMyNotaryUrl });

  async createAssociation(args: CreateAssociationArgs): Promise<void> {
    await this.mainApiClient.post<void>('/external-organization-associations', {
      externalId: args.externalId,
      mynotaryId: args.mynotaryId,
      type: convertEnum(OrganizationAssociationTypeDto, args.type)
    } satisfies OrganizationAssociationDto);
  }

  async getApiApplicationClients(): Promise<ApiApplicationsResponse> {
    const { data } = await ApiHelpers.get<ApiApplicationsResponse>(`/api-application-clients`);

    return data;
  }

  async deleteApiApplicationClient(appName: string): Promise<void> {
    await ApiHelpers.delete<number>(`/api-application-clients/${appName}`, null);
  }

  async deleteAssociation(associationId: string): Promise<void> {
    await this.mainApiClient.delete(`/associations/${associationId}`);
  }
}
