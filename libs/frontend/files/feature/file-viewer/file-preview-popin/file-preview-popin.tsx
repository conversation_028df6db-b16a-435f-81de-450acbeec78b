import './file-preview-popin.scss';
import { MnSvg, SheetHeader } from '@mynotary/frontend/shared/ui';
import { FileViewer } from '../file-viewer';

interface FilePreviewPopinProps {
  fileId: string;
  fileName: string;
  onClose: () => void;
}
export const FilePreviewPopin = ({ fileId, fileName, onClose }: FilePreviewPopinProps) => {
  return (
    <div className='file-preview-popin'>
      <SheetHeader onClose={onClose}>
        <MnSvg path='/assets/images/pictos/icon/file.svg' variant='gray700' />
        <div className='fpp-label'>{fileName}</div>
      </SheetHeader>
      <div className='fpp-iframe'>
        <FileViewer fileId={fileId} />
      </div>
    </div>
  );
};
