import { FilesClient } from '@mynotary/crossplatform/files-client/api';
import { FilesService } from '@mynotary/frontend/files/core';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { useGlobalLoaderClass } from '@mynotary/frontend/shared/util';
import { Folder } from '@mynotary/crossplatform/files/core';

export const useDownloadFiles = () => {
  const filesService = useService(FilesService);
  const filesClient = useService(FilesClient);

  const { displayGlobalLoader, hideGlobalLoader, isGlobalLoaderVisible } = useGlobalLoaderClass();

  const handleDownloadFile = async (file?: { downloadUrl?: string; id: string }) => {
    try {
      displayGlobalLoader();
      await filesService.downloadFile(file);
    } finally {
      hideGlobalLoader();
    }
  };

  const handleDownloadFiles = async (folder: Folder) => {
    try {
      displayGlobalLoader();
      const file = await filesClient.createArchive(folder);
      await handleDownloadFile(file);
    } finally {
      hideGlobalLoader();
    }
  };

  return { handleDownloadFile, handleDownloadFiles, isGlobalLoaderVisible };
};
