@use 'style/variables/colors' as *;
@use 'style/mixins/layout' as *;
@use 'style/mixins/responsive' as *;

.file-information {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;

  .mn-ft-file-picto {
    @include responsive($mobile-max) {
      display: none;
    }
  }

  .mn-ft-header {
    overflow: hidden;
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 8px;

    max-width: 456px;

    text-align: left;
  }

  .mn-ft-file-name {
    > div {
      @include multi-line-ellipsis(1);
    }
  }

  .mn-ft-middot {
    font-size: 24px;
  }

  .mn-ft-size {
    display: inline-flex;
    gap: 8px;
    color: $gray500;
  }
}
