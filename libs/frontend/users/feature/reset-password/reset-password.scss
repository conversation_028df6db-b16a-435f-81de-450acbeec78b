@use 'style/variables/colors' as *;
@use 'style/mixins' as *;

.reset-password {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;

  .content {
    position: absolute;
    z-index: 2;
    top: 0;

    overflow: auto;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    width: 100%;
    height: 100%;
    padding: 20px 120px;
  }

  .form-container {
    position: relative;

    width: 500px;
    height: auto;
    margin: auto;
    padding: 40px 55px;

    text-align: center;

    background-color: $white;
    box-shadow: 0 0 40px 1px #CFCFCF;
  }

  h4 {
    display: inline-block;

    margin: 0 0 15px;

    font-size: 19px;
    font-weight: 500;
    color: var(--primary);
    text-transform: uppercase;
  }

  .reinit-button {
    width: 100%;
    margin-top: 15px;
  }

  .rp-input-error-warning-text {
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: left;

    width: 100%;
    height: auto;
    margin-top: 15px;

    font-size: 12px;
    color: $red400;
    text-align: left;

    transition: margin .3s, height .3s;
  }

  .rp-input-error-warning-image {
    width: 11px;
    height: 11px;
    margin-right: 5px;
  }
}
