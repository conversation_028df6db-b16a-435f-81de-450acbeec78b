import './signup.scss';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Form } from '@mynotary/frontend/legals/api';
import { Clouds, MnButton, MnTerms, MnCustomLoader } from '@mynotary/frontend/shared/ui';
import { login } from '@mynotary/frontend/user-session/api';
import { computeProgression, mergeAndCopyAnswer } from '@mynotary/frontend/legals/api';
import { TokenType } from '@mynotary/frontend/auth/api';
import { routePaths } from '@mynotary/frontend/routes/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { createUser } from '@mynotary/frontend/users/store';
import { FormQuestion, SelectFormQuestion, TextFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { setErrorMessage } from '@mynotary/frontend/snackbars/api';
import { handleApiKnownError } from '@mynotary/frontend/shared/axios-util';

export const Signup = () => {
  const dispatch = useAsyncDispatch();
  const [answer, setAnswer] = useState<AnswerDict>({});
  const [accepted, setAccepted] = useState<boolean>(false);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const handleFormUpdate = (answerModification: AnswerDict): void => {
    setAnswer((oldAnswer: AnswerDict) => mergeAndCopyAnswer(oldAnswer, answerModification));
  };

  const handleRegister = async (): Promise<void> => {
    if (answer['password']?.value !== answer['password_confirmation']?.value) {
      dispatch(setErrorMessage('Veuillez renseigner le même mot de passe'));
      return;
    }

    try {
      setLoading(true);
      await dispatch(
        createUser({
          civility: answer['civility'].value,
          email: answer['email'].value,
          firstname: answer['firstname'].value,
          lastname: answer['lastname'].value,
          password: answer['password'].value,
          phone: answer['phone'].value
        })
      );
      await dispatch(
        login({
          email: answer['email'].value,
          password: answer['password'].value,
          type: TokenType.PASSWORD
        })
      );
      navigate(routePaths.public.codeVerification.path);
    } catch (error) {
      handleApiKnownError(error);
    } finally {
      setLoading(false);
    }
  };

  const progression = computeProgression(registerQuestions, answer);

  return (
    <div className='register'>
      <div className='content'>
        <div className='form-container'>
          <h4>REMPLIR VOS INFORMATIONS</h4>
          <Form answer={answer} debounce={false} forms={registerQuestions} onChange={handleFormUpdate} />
          <div className='register-check'>
            <MnTerms
              label={`J'ai lu et accepte les `}
              link={'https://mynotary.fr/cgu/'}
              linkLabel={`conditions générales de vente`}
              onChange={setAccepted}
            />
          </div>
          <MnCustomLoader loading={loading} position='right-inner'>
            <MnButton
              className='register-button'
              disabled={loading || !accepted || progression.filled !== progression.total}
              label="S'inscrire"
              onClick={handleRegister}
              variant='primary'
            />
          </MnCustomLoader>
          <Link className='login-link' to={routePaths.public.login.path}>
            Vous avez déjà un compte ?
          </Link>
        </div>
      </div>
      <Clouds />
    </div>
  );
};

const registerQuestions: Array<FormQuestion | SelectFormQuestion | TextFormQuestion> = [
  {
    id: 'email',
    label: 'E-mail',
    type: 'EMAIL'
  },
  {
    id: 'password',
    label: 'Mot de passe',
    placeholder: 'Mot de passe',
    type: 'PASSWORD'
  },
  {
    id: 'password_confirmation',
    label: 'Confirmation',
    placeholder: 'Confirmation',
    type: 'PASSWORD'
  },
  {
    choices: [
      {
        id: 'MAN',
        label: 'Monsieur'
      },
      {
        id: 'WOMAN',
        label: 'Madame'
      }
    ],
    id: 'civility',
    label: 'Civilité',
    placeholder: 'Civilité',
    type: 'SELECT'
  },
  {
    id: 'firstname',
    label: 'Prénom',
    type: 'TEXT',
    uppercase: 'WORD'
  },
  {
    id: 'lastname',
    label: 'Nom',
    type: 'TEXT',
    uppercase: 'UPPERCASE'
  },
  {
    id: 'phone',
    label: 'Téléphone',
    type: 'PHONE'
  }
];
