import { CreateUserArgs, UpdateUserArgs, UsersClient } from '@mynotary/frontend/users/core';
import { AppAsyncThunk } from '@mynotary/frontend/shared/redux-util';
import { inject } from '@mynotary/frontend/shared/injector-util';
import { setUser, updateUserSession } from '@mynotary/frontend/user-session/api';

export const createUser =
  (args: CreateUserArgs): AppAsyncThunk<void> =>
  async (dispatch) => {
    const usersClient = inject(UsersClient);

    const user = await usersClient.createUser(args);
    dispatch(setUser(user));
  };

export const updateUser =
  (args: UpdateUserArgs): AppAsyncThunk<void> =>
  async (dispatch) => {
    const usersClient = inject(UsersClient);
    await usersClient.updateUser(args);
    dispatch(updateUserSession({ email: args.email }));
  };
