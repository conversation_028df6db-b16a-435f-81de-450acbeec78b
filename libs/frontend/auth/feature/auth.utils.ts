import { jwtDecode } from 'jwt-decode';

const ONE_DAY = 24 * 60 * 60;

export function isTokenExpiringSoon(token: string): boolean {
  const decodedToken = jwtDecode(token);
  return decodedToken.exp != null && decodedToken.exp < Date.now() / 1000 + ONE_DAY;
}

export function isValidToken(token: string): boolean {
  try {
    const decodedToken = jwtDecode(token);

    return decodedToken.exp != null && decodedToken.exp > Date.now() / 1000;
  } catch (e) {
    console.error('Invalid token', e);
    return false;
  }
}
