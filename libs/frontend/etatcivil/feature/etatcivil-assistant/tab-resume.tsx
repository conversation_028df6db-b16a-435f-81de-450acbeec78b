import styles from './tab-resume.module.scss';
import { EtatCivilDemandeBff, EtatCivilSummary } from '@mynotary/crossplatform/bff-portalys/api';
import { AlertBox, MnCheckbox, MnSvg, MnSwitch, Tag } from '@mynotary/frontend/shared/ui';

type TabResumeProps = {
  onChange: (id: string, demandeIdx: number, changes: Partial<EtatCivilDemandeBff>) => void;
  summaries: EtatCivilSummary[];
};

export function TabResume(props: TabResumeProps) {
  function handleChange(id: string, demandeIdx: number, value: Partial<EtatCivilDemandeBff>) {
    props.onChange(id, demandeIdx, value);
  }

  return (
    <div>
      <h5>Résumé des demandes</h5>
      <p>Vérifiez et complétez les demandes</p>

      {props.summaries.map((summary, summaryIdx) => (
        <div className={styles.tab} key={summaryIdx}>
          <div className={styles.title}>
            <MnSvg path={'/assets/images/pictos/icon/user.svg'} variant={'black'} />
            {summary.nom}
          </div>
          <div className={styles.taglist}>
            {summary.tags.map((tag, index) => (
              <Tag color={'dark-gray'} key={index} label={tag} shape={'rounded'} />
            ))}
          </div>

          {summary.demandes.map((demande, demandeIdx) => {
            if (demande.askFiliationIntegral) {
              return (
                <div className={styles.questions} key={demandeIdx}>
                  <div className={styles.question}>Que souhaitez-vous demander sur l'acte de {demande.typeActe} ?</div>
                  <MnSwitch
                    label={demande.integral ? 'Acte intégral' : `Extrait d'acte`}
                    onChange={(value) => handleChange(summary.id, demandeIdx, { filiation: false, integral: value })}
                    value={demande.integral}
                  />
                  {!demande.integral && (
                    <MnSwitch
                      label={demande.filiation ? 'Avec filiation' : 'Sans filiation'}
                      onChange={(value) => handleChange(summary.id, demandeIdx, { filiation: value })}
                      value={demande.filiation}
                    />
                  )}
                  <div className={styles.question}>Conditions d'utilisation du Service Central d'Etat Civil (SCEC)</div>
                  <div>
                    <p>
                      Votre demande concerne un évènement survenu à l'étranger et sera de ce fait traitée par le SCEC.
                    </p>
                    <p>Merci de vous assurer que votre demande:</p>
                    <ul>
                      <li>Porte réellement un évènement survenu à l'étranger,</li>
                      <li>Concerne un (des) ressortissant(s) français,</li>
                      <li>
                        Porte sur un évènement qui a fait l’objet de l’établissement d’un acte par un consulat de France
                        à l’étranger (ou par le SCEC pour les personnes ayant acquis la nationalité française après la
                        naissance : naturalisation, etc.).
                      </li>
                    </ul>
                    <MnCheckbox
                      label={'Je confirme que ma demande répond aux critères ci-dessus.'}
                      onChange={(value) => handleChange(summary.id, demandeIdx, { confirmationScec: value })}
                      value={demande.confirmationScec}
                    />
                  </div>
                </div>
              );
            }
            return '';
          })}

          <div className={styles.messages}>
            {summary.messages.map((message, index) => (
              <AlertBox key={index} label={message.content} type={message.type} />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
