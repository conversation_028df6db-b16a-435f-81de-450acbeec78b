@use 'style/variables/colors' as *;

.container {
  display: flex;
  flex-direction: row;
  gap: 32px;
  justify-content: center;

  width: 100%;
}

.formColumn {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.responseColumn {
  display: flex;
  flex-direction: column;

  width: 450px;
  height: fit-content;
  padding: 24px;
  border: 1px solid $gray50;
  border-radius: 10px;

  background-color: white;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 4%), 0 8px 16px 0 rgba(0, 0, 0, 8%);
}

.responseHeader {
  display: flex;
  flex-direction: row;
  gap:8px;
  align-items: center;
  justify-content: left;

  margin-bottom:32px;
}

.responseHeaderIcon {
  display: flex;
  align-items: center;
  justify-content: center;

  width: 40px;
  height: 40px;
  padding: 8px;
  border-radius: 100px;

  background: var(--primary-light);
}

.responseDifference {
  display: flex;
  flex-direction: row;
  gap: 8px;

  width: 100%;
  margin-bottom: 32px;
}

.responseDifferenceLabels {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.responseField {
  font-size: 14px;
  font-weight: 700;
}

.responseAction {
  cursor: pointer;
}

.alertBox {
  width: 600px;
}

.form {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  width: 600px;
  margin-top: 24px;
  margin-bottom: 32px;
  padding: 24px;
  border: 1px solid $gray50;
  border-radius: 10px;

  background: $white;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 8%), 0 0 4px 0 rgba(0, 0, 0, 4%);

}

.title {
  padding: 8px;
  border-radius: 4px;

  font-size: 20px;
  font-weight: 600;
  color: $gray700;

  background-color: $gray50;
}

.separator {
  margin: 16px 0 32px;
  border: solid $gray300;
  border-width: 1px 0 0;
}
