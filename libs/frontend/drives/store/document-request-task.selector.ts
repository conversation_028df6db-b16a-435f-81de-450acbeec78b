import { createSelector } from '@reduxjs/toolkit';
import { FileType, selectFilesFeature } from '@mynotary/frontend/files/api';
import { selectTask } from '@mynotary/frontend/legals/api';
import { TaskType } from '@mynotary/crossplatform/legals/api';
import { memoize } from 'lodash';
import { selectDrivesFeature } from './drives.slice';
import { getNameWithExtension } from '@mynotary/crossplatform/files/api';

export const selectDocumentRequestTask = memoize((taskId: number) =>
  createSelector(selectTask(taskId), selectDrivesFeature, selectFilesFeature, (task, drivesFeature, filesFeature) => {
    if (task == null || task.type !== TaskType.DOCUMENT_REQUEST) {
      return null;
    }
    const taskReference = task.reference;
    // @todo: remove default value when type is updated
    const folderId = taskReference.folderId ?? '-1';

    const driveFileByFolderId = drivesFeature.files.filter((file) => file.folderId === folderId);

    return taskReference.documentLabels.map((label) => {
      const files = driveFileByFolderId
        .filter(
          (drivefile) =>
            getNameWithExtension({ name: drivefile.documentLabel ?? '', type: FileType.PDF }).name === label
        )
        .map((drivefile) => {
          const file = filesFeature[drivefile.fileId];
          return {
            driveFileId: drivefile.id,
            fileId: drivefile.fileId,
            fileSize: file.size,
            filename: file.name
          };
        });

      return {
        documentLabel: label,
        files,
        folderId: folderId,
        operationId: task.legalComponentId
      };
    });
  })
);
