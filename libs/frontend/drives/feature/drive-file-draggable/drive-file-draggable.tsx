import './drive-file-draggable.scss';
import { useDraggable } from '@dnd-kit/core';
import classNames from 'classnames';
import { ReactNode } from 'react';

interface DriveFileDragableProps {
  children: ReactNode;
  id: string | number;
  isSelected: boolean;
}

export const DriveFileDragable = ({ children, id, isSelected }: DriveFileDragableProps) => {
  const { attributes, isDragging, listeners, setNodeRef } = useDraggable({
    id
  });

  return (
    <div
      className={classNames('drive-file-draggable', { isDragging: isSelected || isDragging })}
      ref={setNodeRef}
      {...attributes}
      {...listeners}
    >
      {children}
    </div>
  );
};
