import './drive-folder-import.scss';
import { FolderAccordion, Tag } from '@mynotary/frontend/shared/ui';
import { DriveFolderInfo } from '@mynotary/frontend/drives/core';
import { ToggleItem, pluralize } from '@mynotary/frontend/shared/util';
import { FileSelectable, FileType } from '@mynotary/frontend/files/api';

interface DriveFolderImportProps {
  areItemsSelected: (id: string[]) => boolean;
  driveFolderInfo: DriveFolderInfo;
  onClick: (items: ToggleItem[]) => void;
  onSelectItems: (items: ToggleItem[]) => void;
}

export const DriveFolderImport = ({
  areItemsSelected,
  driveFolderInfo,
  onClick,
  onSelectItems
}: DriveFolderImportProps) => {
  return (
    <FolderAccordion
      actions={
        <Tag
          className='drive-folder-import-tag'
          color='info'
          label={`${driveFolderInfo.files.length} ${pluralize('fichier', driveFolderInfo.files.length)}`}
        />
      }
      icon={{
        mode: 'fill-stroke',
        path: '/assets/images/pictos/icon/folder-light-filled.svg',
        size: 'medium',
        variant: 'gray700'
      }}
      label={driveFolderInfo.folder.label}
    >
      <>
        {driveFolderInfo.files.map((driveFile) => (
          <FileSelectable
            className='drive-folder-import-file-selectable'
            contentType={FileType.PDF}
            fileDate={driveFile.fileDate}
            fileId={driveFile.fileId}
            filename={driveFile.fileName}
            isSelected={areItemsSelected([driveFile.fileId])}
            key={driveFile.id}
            onClick={() =>
              onClick([
                {
                  id: driveFile.fileId,
                  selected: !areItemsSelected([driveFile.fileId])
                }
              ])
            }
            onSelect={() => onSelectItems([{ id: driveFile.fileId, selected: !areItemsSelected([driveFile.fileId]) }])}
            selectable={onSelectItems != null}
            size={driveFile.fileSize}
          />
        ))}
      </>
    </FolderAccordion>
  );
};
