import './drive-file-popin.scss';
import { FileViewerActions, FilesService, selectFile } from '@mynotary/frontend/files/api';
import { DriveFileInfo } from '@mynotary/frontend/drives/core';
import { MnSvg, SheetHeader, SheetPopin } from '@mynotary/frontend/shared/ui';
import { useDriveFiles } from '../use-drive-files';
import { useState } from 'react';
import { DriveFileDeleteConfirmation } from '../drive-file-delete-confirmation/drive-file-delete-confirmation';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { useSelector } from 'react-redux';
import { Folder } from '@mynotary/crossplatform/files/api';

interface DriveFilePopinProps {
  driveFileInfo?: DriveFileInfo;
  isOpened: boolean;
  onClose: () => void;
}

export const DriveFilePopin = ({ driveFileInfo, isOpened, onClose }: DriveFilePopinProps) => {
  const [opened, setOpened] = useState(false);

  const filesService = useService(FilesService);
  const file = useSelector(selectFile(driveFileInfo?.fileId));
  const { RenamePopin, canShareDocument, handleSharingTask, renameFile } = useDriveFiles();

  const handleSharingFile = () => {
    if (driveFileInfo != null) {
      const folder: Folder = {
        files: [{ id: driveFileInfo.fileId, name: driveFileInfo.fileName }],
        name: driveFileInfo.fileName
      };

      handleSharingTask(folder);
      onClose();
    }
  };

  if (driveFileInfo == null) {
    return null;
  }
  const handleFinishDelete = async () => {
    setOpened(false);
    onClose();
  };

  const handleDownload = async () => {
    if (file == null) {
      throw new Error('File not found');
    }
    await filesService.downloadFile(file);
  };

  return (
    <>
      <SheetPopin isOpened={isOpened}>
        <div className='drive-file-popin'>
          <SheetHeader onClose={onClose}>
            <MnSvg path='/assets/images/pictos/icon/file.svg' variant='gray700' />
            <div className='drive-file-label'>{driveFileInfo.fileName}</div>
          </SheetHeader>
          <FileViewerActions
            className='drive-file-actions'
            fileId={driveFileInfo.fileId}
            onDelete={() => setOpened(true)}
            onDownload={handleDownload}
            onRename={() => renameFile({ driveFileInfo })}
            onSharing={canShareDocument ? handleSharingFile : undefined}
          />
        </div>
      </SheetPopin>
      <RenamePopin />
      <DriveFileDeleteConfirmation driveFileIds={[driveFileInfo.id]} onClose={handleFinishDelete} opened={opened} />
    </>
  );
};
