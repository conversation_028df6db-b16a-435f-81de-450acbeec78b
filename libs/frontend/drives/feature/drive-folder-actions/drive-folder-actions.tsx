import './drive-folder-actions.scss';
import { <PERSON>over, PopoverAction<PERSON>ist, <PERSON>over<PERSON>ontent, PopoverTrigger } from '@mynotary/frontend/shared/ui';
import React, { useState } from 'react';
import { DriveFolderDelete } from '../drive-folder-delete/drive-folder-delete';
import { DriveFolderRename } from '../drive-folder-rename/drive-folder-rename';
import { DriveFileAddAction } from '../drive-file-add-action/drive-file-add-actions';
import { useSelector } from 'react-redux';
import { selectDriveFolder } from '@mynotary/frontend/drives/store';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';

interface DriveFolderActionsProps {
  folderId: string;
}

export const DriveFolderActions = ({ folderId }: DriveFolderActionsProps) => {
  const [open, setOpen] = useState(false);

  const folder = useSelector(selectDriveFolder(folderId));
  const user = useSelector(selectCurrentUser);

  const handleOnClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
    setOpen(!open);
  };

  const handleClosePopover = () => {
    setOpen(false);
  };

  if (folder == null || user == null) {
    return null;
  }

  return (
    <Popover modal={false} onOpenChange={setOpen} open={open}>
      <PopoverTrigger>
        <div className={'drive-folder-actions-btn'} onClick={handleOnClick}>
          <div className={'dfa-title'}>Actions</div>
        </div>
      </PopoverTrigger>
      <PopoverContent>
        <PopoverActionList>
          <DriveFolderRename folderId={folderId} onFinish={handleClosePopover} />
          <DriveFolderDelete folderId={folderId} onFinish={handleClosePopover} operationId={folder.operationId} />
          <DriveFileAddAction
            folderId={folderId}
            onFinish={handleClosePopover}
            operationId={folder.operationId}
            userId={user.id}
          />
        </PopoverActionList>
      </PopoverContent>
    </Popover>
  );
};
