import './drive-file-drop-area.scss';
import { useDroppable } from '@dnd-kit/core';
import React, { ReactNode, useEffect } from 'react';

interface DriveFileDropAreaProps {
  children: ReactNode;
  folderId: string | 'empty-folder';
  onOverChange?: (over: boolean) => void;
}

export const DriveFileDropArea = ({ children, folderId, onOverChange }: DriveFileDropAreaProps) => {
  const { isOver, setNodeRef } = useDroppable({ id: folderId });

  useEffect(() => {
    const timeout = setTimeout(() => {
      onOverChange?.(isOver);
    }, 500);

    return () => clearTimeout(timeout);
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [isOver]);

  return (
    <div className={isOver ? 'drive-file-drop-area' : ''} ref={setNodeRef}>
      {children}
    </div>
  );
};
