import { createSelector } from '@reduxjs/toolkit';
import { selectUserSessionFeature } from './userSession.slice';
import { Member } from '@mynotary/crossplatform/members/api';

export const selectCurrentMember = createSelector(selectUserSessionFeature, (userInfo): Member | undefined => {
  if (userInfo.currentMemberId && userInfo.members) {
    return userInfo.members.find((member) => parseInt(member.id) === userInfo.currentMemberId);
  }

  return undefined;
});
