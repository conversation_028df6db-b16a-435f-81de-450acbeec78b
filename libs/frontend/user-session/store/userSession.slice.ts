import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { merge } from 'lodash';
import { LocalStorageUtils, User } from '@mynotary/frontend/shared/util';
import { Member } from '@mynotary/crossplatform/members/api';

export enum UserSessionStatus {
  INITIALIZED = 'INITIALIZED',
  NOT_INITIALIZED = 'NOT_INITIALIZED',
  PENDING = 'PENDING'
}

interface UserSession {
  currentMemberId?: number;
  members?: Member[];
  status: UserSessionStatus;
  user?: User;
}
const initialState: UserSession = {
  status: UserSessionStatus.NOT_INITIALIZED
};

export const userSession = createSlice({
  initialState: initialState,
  name: 'userSession',
  reducers: {
    setMembers: (state, action: PayloadAction<Member[]>) => {
      const members = action.payload;
      state.members = members;
    },
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
    },
    switchMember: (state, action: PayloadAction<number>) => {
      state.currentMemberId = action.payload;
      const userId = state.user?.id;

      const currentMember = state.members?.find((m) => parseInt(m.id) === state.currentMemberId);

      if (currentMember && userId) {
        saveMemberIdInLocalStorage(userId, parseInt(currentMember.id));
      }
    },
    switchMemberByOrganizationId: (state, action: PayloadAction<number>) => {
      const organizationId = action.payload;
      const userId = state.user?.id;

      const currentMember = state.members?.find((member) => parseInt(member.organizationId) === organizationId);
      if (currentMember == null || currentMember.id === state.currentMemberId?.toString()) {
        return;
      }
      state.currentMemberId = parseInt(currentMember.id);
      if (currentMember.id && userId) {
        saveMemberIdInLocalStorage(userId, parseInt(currentMember.id));
      }
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      merge(state.user, action.payload);
    },
    updateUserSessionStatus: (state, action: PayloadAction<UserSessionStatus>) => {
      state.status = action.payload;
    }
  }
});

const saveMemberIdInLocalStorage = (userId: number, memberId: number) => {
  LocalStorageUtils.set(`LAST_MEMBER_ID_${userId}`, memberId);
};

export const loadMemberIdFromLocalStorage = (userId: number) => {
  return LocalStorageUtils.get(`LAST_MEMBER_ID_${userId}`);
};

export const {
  setMembers,
  setUser,
  switchMember,
  switchMemberByOrganizationId,
  updateUser: updateUserSession,
  updateUserSessionStatus
} = userSession.actions;

export interface UserSessionState {
  [userSession.name]: UserSession;
}

export const selectUserSessionFeature = (state: UserSessionState) => state[userSession.name];
