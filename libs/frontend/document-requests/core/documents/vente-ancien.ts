import { DocumentCategory, DocumentLabel } from '../document-request-categories';

export const venteDocuments = [
  { category: DocumentCategory.RecurrentCommercialisation, label: DocumentLabel.TitreDePropriete },
  { category: DocumentCategory.RecurrentCommercialisation, label: DocumentLabel.PlanCadastral },
  { category: DocumentCategory.DiagnosticsComplementaires, label: DocumentLabel.EtatDesRisques },
  { category: DocumentCategory.DiagnosticsComplementaires, label: DocumentLabel.DiagnosticDePerformanceEnergetique },
  { category: DocumentCategory.DiagnosticsComplementaires, label: DocumentLabel.DiagnosticGaz },
  { category: DocumentCategory.DiagnosticsComplementaires, label: DocumentLabel.DiagnosticElectrique },
  { category: DocumentCategory.DiagnosticsComplementaires, label: DocumentLabel.DiagnosticTermites },
  { category: DocumentCategory.DiagnosticsComplementaires, label: DocumentLabel.DiagnosticMerules },
  { category: DocumentCategory.DiagnosticsComplementaires, label: DocumentLabel.DiagnosticPlomb },
  { category: DocumentCategory.DiagnosticsComplementaires, label: DocumentLabel.DiagnosticAmiante },
  {
    category: DocumentCategory.DiagnosticsComplementaires,
    label: DocumentLabel.ControleDuSystemeDAssainissementIndividuelOuCollectif
  },
  { category: DocumentCategory.DiagnosticsComplementaires, label: DocumentLabel.AuditEnergetique },
  { category: DocumentCategory.SiPersonnePhysique, label: DocumentLabel.PieceDIdentite },
  { category: DocumentCategory.SiPersonnePhysique, label: DocumentLabel.TaxeFonciere },
  { category: DocumentCategory.SiPersonnePhysique, label: DocumentLabel.SimulationBancaireOuAccordDePrincipeDUneBanque },
  { category: DocumentCategory.SiPersonnePhysique, label: DocumentLabel.JustificatifDApport },
  { category: DocumentCategory.SiPersonnePhysique, label: DocumentLabel.CarteTitreDeSejour },
  { category: DocumentCategory.SiMariagePACSDivorce, label: DocumentLabel.ContratDeMariageOuPacs },
  { category: DocumentCategory.SiMariagePACSDivorce, label: DocumentLabel.EngagemementDuFuturExConjoint },
  { category: DocumentCategory.SiMariagePACSDivorce, label: DocumentLabel.JugementDeDivorce },
  { category: DocumentCategory.SiMariagePACSDivorce, label: DocumentLabel.ActeDeDepotDeLaConventionDeDivorce },
  { category: DocumentCategory.SiMariagePACSDivorce, label: DocumentLabel.LivretDeFamille },
  { category: DocumentCategory.SiMariagePACSDivorce, label: DocumentLabel.ActeDeChangementDeRegime },
  { category: DocumentCategory.SiPersonneMorale, label: DocumentLabel.ExtraitKBIS },
  { category: DocumentCategory.SiPersonneMorale, label: DocumentLabel.PieceDIdentiteDuRepresentant },
  { category: DocumentCategory.SiPersonneMorale, label: DocumentLabel.StatutDeLaSociete },
  { category: DocumentCategory.SiPersonneMorale, label: DocumentLabel.RIB },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.ReglementDeCoproprieteEDDEtModificatifs },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.CarnetDEntretien },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.FicheSynthetique },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.PVAssembleeGeneraleDesTroisDernieresAnnees },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.PlansDesLots },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.TroisDerniersAppelsDeCharges },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.AttestationDeMesurageCarrez },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.PreEtatDate },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.ReglementDeLotissement },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.StatutsProcesVerbauxPiecesDeLASL },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.CoordonneesEtInformationsDuSyndic },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.DiagnosticPlombPartiesCommunes },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.DiagnosticAmiantePartiesCommunes },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.DiagnosticTermitesPartiesCommunes },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.DiagnosticTechniqueGlobal },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.PlanPluriannuelDeTravaux },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.ProjetDePlanPluriannuelDeTravaux },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.AutorisationDesTravauxParLaCopropriete },
  { category: DocumentCategory.SiCoproLotissement, label: DocumentLabel.PermisDeConstruireCopropriete },
  { category: DocumentCategory.SiTravauxOuBienBatiMoins10Ans, label: DocumentLabel.PermisDeConstruire },
  { category: DocumentCategory.SiTravauxOuBienBatiMoins10Ans, label: DocumentLabel.DeclarationDAchevementConstruction },
  { category: DocumentCategory.SiTravauxOuBienBatiMoins10Ans, label: DocumentLabel.CertificatDeConformiteConstruction },
  { category: DocumentCategory.SiTravauxOuBienBatiMoins10Ans, label: DocumentLabel.AssuranceDommagesOuvrage },
  { category: DocumentCategory.SiTravauxOuBienBatiMoins10Ans, label: DocumentLabel.FacturesTravaux },
  { category: DocumentCategory.SiTravauxOuBienBatiMoins10Ans, label: DocumentLabel.DeclarationPrealableConstruction },
  { category: DocumentCategory.SiTravauxOuBienBatiMoins10Ans, label: DocumentLabel.AssuranceDecennaleConstruction },
  { category: DocumentCategory.SiBienVenduLoue, label: DocumentLabel.CongeDuLocataire },
  { category: DocumentCategory.SiBienVenduLoue, label: DocumentLabel.ContratDeBail },
  { category: DocumentCategory.SiBienVenduLoue, label: DocumentLabel.TroisDernieresQuittancesDeLoyer },
  { category: DocumentCategory.SiBienVenduLoue, label: DocumentLabel.EtatDesLieuxDEntree },
  { category: DocumentCategory.SiPiscine, label: DocumentLabel.AttestationDeNormesDuDispositifDeSecuritePiscine },
  { category: DocumentCategory.SiContratAffichagePub, label: DocumentLabel.ContratDAffichagePublicitaire },
  { category: DocumentCategory.SiZoneAerodrome, label: DocumentLabel.CartePlanDExpositionDesBruits },
  { category: DocumentCategory.SiPlacementSousRegimeProtection, label: DocumentLabel.JugementPlacementSousRegimeDeProtection }
];
