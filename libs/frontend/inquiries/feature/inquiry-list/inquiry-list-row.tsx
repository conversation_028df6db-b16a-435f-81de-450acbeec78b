import styles from './inquiry-list.module.scss';
import { Inquiry } from '@mynotary/crossplatform/bff-portalys/api';
import { MnActionPopover, MnSvg, Tag } from '@mynotary/frontend/shared/ui';
import { useMutationInquiryDelete, useMutationInquiryUpdate } from '@mynotary/frontend/inquiries/core';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { setErrorMessage, setSuccessMessage } from '@mynotary/frontend/snackbars/api';
import { useSelector } from 'react-redux';
import { selectActiveTokenInfo } from '@mynotary/frontend/auth/api';
import { classNames } from '@mynotary/frontend/shared/util';

interface PlInquiryListProps {
  onClick: (selected: Inquiry) => void;
  rowData: Inquiry;
}

export const PlInquiryListRow = ({ onClick, rowData }: PlInquiryListProps) => {
  const mutationDelete = useMutationInquiryDelete();
  const mutationUpdate = useMutationInquiryUpdate();
  const dispatch = useAsyncDispatch();
  const auth = useSelector(selectActiveTokenInfo);

  const actions = [
    {
      id: 'edit',
      label: 'Modifier...',
      onClick: () => {
        onClick(rowData);
      }
    },
    {
      id: 'next',
      label: 'Envoyer',
      onClick: () => {
        mutationUpdate.mutate(
          { ...rowData, gotoNextStep: true, userId: auth?.userId },
          {
            onError: () => {
              dispatch(setErrorMessage("Erreur lors de l'envoi de la demande"));
            },
            onSuccess: () => {
              dispatch(setSuccessMessage('Demande envoyée'));
            }
          }
        );
      }
    },
    {
      id: 'delete',
      label: 'Supprimer',
      onClick: () => {
        mutationDelete.mutate(rowData, {
          onError: () => {
            dispatch(setErrorMessage('Erreur lors de la suppression de la demande'));
          },
          onSuccess: () => {
            dispatch(setSuccessMessage('Demande supprimée'));
          }
        });
      }
    }
  ];

  return (
    <div
      className={styles.row}
      onClick={() => {
        onClick(rowData);
      }}
    >
      <div className={classNames(styles.col, styles.col1)}>
        <MnSvg mode='normal' path='/assets/images/pictos/icon/documents-light.svg' size='large' variant='primary' />
      </div>
      <div className={classNames(styles.col, styles.col2)}>
        <strong>{rowData.type}</strong>
        <br />
        {rowData.label}
      </div>
      <div className={classNames(styles.col, styles.col3)}>
        <Tag color={rowData.statusColor} label={rowData.status} shape='rounded' />
      </div>
      <div className={classNames(styles.col, styles.col4)}>{rowData.updatedAt.toLocaleDateString()}</div>
      <div className={classNames(styles.col, styles.col5)}>
        <div className={styles.badge}>{rowData.creator}</div>
      </div>
      <div className={classNames(styles.col, styles.col6)}>
        <MnActionPopover actions={actions} />
      </div>
    </div>
  );
};
