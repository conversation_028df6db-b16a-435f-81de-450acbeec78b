export type SnackbarTemplateType = 'PERMANENT' | 'TEMPORARY' | 'OPERATION_ARCHIVED';

export type SnackbarColor = 'error' | 'info' | 'success' | 'warning';

export interface SnackbarAction {
  id: string;
  label: string;
  onClick: () => void;
}

export type SnackbarOptions = {
  actions?: SnackbarAction[];
  color?: SnackbarColor;
  description?: string;
  link?: string;
  linkLabel?: string;
  title?: string;
};

export interface NewSnackbarMessage {
  options?: SnackbarOptions;
  templateId: SnackbarTemplateType;
}
