@use 'style/mixins/index' as *;

.record-card {
  cursor: pointer;

  display: flex;
  gap: 24px;
  align-items: center;
  justify-content: space-between;

  height: 72px;
  padding: 24px;

  .rc-record-label {
    @include mn-ellipsis(100%);

    font-weight: $medium;
  }

  .rc-container {
    display: flex;
    gap: 24px;
    align-items: center;
  }

  .rc-more-icon {
    & > svg {
      stroke-width: 0.5px;
    }
  }
}