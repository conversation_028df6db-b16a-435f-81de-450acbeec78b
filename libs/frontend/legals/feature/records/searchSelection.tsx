import './searchSelection.scss';
import React, { ReactElement, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { debounce, filter, find, isEmpty, some } from 'lodash';
import { MnSearchInput } from '@mynotary/frontend/shared/ui';
import { MnLoader } from '@mynotary/frontend/shared/ui';
import { MnRecordTile } from './recordTile';
import { containsSpecificTypes, Record, RecordFiltering, getQuestionIds } from '@mynotary/frontend/legals/core';
import {
  getRecords,
  selectOperationRecords,
  selectFormNavigation,
  selectTemplatesByType
} from '@mynotary/frontend/legals/store';
import { MnProps } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { selectLoader } from '@mynotary/frontend/loader/api';
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';
import { RecordSheetHeader } from './record-sheet-header';
import { FooterActionBtnPopin } from '@mynotary/frontend/shared/ui';

interface RecordSearchSelectionProps extends MnProps {
  defaultSearch?: string;
  filteredRecordIds?: number[];
  label: string;
  method: 'EXACT_SPECIFIC_TYPES' | 'SPECIFIC_TYPES';
  onClickNotFound?: () => void;
  onClosePopin: () => void;
  onSelect: (record: Record) => void;
  operationId?: number;
  types?: string[][];
}

const PAGE_SIZE = 20;

const RecordSearchSelection = ({
  defaultSearch,
  filteredRecordIds,
  label,
  method,
  onClickNotFound,
  onClosePopin,
  onSelect,
  operationId,
  types
}: RecordSearchSelectionProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const [searchValue, setSearchValue] = useState(defaultSearch ?? '');
  const [options, setOptions] = useState<Record[] | null>(null);
  const loading = useSelector(selectLoader('SEARCH'));
  const templates = useSelector(selectTemplatesByType('RECORD')) as LegalRecordTemplate[];
  const operationRecords = useSelector(selectOperationRecords(operationId));
  const [operationFilteredRecords, setOperationFilteredRecords] = useState<Record[]>([]);
  const [hasSearch, setHasSearch] = useState(false);
  const formNavigation = useSelector(selectFormNavigation);

  const questionIds = useMemo(() => {
    const legalRecordTemplate = filter(templates, (template) => template.type === 'RECORD') as LegalRecordTemplate[];
    return getQuestionIds({ templates: legalRecordTemplate, types });
  }, [templates, types]);

  useEffect(() => {
    if (!isEmpty(operationRecords)) {
      setOperationFilteredRecords(
        filter(operationRecords, (record) => {
          const template = find(templates, (template) => template.id === record.template.id);
          return template != null && some(types, (type) => containsSpecificTypes(template.specificTypes, type));
        })
      );
    }
  }, [templates, operationRecords, types]);

  const filteredRecords: Record[] = useMemo(
    () => (options && !isEmpty(options) ? options : operationFilteredRecords),
    [operationFilteredRecords, options]
  );

  const handleSearch = useMemo(
    () =>
      debounce((value: string) => {
        if (types && value) {
          const filters: RecordFiltering = {
            ANSWER: { NOT_EMPTY: true },
            SEARCH: {
              QUESTION_IDS: questionIds,
              WORDS: value.trim().split(' ')
            },
            [method]: types
          };

          if (filteredRecordIds && !isEmpty(filteredRecordIds)) {
            filters.EXCLUDE_IDS = filteredRecordIds;
          }

          dispatch(getRecords(filters, 'SEARCH', { page: 0, pageSize: PAGE_SIZE })).then((page) => {
            setHasSearch(true);
            setOptions(page.data as Record[]);
          });
        } else {
          setHasSearch(false);
          setOptions([]);
        }
      }, 500),
    [dispatch, filteredRecordIds, method, questionIds, types]
  );

  const handleRecordSelection = (record: Record): void => {
    onSelect(record);
  };

  const handleSearchChange = (value: string): void => {
    setSearchValue(value);
    handleSearch(value);
  };

  const handleDeleteRecord = (record: Record): void => {
    setOptions((prevState) => {
      if (prevState) {
        return prevState.filter((option) => option.id !== record.id);
      }
      return prevState;
    });

    setOperationFilteredRecords((prevState) => {
      if (prevState) {
        return prevState.filter((filteredRecord) => filteredRecord.id !== record.id);
      }
      return prevState;
    });
  };

  const isNotFound = onClickNotFound != null && !loading && searchValue.length > 0 && hasSearch;

  const hasPlaceholder = !loading && !!searchValue && options && isEmpty(options);

  return (
    <div className='record-search-selection'>
      <RecordSheetHeader label={label} onClose={onClosePopin} />
      <div className='rs-container'>
        <MnSearchInput
          debounceTime={500}
          onChange={(value) => handleSearchChange(value ?? '')}
          placeholder={label}
          value={searchValue}
          variant='tile'
        />
        <div className='rs-options'>
          {loading && <MnLoader className='rs-loader' />}
          {!loading &&
            filteredRecords.map((option) => (
              <MnRecordTile
                hasAction={true}
                key={option.id}
                onClick={() => handleRecordSelection(option)}
                onDeleteRecord={handleDeleteRecord}
                recordId={option.id}
              />
            ))}
          {hasPlaceholder && <div className='rs-options-empty'>Aucune fiche correspondante</div>}
        </div>
      </div>
      {(isNotFound || formNavigation != null) && (
        <FooterActionBtnPopin onClick={onClickNotFound} types={types ? types[0] : undefined} />
      )}
    </div>
  );
};

export { RecordSearchSelection };
