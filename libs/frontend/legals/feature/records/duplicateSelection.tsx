import './duplicateSelection.scss';
import React, { ReactElement, useState } from 'react';
import { useSelector } from 'react-redux';
import { map } from 'lodash';
import { MnRecordTile } from './recordTile';
import { selectRecordsByIds } from '@mynotary/frontend/legals/store';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Record } from '@mynotary/frontend/legals/core';
import { FooterActionBtnPopin } from '@mynotary/frontend/shared/ui';

interface RecordSelectionProps extends MnProps {
  duplicateIds: number[];
  onCancel: () => void;
  onSelect: (record: Record) => void;
  types?: string[];
}

const RecordDuplicateSelection = ({ duplicateIds, onCancel, onSelect, types }: RecordSelectionProps): ReactElement => {
  const duplicates = useSelector(selectRecordsByIds(duplicateIds));
  const [loading, setLoading] = useState(false);
  const [hasClickSelect, setHasClickSelect] = useState(false);

  const handleIgnore = (): void => {
    setLoading(true);
    onCancel();
  };

  const handleSelectDuplicate = (duplicate: Record) => {
    if (!hasClickSelect) {
      setHasClickSelect(true);
      onSelect(duplicate);
    }
  };

  return (
    <>
      <div className='mn-record-duplicate-selection'>
        <h5 className='mn-rds-label'>Résultats similaires existants déjà dans la plateforme</h5>
        <div className='mn-rds-list'>
          {map(duplicates, (duplicate) => {
            return (
              <MnRecordTile
                hasAction={true}
                key={duplicate.id}
                onClick={() => handleSelectDuplicate(duplicate)}
                recordId={duplicate.id}
              />
            );
          })}
        </div>
      </div>
      <FooterActionBtnPopin onClick={loading ? undefined : handleIgnore} types={types} />
    </>
  );
};
export { RecordDuplicateSelection };
