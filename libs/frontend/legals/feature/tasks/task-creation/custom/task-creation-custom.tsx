import React, { ReactElement, useState } from 'react';
import { TaskCreationEmail } from '../task-creation-email';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Form } from '../../../forms/form';
import { MnLoader } from '@mynotary/frontend/shared/ui';
import { useTaskCreation } from '../use-task-creation';
import { TaskTypeAndReference } from '@mynotary/frontend/legals/core';
import { TaskType } from '@mynotary/crossplatform/legals/core';
import { Email } from '@mynotary/frontend/email-editor/api';
import { useCustomTaskForm } from './use-custom-task-form';
import { isEmpty } from 'lodash';
import { TaskFormQuestionId, getTaskFormAnswer } from '../task-form';
import {
  TaskCreationContainer,
  TaskCreationEmailValidation,
  TaskCreationForm,
  TaskCreationPreviousStep,
  TaskCreationTitle
} from '@mynotary/frontend/legals/ui';
import { AnswerDict } from '@mynotary/crossplatform/records/api';

interface TaskCreationProps extends MnProps {
  onTaskCreated: () => void;
  operationId: number;
}

export const TaskCreationCustom = ({ onTaskCreated, operationId }: TaskCreationProps): ReactElement => {
  const [currentStep, setCurrentStep] = useState<'FORM' | 'EMAIL'>('FORM');
  const [email, setEmail] = useState<Email | null>(null);
  const reference: TaskTypeAndReference = { reference: undefined, type: TaskType.CUSTOM };

  const { answer, form, handleAnswerChange, isCompleted } = useCustomTaskForm({
    operationId
  });

  const { handleValidation, isLoading } = useTaskCreation({
    answer,
    email,
    operationId,
    reference
  });

  const { hasMail } = getTaskFormAnswer(answer);

  const handleFormValidation = async () => {
    if (hasMail) {
      setCurrentStep('EMAIL');
    } else {
      await handleValidation({ onFinish: onTaskCreated });
    }
  };

  if (isEmpty(form)) {
    return <MnLoader variant='large' />;
  }

  return (
    <TaskCreationContainer>
      {currentStep === 'EMAIL' && <TaskCreationPreviousStep onClick={() => setCurrentStep('FORM')} />}
      <TaskCreationTitle step={currentStep} />

      {currentStep === 'FORM' && (
        <TaskCreationForm
          hasMail={hasMail}
          isCompleted={isCompleted}
          isLoading={isLoading}
          onClick={handleFormValidation}
        >
          <Form answer={answer} editable={!isLoading} forms={form} onChange={handleAnswerChange} />
        </TaskCreationForm>
      )}
      {currentStep === 'EMAIL' && (
        <>
          <TaskCreationEmail
            defaulEmailContent={getEmailDefaultContent(answer)}
            onEmailChange={setEmail}
            operationId={operationId}
            typeAndReference={reference}
          />
          <TaskCreationEmailValidation
            disabled={isLoading || !email}
            isLoading={isLoading}
            onClick={() => handleValidation({ onFinish: onTaskCreated })}
          />
        </>
      )}
    </TaskCreationContainer>
  );
};

function getEmailDefaultContent(answer?: AnswerDict): string | undefined {
  const description = answer?.[TaskFormQuestionId.description]?.value;
  const title = answer?.[TaskFormQuestionId.title]?.value;

  if (description == null) {
    return undefined;
  }

  const formattedDescription = description.replace(/\n/g, '<br/>');

  return `Bonjour, 
  <br/>
  <br/>
  Voici le descriptif de la tâche qui vous est attribuée :
  <br/>
  <br/>${title}<br/>
  <br/>${formattedDescription}<br/>
  <br/>Cordialement,`;
}
