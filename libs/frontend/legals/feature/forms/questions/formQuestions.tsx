import './formQuestions.scss';
import React, { ReactElement, useContext } from 'react';
import { useSelector } from 'react-redux';
import { MnCheckbox, MnFormQuestionLabel } from '@mynotary/frontend/shared/ui';
import { isFormAnswer, isRepeatQuestion, QuestionsProps } from '@mynotary/frontend/shared/forms-util';
import { MnProps, classNames } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import {
  toggleSelections,
  selectFormSelectedValue,
  selectCurrentOperationType,
  selectFormsMode
} from '@mynotary/frontend/legals/store';
import { QuestionLockedTooltip, DefaultAnswerCheckbox } from '@mynotary/frontend/legals/ui';
import { getQuestionId } from '@mynotary/frontend/legals/core';
import { FormSelectionContext } from '../../index';
import { JSONValue } from '@mynotary/crossplatform/shared/util';
import { Answer, FormQuestion } from '@mynotary/crossplatform/shared/forms-util';

interface MnFormQuestionsProps extends MnProps {
  Questions: (props: QuestionsProps) => ReactElement;
  answer?: Answer;
  debounce?: boolean;
  editable?: boolean;
  label: string;
  onChange: (value: JSONValue, isLocked?: boolean) => void;
  question: FormQuestion;
  questionId: string;
  recordId?: number;
  selectable?: boolean;
}

export function MnFormQuestion({
  Questions,
  answer,
  className,
  debounce = true,
  editable,
  label,
  onChange,
  question,
  questionId,
  recordId,
  selectable
}: MnFormQuestionsProps): ReactElement {
  const dispatch = useAsyncDispatch();
  const selectionContext = useContext(FormSelectionContext);
  const selected = useSelector(selectFormSelectedValue(`${recordId}-${question.id}`));
  const formTypeMode = useSelector(selectFormsMode);
  const isDefaultQuestion = formTypeMode === 'DEFAULT_QUESTIONS';

  const operationType = useSelector(selectCurrentOperationType);

  const toggleSelect = (value: boolean): void => {
    if (recordId) {
      dispatch(
        toggleSelections([
          {
            key: `${recordId}-${question.id}`,
            questionId: question.id,
            recordId,
            value
          }
        ])
      );
    }
  };

  const isFormQuestionDisabled = !editable || !!answer?.locked;

  return (
    <div className='mn-form-question' data-testid={question.id} id={getQuestionId(question.id, recordId)}>
      <div className='mnfq-container'>
        {!!selectable && (
          <MnCheckbox
            className={classNames('mnfq-select-box', { visible: selectionContext.hasSelectedItems })}
            onChange={toggleSelect}
            tooltipContent='Sélectionnez des éléments via la case à cocher, pour pouvoir appliquer des actions de relecture du contrat'
            value={!!selected}
          />
        )}
        <QuestionLockedTooltip disabled={!answer?.locked}>
          <div className='mn-form-question-input-container'>
            <MnFormQuestionLabel
              className={classNames({ locked: isFormQuestionDisabled })}
              explicitOptional={question.explicitOptional}
              label={label}
              operationType={operationType}
              questionId={questionId}
              recordId={recordId}
            />
            <div className='mn-form-question-input'>
              {isFormAnswer(answer, question) && (
                <Questions
                  answer={answer}
                  className={className}
                  debounce={debounce}
                  editable={editable}
                  onChange={onChange}
                  question={question}
                />
              )}
              {question.description && (
                <div className={classNames('mn-form-question-description', { 'with-selection': !!selectable })}>
                  {question.description}
                </div>
              )}
            </div>
            {isDefaultQuestion && isFormAnswer(answer, question) && isRepeatQuestion(question) === false && (
              <DefaultAnswerCheckbox
                isLocked={!!answer?.locked}
                onChange={(islocked) => onChange(answer?.value, islocked)}
              />
            )}
          </div>
        </QuestionLockedTooltip>
      </div>
    </div>
  );
}
