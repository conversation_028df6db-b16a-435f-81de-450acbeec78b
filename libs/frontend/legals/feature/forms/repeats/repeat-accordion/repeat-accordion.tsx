import './repeat-accordion.scss';
import React, { ReactElement } from 'react';
import { MnAccordion } from '@mynotary/frontend/shared/ui';
import { RepeatAccordionHeader } from '../repeat-accordion-header';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';

import {FormNode} from "@mynotary/crossplatform/shared/forms-util";

interface RepeatAccordionProps extends MnProps {
  children?: ReactElement;
  editable?: boolean;
  onRemove: (questionKey: string) => void;
  onToggle?: (questionKey: string) => void;
  opened?: boolean;
  questionKey: string;
  recordId?: number;
  repeatItem: FormNode;
}

export const RepeatAccordion = ({
  children,
  className,
  editable,
  onRemove,
  onToggle,
  opened,
  questionKey,
  recordId,
  repeatItem
}: RepeatAccordionProps): ReactElement => {
  return (
    <div className={classNames('form-repetition-accordion', className)}>
      <MnAccordion
        className='frc-accordion'
        header={() => (
          <RepeatAccordionHeader editable={editable} onRemove={onRemove} recordId={recordId} repeat={repeatItem} />
        )}
        key={repeatItem.id}
        mode='right'
        noBorder={true}
        noHover={true}
        onChange={() => onToggle?.(questionKey)}
        opened={opened}
      >
        <div className='frc-content'>{children}</div>
      </MnAccordion>
    </div>
  );
};
