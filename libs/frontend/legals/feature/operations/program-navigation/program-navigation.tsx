import { useSelector } from 'react-redux';
import { OperationTabList } from '../operation-tab-list/operation-tab-list';
import {
  selectCanCreateContractModels,
  selectLegalComponentTemplate,
  selectOperationPermission
} from '@mynotary/frontend/legals/store';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { reverse, routePaths } from '@mynotary/frontend/routes/api';
import { OperationContractTitleMenu, OperationTab } from '../operation-navigation';
import { isProgramConfig, LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api'

interface ProgramNavigationProps {
  operationId: number;
}

export const ProgramNavigation = ({ operationId }: ProgramNavigationProps) => {
  const operationTemplate = useSelector(selectLegalComponentTemplate<LegalOperationTemplate>(operationId));
  const canReadBatch = useSelector(
    selectOperationPermission(PermissionType.READ_OPERATION_SYNTHESIS, operationId, true)
  );
  const tables = isProgramConfig(operationTemplate?.config) ? operationTemplate?.config.synthesis?.tables : [];

  const hasContractModels = useSelector(selectCanCreateContractModels(operationId));

  const menuItems: OperationTab[] = [];

  const synthesisTables: OperationTab[] = tables.map((table) => {
    if (table.id === OperationContractTitleMenu.LOTS) {
      return {
        checkCondition: () => canReadBatch,
        id: table.id,
        label: table.title,
        path: reverse(routePaths.operation.grilleDeLots.path, { id: operationId })
      };
    } else if (table.id === OperationContractTitleMenu.COPROS) {
      return {
        checkCondition: () => canReadBatch,
        id: table.id,
        label: table.title,
        path: reverse(routePaths.operation.coproprietes.path, { id: operationId })
      };
    } else {
      return {
        checkCondition: () => canReadBatch,
        id: table.id,
        label: table.title,
        path: reverse(routePaths.operation.ventes.path, { id: operationId })
      };
    }
  });

  menuItems.push({
    id: OperationContractTitleMenu.VENTES,
    label: 'Ventes',
    path: reverse(routePaths.operation.ventes.path, { id: operationId })
  });

  menuItems.push(...synthesisTables);

  menuItems.push({
    checkCondition: () => hasContractModels,
    id: OperationContractTitleMenu.TRAMES,
    label: 'Trames',
    path: reverse(routePaths.operation.trames.path, { id: operationId })
  });

  menuItems.push({
    id: OperationContractTitleMenu.DRIVE,
    label: 'Stockage fichiers',
    path: reverse(routePaths.operation.drive.path, { id: operationId })
  });

  return <OperationTabList menuItems={menuItems} />;
};
