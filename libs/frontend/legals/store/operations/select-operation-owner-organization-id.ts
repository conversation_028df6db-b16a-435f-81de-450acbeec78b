import { Operation } from '@mynotary/frontend/legals/core';
import { createSelector } from '@reduxjs/toolkit';
import { selectOperation } from './selectOperation';
import { selectLegalComponentsFeature } from '../legals.slice';

export const selectOperationOwnerOrganizationId = (operationId?: number) =>
  createSelector(
    selectLegalComponentsFeature,
    selectOperation(operationId),
    (legalComponentsById, operation): number | undefined => {
      if (operation?.parentOperationId) {
        const parentOperation = legalComponentsById[operation.parentOperationId] as Operation;

        return parentOperation?.organizationId;
      }
      return operation?.organizationId;
    }
  );
