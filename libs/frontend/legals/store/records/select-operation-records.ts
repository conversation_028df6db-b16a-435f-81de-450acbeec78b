import { createSelector } from '@reduxjs/toolkit';
import { memoize, forEach } from 'lodash';
import { selectOperationLinkedLegalComponentIds } from '../links/select-operation-linked-legal-component-ids';
import { NumericDictionary } from '@mynotary/frontend/shared/util';
import { isRecord, Record } from '@mynotary/frontend/legals/core';
import { selectLegalComponentsFeature } from '../legals.slice';

export const selectOperationRecords = memoize(
  (operationId?: number, specificContractId?: number) =>
    createSelector(
      selectOperationLinkedLegalComponentIds(operationId, specificContractId),
      selectLegalComponentsFeature,
      (recordIds, components) => {
        const res: NumericDictionary<Record> = {};

        forEach(recordIds, (id: number) => {
          if (isRecord(components[id])) {
            res[id] = components[id] as Record;
          }
        });

        return res;
      }
    ),
  (operationId?: number, specificContractId?: number) => `${operationId}_${specificContractId}`
);
