import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  FormFilter,
  QuestionsUnanswered,
  QuestionsUnlocked,
  RecordFilteringType
} from '@mynotary/frontend/legals/core';

export interface FormFiltering {
  filtering: FormFilter;
  questionsUnanswered: QuestionsUnanswered;
  questionsUnlocked: QuestionsUnlocked;
  type?: RecordFilteringType;
}

const initialState: FormFiltering = { filtering: {}, questionsUnanswered: {}, questionsUnlocked: {} };

export const formFilteringSlice = createSlice({
  initialState,
  name: 'formFiltering',
  reducers: {
    setFiltering: (state, action: PayloadAction<FormFilter>) => {
      state.filtering = action.payload;
    },
    setFilteringType: (state, action: PayloadAction<RecordFilteringType>) => {
      state.filtering = {};
      state.type = action.payload;
    },
    setFormSearch: (state, action: PayloadAction<string | undefined>) => {
      if (action.payload) {
        state.filtering = {
          ...state.filtering,
          SEARCH: { text: action.payload }
        };
      } else {
        delete state.filtering.SEARCH;
        state.filtering = {
          ...state.filtering
        };
      }
    }
  }
});

export interface FormFilteringState {
  [formFilteringSlice.name]: FormFiltering;
}

export const selectFormFilteringFeature = (state: FormFilteringState) => state[formFilteringSlice.name];

export const { setFiltering, setFilteringType, setFormSearch } = formFilteringSlice.actions;
