import { memoize } from 'lodash';
import { createSelector } from '@reduxjs/toolkit';
import { selectCurrentOperationFormContext } from '../legals.selector';
import { selectLegalComponentTemplate } from '../templates/select-legal-component-template';
import { selectOperationRecordsUnfold } from '../records/select-operation-records-form';
import { selectRecordLabels } from '../records/select-record-labels.selector';
import { selectRecordDocumentsImpl } from './select-record-documents.impl';
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

/**
 * Select record documents that are used in operation records in documents that match the filter "Tous les documents du dossier"
 * This selector combined with {@selectOperationFiles} are used to display all files that are shareable by a user.
 */
export const selectRecordDocuments = memoize((operationId: number) =>
  createSelector(
    selectOperationRecordsUnfold(operationId),
    selectCurrentOperationFormContext,
    selectLegalComponentTemplate<LegalOperationTemplate>(operationId),
    selectRecordLabels,
    (forms, formContext, operationTemplate, recordLabels) => {
      return selectRecordDocumentsImpl({ formContext, forms, operationTemplate, recordLabels });
    }
  )
);

export const selectContractRecordDocuments = memoize(
  (operationId: number, contratId: number) =>
    createSelector(
      selectOperationRecordsUnfold(operationId, contratId),
      selectCurrentOperationFormContext,
      selectLegalComponentTemplate<LegalOperationTemplate>(operationId),
      selectRecordLabels,
      (forms, formContext, operationTemplate, recordLabels) => {
        return selectRecordDocumentsImpl({ formContext, forms, operationTemplate, recordLabels });
      }
    ),
  (...args) => args.join('-')
);
