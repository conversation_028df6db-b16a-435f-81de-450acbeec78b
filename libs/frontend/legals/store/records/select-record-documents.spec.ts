import { individuelHabitationForm } from '@mynotary/testing';

import { selectRecordDocumentsImpl } from './select-record-documents.impl';
import {FormNode} from "@mynotary/crossplatform/shared/forms-util";
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api'

describe(selectRecordDocumentsImpl.name, () => {
  /**
   * Test case : This scenario emulate the case where the user has filled diagnostic_plomb and diagnostic_termites
   * with the same file and taxe_fonciere with another file.
   * Why : diagnostic_plomb and diagnostic_termites have been chosen because they are nested in form and taxe_fonciere is not.
   * Expected : The function should return all the documents with their respective files.
   */
  it(`should return all document with files`, () => {
    const form = individuelHabitationForm as FormNode[];
    const answer = {
      construction: { value: 'avant_1949' },
      diagnostic_plomb: { value: { 'file-id-des-diag-plomb-et-termite': 1 } },
      diagnostic_plomb_statut: { value: 'oui' },
      diagnostic_termites: { value: { 'file-id-des-diag-plomb-et-termite': 1 } },
      diagnostic_termites_commune: { value: 'oui' },
      diagnostic_termites_informatif_effectue: { value: 'oui' },
      diagnostic_termites_statut: { value: 'oui' },
      taxe_fonciere: { value: { 'file-id-de-la-taxe-fonciere': 1 } }
    };
    const operationTemplate = {
      config: {
        documentsToExclude: [],
        documentsToInclude: ['diagnostic_plomb', 'diagnostic_termites', 'taxe_fonciere']
      }
    } as unknown as LegalOperationTemplate;

    const res = selectRecordDocumentsImpl({
      formContext: {},
      forms: { 1: { answer, form } },
      operationTemplate,
      recordLabels: { 1: 'Maison - 1 rue du paradis' }
    });

    expect(res).toEqual({
      fileIds: ['file-id-de-la-taxe-fonciere', 'file-id-des-diag-plomb-et-termite'],
      recordDocuments: [
        {
          documentId: 'taxe_fonciere',
          documentLabel: 'Dernière taxe foncière',
          fileId: 'file-id-de-la-taxe-fonciere',
          recordId: 1,
          recordLabel: 'Maison - 1 rue du paradis'
        },
        {
          documentId: 'diagnostic_termites',
          documentLabel: 'Diagnostic Termites',
          fileId: 'file-id-des-diag-plomb-et-termite',
          recordId: 1,
          recordLabel: 'Maison - 1 rue du paradis'
        },
        {
          documentId: 'diagnostic_plomb',
          documentLabel: 'Diagnostic Plomb',
          fileId: 'file-id-des-diag-plomb-et-termite',
          recordId: 1,
          recordLabel: 'Maison - 1 rue du paradis'
        }
      ]
    });
  });
});
