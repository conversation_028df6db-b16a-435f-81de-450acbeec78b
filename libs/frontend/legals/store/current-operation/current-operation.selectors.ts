import { createSelector } from '@reduxjs/toolkit';
import { specificTypesToString } from '@mynotary/frontend/shared/util';
import { Operation } from '@mynotary/frontend/legals/core';
import { selectLegalComponentsFeature } from '../legals.slice';
import { selectTemplatesFeatures } from '../templates/templates.slice';
import { selectCurrentOperationId } from '@mynotary/frontend/current-operation/api';
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api'

export const selectCurrentOperationType = createSelector(
  selectCurrentOperationId,
  selectLegalComponentsFeature,
  selectTemplatesFeatures,
  (currentOperationId, legalComponentsById, templates): string | undefined => {
    if (currentOperationId && legalComponentsById[currentOperationId]) {
      const template = templates[legalComponentsById[currentOperationId].template.id];
      return specificTypesToString(template?.specificTypes);
    }
    return undefined;
  }
);

export const selectCurrentOperation = createSelector(
  selectCurrentOperationId,
  selectLegalComponentsFeature,
  (currentOperationId, legalComponentsById): Operation | undefined => {
    if (currentOperationId == null) {
      return;
    }

    return legalComponentsById[currentOperationId] as Operation;
  }
);

export const selectCurrentOperationTemplate = createSelector(
  selectCurrentOperation,
  selectTemplatesFeatures,
  (currentOperation, templates): LegalOperationTemplate | undefined => {
    if (currentOperation == null) {
      return;
    }

    return templates[currentOperation.template.id] as LegalOperationTemplate;
  }
);

export const selectCurrentOperationOrganizationId = createSelector(selectCurrentOperation, (currentOperation) => {
  return currentOperation?.organizationId;
});
