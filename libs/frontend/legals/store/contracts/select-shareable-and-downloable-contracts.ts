import { createSelector } from '@reduxjs/toolkit';
import { filter, memoize } from 'lodash';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { hasPermission, selectConnectedUserRole } from '@mynotary/frontend/roles/api';
import { specificTypesToString } from '@mynotary/frontend/shared/util';
import { selectContractsByOperationId } from './select-contracts-by-operation-id';
import { isRedactionPending } from '@mynotary/crossplatform/legals/core';

export const selectShareableAndDownloableContracts = memoize((operationId?: number) =>
  createSelector(selectContractsByOperationId(operationId), selectConnectedUserRole, (contracts, role) => {
    return filter(contracts, (contract) => {
      if (contract.legalContractTemplateId === 'IMPORT') {
        return true;
      }

      const operationTemplateType = specificTypesToString(contract?.operation?.template?.specificTypes);

      const hasRequiredPermission = hasPermission(
        PermissionType.READ_CONTRACT,
        role,
        operationTemplateType,
        contract?.legalContractTemplateId
      );

      return hasRequiredPermission || !isRedactionPending(contract.status);
    });
  })
);
