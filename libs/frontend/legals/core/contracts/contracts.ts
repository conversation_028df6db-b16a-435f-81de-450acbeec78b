import { map } from 'lodash';
import { ApiBranchPayload } from '../legal-links';
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';
import { FileInfo } from '@mynotary/crossplatform/files-client/api';
import { Folder } from '@mynotary/crossplatform/files/api';
import { Contract, ContractStatus, LegalData } from '@mynotary/crossplatform/legals/core';

export type OperationLight = {
  id: number;
  label: string;
  organizationId: number;
  parentOperationId?: number;
  parentOperationStatusId?: number;
  statusId?: number;
  template: Pick<LegalOperationTemplate, 'id' | 'type' | 'specificTypes'>;
};

export type ContractLegacy = {
  archiveTime: number | null;
  contractFileId?: string;
  creationContext?: string;
  creationTime: number;
  creatorFirstName?: string;
  creatorLastName?: string;
  creatorUserId: number;
  id: number;
  label: string;
  legalContractTemplateId: string;
  operation: OperationLight;
  projectFileId?: string;
  status: ContractStatus;
};

export const computeContractStatusLabel = (contract: ContractLegacy) => {
  const status = contract?.status;

  if (status) {
    switch (status) {
      case ContractStatus.REDACTION:
        return 'En rédaction';
      case ContractStatus.DRAFT:
        return 'Brouillon';
      case ContractStatus.NOTIFICATION_DRAFT:
        return 'Config recommandé en cours';
      case ContractStatus.NOTIFICATION_COMPLETED:
        return 'Recommandé élec. accepté';
      case ContractStatus.NOTIFICATION_ERROR:
        return 'Recommandé en erreur';
      case ContractStatus.NOTIFICATION_PENDING:
        return 'Recommandé élec. en cours';
      case ContractStatus.SIGNATURE_DRAFT:
        return 'Config signature en cours';
      case ContractStatus.SIGNATURE_PENDING_CREATION:
        return 'Signature - Envoi en cours';
      case ContractStatus.SIGNATURE_ERROR:
        return "Signature - Échec de l'envoi";
      case ContractStatus.SIGNATURE_COMPLETED:
        return 'Signé';
      case ContractStatus.SIGNATURE_EXPIRED:
        return 'Signature expirée';
      case ContractStatus.SIGNATURE_PENDING:
        return 'Signature en cours';
      case ContractStatus.VALIDATED:
        return 'Contrat validé';
      case ContractStatus.VALIDATION_PENDING:
        return 'En attente de validation';
    }
  }
  return '';
};

export type OperationContractWithBranches = {
  branches: ApiBranchPayload[];
  contract: ContractLegacy;
};

export type OperationContractCreationContext = {
  files?: FileInfo[];
};

export type JeffersonGlobalSummaryHeading = {
  anchor: string;
  level: number;
  title: string;
};

export type JeffersonGlobalSummary = {
  headings: JeffersonGlobalSummaryHeading[];
};

export const createContractFolder = (contract: ContractLegacy, files: FileInfo[]): Folder => {
  return {
    files: map(files, (file) => {
      return {
        id: file.id,
        name: file.name
      };
    }),
    name: contract.label
  };
};

export interface ContractWithLegalData {
  contract: Contract;
  legalData?: LegalData;
}
