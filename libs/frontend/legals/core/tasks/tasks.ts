import { User } from '@mynotary/frontend/shared/util';
import { Organization } from '@mynotary/frontend/organizations/api';
import { Folder } from '@mynotary/crossplatform/files/api';
import { TaskType } from '@mynotary/crossplatform/legals/core';

export type TaskAssignee = {
  seen: boolean;
  taskId: number;
  user: User;
};

export type TaskReminderType = 'ONE_DAY_BEFORE' | 'THREE_DAYS_BEFORE' | 'FIVE_DAYS_BEFORE';

export type TaskDownloadDocumentsReference = { contractId?: number; folder: Folder; level: number };

export type TaskDocumentRequestReference = {
  documentLabels: string[];
  folderId?: string;
  operationId: string;
  resourceToken?: string;
};

export type TaskPaymentRequestReference = {
  operationId: string;
  orderId: string;
};

export type TaskScanReference = { documentId: string; recordId: number };

export type TaskFillOperationRecordsReference = {
  contractId?: number;
  recordsQuestions: RecordQuestion[];
};

export type RecordQuestion = {
  id: number;
  legalRecordToken?: string;
  questions: {
    answerPath: string[];
    id: string;
    title: string;
  }[];
};

export type TaskShareOperationRecordReference = {
  contractId?: number;
  legalRecordToken?: string;
  operationId?: number;
  operationLinkTemplateId?: string;
  recordId: number;
};

export type ValidateContractTaskReference = {
  reference: {
    contractId: number;
  };
  type: TaskType.VALIDATE_CONTRACT;
};

export type TaskReviewContractReference = {
  reference: {
    contractId: number;
  };
  type: TaskType.REVIEW_CONTRACT;
};

export type TaskTypeAndReference =
  | {
      reference: undefined;
      type: TaskType.CUSTOM;
    }
  | {
      reference: TaskFillOperationRecordsReference;
      type: TaskType.FILL_OPERATION_RECORDS;
    }
  | {
      reference: TaskShareOperationRecordReference;
      type: TaskType.SHARE_OPERATION_RECORD;
    }
  | {
      reference: TaskScanReference;
      type: TaskType.SCAN_DOCUMENT;
    }
  | TaskReviewContractReference
  | ValidateContractTaskReference
  | {
      reference: ReadProjectContractTaskReference | ReadProjectContractTaskLegacy;
      type: TaskType.READ_PROJECT_CONTRACT;
    }
  | {
      reference: TaskDownloadDocumentsReference;
      type: TaskType.DOWNLOAD_FILES;
    }
  | {
      reference: TaskDownloadDocumentsReference;
      type: TaskType.DOWNLOAD_FILES_OPERATION;
    }
  | {
      reference: TaskDocumentRequestReference;
      type: TaskType.DOCUMENT_REQUEST;
    }
  | {
      reference: TaskPaymentRequestReference;
      type: TaskType.PAYMENT_REQUEST;
    };

export type ReadProjectContractFiles = {
  category?: string;
  id: string;
};

export type ReadProjectContractTaskReference = {
  contractId: number;
  folder: Folder;
};

export type ReadProjectContractTaskLegacy = {
  contractId: number;
  files: ReadProjectContractFiles[];
};

export interface TaskUpdate {
  description?: string;
  dueDate?: number | null;
  reminderType?: TaskReminderType | null;
  title: string;
}

export type Task = {
  assignees: TaskAssignee[];
  completionTime?: number;
  creationTime: number;
  creatorUser: {
    email: string;
    firstname: string;
    lastname: string;
    phone: string;
    profilePictureFileId?: string;
  };
  creatorUserId: number;
  description?: string;
  dueDate?: number;
  emails?: string[];
  expirationTime?: number;
  id: number;
  legalComponentId: number;
  organization: Organization;
  reminderDate?: number;
  reminderTime?: number;
  reminderType?: TaskReminderType;
  seen: boolean;
  title: string;
  type: TaskType;
  uuid?: string;
} & TaskTypeAndReference;

export type TaskNew = {
  assignees: string[];
  description?: string;
  dueDate?: number;
  email?: { content: string; subject: string } | null;
  legalComponentId: number;
  reminderType?: TaskReminderType;
  title: string;
} & TaskTypeAndReference;
