import { Task, TaskTypeAndReference } from './tasks';
import { TaskType } from '@mynotary/crossplatform/legals/core';
import { reverse, routePaths } from '@mynotary/frontend/routes/api';

import { EmailContentTemplateId } from '@mynotary/crossplatform/emails/api';

export function getTaskLink(task: Task): string {
  const path = `${reverse(routePaths.public.task.path, { taskId: task.id })}?taskUuid=${task.uuid}`;
  return `${window.location.origin}${path}`;
}

export const getTaskContractId = (typeAndReference: TaskTypeAndReference): number | undefined => {
  switch (typeAndReference.type) {
    case 'DOWNLOAD_FILES':
    case 'FILL_OPERATION_RECORDS':
    case 'VALIDATE_CONTRACT':
      return typeAndReference.reference.contractId;
  }
  return undefined;
};

export const getEmailEditorTemplateId = (type: TaskType): EmailContentTemplateId => {
  switch (type) {
    case TaskType.DOWNLOAD_FILES:
      return EmailContentTemplateId.TASK_DOWNLOAD_FILES;
    case TaskType.DOWNLOAD_FILES_OPERATION:
      return EmailContentTemplateId.TASK_DOWNLOAD_FILES_OPERATION;
    case TaskType.FILL_OPERATION_RECORDS:
      return EmailContentTemplateId.TASK_FILL_OPERATION_RECORDS;
    case TaskType.SHARE_OPERATION_RECORD:
      return EmailContentTemplateId.TASK_SHARE_OPERATION_RECORD;
    case TaskType.VALIDATE_CONTRACT:
      return EmailContentTemplateId.TASK_VALIDATE_CONTRACT;
    case TaskType.READ_PROJECT_CONTRACT:
      return EmailContentTemplateId.TASK_READ_PROJECT_CONTRACT;
    case TaskType.CUSTOM:
      return EmailContentTemplateId.TASK_CUSTOM;
    case TaskType.REVIEW_CONTRACT:
      return EmailContentTemplateId.TASK_REVIEW_CONTRACT;
    default:
      return EmailContentTemplateId.TASK_CUSTOM;
  }
};
