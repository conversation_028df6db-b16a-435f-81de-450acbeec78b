import { Dictionary, JSONValue } from '@mynotary/crossplatform/shared/util';
import { AnswerDict, mergeAnswer } from '@mynotary/crossplatform/records/api';
import { NewLegalComponentTemplate } from '../templates';
import {
  concat,
  filter,
  find,
  flatten,
  forEach,
  isEmpty,
  isNil,
  isNumber,
  isObject,
  last,
  map,
  merge,
  reduce
} from 'lodash';
import { NumericDictionary } from '@mynotary/frontend/shared/util';
import { MnBranch, MnLink } from '../legal-links';
import {
  Answer,
  FormNode,
  FormQuestion,
  MnQuestionType,
  UploadFormQuestion
} from '@mynotary/crossplatform/shared/forms-util';
import { Operation } from '../operations';
import { getRecordAdvancedConfig } from './records-config';
import { getConditionsState } from './records-filters';
import {
  LegalLinkTemplate,
  LegalRecordTemplateId,
  LegalTemplate,
  MandatoryDocument
} from '@mynotary/crossplatform/legal-templates/api';

export interface Record {
  answer: { data?: JSONValue; id: number };
  creationTime: number;
  creatorUser?: {
    email?: string;
    firstname: string;
    id: number;
    lastname: string;
    profilePictureFileId?: string;
  };
  id: number;
  links: { id: number }[];
  organizationId: number;
  tags?: Dictionary<string[]>;
  template: RecordTemplateShort;
  type: 'RECORD';
}

export interface RecordTemplateShort {
  id: LegalRecordTemplateId;
  specificTypes?: string[];
}

/**
 * Removed from record creation use RecordNew instead but still used in branch and link models
 */
export type NewRecord = {
  answer?: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data: any;
  };
  id?: number;
  template: NewLegalComponentTemplate;
  type: 'RECORD';
};

export const isLegacyNewRecord = (record?: Record | NewRecord): record is NewRecord => {
  return !record?.id && (record as NewRecord)?.template != null;
};

export interface RecordNew {
  answer?: AnswerDict;
  creatorId: number;
  organizationId: number;
  templateId: LegalRecordTemplateId;
  token?: string;
}

export type FormContextRecordMeta = {
  associatedRecord?: Record;
  recordLinks?: Dictionary<MnLink>;
};

export type FormContext = {
  contractModel?: string;
  operationType?: string;
  recordAnswerById?: NumericDictionary<AnswerDict>;
  records?: Dictionary<Dictionary<Record[]>>; // records by links
  recordsMeta?: Dictionary<FormContextRecordMeta>;
};

export interface LegacyApiLegalRecord extends Record {
  answer: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data?: any;
    id: number;
  };
}

export function isApiRecord(node: unknown): node is LegacyApiLegalRecord {
  return (node as LegacyApiLegalRecord)?.type === 'RECORD';
}

export const hasAnswerValue = (answer: Answer): boolean => {
  return !isEmpty(answer?.value) || isNumber(answer?.value);
};

export const hasLockedValue = (answer: Answer): boolean => {
  return !!answer?.locked;
};

export function isFormQuestion(node: FormNode): node is FormQuestion {
  return (
    !!(node as FormQuestion).type &&
    (node as FormQuestion).type !== 'CATEGORY' &&
    (node as FormQuestion).type !== 'CONDITION_BLOCK' &&
    (node as FormQuestion).type !== 'REPEAT'
  );
}

export const mergeAndCopyAnswer = (answer?: AnswerDict, update?: AnswerDict): AnswerDict => {
  const newAnswer: AnswerDict = merge({}, answer);
  mergeAnswer(newAnswer, update);
  return newAnswer;
};

export const mergeAndCopyAnswerRemoveNull = (answer?: AnswerDict, update?: AnswerDict): AnswerDict => {
  const newAnswer: AnswerDict = merge({}, answer);
  mergeAnswer(newAnswer, update);
  removeNil(newAnswer);
  return newAnswer;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const removeNil = (obj: any): void => {
  forEach(obj, (value, key) => {
    if (isObject(value)) {
      removeNil(value);
    }
    if (isNil(value)) {
      delete obj[key];
    }
  });
};

export function isRecord(node?: unknown | null): node is Record {
  return (node as Record)?.type === 'RECORD';
}

export type FormProgression = {
  filled: number;
  locked: number;
  mandatoryFilled: number;
  mandatoryTotal: number;
  optionalFilled: number;
  optionalTotal: number;
  total: number;
};

/**
 * Theses models contain question's id already answered or locked
 * The finalShape looks like this :
 * {
 *    [recordId]: {
 *      [questionId]: boolean
 *    }
 * }
 */
export type QuestionsUnanswered = Dictionary<Dictionary<boolean>>;

export type QuestionsUnlocked = Dictionary<Dictionary<boolean>>;

export type UnfoldForm = {
  answer: AnswerDict;
  form: FormNode[];
};

export type SourceLevelLinkRecord = {
  EXTENSION?: Record[];
  RECORD?: Record[];
};

export type SourceLevelRecords = {
  EXTENSIONS?: Record[];
  RECORDS: Record[];
};

export type SourceLevelBranch = SourceLevelRecords & {
  LINKS?: Dictionary<SourceLevelLink>;
  TYPES: Dictionary<SourceLevelRecords>;
};

// Also rename file
export type SourceLevelLink = {
  BRANCHES: Dictionary<SourceLevelBranch>;
} & SourceLevelLinkRecord;

export const getFormNodePathById = (form: FormNode[], id: string): FormNode[] | undefined => {
  let res: FormNode[] | undefined;
  find(form, (child) => {
    if (child.id === id) {
      res = [child];
    } else if (child.children) {
      const found = getFormNodePathById(child.children, id);
      if (found) {
        res = concat([child], found);
      }
    }
    return !!res;
  });
  return res;
};

export const getFormNodeById = (form: FormNode[], id: string): FormNode | undefined => {
  return last(getFormNodePathById(form, id));
};

const _getInDepthNodeByType = (node: FormNode, questions: FormQuestion[], type?: MnQuestionType): void => {
  if ((!type && isFormQuestion(node)) || (type && (node as FormQuestion).type === type)) {
    questions.push(node);
  }
  forEach(node.children, (child) => {
    _getInDepthNodeByType(child, questions, type);
  });
};

export const getInDepthNodesQuestionsByType = (nodes: FormNode[], questionType?: MnQuestionType): FormQuestion[] => {
  const questions: FormQuestion[] = [];
  forEach(nodes, (node) => {
    _getInDepthNodeByType(node, questions, questionType);
  });
  return questions;
};

export type MissingRecord = {
  branches: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    constraints: any;
    count: number;
    type: string;
  }[];
  label: string;
  type: string;
};

export type MissingMandatoryDocument = {
  document: MandatoryDocument;
  question: UploadFormQuestion;
  record: Record;
};

export type MissingDocument = {
  question: UploadFormQuestion;
  record: Record;
};

export type ContractMappingData = {
  answers: NumericDictionary<AnswerDict>;
  branchesRecordsByRecordIdAndBranchType: Dictionary<Dictionary<Record[]>>;
  linksByLinkIdAndBranchType: Dictionary<Dictionary<MnLink[]>>;
  linksRecordsByLinkIdAndBranchType: Dictionary<Dictionary<Record[]>>;
};

export const getBranchTargetId = (legalComponentId: number, branch: MnBranch): number | undefined | null => {
  const targetId = legalComponentId === branch?.to?.id ? branch?.from?.id : branch?.to?.id;
  return targetId;
};

export enum FormTypeMode {
  DEFAULT_QUESTIONS = 'DEFAULT_QUESTIONS'
}

export type OperationLink = {
  groups: OperationLinkGroup[];
  link: MnLink;
  template: LegalLinkTemplate;
};

export type OperationLinkGroupRecord = {
  branch?: MnBranch;
  id: number;
  label: string;
  record: Record;
};

export type QuestionsAndDocuments = {
  documents: FormNode[];
  questions: FormNode[];
};

export type OperationLinkGroup = {
  branch: MnBranch;
  id: number;
  label: string;
  link: MnLink;
  questionsAndDocumentsByRecordId: NumericDictionary<QuestionsAndDocuments>;
  records: OperationLinkGroupRecord[];
};

export type RecordFiltering = {
  AFTER_TIMESTAMP?: number;
  ANSWER?: { EMPTY?: boolean; NOT_EMPTY?: boolean };
  BEFORE_TIMESTAMP?: number;
  CREATION_TIME?: { after?: number; before?: number };
  EXACT_SPECIFIC_TYPES?: string[][];
  EXCLUDE_IDS?: number[];
  OPERATION_ID?: number;
  RESERVATION?: boolean;
  SEARCH?: {
    QUESTION_IDS?: string[];
    WORDS?: string[];
  };
  SPECIFIC_TYPES?: string[][];
};

export const createValue = (value: unknown): Answer => {
  return { value: value };
};

export const addProgression = (a: FormProgression, b: FormProgression): FormProgression => {
  const progression = {
    filled: a.filled + b.filled,
    locked: a.locked + b.locked,
    mandatoryFilled: a.mandatoryFilled + b.mandatoryFilled,
    mandatoryTotal: a.mandatoryTotal + b.mandatoryTotal,
    optionalFilled: a.optionalFilled + b.optionalFilled,
    optionalTotal: a.optionalTotal + b.optionalTotal,
    total: a.total + b.total
  };

  return progression;
};

export const getDocumentAnswerPath = (question: FormNode): string[] => {
  if (question.key) {
    return [question.id, question.key];
  } else {
    return [question.id];
  }
};

export const getUnfoldFormQuestionIds = (nodes: FormNode[], filterFn?: (node: FormNode) => boolean): string[] => {
  const ids = map(nodes, (node) => {
    if (node.children && !isEmpty(node.children)) {
      return getUnfoldFormQuestionIds(node.children, filterFn);
    } else if (!filterFn || filterFn(node)) {
      return node.id;
    }
    return undefined;
  });
  return filter(flatten(ids), (id) => !!id) as string[];
};

export function getlabelMap(
  legalComponent?: Operation | Record,
  legalComponentTemplate?: LegalTemplate,
  answer?: AnswerDict
): Dictionary<string> | undefined {
  if (isRecord(legalComponent) && legalComponentTemplate && answer) {
    return getRecordAdvancedConfig(legalComponentTemplate)?.getlabelMap?.();
  }
  return undefined;
}

export const authorizedTypesToRecordLabel = (types?: string[][]): string => {
  const currentType = types?.[0][0];
  switch (currentType) {
    case 'BIEN':
      return 'un bien';
    case 'PERSONNE':
      return 'une personne';
    default:
      return 'une fiche';
  }
};

export const getFlatifiedFormQuestions = (nodes: FormNode[]): FormNode[] => {
  return reduce(
    nodes,
    (result, node) => {
      if (isEmpty(node.children) && isFormQuestion(node)) {
        result.push(node);
      } else if (node.children) {
        result = concat(result, getFlatifiedFormQuestions(node.children));
      } else {
        if (node.id !== 'divers') {
          console.warn('[DEBUG] Unknown node to process', node);
        }
      }

      return result;
    },
    [] as FormNode[]
  );
};
export type FormSelectionItem = {
  key: string;
  questionId: string;
  recordId: number;
  value: boolean;
};

export const getQuestionId = (questionId: string, recordId?: number): string | undefined => {
  if (recordId != null) {
    return `${recordId}-${questionId}`;
  }
  return undefined;
};
type FormClass = 'form-mode-small' | 'form-mode-medium' | 'form-mode-large' | 'form-mode-default-questions';

export enum FormMode {
  DEFAULT_QUESTIONS = 'DEFAULT_QUESTIONS',
  LARGE = 'LARGE',
  MEDIUM = 'MEDIUM',
  SMALL = 'SMALL'
}

export const convertFormModeToClassname = (mode: FormMode): FormClass => {
  switch (mode) {
    case FormMode.LARGE:
      return 'form-mode-large';
    case FormMode.MEDIUM:
      return 'form-mode-medium';
    case FormMode.DEFAULT_QUESTIONS:
      return 'form-mode-default-questions';
    default:
      return 'form-mode-small';
  }
};
export const getQuestionTitle = (node: FormQuestion, answer?: AnswerDict): string => {
  if (node.conditionalTitles && answer) {
    const conditionalTitle = node.conditionalTitles.find((conditionalTitle) => {
      return getConditionsState(conditionalTitle.conditions, answer) === true;
    });
    if (conditionalTitle) {
      return conditionalTitle.title;
    }
  }

  return node.label;
};

export interface RecordDocument {
  annexed?: boolean;
  documentId: string;
  documentLabel: string;
  fileId: string;
  recordId: number;
  recordLabel: string;
}
