import { createSelector } from '@reduxjs/toolkit';
import {
  REGISTER_LETTER_NOTIFICATION_THRESHOLD,
  SIGNATURE_NOTIFICATION_THRESHOLD
} from '@mynotary/frontend/billings/core';
import { selectCreditsFeature } from './credits.slice';

export const selectSignatureCredits = createSelector(selectCreditsFeature, (creditsState) => {
  return creditsState.credits.signatureCredits?.quantity;
});

export const selectAdvancedSignatureCredits = createSelector(selectCreditsFeature, (creditsState) => {
  return creditsState.credits.advancedSignatureCredits?.quantity;
});

export const selectRegisteredLetterCredits = createSelector(selectCreditsFeature, (creditsState) => {
  return creditsState.credits.registeredLetterCredits?.quantity;
});

export const selectPreEtatDateCredits = createSelector(selectCreditsFeature, (creditsState) => {
  return creditsState.credits.preEtatDateCredits?.quantity;
});

export const selectErpCredits = createSelector(selectCreditsFeature, (creditsState) => {
  return creditsState.credits.erpCredits?.quantity;
});

export const selectHasCreditNotification = createSelector(
  selectSignatureCredits,
  selectRegisteredLetterCredits,
  (signatureCredit, registerLetterCredit) => {
    return (
      (signatureCredit != null && signatureCredit < SIGNATURE_NOTIFICATION_THRESHOLD) ||
      (registerLetterCredit != null && registerLetterCredit < REGISTER_LETTER_NOTIFICATION_THRESHOLD)
    );
  }
);
