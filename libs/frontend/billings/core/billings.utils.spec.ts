import { CreditPack } from './credits/credits';
import { computeHtPrice, computeTtcPrice, getDiscountAmount } from './billings.utils';
import { Coupon, CreditPackType, DiscountType, getPackUnitPrice } from '@mynotary/crossplatform/billings/core';

const packs: CreditPack[] = [
  {
    quantity: 200,
    type: CreditPackType.PACK_SIGNATURE_CREDITS
  },
  {
    quantity: 200,
    type: CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS
  },
  {
    quantity: 200,
    type: CreditPackType.PACK_REGISTERED_LETTER_CREDITS
  }
];

const couponPercentage: Coupon = {
  code: 'coupon_id',
  discountType: DiscountType.PERCENTAGE,
  discountedProducts: [CreditPackType.PACK_SIGNATURE_CREDITS, CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS],
  value: 10
};

const couponAmount: Coupon = {
  code: 'coupon_id',
  discountType: DiscountType.AMOUNT,
  discountedProducts: [CreditPackType.PACK_SIGNATURE_CREDITS, CreditPackType.PACK_SIGNATURE_ADVANCED_CREDITS],
  value: 10
};

describe(computeTtcPrice.name, () => {
  it('should get right ttc price with percentage discount ', () => {
    const htPrice = computeHtPrice({
      addons: packs.map((pack) => ({
        price: getPackUnitPrice(pack.type, pack.quantity),
        quantity: pack.quantity
      }))
    });

    const discount = getDiscountAmount(packs, couponPercentage);

    const totalHtPrice = htPrice - discount;

    const ttcPrice = computeTtcPrice({
      htPrice: totalHtPrice,
      tva: 20
    });

    expect(htPrice).toEqual(1338);
    expect(discount).toEqual(74);
    expect(ttcPrice).toEqual(1516.8);
  });

  it('should get right discount amount ', () => {
    const discount = getDiscountAmount(packs, couponAmount);
    expect(discount).toEqual(20);
  });

  it('should return 0 if value is under 0', () => {
    const ttcPrice = computeTtcPrice({
      htPrice: -1,
      tva: 20
    });

    expect(ttcPrice).toEqual(0);
  });
});
