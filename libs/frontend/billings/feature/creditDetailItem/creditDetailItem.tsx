import './creditDetailItem.scss';

import { ReactElement } from 'react';

import { MnProps } from '@mynotary/frontend/shared/util';

interface CreditDetailItemProps extends MnProps {
  label: string;
  quantity: number;
  quantityThreshold: number;
}

const CreditDetailItem = ({ label, quantity, quantityThreshold }: CreditDetailItemProps): ReactElement => {
  const status = quantity < quantityThreshold ? 'alert' : 'normal';
  return (
    <div className='cd-item'>
      <div className='cd-item-label'>{label}</div>
      <div className={`cd-item-quantity--${status}`}>{quantity}</div>
    </div>
  );
};

export { CreditDetailItem };
