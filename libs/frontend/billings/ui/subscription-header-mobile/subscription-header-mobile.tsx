import { classNames } from '@mynotary/frontend/shared/util';
import './subscription-header-mobile.scss';

import { MnButtonIcon, MnCircularProgressionBar } from '@mynotary/frontend/shared/ui';
import { ReactNode } from 'react';

interface SubscriptionMobileHeaderProps {
  onClickPrevious?: () => void;
  progression: number;
  status: 'pending' | 'success';
  title: string | ReactNode;
}

export const SubscriptionMobileHeader = ({
  onClickPrevious,
  progression,
  status,
  title
}: SubscriptionMobileHeaderProps) => {
  return (
    <div className={classNames('subscription-mobile-header', status)}>
      <div>
        {onClickPrevious != null && (
          <MnButtonIcon
            onClick={onClickPrevious}
            path='/assets/images/pictos/icon/arrow-left-circle.svg'
            size='large'
            variant={status === 'success' ? 'green400' : 'gray700'}
          />
        )}
      </div>
      <div className='smh-title'>
        <MnCircularProgressionBar
          className='smh-progression'
          color={status === 'success' ? 'success' : 'normal'}
          size={72}
          strokeWidth={6}
          value={progression}
        />
        <div className={classNames('smh-subtitle', status)}>{title}</div>
      </div>
    </div>
  );
};
