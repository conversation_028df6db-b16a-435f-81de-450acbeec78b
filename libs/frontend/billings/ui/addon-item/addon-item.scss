@use 'style/variables/colors' as *;
@use 'style/mixins' as *;

.addon-item {
  @include large-font;

  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  justify-content: space-between;

  .addon-item-optional {

    @include large-font($regular, italic);

    color: $gray700;
  }

  .addon-item-label {
    &.item-bold {
      @include large-font($semi-bold);
    }

    &.disabled {
      color: $gray500 !important;
    }

    &.color-gray {
      color: $gray700;
    }

    &.color-success {
      color: $green300;
    }
  }

  .color-error {
    color: $red300;
  }

  .addon-item-icon {
    flex: 0 0 auto;
  }
}
