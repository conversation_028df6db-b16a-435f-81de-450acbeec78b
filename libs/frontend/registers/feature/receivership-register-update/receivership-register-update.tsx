import { ReactElement, useState } from 'react';
import { ReceivershipRegisterConfig, RegisterUpdateProps } from '@mynotary/frontend/registers/core';
import { ReceivershipRegisterForm } from '../receivership-register-form/receivership-register-form';
import { useNextRegisterNumber } from '../use-next-register-number';
import { useSelector } from 'react-redux';
import { selectReceivershipRegister, updateReceivershipRegister } from '@mynotary/frontend/registers/store';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';

export const ReceivershipRegisterUpdate = ({
  featureId,
  onUpdate,
  organizationId
}: RegisterUpdateProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const [isUpdating, setIsUpdating] = useState(false);

  const receivershipRegister = useSelector(selectReceivershipRegister(featureId));

  const { nextNumber } = useNextRegisterNumber({ organizationId, registerType: 'RECEIVERSHIP' });

  const hasStarted = receivershipRegister?.config?.initialValue !== nextNumber;

  const handleUpdate = async (config: ReceivershipRegisterConfig) => {
    if (receivershipRegister == null) {
      throw new Error("Receivership register doesn't exist");
    }
    try {
      setIsUpdating(true);
      await dispatch(
        updateReceivershipRegister({
          config,
          id: receivershipRegister.id,
          organizationId: receivershipRegister.organizationId
        })
      );
      onUpdate();
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <ReceivershipRegisterForm
      hasStarted={hasStarted}
      initialValue={receivershipRegister?.config ?? null}
      isLoading={isUpdating}
      onValidate={handleUpdate}
    />
  );
};
