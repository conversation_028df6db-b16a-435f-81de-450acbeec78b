import './organization-type-page.scss';
import React, { ReactElement } from 'react';
import { OrganizationPageTemplate } from '../organization-page-template/organization-page-template';
import { OrganizationType } from '@mynotary/crossplatform/organizations/api';
import { map } from 'lodash';
import { useDispatch } from 'react-redux';
import { updateDraftType } from '@mynotary/frontend/organizations/store';
import { routePaths } from '@mynotary/frontend/routes/api';
import { useNavigate } from 'react-router';

type OrganizationTypeConfig = {
  icon: string;
  label: string;
};

const organizationTypeConfig: Record<OrganizationType, OrganizationTypeConfig | null> = {
  [OrganizationType.AGENCY]: {
    icon: '/assets/images/illustration/organization/8-Agence.png',
    label: 'Agence'
  },
  [OrganizationType.DEVELOPER]: {
    icon: '/assets/images/illustration/organization/4-Promoteur.png',
    label: 'Promoteur'
  },
  [OrganizationType.HOUSE_BUILDER]: {
    icon: '/assets/images/illustration/organization/7-Constructeur.png',
    label: 'Constructeur'
  },
  [OrganizationType.MARKETER]: {
    icon: '/assets/images/illustration/organization/5-Bien.png',
    label: 'Administration de biens'
  },
  [OrganizationType.NEGOCIATOR_NETWORK]: {
    icon: '/assets/images/illustration/organization/3-ReseauNego.png',
    label: 'Réseau de négociateurs'
  },
  [OrganizationType.NOTARY_OFFICE]: {
    icon: '/assets/images/illustration/organization/1-Etude.png',
    label: 'Étude'
  },
  [OrganizationType.PORTALYS_NOTARY_OFFICE]: {
    icon: '/assets/images/illustration/organization/1-Etude.png',
    label: 'Étude Portalys'
  },
  [OrganizationType.PUBLIC_HOUSER]: {
    icon: '/assets/images/illustration/organization/2-BailleurSocial.png',
    label: 'Bailleur social'
  },
  [OrganizationType.AGENCY_NETWORK]: null
};

export const OrganizationTypePage = (): ReactElement => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleOrganizationSelected = (type: OrganizationType) => {
    dispatch(updateDraftType(type));
    navigate(routePaths.organization.organizationInformation.path);
  };

  const handlePreviousStep = () => {
    navigate(routePaths.organization.organizationSize.path);
  };

  return (
    <OrganizationPageTemplate
      hasMobileNavigation={false}
      onPreviousStep={handlePreviousStep}
      title='Sélectionnez votre type d’organisation'
    >
      <div className='organization-type-page'>
        {map(OrganizationType, (type, idx) => {
          const config = organizationTypeConfig[type];
          if (type === OrganizationType.PORTALYS_NOTARY_OFFICE) {
            return null;
          }

          return (
            config != null && (
              <div className='otp-tile' key={idx} onClick={() => handleOrganizationSelected(type)}>
                <div className='otp-icon-container'>
                  <img className='otp-icon' src={config.icon} />
                </div>
                <div className='otp-label'>{config.label}</div>
              </div>
            )
          );
        })}
      </div>
    </OrganizationPageTemplate>
  );
};
