@use 'style/variables/colors' as *;
@use 'style/mixins' as *;

.organization-size-page {
  display: flex;
  flex-direction: column;
  margin: auto;

  .osp-description {
    @include large-font;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    margin-bottom: 24px;
    padding: 32px;
    border-radius: 10px;

    color: $black;

    background: #FFF;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 6%), 0 0 4px 0 rgba(0, 0, 0, 4%);
  }

  .osp-tiles {
    @include responsive($desktop-min) {
      display: flex;
      flex-direction: row;
    }

    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .osp-tile {
    @include responsive($desktop-min) {
      width: 100%;
      max-width: 412px;
    }

    cursor: pointer;

    display: flex;
    flex: 1 0 0;
    flex-direction: column;
    gap: 24px;
    align-items: center;

    padding: 24px 48px;
    border: 1px solid $gray300;
    border-radius: 10px;

    background: $white;
  }

  .osp-tile-icon {
    @include responsive($desktop-min) {
      display: block;
      display: flex;
      align-items: center;
      justify-content: center;

      width: 160px;
      height: 160px;
      padding: 16px;

      & > img {
        width: 108px;
        height: 108px;
      }
    }

    display: none;
    border-radius: 100px;
    background: var(--backgrounds-100, #F5FAFA);
  }

  .osp-tile-choice {
    display: flex;
    flex-direction: row;
    gap: 16px;
    align-items: center;
  }

  .osp-tile-choice-icon {
    width: 40px;
    height: 40px;
    padding: 8px;
    border-radius: 100px;

    background: var(--primary-primary-50, #EBFAFA);
  }

  .osp-tile-choice-label {
    @include h5-font;
  }

  .osp-tile-description {
    @include medium-font;

    text-align: center;
  }
}
