export interface GetContractValidatorsArgs {
  operationId?: number;
  organizationId?: number;
}

export interface ContractValidators {
  id: number;
  locked: boolean;
  users: Validators[];
}

export interface UpdateContractValidatorsArgs {
  id: number;
  locked?: boolean;
  organizationId: number;
  users: Array<Validators>;
}

export interface Validators {
  email: string;
  fullName: string;
  id: number;
}
