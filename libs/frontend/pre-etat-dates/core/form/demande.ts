import {
  DateFormQuestion,
  EmailFormQuestion,
  FormQuestion,
  NumberFormQuestion,
  RegisterNumberFormQuestion,
  SelectFormQuestion,
  TextFormQuestion
} from '@mynotary/crossplatform/shared/forms-util';

export enum DemandeQuestionId {
  civilite = 'civilite',
  email = 'email',
  identifiant_extranet_syndic = 'identifiant_extranet_syndic',
  mot_de_passe_extranet_syndic = 'mot_de_passe_extranet_syndic',
  nom = 'nom',
  nom_syndic = 'nom_syndic',
  prenom = 'prenom',
  telephone = 'telephone',
  vente_lots_totale_copropriete = 'vente_lots_totale_copropriete'
}

export const getDemandeInfoNegoImmobilierQuestions = (): Array<DemandeQuestion> => {
  return [
    {
      choices: [
        {
          id: 'monsieur',
          label: 'Monsieur'
        },
        {
          id: 'madame',
          label: 'Madame'
        }
      ],
      id: DemandeQuestionId.civilite,
      label: 'Civilité',
      placeholder: 'Civilité',
      required: true,
      type: 'SELECT'
    },
    {
      id: DemandeQuestionId.nom,
      label: 'Nom',
      placeholder: 'Nom',
      required: true,
      type: 'TEXT'
    },
    {
      id: DemandeQuestionId.prenom,
      label: 'Prénom',
      placeholder: 'Prénom',
      required: true,
      type: 'TEXT'
    },
    {
      id: DemandeQuestionId.telephone,
      label: 'Téléphone',
      placeholder: 'Téléphone',
      required: true,
      type: 'PHONE'
    },
    {
      id: DemandeQuestionId.email,
      label: 'Email',
      placeholder: 'Email',
      required: true,
      type: 'EMAIL'
    }
  ];
};

export const getDemandeFormQuestions = (): Array<DemandeQuestion> => {
  return [
    {
      choices: [
        {
          id: 'non',
          label: 'NON'
        },
        {
          id: 'oui',
          label: 'OUI'
        }
      ],
      id: DemandeQuestionId.vente_lots_totale_copropriete,
      label: "Le vendeur vend-il tous les lots qu'il possède dans la copropriété ?",
      required: true,
      type: 'SELECT-BINARY'
    },
    {
      id: DemandeQuestionId.nom_syndic,
      label: 'Nom du syndic',
      maxLength: 255,
      placeholder: 'Nom du syndic',
      required: true,
      type: 'TEXT'
    },
    {
      id: DemandeQuestionId.identifiant_extranet_syndic,
      label: 'Identifiant extranet syndic',
      placeholder: 'Identifiant',
      preventAutoComplete: false,
      required: true,
      type: 'TEXT'
    },
    {
      id: DemandeQuestionId.mot_de_passe_extranet_syndic,
      label: 'Mot de passe extranet syndic',
      placeholder: 'Mot de passe',
      preventAutoComplete: false,
      required: true,
      type: 'TEXT'
    }
  ];
};

export type DemandeQuestion =
  | FormQuestion
  | TextFormQuestion
  | SelectFormQuestion
  | NumberFormQuestion
  | DateFormQuestion
  | RegisterNumberFormQuestion
  | EmailFormQuestion;
