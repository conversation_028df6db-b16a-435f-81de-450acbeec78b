import './pre-etat-date-ui.scss';
import { ReactNode } from 'react';
import { PreEtatDateStep } from '@mynotary/frontend/pre-etat-dates/core';
import { MnButtonController } from '@mynotary/frontend/shared/ui';
import { openInNewTab } from '@mynotary/frontend/shared/util';

interface PreEtatDateWorkflowContentProps {
  children: ReactNode;
}

export const PreEtatDateWorkflowContent = ({ children }: PreEtatDateWorkflowContentProps) => {
  return <div className='pre-etat-date-workflow-content'>{children}</div>;
};

interface PreEtatDateWorkflowTitleProps {
  info: PreEtatDateStepInfo;
}

export const PreEtatDateWorkflowTitle = ({ info }: PreEtatDateWorkflowTitleProps) => {
  return (
    <div className='pre-etat-date-workflow-title'>
      <h4 className='pedwt-title'>{info.title}</h4>
      <div>{info.description}</div>
      <MnButtonController
        className='pedwt-help'
        iconVariant='primary'
        label={`Comment commander un pré-état daté ?`}
        leftIcon={'/assets/images/pictos/icon/question-light.svg'}
        onClick={() =>
          openInNewTab('https://support.mynotary.fr/fr/articles/10393346-comment-commander-un-pre-etat-date')
        }
        underlined={false}
      />
    </div>
  );
};

interface PreEtatDateWorkflowTitleStepProps {
  step: PreEtatDateStep;
}

export const PreEtatDateWorkflowTitleStep = ({ step }: PreEtatDateWorkflowTitleStepProps) => {
  const stepInfo = stepsInfo[step];
  return <PreEtatDateWorkflowTitle info={stepInfo} />;
};

interface PreEtatDateStepInfo {
  description: string;
  title: string;
}

const stepsInfo: Record<PreEtatDateStep, PreEtatDateStepInfo> = {
  [PreEtatDateStep.HOUSE_INFO]: {
    description: 'Vérifiez les biens et complétez si besoin les informations nécessaires.',
    title: 'Informations du bien'
  },
  [PreEtatDateStep.ORDER_INFO]: {
    description: 'Complétez les informations sur la demande',
    title: 'Informations de la demande'
  },
  [PreEtatDateStep.OVERVIEW]: {
    description: 'Partager le lien de paiement avec votre client',
    title: 'Lien à destination du client'
  },
  [PreEtatDateStep.PAYMENT_CHOICE]: {
    description: 'Remplir les éléments nécessaires à la facturation',
    title: 'Prestation payante'
  },
  [PreEtatDateStep.PAYMENT_CREDIT]: {
    description: 'Acceptez-vous d’utiliser un crédit pour les éléments suivants? ',
    title: 'Prestation payante'
  },
  [PreEtatDateStep.PAYMENT_SHARE]: {
    description: 'Choisir la modalité d’envoi et, si nécessaire, éditer l’email',
    title: 'Options d’envoi'
  }
};
