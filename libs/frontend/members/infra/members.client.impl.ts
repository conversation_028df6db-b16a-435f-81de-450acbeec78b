import { MembersClient, GetMembersArgs } from '@mynotary/frontend/members/core';
import { createAxiosInstance } from '@mynotary/frontend/shared/axios-util';
import { environment } from '@mynotary/frontend/shared/environments-util';
import { MemberDto, MembersDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { Member } from '@mynotary/crossplatform/members/core';
import { convertEnum } from '@mynotary/crossplatform/shared/util';
import { Civility } from '@mynotary/crossplatform/shared/users-core';

export class MembersClientImpl implements MembersClient {
  mainApiClient = createAxiosInstance({ baseURL: environment.apiMyNotaryUrl });

  async getMembers(args: GetMembersArgs): Promise<Member[]> {
    const { data } = await this.mainApiClient.get<MembersDto>(`/members`, {
      params: {
        limit: args.limit,
        organizationId: args.organizationId,
        search: args.search,
        userId: args.userId
      }
    });
    return data.items.map(convertMember);
  }
}

function convertMember(member: MemberDto): Member {
  const commonProperties = {
    creationTime: member.creationTime,
    email: member.email,
    id: member.id,
    organizationId: member.organizationId,
    organizationName: member.organizationName,
    roleId: member.roleId,
    roleName: member.roleName
  };

  if (
    member.civility == null ||
    member.firstname == null ||
    member.lastname == null ||
    member.userId == null ||
    member.userCreationTime == null
  ) {
    return commonProperties;
  } else {
    return {
      ...commonProperties,
      civility: convertEnum(Civility, member.civility),
      email: member.email,
      firstname: member.firstname,
      lastname: member.lastname,
      userCreationTime: member.userCreationTime,
      userId: member.userId
    };
  }
}
