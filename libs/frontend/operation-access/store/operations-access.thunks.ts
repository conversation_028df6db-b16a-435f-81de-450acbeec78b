import { OperationAccessClient } from '@mynotary/frontend/operation-access/core';
import { inject } from '@mynotary/frontend/shared/injector-util';
import { AppAsyncThunk } from '@mynotary/frontend/shared/redux-util';
import { setOperationAccess } from './operations-access.slice';

export const getOperationAccess =
  (organizationId: number): AppAsyncThunk<void> =>
  async (dispatch) => {
    const operationAccessClient = inject(OperationAccessClient);

    try {
      const operationAccess = await operationAccessClient.getOperationAccess({ organizationId });
      dispatch(setOperationAccess({ operationAccess, organizationId }));
    } catch (e) {
      console.error('Operation access not found', e);
    }
  };
