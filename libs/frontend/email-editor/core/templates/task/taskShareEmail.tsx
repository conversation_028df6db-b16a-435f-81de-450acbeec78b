import contentDownload from './taskShareDownloadEmail.html';
import contentFill from './taskShareFillEmail.html';
import contentValidate from './taskValidateContract.html';
import contentShare from './taskShareRecordEmail.html';
import readContractProject from './task-read-contract-project.html';
import taskPaymentRequest from './task-payment-request.html';
import taskReviewContract from './task-review-email.html';

export const TASK_DOWNLOAD_FILES = {
  content: contentDownload,
  subject: 'Partage de documents pour le contrat {{contractName}}'
};

export const TASK_DOWNLOAD_FILES_OPERATION = {
  content: contentDownload,
  subject: 'Partage de documents pour le dossier {{operationLabel}}'
};

export const TASK_FILL_OPERATION_RECORDS = {
  content: contentFill,
  subject: 'Demande de documents'
};

export const TASK_REVIEW_CONTRACT = {
  content: taskReviewContract,
  subject: `{{operationLabel}} - Vous avez reçu une nouvelle demande de validation du contrat de la part de {{senderFirstname}} {{senderName}}`
};

export const TASK_SHARE_OPERATION_RECORD = {
  content: contentShare,
  subject: "Demande d'informations"
};

export const TASK_VALIDATE_CONTRACT = {
  content: contentValidate,
  subject:
    '{{operationLabel}} - Vous avez reçu une demande de validation pour le contrat {{contractName}} par {{senderFirstname}} {{senderName}}'
};

export const TASK_READ_PROJECT_CONTRACT = {
  content: readContractProject,
  subject:
    'Vous avez reçu un projet de contrat à relire de la part de ( {{senderFirstname}} {{senderName}} - {{senderOrganizationName}} )'
};

export const TASK_PAYMENT_REQUEST = {
  content: taskPaymentRequest,
  subject: 'Commander mon pré-état daté en ligne'
};

export const TASK_CUSTOM = {
  content: '',
  subject: 'Vous avez reçu une nouvelle tâche de la part de {{senderFirstname}} {{senderName}}'
};
