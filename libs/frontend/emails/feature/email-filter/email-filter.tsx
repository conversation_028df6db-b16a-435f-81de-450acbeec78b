import './email-filter.scss';
import {
  Action,
  MnDropDownTileHeader,
  MnSearchInput,
  Popover,
  PopoverActionList,
  PopoverContent,
  PopoverTrigger
} from '@mynotary/frontend/shared/ui';
import { useState } from 'react';
import { EmailFiltering } from '@mynotary/frontend/emails/core';
import { EmailCategory } from '@mynotary/crossplatform/emails/core';
import { isEmpty } from 'lodash';

interface EmailFilterProps {
  onChangeFilter: (filtering: EmailFiltering) => void;
}

export const EmailFilter = ({ onChangeFilter }: EmailFilterProps) => {
  const [search, setSearch] = useState<string>();
  const [category, setCategory] = useState<EmailCategory>();
  const [opened, setOpened] = useState(false);

  const handleChangeSearch = (value: string | undefined) => {
    const trimValue = value?.trim();
    const newValue = isEmpty(trimValue) ? undefined : trimValue;
    setSearch(newValue);
    onChangeFilter({ category, search: newValue });
  };

  const handleChangeCategory = (value?: EmailCategory) => {
    setCategory(value);
    onChangeFilter({ category: value, search });
    setOpened(false);
  };

  const getLabel = (category?: EmailCategory) => {
    return items.find((item) => item.category === category)?.label ?? 'Tous les évènements';
  };

  const items = [
    { id: '1', label: 'Tous les évènements' },
    { category: EmailCategory.SIGNATURE, id: '2', label: 'Signatures' },
    { category: EmailCategory.TASK, id: '3', label: 'Tâches' },
    { category: EmailCategory.OPERATION_INVITATION, id: '4', label: 'Invitations' },
    { category: EmailCategory.REGISTERED_LETTER, id: '5', label: 'Lettres recommandées' }
  ];

  return (
    <div className={'email-filter'}>
      <MnSearchInput className={'ef-search'} onChange={handleChangeSearch} placeholder={'Rechercher ...'} />
      <Popover onOpenChange={setOpened} open={opened}>
        <PopoverTrigger asChild={true}>
          <MnDropDownTileHeader
            className={'sd-button-selector'}
            label={getLabel(category)}
            path='/assets/images/pictos/icon/chevron-down-light.svg'
          />
        </PopoverTrigger>
        <PopoverContent align={'start'}>
          <PopoverActionList>
            {items.map((item) => (
              <Action className={'ef-actions'} key={item.id} onClick={() => handleChangeCategory(item.category)}>
                {item.label}
              </Action>
            ))}
          </PopoverActionList>
        </PopoverContent>
      </Popover>
    </div>
  );
};
