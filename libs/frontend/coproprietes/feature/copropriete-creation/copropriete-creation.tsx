import './copropriete-creation.scss';
import { Copropriete } from '@mynotary/crossplatform/coproprietes/core';
import {
  convertAddressToCoproRecord,
  convertCoproprieteToCoproRecord,
  CoproprietesClient,
  UserWithOrganization
} from '@mynotary/frontend/coproprietes/core';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { Record, MnRecordTile, RecordNew, getRecordDuplicates } from '@mynotary/frontend/legals/api';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { MnButton, MnLoaderPopin, DataGouvLogo } from '@mynotary/frontend/shared/ui';
import { ReactNode, useState } from 'react';
import { MnAddress } from '@mynotary/crossplatform/shared/util';
import { MnProps, slowDown } from '@mynotary/frontend/shared/util';
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';
import { CoproprieteCreationContent, CoproprieteListContainer } from '../copropriete-ui/copropriete-ui';
import { CoproprieteTile } from '../copropriete-tile/copropriete-tile';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { setSuccessMessage } from '@mynotary/frontend/snackbars/api';
import { MnInputPlaces } from '@mynotary/frontend/places/api';
import { FooterActionBtnPopin } from '@mynotary/frontend/shared/ui';

export interface RecordCreationComponentProps extends MnProps {
  defaultCreationAnswer?: AnswerDict;
  onClosePopin: () => void;
  onValidate: (record: Record | RecordNew) => void | Promise<void>;
  template: LegalRecordTemplate;
  templateSelector: ReactNode;
}

type Step = 'form' | 'search' | 'record-duplicate' | 'copro-duplicate';

export const CoproprieteCreation = ({ defaultCreationAnswer, onValidate }: RecordCreationComponentProps) => {
  const [step, setStep] = useState<Step>('form');
  const [coproprietes, setCoproprietes] = useState<Copropriete[]>([]);
  const [records, setRecords] = useState<Record[]>([]);
  const [address, setAddress] = useState<MnAddress | null>(null);

  const organization = useSelector(selectCurrentOrganization);
  const user = useSelector(selectCurrentUser);

  const coproprietesClient = useService(CoproprietesClient);

  const dispatch = useAsyncDispatch();

  if (user == null || organization == null) {
    return null;
  }

  const userWithOrganization = { organizationId: organization.id, userId: user.id };

  const handleSearch = async (address: MnAddress) => {
    setAddress(address);
    setStep('search');
    const duplicateRecords = await slowDown(
      dispatch(getRecordDuplicates({ adresse: { value: address } }, 'RECORD__STRUCTURE__COPROPRIETE'))
    );

    if (duplicateRecords.length > 0) {
      setRecords(duplicateRecords);
      setStep('record-duplicate');
      return;
    }

    await searchCoproprietes(address);
  };

  const searchCoproprietes = async (address: MnAddress) => {
    const latitude = address.location?.lat;
    const longitude = address.location?.lng;
    if (latitude != null && longitude != null) {
      const coproprietes = await slowDown(coproprietesClient.getCoproprietes({ latitude, longitude }));
      setCoproprietes(coproprietes);
      setStep('copro-duplicate');
      return;
    }

    await createRecord(address);
  };

  const createRecord = async (address: MnAddress) => {
    await onValidate(convertAddressToCoproRecord({ address, user: userWithOrganization }));
    dispatch(setSuccessMessage('Fiche ajoutée'));
  };

  const handleClickNotFound = async () => {
    setStep('search');
    if (address != null) {
      await searchCoproprietes(address);
    }
  };

  const defaultAddress = defaultCreationAnswer?.['adresse']?.value;

  const handleValidationDuplicate = (record: Record) => {
    dispatch(setSuccessMessage('Fiche ajoutée'));
    onValidate(record);
  };

  return (
    <div className='copropriete-creation'>
      {step === 'form' && <CoproprieteSearch defaultAddress={defaultAddress} onSearch={handleSearch} />}
      {step === 'search' && (
        <MnLoaderPopin
          gifSrc='/assets/images/gif/cadastre.gif'
          isVisible={true}
          subtitle='Veuillez patienter...'
          title={`Nous recherchons \nles copropriétés \nconnues à cette adresse`}
        />
      )}
      {step === 'record-duplicate' && (
        <CoproprieteRecordDuplicate
          onNotFound={handleClickNotFound}
          onValidate={handleValidationDuplicate}
          records={records}
        />
      )}
      {step === 'copro-duplicate' && address != null && (
        <CoproprieteDuplicate
          addressBeingSearched={address}
          coproprietes={coproprietes}
          onNotFound={createRecord}
          onSearch={handleSearch}
          onValidate={onValidate}
          user={userWithOrganization}
        />
      )}
    </div>
  );
};

interface CoproprieteSearchProps {
  currentAddress?: MnAddress;
  defaultAddress?: MnAddress;
  onSearch: (address: MnAddress) => void;
}

const CoproprieteSearch = ({ currentAddress, defaultAddress, onSearch }: CoproprieteSearchProps) => {
  const [address, setAddress] = useState<MnAddress | null>(defaultAddress ?? null);

  const handleSearch = () => {
    if (address == null) {
      return;
    }

    onSearch(address);
  };

  return (
    <div className='copropriete-search'>
      <MnInputPlaces onChange={setAddress} placeholder={"l'adresse"} value={address} />
      <MnButton
        className='cs-button'
        disabled={address == null || currentAddress?.formattedAddress === address.formattedAddress}
        label={'confirmer'}
        onClick={handleSearch}
      />
    </div>
  );
};

interface CoproprieteRecordsDuplicateProps {
  onNotFound: () => void;
  onValidate: (record: Record) => void;
  records: Record[];
}

const CoproprieteRecordDuplicate = ({ onNotFound, onValidate, records }: CoproprieteRecordsDuplicateProps) => {
  return (
    <CoproprieteCreationContent className='copropriete-record-duplicate'>
      <CoproprieteListContainer className='crd-list'>
        <h5 className='crd-label'>Résultats similaires existants déjà dans la plateforme</h5>
        {records.map((record) => (
          <MnRecordTile hasAction={true} key={record.id} onClick={() => onValidate(record)} recordId={record.id} />
        ))}
      </CoproprieteListContainer>
      <FooterActionBtnPopin onClick={onNotFound} types={['COPROPRIETE']} />
    </CoproprieteCreationContent>
  );
};

interface CoproprieteDuplicateProps {
  addressBeingSearched: MnAddress;
  coproprietes: Copropriete[];
  onNotFound: (address: MnAddress) => void;
  onSearch: (address: MnAddress) => Promise<void>;
  onValidate: (record: RecordNew) => void;
  user: UserWithOrganization;
}

const CoproprieteDuplicate = ({
  addressBeingSearched,
  coproprietes,
  onNotFound,
  onSearch,
  onValidate,
  user
}: CoproprieteDuplicateProps) => {
  const dispatch = useAsyncDispatch();

  const handleClick = async (copropriete: Copropriete) => {
    const recordNew = convertCoproprieteToCoproRecord({ copropriete, user });
    onValidate(recordNew);
    dispatch(setSuccessMessage('Infos récupérées automatiquement sur le site du gouvernement'));
  };

  return (
    <CoproprieteCreationContent className='copropriete-duplicate'>
      <CoproprieteSearch
        currentAddress={addressBeingSearched}
        defaultAddress={addressBeingSearched}
        onSearch={onSearch}
      />
      <DataGouvLogo count={coproprietes.length} />
      <CoproprieteListContainer className='cd-list'>
        {coproprietes.map((copropriete) => (
          <CoproprieteTile
            copropriete={copropriete}
            key={copropriete.denomination}
            onClick={() => handleClick(copropriete)}
          />
        ))}
      </CoproprieteListContainer>
      <FooterActionBtnPopin onClick={() => onNotFound(addressBeingSearched)} types={['COPROPRIETE']} />
    </CoproprieteCreationContent>
  );
};
