@use 'style/mixins/typography' as *;
@use 'style/variables' as *;

.copropriete-creation {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.copropriete-search {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  justify-content: center;

  padding-top: 32px;
}

.copropriete-record-duplicate {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .crd-label {
    padding-bottom: 8px;
    color: $black;
    text-align: center;
  }
}

.copropriete-duplicate {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
