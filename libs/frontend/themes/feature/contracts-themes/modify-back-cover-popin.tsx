import './modify-cover-popin.scss';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON><PERSON> } from '@mynotary/frontend/shared/ui';
import { AcceptType } from '@mynotary/crossplatform/shared/forms-util';
import { CustomizationContractFormFileBlock } from './file-block/customization-contract-form-file-block';
import { selectTheme } from '@mynotary/frontend/themes/store';
import { useSelector } from 'react-redux';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { useState } from 'react';
import { SpecificTheme } from '@mynotary/frontend/themes/core';
import { getContractSpecificTheme } from '@mynotary/crossplatform/themes/api';
import { ContractThemePopinProps } from './contracts-theme';
import { FileUploadWithPreview } from '@mynotary/frontend/files/api';

export const ModifyBackCoverPopin = ({
  legalContractLabel,
  legalContractTemplateId,
  legalOperationLabel,
  legalOperationTemplateId,
  onChange,
  onClose,
  open
}: ContractThemePopinProps) => {
  const organization = useSelector(selectCurrentOrganization);
  const theme = useSelector(selectTheme(organization?.id ?? -1));
  const [specificTheme, setSpecificTheme] = useState<SpecificTheme>({});

  const { backCover } = getContractSpecificTheme({
    contractModelId: legalContractTemplateId,
    contractsTheme: theme.contracts,
    operationTemplateId: legalOperationTemplateId
  });

  const handleFileChange = (fileId: string | undefined) => {
    setSpecificTheme({ backCover: fileId });
  };

  const handleSave = () => {
    onChange(specificTheme);
    onClose();
  };

  return (
    <SheetPopin className={'modify-cover-popin'} isOpened={open}>
      <SheetHeader onClose={onClose}>
        <div className={'mcp-header'}>
          <h5>Modifier la quatrième page de couverture</h5>
          <div className={'mcp-header-subtitle'}>
            <div>{legalOperationLabel} :</div>
            <div>{legalContractLabel}</div>
          </div>
        </div>
      </SheetHeader>
      <div className={'mcp-content'}>
        <CustomizationContractFormFileBlock
          className='ccf-file-import'
          description={`La quatrième de couverture est affichée à la fin du document après qu'il soit signé`}
          title='Quatrième de couverture'
        >
          <FileUploadWithPreview
            accept={AcceptType.PDF}
            defaultFileId={backCover}
            description='Type : .pdf. Format: A4.'
            onUpload={(file) => handleFileChange(file?.id)}
          />
        </CustomizationContractFormFileBlock>
      </div>
      <SheetFooter>
        <MnButton label='Annuler' onClick={() => onClose?.()} type='button' variant={'borderless-primary'} />
        <MnButton label='Sauvegarder' onClick={handleSave} />
      </SheetFooter>
    </SheetPopin>
  );
};
