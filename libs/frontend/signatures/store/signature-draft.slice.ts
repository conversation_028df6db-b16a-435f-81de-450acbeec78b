import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  NewPartialSignatory,
  NewPartialSignatoryWithId,
  NewSignatureFile,
  Signature,
  SignatureDraft,
  SignatureDraftStep,
  SignatureSubscriber
} from '@mynotary/frontend/signatures/core';
import { Email } from '@mynotary/frontend/email-editor/api';
import { findIndex, map, some } from 'lodash';
import { v4 as uuid } from 'uuid';
import { SendCodeType, SignaturePlace, SignatureProviderType } from '@mynotary/crossplatform/signatures/api';
import { swapArrayElements } from '@mynotary/frontend/shared/util';

type SignatureWorkflowDraft = {
  init: boolean;
  selectedStep?: SignatureDraftStep;
  signature: SignatureDraft;
  waitForUpdate: boolean;
};

/**
 * We cast SignatureWorkflowDraft to avoid having to check in every action.
 * It's safe to do because the first action will always set the state to a valid value with setSignatureDraft.
 */
const initialState: SignatureWorkflowDraft = {
  init: false,
  signature: {},
  waitForUpdate: false
} as SignatureWorkflowDraft;

export const signatureDraftSlice = createSlice({
  initialState,
  name: 'signatureDraft',
  reducers: {
    addFile: (state, action: PayloadAction<Omit<NewSignatureFile, 'order'>>) => {
      state.waitForUpdate = true;
      state.signature.files = state.signature.files ?? [];
      state.signature.files.push({ ...action.payload, order: state.signature.files.length });
    },
    addSignatory: (state, action: PayloadAction<NewPartialSignatory>) => {
      state.waitForUpdate = true;
      state.signature.signatories = state.signature.signatories ?? [];
      state.signature.signatories.push({ ...action.payload, id: uuid(), order: 1 });
    },
    cleanUpSignatureDraft: () => {
      return { ...initialState };
    },
    deleteSignatoriesGroup: (state, action: PayloadAction<number>) => {
      state.waitForUpdate = true;
      state.signature.signatories = state.signature.signatories?.map((signatory) => ({
        ...signatory,
        order: signatory.order === action.payload ? signatory.order - 1 : signatory.order
      }));
    },
    goToNextSignatureDraftStep: (state) => {
      switch (state.selectedStep) {
        case SignatureDraftStep.SIGNATORIES: {
          state.selectedStep = state.signature.ordered
            ? SignatureDraftStep.SIGNATORIES_GROUPS
            : SignatureDraftStep.FILES;
          break;
        }
        case SignatureDraftStep.SIGNATORIES_GROUPS:
          state.selectedStep = SignatureDraftStep.FILES;
          break;
        case SignatureDraftStep.FILES: {
          const hasEmail = some(
            state.signature.signatories,
            (signatory) => signatory.signaturePlace === SignaturePlace.ONLINE
          );
          state.selectedStep = hasEmail ? SignatureDraftStep.EMAIL : SignatureDraftStep.SUMMARY;
          break;
        }
        case SignatureDraftStep.EMAIL:
          state.selectedStep = SignatureDraftStep.SUMMARY;
          break;
        case SignatureDraftStep.SUMMARY:
          state.selectedStep = SignatureDraftStep.SUMMARY;
          break;
      }
    },
    goToPreviousSignatureDraftStep: (state) => {
      switch (state.selectedStep) {
        case SignatureDraftStep.SIGNATORIES_GROUPS:
          state.selectedStep = SignatureDraftStep.SIGNATORIES;
          break;
        case SignatureDraftStep.FILES: {
          state.selectedStep = state.signature.ordered
            ? SignatureDraftStep.SIGNATORIES_GROUPS
            : SignatureDraftStep.SIGNATORIES;
          break;
        }
        case SignatureDraftStep.EMAIL:
          state.selectedStep = SignatureDraftStep.FILES;
          break;
        case SignatureDraftStep.SUMMARY: {
          const hasEmail = some(
            state.signature.signatories,
            (signatory) => signatory.signaturePlace === SignaturePlace.ONLINE
          );
          state.selectedStep = hasEmail ? SignatureDraftStep.EMAIL : SignatureDraftStep.FILES;
          break;
        }
      }
    },
    initSignatureDraft: (
      state,
      action: PayloadAction<{ contractId: number; contractModelId: string; signature: Signature }>
    ) => {
      const { contractId, contractModelId, signature } = action.payload;

      if (!signature.creationContext?.method) {
        throw new Error('Method is required to initialize a signature');
      }

      const defaultSignatories = signature.creationContext.signatories?.map((signatory) => ({
        ...getPartialSignatoryByProviderType({
          signatory,
          type: signature.creationContext.providerType
        }),
        id: uuid(),
        order: signatory.order ?? 1
      }));

      state.init = true;
      state.waitForUpdate = false;

      state.signature = {
        contractId,
        contractModelId,
        dueDate: signature.creationContext.dueDate,
        email: signature.creationContext.emailTemplate,
        files: signature.creationContext.files?.map((file, index) => ({ ...file, order: file.order ?? index })) ?? [],
        id: signature.id,
        method: signature.creationContext.method,
        ordered: signature.creationContext.ordered,
        providerType: signature.creationContext.providerType,
        shouldNotifyAfterSignature: signature.creationContext.shouldNotifyAfterSignature,
        signatories: defaultSignatories ?? [],
        subscribers: signature.creationContext.subscribers
      };
    },
    moveFileDown: (state, action: PayloadAction<string>) => {
      state.waitForUpdate = true;
      const files = state.signature.files ?? [];
      const currIndex = findIndex(files, (file) => file.id === action.payload);

      swapArrayElements<NewSignatureFile>(files, currIndex, currIndex + 1);
      state.signature.files = map(files, (file, index) => {
        return {
          ...file,
          order: index
        };
      });
    },
    moveFileUp: (state, action: PayloadAction<string>) => {
      state.waitForUpdate = true;
      const files = state.signature.files ?? [];
      const currIndex = findIndex(files, (file) => file.id === action.payload);

      swapArrayElements<NewSignatureFile>(files, currIndex, currIndex - 1);
      state.signature.files = map(files, (file, index) => {
        return {
          ...file,
          order: index
        };
      });
    },
    removeFile: (state, action: PayloadAction<string>) => {
      state.waitForUpdate = true;
      state.signature.files = state.signature.files?.filter((file) => file.id !== action.payload);
    },
    removeSignatory: (state, action: PayloadAction<string>) => {
      state.waitForUpdate = true;
      const index = state.signature.signatories?.findIndex((signatory) => signatory.id === action.payload);
      if (index !== undefined && index !== -1) {
        state.signature.signatories?.splice(index, 1);
      }
    },
    setDueDate: (state, action: PayloadAction<number | undefined>) => {
      state.waitForUpdate = true;
      state.signature.dueDate = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.signature.error = action.payload;
    },
    setSignatureStep: (state, action: PayloadAction<SignatureDraftStep>) => {
      state.selectedStep = action.payload;
    },
    setSubscribers: (state, action: PayloadAction<SignatureSubscriber[]>) => {
      state.waitForUpdate = true;
      state.signature.subscribers = action.payload;
    },
    updateEmail: (state, action: PayloadAction<Email>) => {
      state.waitForUpdate = true;
      state.signature.email = action.payload;
    },
    updateFile: (state, action: PayloadAction<Partial<NewSignatureFile> & { id: string }>) => {
      state.waitForUpdate = true;
      const index = state.signature.files?.findIndex((file) => file.id === action.payload.id);

      if (index === undefined) {
        return;
      }

      state.signature.files = state.signature.files ?? [];
      state.signature.files[index] = { ...state.signature.files[index], ...action.payload };
    },
    updateOrdered: (state, action: PayloadAction<boolean>) => {
      state.waitForUpdate = true;
      const isOrdered = action.payload;
      state.signature.ordered = isOrdered;
      state.signature.signatories = state.signature.signatories?.map((signatory) => ({
        ...signatory,
        order: isOrdered ? signatory.order : 1
      }));
    },
    updateShouldNotifyAfterSignature: (state, action: PayloadAction<boolean>) => {
      state.waitForUpdate = true;
      state.signature.shouldNotifyAfterSignature = action.payload;
    },
    updateSignatory: (state, action: PayloadAction<NewPartialSignatoryWithId>) => {
      state.waitForUpdate = true;
      state.signature.signatories = state.signature.signatories ?? [];

      const index = state.signature.signatories.findIndex((signatory) => signatory.id === action.payload.id);
      state.signature.signatories[index] = {
        ...state.signature.signatories[index],
        ...action.payload
      };
    },
    waitForSignatureDraftUpdate: (state, action: PayloadAction<boolean>) => {
      state.waitForUpdate = action.payload;
    }
  }
});

export const {
  addFile,
  addSignatory,
  cleanUpSignatureDraft,
  deleteSignatoriesGroup,
  goToNextSignatureDraftStep,
  goToPreviousSignatureDraftStep,
  initSignatureDraft,
  moveFileDown,
  moveFileUp,
  removeFile,
  removeSignatory,
  setDueDate,
  setError,
  setSignatureStep,
  setSubscribers,
  updateEmail,
  updateFile,
  updateOrdered,
  updateShouldNotifyAfterSignature,
  updateSignatory,
  waitForSignatureDraftUpdate
} = signatureDraftSlice.actions;

export interface SignatureDraftState {
  [signatureDraftSlice.name]: SignatureWorkflowDraft;
}

export const selectSignatureDraftFeature = (state: SignatureDraftState) => state[signatureDraftSlice.name];

const getPartialSignatoryByProviderType = ({
  signatory,
  type
}: {
  signatory: NewPartialSignatory;
  type: SignatureProviderType;
}): NewPartialSignatory => {
  switch (type) {
    case SignatureProviderType.YOUSIGN_V2:
    case SignatureProviderType.YOUSIGN_V3:
      return getSignatoryElectronic(signatory);
    case SignatureProviderType.MYNOTARY_PAPER:
      return getSignatoryPaper(signatory);
    case SignatureProviderType.UNCERTIFIED:
      return getSignatoryUncertified(signatory);
  }
};

const getSignatoryElectronic = (signatory: NewPartialSignatory): NewPartialSignatory => {
  return {
    ...signatory,
    sendCode: signatory.sendCode || SendCodeType.SMS,
    signaturePlace: signatory.signaturePlace || SignaturePlace.ONLINE
  };
};

const getSignatoryPaper = (signatory: NewPartialSignatory): NewPartialSignatory => {
  return {
    firstname: signatory.firstname,
    lastname: signatory.lastname,
    mention: signatory.mention,
    signaturePlace: signatory.signaturePlace || SignaturePlace.PHYSICAL
  };
};

const getSignatoryUncertified = (signatory: NewPartialSignatory): NewPartialSignatory => {
  return {
    email: signatory.email,
    firstname: signatory.firstname,
    lastname: signatory.lastname,
    phone: signatory.phone,
    signaturePlace: signatory.signaturePlace || SignaturePlace.ONLINE
  };
};
