import { addHead<PERSON><PERSON><PERSON>, ApiHel<PERSON>, createAxiosInstance } from '@mynotary/frontend/shared/axios-util';
import { environment } from '@mynotary/frontend/shared/environments-util';
import {
  SignatoryDto,
  SignatoryUpdateDto,
  SignatureArchiveDto,
  SignatureDto,
  SignatureLinkDto,
  SignatureReminderDto
} from '@mynotary/crossplatform/api-signatures/openapi';
import {
  GetSignatureArgs,
  UpdateUncertifiedSignatory,
  SignaturesClient,
  GetSignatureArchiveArgs,
  SignatureArchive,
  StartSignatureArgs,
  SignatureLinkArgs,
  SendReminderArgs,
  UpdateSignatoryArgs
} from '@mynotary/frontend/signatures/core';
import { SignatoryV2, SignatureV2 } from '@mynotary/frontend/signatures/core';

const BASE_URL = 'api-signatures/v1';

export class SignaturesClientImpl implements SignaturesClient {
  private signatureApiClient = createAxiosInstance({ baseURL: `${environment.apiV2SignatureUrl}/${BASE_URL}` });

  async getSignature({ signatureId, signatureToken }: GetSignatureArgs): Promise<SignatureV2> {
    const { data } = await this.signatureApiClient.get<SignatureDto>(`/signatures/${signatureId}`, {
      params: { token: signatureToken }
    });
    return this.convertDtoToCore(data);
  }

  async getSignatureLink({ signatoryId, signatureId }: SignatureLinkArgs): Promise<string> {
    const { data } = await this.signatureApiClient.post<SignatureLinkDto>(`/signature-link`, {
      signatoryId,
      signatureId
    });

    return data.signatureLink;
  }

  async syncSignature(signatureId: string): Promise<void> {
    await this.signatureApiClient.post<void>(`/signature-sync`, {
      signatureId
    });
  }

  async cancelSignature(signatureId: string): Promise<void> {
    await this.signatureApiClient.delete<void>(`/signatures/${signatureId}`);
  }

  async startSignature({ signatureId, userId }: StartSignatureArgs): Promise<void> {
    await this.signatureApiClient.post<void>(`/signature-actives`, {
      signatureId,
      userId
    });
  }

  async sendReminder({ signatoryId, signatureId }: SendReminderArgs): Promise<void> {
    await this.signatureApiClient.post<void>(`/signature-reminder`, {
      signatoryId,
      signatureId
    } satisfies SignatureReminderDto);
  }

  async getSignatureArchive({ signatureId, token }: GetSignatureArchiveArgs): Promise<SignatureArchive> {
    const { data } = await this.signatureApiClient.get<SignatureArchiveDto>(`/signature-archives/${signatureId}`, {
      headers: addHeaderToken(token),
      params: { token }
    });
    return { folder: data.folder, sender: data.sender };
  }

  async updateSignatory({
    email,
    firstname,
    lastname,
    phone,
    signatoryId,
    signatureId
  }: UpdateSignatoryArgs): Promise<void> {
    await this.signatureApiClient.put<void>(`/signature-signatory/${signatoryId}`, {
      email,
      firstname,
      lastname,
      phone,
      signatureId: signatureId.toString()
    } satisfies SignatoryUpdateDto);
  }

  async updateUncertifiedSignatory({
    consent,
    signatoryId,
    signatureToken
  }: UpdateUncertifiedSignatory): Promise<void> {
    await ApiHelpers.post<{ email: string; token: string }>(
      `/electronic-signatures/${signatureToken}/signature-uncertified-callback`,
      {
        consent,
        signatoryId
      }
    );
  }

  private convertDtoToCore(dto: SignatureDto): SignatureV2 {
    return {
      creator: dto.creator,
      files: dto.files.filter((file) => file.toSignFileId != null),
      id: dto.id,
      organizationId: parseInt(dto.organizationId),
      signatories: this.convertSignatoryDtoToCore(dto.signatories)
    };
  }

  private convertSignatoryDtoToCore(dtos: SignatoryDto[]): SignatoryV2[] {
    return dtos.map((dto) => ({
      ...dto,
      signatureTime: dto.signatureTime ?? undefined
    }));
  }
}
