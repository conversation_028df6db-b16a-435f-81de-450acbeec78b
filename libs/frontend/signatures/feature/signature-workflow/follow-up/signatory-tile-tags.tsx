import './signatory-tile-tags.scss';
import { formatDate } from '@mynotary/frontend/shared/util';
import { SignaturePlace } from '@mynotary/crossplatform/signatures/api';
import { Tag } from '@mynotary/frontend/shared/ui';
import { Signatory, Signature, SignaturePlaceLabel } from '@mynotary/frontend/signatures/core';
import { fns } from '@mynotary/crossplatform/shared/dates-util';
import { useSelector } from 'react-redux';
import { selectActiveSignatoryCanRemind } from '@mynotary/frontend/signatures/store';

interface SignatoryTileTagsProps {
  signatory: Signatory;
  signature: Signature;
}

export const SignatoryTileTags = ({ signatory, signature }: SignatoryTileTagsProps) => {
  const canRemind = useSelector(
    selectActiveSignatoryCanRemind({ signatoryId: signatory.id, signatureId: signature.id })
  );

  return (
    <div className={'signatory-tile-tag'}>
      {signatory.signatureTime && <div>Date de signature : {formatDate(signatory.signatureTime)}</div>}
      {!signatory.signatureTime && signatory.lastReminderTime && signatory.signaturePlace === SignaturePlace.ONLINE && (
        <div>
          <span>
            Envoi de l'e-mail :{' '}
            {`${formatDate(signatory.lastReminderTime)}${
              canRemind && signatory.lastReminderTime && !fns.isOlderThanOneMinute(signatory.lastReminderTime)
                ? " (relancé il y a moins d'une minute)"
                : ''
            }`}
          </span>
        </div>
      )}
      <div className={'stt-tag-inline'}>
        {signatory.signatureTime && <Tag color={'success'} label={'Signé'} />}
        {!signatory.signatureTime && <Tag color={'info'} label={'Signature en attente'} />}
        {signatory.signaturePlace && <Tag label={SignaturePlaceLabel[signatory.signaturePlace]} />}
      </div>
    </div>
  );
};
