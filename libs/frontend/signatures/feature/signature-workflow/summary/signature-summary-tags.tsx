import { SignatureDraft } from '@mynotary/frontend/signatures/core';
import { SendCodeType, SignaturePlace } from '@mynotary/crossplatform/signatures/api';
import { Tag } from '@mynotary/frontend/shared/ui';
import { pluralize } from '@mynotary/frontend/shared/util';
import { uniq } from 'lodash';

export const SignatureSummaryTags = ({ signature }: { signature: SignatureDraft }) => {
  const remoteEmailSignatoryCount = getRemoteSignatoryCount({ signature, type: SendCodeType.MAIL });
  const remoteSmsSignatoryCount = getRemoteSignatoryCount({ signature, type: SendCodeType.SMS });
  const onPlaceMailSignatoryCount = getOnPlaceSignatoryCount({ signature, type: SendCodeType.MAIL });
  const onPlaceSmsSignatoryCount = getOnPlaceSignatoryCount({ signature, type: SendCodeType.SMS });
  const groupCount = getSignatoriesGroupCount(signature);
  const annexCount = getAnnexCount(signature);

  return (
    <>
      <Tag className='ssp-summary-tile' label={`${annexCount} ${pluralize('annexe', annexCount)}`} />
      {groupCount > 1 && (
        <Tag className='ssp-summary-tile' label={`${groupCount} ${pluralize('groupe', groupCount)}`} />
      )}
      {remoteSmsSignatoryCount > 0 && (
        <Tag
          className='ssp-summary-tile'
          label={`${remoteSmsSignatoryCount} ${pluralize('signataire', remoteSmsSignatoryCount)} à distance + code sms`}
        />
      )}
      {remoteEmailSignatoryCount > 0 && (
        <Tag
          className='ssp-summary-tile'
          label={`${remoteEmailSignatoryCount} ${pluralize('signataire', remoteEmailSignatoryCount)} à distance + code mail`}
        />
      )}
      {onPlaceMailSignatoryCount > 0 && (
        <Tag
          className='ssp-summary-tile'
          label={`${onPlaceMailSignatoryCount} ${pluralize('signataire', onPlaceMailSignatoryCount)} sur place + code mail`}
        />
      )}
      {onPlaceSmsSignatoryCount > 0 && (
        <Tag
          className='ssp-summary-tile'
          label={`${onPlaceSmsSignatoryCount} ${pluralize('signataire', onPlaceSmsSignatoryCount)} sur place + code sms`}
        />
      )}
    </>
  );
};

function getAnnexCount(signature: SignatureDraft) {
  return signature?.files?.length == null ? 0 : signature?.files.length;
}

function getSignatoriesGroupCount(signature: SignatureDraft) {
  return uniq(signature.signatories?.map((signatory) => signatory.order)).length;
}

function getRemoteSignatoryCount({ signature, type }: { signature: SignatureDraft; type: SendCodeType }) {
  if (signature.providerType === 'MYNOTARY_PAPER') {
    return 0;
  }
  return (
    signature?.signatories?.filter(
      (signatory) => signatory.signaturePlace === SignaturePlace.ONLINE && signatory.sendCode === type
    ).length ?? 0
  );
}

function getOnPlaceSignatoryCount({ signature, type }: { signature: SignatureDraft; type: SendCodeType }) {
  if (signature.providerType === 'MYNOTARY_PAPER') {
    return 0;
  }
  return (
    signature?.signatories?.filter(
      (signatory) => signatory.signaturePlace === SignaturePlace.PHYSICAL && signatory.sendCode === type
    ).length ?? 0
  );
}
