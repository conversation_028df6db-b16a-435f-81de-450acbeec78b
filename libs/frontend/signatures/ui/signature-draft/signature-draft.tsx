import './signature-draft.scss';
import { MnProps, classNames } from '@mynotary/frontend/shared/util';

import { ReactNode } from 'react';

interface SignatureDraftPageContainerProps extends MnProps {
  children: ReactNode;
}

export const SignatureDraftPage = ({ className, ...props }: SignatureDraftPageContainerProps) => {
  return <div className={classNames('signature-draft-page', className)} {...props} />;
};

export const SignatureDraftPageContainer = ({ className, ...props }: SignatureDraftPageContainerProps) => {
  return <div className={classNames('signature-draft-page-container', className)} {...props} />;
};
