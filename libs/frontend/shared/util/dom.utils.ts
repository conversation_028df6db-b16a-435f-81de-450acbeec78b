import { isNil } from 'lodash';
import { isIOS } from './is-ios';

export const getTopDistanceBetweenElement = (a: HTMLElement, b: HTMLElement): number => {
  return Math.abs(a.getBoundingClientRect().top - b.getBoundingClientRect().top + b.scrollTop);
};

let previousScrollPosition = 0;

export const setBodyOverflow = (isOverflowing: boolean, scrollPosition?: number): void => {
  const body: HTMLElement = document.body;
  if (!isNil(scrollPosition)) {
    previousScrollPosition = scrollPosition;
  }

  if (!isOverflowing) {
    if (isIOS()) {
      previousScrollPosition = window.pageYOffset;
      body.style.position = 'fixed';
    }
    body.classList.add('no-scroll');
  } else {
    body.classList.remove('no-scroll');
    if (isIOS()) {
      body.style.position = 'relative';
      window.scrollTo(0, previousScrollPosition);
    }
  }
};
