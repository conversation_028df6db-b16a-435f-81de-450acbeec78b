import {
  AddressFormQuestion,
  Answer,
  ColorFormQuestion,
  CountryF<PERSON>Question,
  DateF<PERSON>Question,
  EmailFormQuestion,
  FormNode,
  FormQuestion,
  NumberFormQuestion,
  OperationLabelQuestion,
  PhoneFormQuestion,
  Pick<PERSON>istFormQuestion,
  PreEtatDateFormQuestion,
  PriceFormQuestion,
  RegisterFormQuestion,
  RegisterNumberFormQuestion,
  SelectBinaryFormQuestion,
  SelectFormQuestion,
  SelectPictureFormQuestion,
  SelectSpfFormQuestion,
  TextareaFormQuestion,
  TextFormQuestion,
  UploadFormAnswer,
  YearFormQuestion
} from '@mynotary/crossplatform/shared/forms-util';

export function isAddressQuestion(node: FormNode): node is AddressFormQuestion {
  return (node as FormQuestion).type === 'ADDRESS';
}

export function isCountryQuestion(node: FormNode): node is CountryFormQuestion {
  return (node as FormQuestion).type === 'COUNTRY';
}

export function isDateQuestion(node: FormNode): node is DateFormQuestion {
  return (node as FormQuestion).type === 'DATE';
}

export function isEmailQuestion(node: FormNode): node is EmailFormQuestion {
  return (node as FormQuestion).type === 'EMAIL';
}

export function isSelectEmailQuestion(node: FormNode): node is EmailFormQuestion {
  return (node as FormQuestion).type === 'SELECT-EMAIL';
}

export function isNumberQuestion(node: FormNode): node is NumberFormQuestion {
  return (node as FormQuestion).type === 'NUMBER';
}

export function isYearQuestion(node: FormNode): node is YearFormQuestion {
  return (node as FormQuestion).type === 'YEAR';
}

export function isPhoneQuestion(node: FormNode): node is PhoneFormQuestion {
  return (node as FormQuestion).type === 'PHONE';
}

export function isPriceQuestion(node: FormNode): node is PriceFormQuestion {
  return (node as FormQuestion).type === 'PRICE';
}

export function isSelectQuestion(node: FormNode): node is SelectFormQuestion {
  return (node as FormQuestion).type === 'SELECT';
}

export function isPickListQuestion(node: FormNode): node is PickListFormQuestion {
  return (node as FormQuestion).type === 'PICK_LIST';
}

export function isSelectPictureQuestion(node: FormNode): node is SelectPictureFormQuestion {
  return (node as FormQuestion).type === 'SELECT-PICTURES';
}

export function isTextQuestion(node: FormNode): node is TextFormQuestion {
  return (node as FormQuestion).type === 'TEXT';
}

export function isRegisterQuestion(node: FormNode): node is RegisterFormQuestion {
  return (node as FormQuestion).type === 'TEXT' && !!(node as RegisterFormQuestion).register;
}

export function isPasswordQuestion(node: FormNode): node is TextFormQuestion {
  return (node as FormQuestion).type === 'PASSWORD';
}

export function isTextareaQuestion(node: FormNode): node is TextareaFormQuestion {
  return (node as FormQuestion).type === 'TEXTAREA';
}

export function isColorQuestion(node: FormNode): node is ColorFormQuestion {
  return (node as FormQuestion).type === 'COLOR';
}

export function isOperationLabelQuestion(node: FormNode): node is OperationLabelQuestion {
  return (node as FormQuestion).type === 'OPERATION_LABEL';
}

export function isRegisterNumberQuestion(node: FormNode): node is RegisterNumberFormQuestion {
  return (node as FormQuestion).type === 'REGISTER_NUMBER';
}

export function isRepeatQuestion(node: FormNode): node is FormQuestion {
  return !!(node as FormQuestion).parentRepeat;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isFormAnswer(value: any, question: FormQuestion): value is Answer {
  return !!question;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isUploadFormAnswer(value: any, question: AddressFormQuestion): value is UploadFormAnswer {
  return !!question;
}

export function isSelectBinaryQuestion(node: FormNode): node is SelectBinaryFormQuestion {
  return (node as FormQuestion).type === 'SELECT-BINARY';
}

export function isGelAvoirQuestion(node: FormNode): node is FormQuestion {
  return (node as FormQuestion).type === 'GEL_AVOIR';
}

export function isPreEtatDateQuestion(node: FormNode): node is PreEtatDateFormQuestion {
  return (node as FormQuestion).type === 'UPLOAD_PRE_ETAT_DATE';
}

export function isSelectSpfQuestion(node: FormNode): node is SelectSpfFormQuestion {
  return (node as FormQuestion).type === 'SELECT-SPF';
}
