@use 'style/variables/colors' as *;
@use 'style/mixins' as *;

.mn-fd-button {
  @include medium-font;

  cursor: pointer;

  display: flex;
  align-items: center;

  width: 100%;
  height: 32px;
  padding: 8px 24px;
  border: 1px solid $gray300;
  border-radius: 5px;

  color: $black;

  &.disabled {
    cursor: initial;

    .mn-fd-label {
      color: $gray500;
    }
  }

  &:hover {
    background-color: $gray150;
  }

  .mn-fd-image {
    margin-left: 8px;
    stroke-width: 1px;
  }

  .mn-fd-container-image {
    display: flex;
    margin-left: auto;
  }

  .mn-fd-label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}