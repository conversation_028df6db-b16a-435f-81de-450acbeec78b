import React, { ReactElement } from 'react';
import './clouds.scss';

const Clouds = (): ReactElement => {
  return (
    <div className='clouds'>
      <img className='cloud x1' src='assets/images/home/<USER>' />
      <img className='cloud x2' src='assets/images/home/<USER>' />
      <img className='cloud x3' src='assets/images/home/<USER>' />
      <img className='cloud x4' src='assets/images/home/<USER>' />
      <img className='cloud x5' src='assets/images/home/<USER>' />
      <img className='cloud x6' src='assets/images/home/<USER>' />
      <img className='cloud x7' src='assets/images/home/<USER>' />
      <img className='cloud x8' src='assets/images/home/<USER>' />
      <img className='cloud x9' src='assets/images/home/<USER>' />
      <img className='cloud x10' src='assets/images/home/<USER>' />
      <img className='cloud x11' src='assets/images/home/<USER>' />
      <img className='cloud x12' src='assets/images/home/<USER>' />
    </div>
  );
};

export { Clouds };
