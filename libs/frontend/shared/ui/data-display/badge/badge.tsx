import React from 'react';
import './badge.scss';
import { MnProps, classNames } from '@mynotary/frontend/shared/util';

type BadgeColor = 'primary' | 'error';

interface BadgeProps extends MnProps {
  badgeContent?: string;
  children: React.ReactNode;
  color: BadgeColor;
  visibility?: boolean;
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ badgeContent, children, className, color = 'primary', visibility = true }, ref) => {
    return (
      <div className={classNames(`mn-badge-root`, className)} ref={ref}>
        {children}
        {visibility && <span className={`badge badge--${color}`}>{badgeContent}</span>}
      </div>
    );
  }
);

export { Badge };
