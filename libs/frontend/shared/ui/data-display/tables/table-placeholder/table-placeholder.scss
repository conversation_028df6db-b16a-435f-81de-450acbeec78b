@use 'style/variables/colors' as *;
@use 'style/mixins/index' as *;

.empty-result-row {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .err-cell {
    display: flex;
    flex-direction: column;
    align-items: center;

    margin-top: 0;
    padding: 40px 0;

    text-align: center;

    .err-cell-label {
      @include h4-font($semi-bold);

      margin-top: 32px;
      color: $black;
    }

    .err-description {
      @include medium-font($regular);

      margin-top: 8px;
    }
  }

  .err-button {
    width: fit-content;
    margin: 32px auto 0;
    color: $white;
  }
}
