import { DraggableNode, useDndContext } from '@dnd-kit/core';
import { BoundingRect } from '@mynotary/frontend/shared/util';
import { isInsideContainerRect } from '@mynotary/frontend/shared/util';
import { ReactNode } from 'react';

interface DndMultipleSelectionProps {
  children: (props: {
    handleFinishSelection: (containerBoundingRect: BoundingRect) => void;
    handleSelectionMove: (containerBoundingRect: BoundingRect) => void;
  }) => ReactNode;
  onSelectedItemsChange: (items: string[]) => void;
  onSelection: (items: string[]) => void;
}

export const DndMultipleSelection = ({ children, onSelectedItemsChange, onSelection }: DndMultipleSelectionProps) => {
  const context = useDndContext();

  const isChildElementInsideParentRect = ({
    containerBoundingRect,
    draggableNode
  }: {
    containerBoundingRect: BoundingRect;
    draggableNode?: DraggableNode;
  }) => {
    if (draggableNode?.node?.current) {
      const { bottom, left, right, top } = draggableNode.node.current.getBoundingClientRect();
      const draggableBoundingRect = { bottom, left, right, top };

      return isInsideContainerRect({
        childRect: draggableBoundingRect,
        containerRect: containerBoundingRect
      });
    }

    return false;
  };

  const handleFinishSelection = (containerBoundingRect: BoundingRect) => {
    const activeItems: string[] = [];

    context.draggableNodes.forEach((draggableNode) => {
      if (
        draggableNode &&
        isChildElementInsideParentRect({
          containerBoundingRect,
          draggableNode
        })
      ) {
        activeItems.push(draggableNode.id.toString());
      }
    });

    onSelection(activeItems);
  };

  const handleSelectionMove = (containerBoundingRect: BoundingRect) => {
    const selectedItems: string[] = [];
    context.draggableNodes.forEach((draggableNode) => {
      if (
        draggableNode &&
        isChildElementInsideParentRect({
          containerBoundingRect,
          draggableNode
        })
      ) {
        selectedItems.push(draggableNode.id.toString());
      }
    });
    onSelectedItemsChange(selectedItems);
  };

  return <>{children({ handleFinishSelection, handleSelectionMove })}</>;
};
