import './footer-action-btn-popin.scss';
import React, { ReactElement } from 'react';
import { MnProps } from '@mynotary/frontend/shared/util';
import { MnButton, SheetFooter } from '../../../ui';

interface FooterActionBtnPopinProps extends MnProps {
  onClick?: () => void;
  types?: string[];
}

const FooterActionBtnPopin = ({ onClick, types }: FooterActionBtnPopinProps): ReactElement => {
  const authorizedTypesLabel = (types?: string[]): { btnLabel: string; message: string } => {
    if (!types) {
      return { btnLabel: 'Créer un nouveau +', message: '' };
    }

    if (types.includes('COPROPRIETE')) {
      return {
        btnLabel: 'Créer une copropriété +',
        message: `La copropriété recherchée n'a pas été trouvée ?`
      };
    }

    if (types.includes('PERSONNE')) {
      return {
        btnLabel: 'Créer une personne +',
        message: `La personne recherchée n'a pas été trouvé ?`
      };
    }

    if (types.includes('BIEN')) {
      if (types.includes('TERRAIN_CONSTRUCTIBLE') || types.includes('TERRAIN_NON_CONSTRUCTIBLE')) {
        return {
          btnLabel: 'Créer un terrain +',
          message: `Le terrain recherché n'a pas été trouvé ?`
        };
      }
      return {
        btnLabel: 'Créer un bien +',
        message: `Le bien recherché n'a pas été trouvé ?`
      };
    }

    if (types.includes('LOT')) {
      return {
        btnLabel: 'Créer un lot +',
        message: `Le lot recherché n'a pas été trouvé ?`
      };
    }

    if (types.includes('LOTISSEMENT')) {
      return {
        btnLabel: 'Créer un lotissement +',
        message: `Le lotissement recherché n'a pas été trouvé ?`
      };
    }

    if (types.includes('STRUCTURE')) {
      if (types.includes('ENSEMBLE_IMMOBILIER')) {
        return {
          btnLabel: 'Créer une monopropriété +',
          message: `La monopropriété recherchée n'a pas été trouvée ?`
        };
      } else if (types.includes('VOLUME')) {
        return {
          btnLabel: 'Créer un volume +',
          message: `Le volume recherché n'a pas été trouvé ?`
        };
      }
    }

    return { btnLabel: 'Créer un nouveau +', message: '' };
  };

  const { btnLabel, message } = authorizedTypesLabel(types);

  return (
    <SheetFooter className='footer-action-btn-popin'>
      {message && <span className='fabp-message'>{message}</span>}
      <MnButton label={btnLabel} onClick={onClick} variant='primary' />
    </SheetFooter>
  );
};
export { FooterActionBtnPopin };
