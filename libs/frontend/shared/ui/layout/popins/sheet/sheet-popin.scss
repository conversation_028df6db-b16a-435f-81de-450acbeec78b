@use 'style/variables/index' as *;
@use 'style/mixins/index' as *;

.sheet-popin {
  @include responsive($small-desktop-max) {
    min-width: initial;
  }

  position: absolute;
  z-index: $z-index-popin;
  top: 0;

  display: flex;
  flex-direction: column;

  width: 100%;
  min-width: 400px;
  height: 100%;

  background-color: $white;

  transition: transform .3s ease-in-out;

  &.cancel-animation {
    transition: none !important;
    animation: none !important;
  }

  &.medium:not(.bottom) {
    @include responsive($small-desktop-max) {
      width: 100%;
      max-width: 100%;
    }

    width: 55%;
  }

  &.large:not(.bottom) {
    @include responsive($small-desktop-max) {
      width: 100%;
      max-width: 100%;
    }

    width: 75%;
  }

  &.left {
    @include box-shadow-right;

    @include responsive($small-desktop-max) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    left: 0;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    animation: slide-left .5s;
  }

  &.right {
    @include box-shadow-left;

    @include responsive($small-desktop-max) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    right: 0;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    animation: slide-right .5s;
  }

  &.bottom {
    @include box-shadow-top;

    top: initial;
    bottom: 0;

    height:fit-content;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;

    animation: slide-bottom .5s;
  }

  &.bottom.fade-out {
    transform: translateY(120%);
  }

  &.right.fade-out {
    /*
    * Increase the distance to the right to avoid weird visual effect with box-shadow
    */
    transform: translateX(120%);
  }

  &.left.fade-out {
    /*
    * Increase the distance to the left to avoid weird visual effect with box-shadow
    */
    transform: translateX(-120%);
  }
}

.sheet-overlay {
  cursor: pointer;

  position: absolute;
  z-index: $z-index-popin;

  width: 100%;
  height: 100vh;

  background: transparent;
}

@keyframes slide-right {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0%);
  }
}

@keyframes slide-bottom {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0%);
  }
}

@keyframes slide-left {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0%);
  }
}
