import './sheet-popin.scss';
import { ReactNode, useEffect, useState } from 'react';
import { MnPortal } from '../../../utils/portal/portal';
import { PopinHelpers } from '../popinHelpers';
import classNames from 'classnames';
import { MnProps, setBodyOverflow } from '@mynotary/frontend/shared/util';

interface SheetPopinProps extends MnProps {
  cancelAnimation?: boolean;
  children: ReactNode;
  direction?: 'left' | 'right' | 'bottom';
  isOpened: boolean;
  onClose?: () => void;
  size?: 'medium' | 'large';
}

export const SheetPopin = ({
  cancelAnimation,
  children,
  className,
  direction = 'right',
  isOpened,
  onClose,
  size = 'medium'
}: SheetPopinProps) => {
  const [shouldFadeOut, setShouldFadeOut] = useState(false);
  const [render, setRender] = useState(false);

  useEffect(() => {
    if (render) {
      setBodyOverflow(false);
    }
  }, [render]);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (isOpened) {
      setRender(true);
    } else {
      setShouldFadeOut(true);
      timer = setTimeout(() => {
        setRender(false);
        /**
         * Should be equal to the transition duration in the css
         */
      }, 500);
    }

    return () => {
      clearTimeout(timer);
      setShouldFadeOut(false);
    };
  }, [isOpened]);

  if (!isOpened) {
    return null;
  }

  return (
    <MnPortal targetDomId={PopinHelpers.rootPopinId}>
      {onClose && isOpened && (
        <div
          className={'sheet-overlay'}
          onClick={(e) => {
            e?.stopPropagation();
            onClose();
          }}
        />
      )}
      <div
        className={classNames(
          'sheet-popin',
          direction,
          size,
          { 'cancel-animation': cancelAnimation, 'fade-out': shouldFadeOut },
          className
        )}
      >
        {children}
      </div>
    </MnPortal>
  );
};
