import './sheet-header.scss';
import { ReactNode } from 'react';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';
import { MnButtonController } from '../../../buttons/buttonController/buttonController';

interface SheetHeaderProps extends MnProps {
  children: ReactNode;
  onClose?: () => void;
  onReturn?: () => void;
}

export const SheetHeader = ({ children, className, onClose, onReturn, testId }: SheetHeaderProps) => {
  return (
    <div className={classNames('sheet-header', className)} data-testid={testId}>
      {onReturn != null && (
        <MnButtonController
          label='Retour'
          leftIcon='/assets/images/pictos/icon/arrow-left-light.svg'
          onClick={onReturn}
          size='small'
          underlined={false}
        />
      )}
      {children}
      {onClose != null && (
        <MnButtonController
          className='sheet-header-button'
          iconVariant='red300'
          label='Fermer'
          onClick={onClose}
          rightIcon='/assets/images/pictos/icon/cross-light.svg'
          size='small'
          underlined={false}
        />
      )}
    </div>
  );
};
