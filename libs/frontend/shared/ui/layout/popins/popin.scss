@use 'style/mixins/index' as *;
@use 'style/variables/index' as *;

$opacity-transition: opacity .2s ease-in-out;

.popin-container {
  position: absolute;
  z-index: $z-index-popin;
  top: 0;
  left: 0;

  width: 100%;
  height: 100%;

  visibility: hidden;
  opacity: 0;

  transition: $opacity-transition;

  &.isOpened {
    visibility: visible;
    opacity: 1;

    .popin-blur {
      visibility: visible;
      opacity: .5;
    }

    .popin {
      visibility: visible;
      opacity: 1;
    }
  }

  .popin-blur {
    position: fixed;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;

    visibility: hidden;
    opacity: .5;
    background-color: $black;

    transition: $opacity-transition;

    &.closable {
      cursor: pointer;
    }
  }

  .popin {
    position: fixed;
    z-index: calc(#{$z-index-frame} + 1);

    overflow: hidden;

    border-radius: 8px;

    visibility: hidden;
    opacity: 0;
    background-color: $white;
    box-shadow: 0 7px 8px -4px rgba(0, 0, 0, 20%), 0 13px 19px 2px rgba(0, 0, 0, 14%),
    0 5px 24px 4px rgba(0, 0, 0, 12%);

    transition: $opacity-transition;
  }

  .popin-centered {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .popin-content {
    @include responsive($mobile-max) {
      min-width: 100%;
    }

    width: 100%;
    height: 100%;
  }

  .popin-close {
    position: absolute;
    z-index: $z-index-frame;
    top: 16px;
    right: 16px;

    .mn-svg {
      stroke-width: 1;
    }
  }
}

.full-size-popin {
  .popin-content {
    width: 100vw;
    height: 100vh;
  }

  .popin {
    border-radius: 0;
  }

  .popin-close {
    z-index: $z-index-left-panel-action-bar + 2;
  }
}