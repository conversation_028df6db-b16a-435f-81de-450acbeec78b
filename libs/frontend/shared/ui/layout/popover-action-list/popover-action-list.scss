@use 'style/variables/colors' as *;
@use 'style/mixins/index' as *;

.popover-action-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 8px;

  &:not(:has(.popover-group)) {
    gap: 16px;
  }

  .popover-action-list-category {
    padding: 0 8px;
    font-size: 14px;
    font-weight: $bold;
    color: $black;
  }

  &.hasLabel {
    margin-bottom: 8px;
  }

  .popover-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .popover-group:empty {
    display: none;
  }

  .popover-group:not(:empty) {
    border: none;

    & ~ .popover-group:not(:empty) {
      padding: 16px 0 0;
      border-top: 1px solid $gray200;
    }

    & ~ .popover-group:last-child {
      padding-bottom: 0;
    }

    & ~ .popover-group:first-child {
      padding-bottom: 0;
    }
  }

  .popover-group:first-child {
    padding-top: 0;
  }

  .popover-group:last-child {
    padding-bottom: 0;
  }
}
