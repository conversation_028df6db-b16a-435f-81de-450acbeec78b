@use 'style/variables/colors' as *;
@use 'style/mixins/index' as *;

.mn-button {
  cursor: pointer;

  position: relative;

  display: inline-flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;

  padding: 0 24px;

  transition: box-shadow .3s ease, transform .3s ease, opacity .3s ease, color .3s ease, background-color .3s ease,
  border-color .3s ease;

  &.small {
    height: 32px;
  }

  &.medium {
    height: 40px;
  }

  &.squircle {
    border-radius: 4px;
  }

  &.rounded {
    border-radius: 20px;
  }

  &.capitalize > span {
    text-transform: lowercase;

    &::first-letter {
      text-transform: uppercase;
    }
  }

  &:visited,
  &:focus {
    text-decoration: none;
  }

  &.icon.left span {
    margin-right: 8px;
  }

  &.icon.right span {
    margin-left: 8px;
  }

  .mnb-icon {
    display: inline-block;

    width: 16px;
    height: 16px;

    vertical-align: middle;

    stroke-width: 2px;
  }

  &.mn-button-primary {
    @include medium-font($medium);

    border: 1px solid var(--primary);
    color: $white;
    background-color: var(--primary);

    &.disabled {
      border-color: $gray150;
      background-color: $gray150;
    }

    @media (hover) {
      &:not(.disabled):hover {
        border: 1px solid var(--primary-dark);
        color: $white;
        background-color: var(--primary-dark);
      }
    }
  }

  &.mn-button-secondary {
    @include medium-font($medium);

    border: 1px solid var(--primary-dark);
    color: var(--primary-dark);
    background-color: $white;

    &.disabled {
      border-color: $gray300;
    }

    @media (hover) {
      &:not(.disabled):hover {
        border-color: var(--primary-dark);
        color: $white;
        background-color: var(--primary-dark);

        & > .mnb-icon * {
          stroke: $white !important;
        }
      }
    }
  }

  &.mn-button-tertiary {
    @include medium-font($medium);

    border: 1px solid $gray100;
    color: $gray700;
    background-color: $gray100;

    &.disabled {
      border-color: $gray150;
      background-color: $gray150;
    }

    @media (hover) {
      &:not(.disabled):hover {
        box-shadow: 0 10px 20px rgba($color: $black, $alpha: 4%), 0 2px 6px rgba($color: $black, $alpha: 4%),
        0 0 1px rgba($color: $black, $alpha: 4%);
      }
    }
  }

  &.mn-button-secondary-disabled {
    @include medium-font($medium);

    border: 1px solid $gray300;
    color: $gray300;
    background-color: $white;


    .mnb-icon > * {
      stroke: $gray300 !important;
    }
  }

  &.mn-button-secondary-green {
    @include medium-font($medium);

    border: 1px solid $green300;
    color: $green300;
    background-color: $white;

    &.disabled {
      border-color: $gray300;
    }

    @media (hover) {
      &:not(.disabled):hover {
        border-color: $green400;
        color: $white;
        background-color: $green400;
      }
    }
  }

  &.mn-button-secondary-red {
    @include medium-font($medium);

    border: 1px solid $red300;
    color: $red300;
    background-color: $white;

    &.disabled {
      border-color: $gray300;
    }

    @media (hover) {
      &:not(.disabled):hover {
        border-color: $red400;
        color: $white;
        background-color: $red400;
      }
    }
  }

  &.mn-button-borderless-primary {
    border: none;
    font-weight: $medium;
    color: var(--primary);
    background-color: transparent;

    .mn-svg {
      &.mnb-icon > * {
        stroke: var(--primary) !important;
      }
    }

    @media (hover) {
      &:not(.disabled):hover {
        color: var(--primary-dark);

        .mn-svg {
          &.mnb-icon > * {
            stroke: var(--primary-dark) !important;
          }
        }
      }
    }
  }

  &.disabled {
    cursor: default;
    color: $gray300;

    .mnb-icon > * {
      stroke: $gray300 !important;
    }
  }

  &:not(.label).icon {
    padding: 0;

    &.small {
      width: 32px;
      height: 32px;
    }

    &.small > .mnb-icon {
      width: 16px;
      height: 16px;
    }

    &.medium {
      width: 40px;
      height: 40px;
    }

    &.medium > .mnb-icon {
      width: 24px;
      height: 24px;
    }

    .mnb-icon {
      margin-left: 0;
    }
  }
}

.mn-button-mobile {
  @include responsive($tablet-min) {
    display: none !important;
  }
}

.mn-button-desktop {
  @include responsive($mobile-max) {
    display: none !important;

    .mnb-icon {
      margin-left: 8px;
    }
  }
}
