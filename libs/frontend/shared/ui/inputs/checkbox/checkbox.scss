@use 'style/variables/colors' as *;
@use 'style/mixins/index' as *;

.mn-checkbox {
  cursor: pointer;
  display: inline-flex;
  flex-direction: row;
  align-items: center;

  &.disabled {
    cursor: initial;
  }

  &.no-label .mn-checkbox-input {
    margin-right: 0;
  }

  .mn-checkbox-label {
    @include medium-font;

    user-select: none;

    display: inline-block;

    max-width: 300px;
    margin-right: 5px;

    color: $gray700;
    word-break: break-word;

    transition: color .2s;
  }

  .mn-checkbox-input {
    @include small-font;

    position: relative;

    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;

    margin-right: 8px;
    border: 1px solid $gray500;

    text-align: center;

    background-color: $white;

    transition: background-color .3s, color .3s, border-color .3s;

    &.squircle {
      border-color: $gray500;
      border-radius: 3px;
    }

    &.radio {
      border-radius: 50%;
    }

    &.small {
      width: 12px;
      height: 12px;
    }

    &.medium {
      width: 16px;
      height: 16px;
    }

    &.large {
      width: 24px;
      height: 24px;
    }

    &.disabled {
      border-color: $gray300;
      background-color: $gray150;
    }

    &:not(.disabled):hover {
      border-color: var(--primary-dark);
      background-color: var(--primary);
    }

    &.isSelected {
      border-color: var(--primary-dark);
      background-color: var(--primary);
    }

    &:not(.disabled).isSelected:hover {
      &:hover {
        border-color: var(--primary);
        background-color: var(--primary-50);
      }
    }

    .mn-checkbox-input-check {
      stroke-width: 2px;

      svg {
        width: 8px;
        height: 8px;
      }
    }
  }
}
