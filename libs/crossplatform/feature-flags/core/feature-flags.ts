export enum FeatureWhiteListed {
  ERP = 'ERP',
  SIGNATURE_DEFAULT_SIGNATORY = 'SIGNATURE_DEFAULT_SIGNATORY'
}

/* eslint-disable unused-imports/no-unused-vars */

const isMynotaryDev = (email?: string) => {
  /* Disable testing account used in e2e testing  */
  return email?.match(/(samuel\.bergerot|sebastien|vincent|valentin\.py)@mynotary.fr$/) != null;
};

export const isMynotary = (email?: string) => {
  return email?.match(/@mynotary.fr$/) != null;
};

const isGiboire = (email?: string) => email?.match(/@giboire.com$/) != null;
const isBvi = (email?: string) => email?.match(/@beauxvillages.com$/) != null;
const isPp = (email?: string) => email?.match(/@proprietes-privees.com$/) != null;
const isKw = (email?: string) => email?.match(/kwfrance.com$/) != null;
const isUdi = (email?: string) =>
  email?.match(/@udi-immo.com$/) != null ||
  email?.match(/@udi-gueret.com$/) != null ||
  email?.match(/@udi-montlucon.com$/) != null;
const isLeforestier = (email?: string) => email?.match(/@leforestier.immo$/) != null;
const isOrpi = (email?: string) => email?.match(/@orpi.com$/) != null;
const isAbriculteurs = (email?: string) => email?.match(/@abriculteurs.com$/) != null;
const isValloire = (email?: string) => email?.match(/@valloire-habitat.com$/) != null;
const isFolliot = (email?: string) => email?.match(/@cabinetfolliot.com$/) != null;

const hasDuplicateOperationAndContract = (email?: string) => {
  return (
    email?.match(/@magestionlocative.fr$/) != null ||
    email?.match(/@ajp-immobilier.com$/) != null ||
    email?.match(/@immobilier-surmesure.com$/) != null ||
    email?.match(/@3s-immo.com$/) != null ||
    email?.match(/@eraimmo.fr$/) != null ||
    email?.match(/@agencecentrale.eu$/) != null ||
    email?.match(/@john-taylor.com$/) != null ||
    email?.match(/@adhoc-immobilier.fr$/) != null ||
    email?.match(/@finistereimmobilier.com$/) != null ||
    isMynotary(email) ||
    isOrpi(email)
  );
};

const isSmallPoolOfClient = (email?: string) =>
  isMynotary(email) || isLeforestier(email) || isAbriculteurs(email) || isUdi(email);

const isMediumPoolOfClient = (email?: string) =>
  isSmallPoolOfClient(email) || isFolliot(email) || isBvi(email) || isGiboire(email);

const isLargePoolOfClient = (email?: string) => isMediumPoolOfClient(email) || isPp(email) || isKw(email);

const isGenerallyAvailable = () => true;

const isCdc = (email?: string) => email?.match(/@cdc-habitat.fr$/) != null;

const hasEmails = (email?: string) => true;

/* eslint-enable unused-imports/no-unused-vars */

const whitelist: Record<FeatureWhiteListed, (email?: string) => boolean> = {
  ERP: (email) => isMynotaryDev(email),
  SIGNATURE_DEFAULT_SIGNATORY: isGenerallyAvailable
};

export const isWhitelisted = (feature: FeatureWhiteListed, email?: string): boolean => {
  return whitelist[feature](email);
};
