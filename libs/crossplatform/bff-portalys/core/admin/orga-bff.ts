import { MnAddress } from '@mynotary/crossplatform/shared/util';

export type OrganizationBffHeader = {
  city: string;
  crpcen: string;
  id: string;
  name: string;
};

export type PlOrganizationBff = {
  address: MnAddress;
  adminRoleId?: string;
  antsRecoveryDelay?: number;
  creatorUserId?: string;
  crpcen: string;
  email?: string;
  fax?: string;
  iban?: {
    bban: string;
    bic: string;
    cle: string;
    domiciliation: string;
    guichet?: string;
    pays: string;
    titulaire?: string;
  };
  id: string;
  interopAppId?: number;
  interopVersion?: string;
  name: string;
  notaryOfficeType: NotaryOfficeType;
  parentOrganizationId?: string;
  phone?: string;
};

enum NotaryOfficeType {
  NOT = 'NOT',
  SCP = 'SCP',
  SEL = 'SEL',
  SELARL = 'SELARL'
}
