/**
 * API Teleactes
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { DemandeCopieDocumentReferenceType2Dto } from './demande-copie-document-reference-type2-dto';
import { DemandeCopieDocumentReferenceType1Dto } from './demande-copie-document-reference-type1-dto';
import { VirementDto } from './virement-dto';


export interface DemandeCopieDocumentDto { 
    anf?: boolean;
    date?: string;
    id?: string;
    idFluxTeleactes?: string;
    label?: string;
    lastModifiedTime?: string;
    operationId?: string;
    referenceType1?: DemandeCopieDocumentReferenceType1Dto;
    referenceType2?: DemandeCopieDocumentReferenceType2Dto;
    /**
     * code SAGES du service ayant publié le document (7 caractères sans espace)
     */
    sagesDepot?: string;
    /**
     * code SAGES du service destinataire de la demande
     */
    sagesEnvoi?: string;
    status?: DemandeCopieDocumentDtoStatusEnum;
    type?: DemandeCopieDocumentDtoTypeEnum;
    /**
     * creator/updater of the Demande
     */
    userId: string;
    virement?: VirementDto;
}
export enum DemandeCopieDocumentDtoStatusEnum {
    ACCEPTED = 'ACCEPTED',
    DRAFT = 'DRAFT',
    PAID = 'PAID',
    PAYMENT_REFUSED = 'PAYMENT_REFUSED',
    RECEIVED = 'RECEIVED',
    REFUSED = 'REFUSED',
    REJECTED = 'REJECTED',
    SENT = 'SENT',
    SIGNED = 'SIGNED',
    TO_ANF = 'TO_ANF',
    TO_PAY = 'TO_PAY',
    TO_SEND = 'TO_SEND',
    TO_SIGN = 'TO_SIGN'
};
export enum DemandeCopieDocumentDtoTypeEnum {
    INSCRIPTION = 'INSCRIPTION',
    PUBLICATION_AUTRE = 'PUBLICATION_AUTRE',
    PUBLICATION_EDD = 'PUBLICATION_EDD',
    SAISIE = 'SAISIE'
};



