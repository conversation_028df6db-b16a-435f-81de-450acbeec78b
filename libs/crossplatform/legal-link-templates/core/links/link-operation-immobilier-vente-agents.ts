// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationImmobilierVenteAgents: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Agence Immobilière / Réseau',
      labelPlural: 'Agence immobiliere',
      labelWithArticle: 'une agence immobilière',
      branches: {
        AGENT_IMMOBILIER: {
          label: 'Agence immobilière',
          labelWithArticle: 'une agence immobilière'
        }
      }
    },
    branches: {
      AGENT_IMMOBILIER: {
        type: 'AGENT_IMMOBILIER',
        reverseType: 'AGENT_IMMOBILIER',
        to: {
          type: 'RECORD',
          specificTypes: [['PERSONNE', 'MORALE', 'AGENT_IMMOBILIER']]
        },
        constraints: {
          min: 0
        },
        links: [],
        linkMatches: [['REPRESENTATION', '*']]
      }
    }
  },
  id: 'LINK__OPERATION__IMMOBILIER__VENTE__AGENTS',
  label: 'Agents',
  mynotaryTemplate: false,
  originTemplate: 'LINK__OPERATION__IMMOBILIER__VENTE__AGENTS',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENTS'],
  type: 'LINK'
};
