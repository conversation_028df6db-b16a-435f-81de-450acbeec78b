// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationAquitanisProgrammeFicheVenteProgramme: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Conditions de la vente pour tester',
      labelPlural: 'Conditions de la vente',
      labelWithArticle: 'les Conditions de la Vente',
      branches: {
        VENTE: {
          label: 'Conditions de la vente pour tester',
          labelWithArticle: 'une condition de la vente'
        },
        RESERVATION: {
          label: 'Conditions de la réservation pour tester',
          labelWithArticle: 'une condition de la réservation'
        }
      }
    },
    branches: {
      FICHE_VENTE: {
        type: 'FICHE_VENTE',
        reverseType: 'FICHE_VENTE',
        to: {
          type: 'RECORD',
          specificTypes: [['OPERATION', 'IMMOBILIER', 'VENTE_SOCIAL', 'VENTE']]
        },
        constraints: {
          min: 1,
          max: 1
        },
        creation: {
          autoCreate: true,
          autoCreateOnly: true
        }
      },
      RESERVATION: {
        type: 'RESERVATION',
        reverseType: 'RESERVATION',
        to: {
          type: 'RECORD',
          specificTypes: [['OPERATION', 'IMMOBILIER', 'PSLA', 'PSLA_PRELIMINAIRE']]
        },
        constraints: {
          min: 1,
          max: 1
        },
        creation: {
          autoCreate: true,
          autoCreateOnly: true
        }
      }
    }
  },
  id: 'LINK__OPERATION__AQUITANIS__PROGRAMME__FICHE_VENTE_PROGRAMME',
  label: 'Fiche Vente Programme',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'FICHE_VENTE_PROGRAMME'],
  type: 'LINK'
};
