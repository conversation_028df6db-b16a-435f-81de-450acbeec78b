// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationColdwellBankerMandant: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Mandant',
      labelPlural: 'Mandants',
      labelWithArticle: 'Un mandant',
      branches: {
        MANDANT: {
          label: 'Mandant',
          labelWithArticle: 'Un Mandant'
        }
      }
    },
    branches: {
      MANDANT: {
        type: 'MANDANT',
        reverseType: 'MANDANT',
        to: {
          type: 'RECORD',
          specificTypes: [['PERSONNE', 'MORALE', 'AGENT_IMMOBILIER']]
        },
        constraints: {
          min: 1,
          max: 1
        },
        links: [
          {
            specificTypes: ['REPRESENTATION', 'PERSONNE_MORALE']
          }
        ],
        linkMatches: [['REPRESENTATION', '*']]
      }
    }
  },
  id: 'LINK__OPERATION__COLDWELL_BANKER__MANDANT',
  label: 'Mandant',
  mynotaryTemplate: false,
  originTemplate: 'LINK__OPERATION__COLDWELL_BANKER__MANDANT',
  specificTypes: ['OPERATION', 'COLDWELL_BANKER', 'MANDANT'],
  type: 'LINK'
};
