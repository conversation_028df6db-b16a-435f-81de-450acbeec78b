// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkRequisitionRequisitionImmeuble: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Immeuble',
      labelPlural: 'Immeubles',
      labelWithArticle: 'un Immeuble',
      branches: {
        REQUISITION: {
          label: 'Requisition - Immeuble',
          labelWithArticle: 'une Requisition - Immeuble'
        },
        IMMEUBLES: {
          label: 'Immeuble',
          labelWithArticle: 'un Immeuble'
        }
      }
    },
    branches: {
      REQUISITION: {
        type: 'REQUISITION',
        reverseType: 'IMMEUBLES',
        from: {
          type: 'RECORD',
          specificTypes: [
            ['BIEN', 'LOT_HORS_HABITATION'],
            ['BIEN', 'LOT_HABITATION'],
            ['BIEN', 'INDIVIDUEL_HABITATION'],
            ['BIEN', 'INDIVIDUEL_HORS_HABITATION'],
            ['BIEN', 'TERRAIN_CONSTRUCTIBLE'],
            ['BIEN', 'TERRAIN_NON_CONSTRUCTIBLE'],
            ['STRUCTURE', 'ENSEMBLE_IMMOBILIER']
          ]
        },
        to: {
          type: 'RECORD',
          specificTypes: [['OPERATION', 'REQUISITION']]
        },
        constraints: {
          min: 0
        }
      },
      IMMEUBLES: {
        type: 'IMMEUBLES',
        reverseType: 'REQUISITION',
        from: {
          type: 'RECORD',
          specificTypes: [['OPERATION', 'REQUISITION']]
        },
        to: {
          type: 'RECORD',
          specificTypes: [
            ['BIEN', 'LOT_HORS_HABITATION'],
            ['BIEN', 'LOT_HABITATION'],
            ['BIEN', 'INDIVIDUEL_HABITATION'],
            ['BIEN', 'INDIVIDUEL_HORS_HABITATION'],
            ['BIEN', 'TERRAIN_CONSTRUCTIBLE'],
            ['BIEN', 'TERRAIN_NON_CONSTRUCTIBLE'],
            ['STRUCTURE', 'ENSEMBLE_IMMOBILIER']
          ]
        },
        constraints: {
          min: 0
        }
      }
    }
  },
  id: 'LINK__REQUISITION__REQUISITION_IMMEUBLE',
  label: 'Requisition Immeuble',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['REQUISITION', 'REQUISITION_IMMEUBLE'],
  type: 'LINK'
};
