// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationImmobilierVenteApporteur: LegalLinkTemplate = {
  config: {
    display: {
      label: "Apporteur d'affaire",
      labelPlural: "Apporteur d'affaire",
      labelWithArticle: "Apporteur d'affaire",
      branches: {
        APPORTEUR: {
          label: 'Apporteur',
          labelWithArticle: 'un Apporteur'
        }
      }
    },
    branches: {
      APPORTEUR: {
        type: 'APPORTEUR',
        reverseType: 'APPORTEUR',
        to: {
          type: 'RECORD',
          specificTypes: [
            ['PERSONNE', 'PHYSIQUE'],
            ['PERSONNE', 'MORALE']
          ]
        },
        constraints: {
          min: 1
        },
        links: [
          {
            specificTypes: ['SITUATION_MARITALE', 'MARIAGE']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'DIVORC<PERSON>']
          },
          {
            specificTypes: ['REPRESENTATION', 'PERSONNE_MORALE']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'PACS']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'VEUVAGE']
          },
          {
            specificTypes: ['CAPACITE', 'SAUVEGARDE_JUSTICE']
          },
          {
            specificTypes: ['CAPACITE', 'CURATELLE']
          },
          {
            specificTypes: ['CAPACITE', 'EMANCIPATION']
          },
          {
            specificTypes: ['CAPACITE', 'HABILITATION_FAMILIALE']
          },
          {
            specificTypes: ['CAPACITE', 'TUTELLE']
          },
          {
            specificTypes: ['CAPACITE', 'MINORITE']
          },
          {
            specificTypes: ['CAPACITE', 'MANDAT_PROTECTION_FUTURE']
          },
          {
            specificTypes: ['PROCURATION', 'PROCURATION']
          },
          {
            specificTypes: ['CAPACITE', 'AUCUN']
          },
          {
            specificTypes: ['PROCURATION', 'AUCUN']
          },
          {
            specificTypes: ['CAPACITE', 'NON_DEFINI']
          }
        ],
        linkMatches: [
          ['SITUATION_MARITALE', '*'],
          ['REPRESENTATION', '*'],
          ['CAPACITE', '*'],
          ['PROCURATION', '*']
        ]
      }
    }
  },
  id: 'LINK__OPERATION__IMMOBILIER__VENTE__APPORTEUR',
  label: "Apporteur d'affaire",
  mynotaryTemplate: false,
  originTemplate: 'LINK__OPERATION__IMMOBILIER__VENTE__APPORTEUR',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'APPORTEUR'],
  type: 'LINK'
};
