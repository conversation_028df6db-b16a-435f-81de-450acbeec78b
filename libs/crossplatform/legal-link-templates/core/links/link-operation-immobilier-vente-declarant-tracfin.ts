// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationImmobilierVenteDeclarantTracfin: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Déclarant Tracfin',
      labelPlural: 'Déclarants Tracfin',
      labelWithArticle: 'un Déclarant Tracfin',
      branches: {
        DECLARANT: {
          label: 'Déclarant Tracfin',
          labelWithArticle: 'un Déclarant Tracfin'
        }
      }
    },
    branches: {
      DECLARANT: {
        type: 'DECLARANT',
        reverseType: 'DECLARANT',
        to: {
          type: 'RECORD',
          specificTypes: [['PERSONNE', 'PHYSIQUE']]
        },
        constraints: {
          min: 1
        },
        links: [
          {
            specificTypes: ['SITUATION_MARITALE', 'MARIAGE']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'DIVORCE']
          },
          {
            specificTypes: ['REPRESENTATION', 'PERSONNE_MORALE']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'PACS']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'VEUVAGE']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'AUCUN']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'NON_DEFINI']
          },
          {
            specificTypes: ['CAPACITE', 'AUCUN']
          },
          {
            specificTypes: ['PROCURATION', 'AUCUN']
          },
          {
            specificTypes: ['CAPACITE', 'NON_DEFINI']
          }
        ],
        linkMatches: [
          ['SITUATION_MARITALE', '*'],
          ['REPRESENTATION', '*'],
          ['CAPACITE', '*'],
          ['PROCURATION', '*']
        ]
      }
    }
  },
  id: 'LINK__OPERATION__IMMOBILIER__VENTE__DECLARANT_TRACFIN',
  label: 'Déclarant Tracfin',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'DECLARANT_TRACFIN'],
  type: 'LINK'
};
