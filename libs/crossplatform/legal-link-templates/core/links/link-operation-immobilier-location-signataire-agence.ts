// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationImmobilierLocationSignataireAgence: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Signataire Agence',
      labelPlural: 'Signataires Agence',
      labelWithArticle: 'un Signataire Agence',
      branches: {
        SIGNATAIRE: {
          label: 'Signataire Agence',
          labelWithArticle: 'un Signataire Agence'
        }
      }
    },
    branches: {
      SIGNATAIRE: {
        type: 'SIGNATAIRE',
        reverseType: 'SIGNATAIRE',
        to: {
          type: 'RECORD',
          specificTypes: [['PERSONNE', 'PHYSIQUE', 'INTERMEDIAIRE_IMMOBILIER']]
        },
        constraints: {
          min: 1
        },
        links: [
          {
            specificTypes: ['REPRESENTATION', 'PERSONNE_MORALE']
          }
        ],
        linkMatches: [['REPRESENTATION', '*']]
      }
    }
  },
  id: 'LINK__OPERATION__IMMOBILIER__LOCATION__SIGNATAIRE_AGENCE',
  label: 'Signataire Agence',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'SIGNATAIRE_AGENCE'],
  type: 'LINK'
};
