// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationI3FImmobilierPslaPreliminaireRepresentantCommercialisateur: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Représentant du Commercialisateur',
      labelPlural: 'Représentants du Commercialisateur',
      labelWithArticle: 'un représentant du Commercialisateur',
      branches: {
        REPRESENTANT_COMMERCIALISATEUR: {
          label: 'Représentant du Commercialisateur',
          labelWithArticle: 'un représentant du Commercialisateur'
        }
      }
    },
    branches: {
      REPRESENTANT_COMMERCIALISATEUR: {
        type: 'REPRESENTANT_COMMERCIALISATEUR',
        reverseType: 'REPRESENTANT_COMMERCIALISATEUR',
        to: {
          type: 'RECORD',
          specificTypes: [['PERSONNE', 'PHYSIQUE', 'INTERMEDIAIRE_IMMOBILIER']]
        },
        constraints: {}
      }
    }
  },
  id: 'LINK__OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__REPRESENTANT_COMMERCIALISATEUR',
  label: 'I3F - Représentant Commercialisateur',
  mynotaryTemplate: false,
  originTemplate: 'LINK__OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__REPRESENTANT_COMMERCIALISATEUR',
  specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PRELIMINAIRE', 'REPRESENTANT_COMMERCIALISATEUR'],
  type: 'LINK'
};
