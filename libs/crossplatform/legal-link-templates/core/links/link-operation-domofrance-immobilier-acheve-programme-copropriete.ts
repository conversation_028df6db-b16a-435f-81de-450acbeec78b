// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationDomofranceImmobilierAcheveProgrammeCopropriete: LegalLinkTemplate = {
  config: {
    display: {
      label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      labelPlural: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      labelWithArticle: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      branches: {
        COPROPRIETE: {
          label: 'Copropri<PERSON><PERSON>',
          labelWithArticle: 'une copropriété'
        }
      }
    },
    branches: {
      COPROPRIETE: {
        type: 'COPROPRIETE',
        reverseType: 'COPROPRIETE',
        to: {
          type: 'RECORD',
          specificTypes: [['STRUCTURE', 'COPROPRIETE']]
        },
        constraints: {
          min: 0
        }
      }
    }
  },
  id: 'LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME__COPROPRIETE',
  label: 'Copropri<PERSON><PERSON>',
  mynotaryTemplate: false,
  originTemplate: 'LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__COPROPRIETE',
  specificTypes: ['OPERATION', 'DOMOFRANCE', 'IMMOBILIER_ACHEVE', 'PROGRAMME', 'COPROPRIETE'],
  type: 'LINK'
};
