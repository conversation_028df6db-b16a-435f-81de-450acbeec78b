// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationSdAccessImmobilierVenteNotaireAcquereur: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Notaire Acquéreur',
      labelPlural: 'Notaire Acquéreur',
      labelWithArticle: 'Notaire Acquéreur',
      branches: {
        NOTAIRE_ACQUEREUR: {
          label: 'Notaire Acquéreur',
          labelWithArticle: 'un notaire Acquéreur'
        }
      }
    },
    branches: {
      NOTAIRE_ACQUEREUR: {
        type: 'NOTAIRE_ACQUEREUR',
        reverseType: 'NOTAIRE_ACQUEREUR',
        to: {
          type: 'RECORD',
          specificTypes: [['PERSONNE', 'MORALE', 'NOTAIRE']]
        },
        constraints: {
          min: 0
        },
        links: [],
        record: {
          specificTypes: [
            'LIEN',
            'OPERATION',
            'IMMOBILIER',
            'PSLA_PRELIMINAIRE',
            'NOTAIRE_ACQUEREUR',
            'NOTAIRE_ACQUEREUR'
          ]
        }
      }
    }
  },
  id: 'LINK__OPERATION__SD_ACCESS__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR',
  label: 'Notaire Acquéreur ',
  mynotaryTemplate: false,
  originTemplate: 'LINK__OPERATION__SD_ACCESS__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR',
  specificTypes: ['OPERATION', 'SD_ACCESS', 'IMMOBILIER', 'VENTE', 'NOTAIRE_ACQUEREUR'],
  type: 'LINK'
};
