// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationI3FImmobilierBrsProgrammeCooperative: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Coopérative Foncière',
      labelPlural: 'Coopérative Foncière',
      labelWithArticle: 'Coopérative Foncière',
      branches: {
        COOPERATIVE: {
          label: 'Coopérative',
          labelWithArticle: 'Une Coopérative'
        }
      }
    },
    branches: {
      COOPERATIVE: {
        type: 'COOPERATIVE',
        reverseType: 'COOPERATIVE',
        to: {
          type: 'RECORD',
          specificTypes: [['PERSONNE', 'MORALE']]
        },
        constraints: {
          min: 1
        },
        links: [
          {
            specificTypes: ['REPRESENTATION', 'PERSONNE_MORALE']
          },
          {
            specificTypes: ['PROCURATION', 'PROCURATION']
          },
          {
            specificTypes: ['PROCURATION', 'AUCUN']
          }
        ],
        linkMatches: [
          ['REPRESENTATION', '*'],
          ['PROCURATION', '*']
        ],
        record: {
          specificTypes: []
        }
      }
    }
  },
  id: 'LINK__OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__COOPERATIVE',
  label: 'Coopérative Foncière',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'COOPERATIVE'],
  type: 'LINK'
};
