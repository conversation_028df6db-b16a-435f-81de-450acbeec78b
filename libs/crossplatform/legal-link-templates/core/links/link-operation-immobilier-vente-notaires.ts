// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationImmobilierVenteNotaires: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Notaires',
      labelPlural: 'Notaires',
      labelWithArticle: 'un notaire',
      branches: {
        NOTAIRE_IMMOBILIER: {
          label: 'Notaire',
          labelWithArticle: 'un notaire'
        }
      }
    },
    branches: {
      NOTAIRE_IMMOBILIER: {
        type: 'NOTAIRE_IMMOBILIER',
        reverseType: 'NOTAIRE_IMMOBILIER',
        to: {
          type: 'RECORD',
          specificTypes: [['PERSONNE', 'MORALE', 'NOTAIRE']]
        },
        constraints: {
          min: 0
        },
        links: [],
        record: {
          specificTypes: ['LIEN', 'OPERATION', 'IMMOBILIER', 'VENTE', 'NOTAIRE', 'NOTAIRE_IMMOBILIER']
        }
      }
    }
  },
  id: 'LINK__OPERATION__IMMOBILIER__VENTE__NOTAIRES',
  label: 'Notaires',
  mynotaryTemplate: false,
  originTemplate: 'LINK__OPERATION__IMMOBILIER__VENTE__NOTAIRES',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'NOTAIRES'],
  type: 'LINK'
};
