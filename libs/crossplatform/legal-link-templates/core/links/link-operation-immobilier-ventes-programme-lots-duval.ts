// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationImmobilierVentesProgrammeLotsDuval: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Lots vendus',
      labelPlural: 'Lots vendus',
      labelWithArticle: 'un lot vendu',
      branches: {
        BIEN_VENDU: {
          label: 'Lot vendu',
          labelWithArticle: 'un lot vendu'
        }
      }
    },
    branches: {
      BIEN_VENDU: {
        type: 'BIEN_VENDU',
        reverseType: 'BIEN_VENDU',
        to: {
          type: 'RECORD',
          specificTypes: [
            ['BIEN', 'LOT_HABITATION'],
            ['BIEN', 'LOT_HORS_HABITATION'],
            ['BIEN', 'INDIVIDUEL_HORS_HABITATION'],
            ['BIEN', 'INDIVIDUEL_HABITATION']
          ]
        },
        constraints: {
          min: 0
        },
        links: [
          {
            specificTypes: ['COMPOSITION', 'LOTISSEMENT', 'LOTISSEMENT']
          },
          {
            specificTypes: ['COMPOSITION', 'COPROPRIETE']
          },
          {
            specificTypes: ['COMPOSITION', 'VOLUME', 'VOLUME']
          },
          {
            specificTypes: ['COMPOSITION', 'LOTISSEMENT', 'AUCUN']
          },
          {
            specificTypes: ['COMPOSITION', 'VOLUME', 'AUCUN']
          }
        ],
        linkMatches: [['COMPOSITION', '*']]
      }
    }
  },
  id: 'LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_DUVAL',
  label: 'Lots',
  mynotaryTemplate: false,
  originTemplate: 'LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'LOTS_DUVAL'],
  type: 'LINK'
};
