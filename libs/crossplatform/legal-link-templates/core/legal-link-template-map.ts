// This code is autogenerated don't modify it manually
import { LegalLinkTemplate, LegalLinkTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import * as templates from './links';

export const legalLinkTemplateMap: Record<LegalLinkTemplateId, LegalLinkTemplate> = {
  LINK__CAPACITE__AUCUN: templates.LinkCapaciteAucun,
  LINK__CAPACITE__CURATELLE: templates.LinkCapaciteCuratelle,
  LINK__CAPACITE__EMANCIPATION: templates.LinkCapaciteEmancipation,
  LINK__CAPACITE__HABILITATION_FAMILIALE: templates.LinkCapaciteHabilitationFamiliale,
  LINK__CAPACITE__MANDAT_PROTECTION_FUTURE: templates.LinkCapaciteMandatProtectionFuture,
  LINK__CAPACITE__MINORITE: templates.LinkCapaciteMinorite,
  LINK__CAPACITE__NON_DEFINI: templates.LinkCapaciteNonD<PERSON><PERSON>,
  <PERSON>IN<PERSON>__CAPACITE__SAUVEGARDE_JUSTICE: templates.LinkCapaciteSauvegardeJustice,
  LINK__CAPACITE__TUTELLE: templates.LinkCapaciteTutelle,
  LINK__COMPOSITION__COPROPRIETE: templates.LinkCompositionCopropriete,
  LINK__COMPOSITION__ENSEMBLE_IMMOBILIER: templates.LinkCompositionEnsembleImmobilier,
  LINK__COMPOSITION__LOTISSEMENT__AUCUN: templates.LinkCompositionLotissementAucun,
  LINK__COMPOSITION__LOTISSEMENT__LOTISSEMENT: templates.LinkCompositionLotissementLotissement,
  LINK__COMPOSITION__LOT_ANNEXE__AUCUN: templates.LinkCompositionLotAnnexeAucun,
  LINK__COMPOSITION__LOT_ANNEXE__LOT_ANNEXE: templates.LinkCompositionLotAnnexeLotAnnexe,
  LINK__COMPOSITION__VOLUME__AUCUN: templates.LinkCompositionVolumeAucun,
  LINK__COMPOSITION__VOLUME__VOLUME: templates.LinkCompositionVolumeVolume,
  LINK__DEMANDE__ETAT_CIVIL: templates.LinkDemandeEtatCivil,
  LINK__OPERATION__AQUITANIS__PROGRAMME__BAILLEUR_SOCIAL: templates.LinkOperationAquitanisProgrammeBailleurSocial,
  LINK__OPERATION__AQUITANIS__PROGRAMME__COPROPRIETE: templates.LinkOperationAquitanisProgrammeCopropriete,
  LINK__OPERATION__AQUITANIS__PROGRAMME__FICHE_PROGRAMME: templates.LinkOperationAquitanisProgrammeFicheProgramme,
  LINK__OPERATION__AQUITANIS__PROGRAMME__FICHE_VENTE_PROGRAMME:
    templates.LinkOperationAquitanisProgrammeFicheVenteProgramme,
  LINK__OPERATION__AQUITANIS__PROGRAMME__LOTS: templates.LinkOperationAquitanisProgrammeLots,
  LINK__OPERATION__AQUITANIS__PROGRAMME__NOTAIRE_PROGRAMME: templates.LinkOperationAquitanisProgrammeNotaireProgramme,
  LINK__OPERATION__AQUITANIS__PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationAquitanisProgrammeRepresentantBailleur,
  LINK__OPERATION__AQUITANIS__PROGRAMME__VENTES: templates.LinkOperationAquitanisProgrammeVentes,
  LINK__OPERATION__AQUITANIS__PROGRAMME__VENTES_BRS: templates.LinkOperationAquitanisProgrammeVentesBrs,
  LINK__OPERATION__AQUITANIS__VENTE__ACQUEREUR: templates.LinkOperationAquitanisVenteAcquereur,
  LINK__OPERATION__AQUITANIS__VENTE__FICHE_VENTE: templates.LinkOperationAquitanisVenteFicheVente,
  LINK__OPERATION__AQUITANIS__VENTE__NOTAIRE_ACQUEREUR: templates.LinkOperationAquitanisVenteNotaireAcquereur,
  LINK__OPERATION__AUTRE_INTERVENANT: templates.LinkOperationAutreIntervenant,
  LINK__OPERATION__AUTRE_LIBRE: templates.LinkOperationAutreLibre,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__BAILLEUR_SOCIAL:
    templates.LinkOperationCdcHabitatImmobilierProgrammeBailleurSocial,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__CLERC_NOTAIRE:
    templates.LinkOperationCdcHabitatImmobilierProgrammeClercNotaire,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__COPROPRIETE:
    templates.LinkOperationCdcHabitatImmobilierProgrammeCopropriete,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME:
    templates.LinkOperationCdcHabitatImmobilierProgrammeFicheProgramme,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_VENTE_PROGRAMME:
    templates.LinkOperationCdcHabitatImmobilierProgrammeFicheVenteProgramme,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__GESTIONNAIRE_VENTE:
    templates.LinkOperationCdcHabitatImmobilierProgrammeGestionnaireVente,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS: templates.LinkOperationCdcHabitatImmobilierProgrammeLots,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationCdcHabitatImmobilierProgrammeNotaireProgramme,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__REFERENT_VENTE:
    templates.LinkOperationCdcHabitatImmobilierProgrammeReferentVente,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationCdcHabitatImmobilierProgrammeRepresentantBailleur,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__VENTES:
    templates.LinkOperationCdcHabitatImmobilierProgrammeVentes,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__VENTE_PSLA:
    templates.LinkOperationCdcHabitatImmobilierProgrammeVentePsla,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__ACQUEREUR: templates.LinkOperationCdcHabitatImmobilierVenteAcquereur,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__COMMERCIALISATEUR:
    templates.LinkOperationCdcHabitatImmobilierVenteCommercialisateur,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__FICHE_VENTE:
    templates.LinkOperationCdcHabitatImmobilierVenteFicheVente,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__INTERVENANTS:
    templates.LinkOperationCdcHabitatImmobilierVenteIntervenants,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationCdcHabitatImmobilierVenteNotaireAcquereur,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__REPRESENTANT_COMMERCIALISATEUR:
    templates.LinkOperationCdcHabitatImmobilierVenteRepresentantCommercialisateur,
  LINK__OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__VENDEUR_CDC:
    templates.LinkOperationCdcHabitatImmobilierVenteVendeurCdc,
  LINK__OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__BAILLEUR_SOCIAL:
    templates.LinkOperationClesenceImmobilierProgrammeBailleurSocial,
  LINK__OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__COPROPRIETE:
    templates.LinkOperationClesenceImmobilierProgrammeCopropriete,
  LINK__OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__FICHE_PSLA:
    templates.LinkOperationClesenceImmobilierProgrammeFichePsla,
  LINK__OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__LOTS: templates.LinkOperationClesenceImmobilierProgrammeLots,
  LINK__OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationClesenceImmobilierProgrammeNotaireProgramme,
  LINK__OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationClesenceImmobilierProgrammeRepresentantBailleur,
  LINK__OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__SUBVENTION:
    templates.LinkOperationClesenceImmobilierProgrammeSubvention,
  LINK__OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__VENTES: templates.LinkOperationClesenceImmobilierProgrammeVentes,
  LINK__OPERATION__CLESENCE__IMMOBILIER__VENTE__ACQUEREUR: templates.LinkOperationClesenceImmobilierVenteAcquereur,
  LINK__OPERATION__CLESENCE__IMMOBILIER__VENTE__FICHE_PSLA: templates.LinkOperationClesenceImmobilierVenteFichePsla,
  LINK__OPERATION__CLESENCE__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationClesenceImmobilierVenteNotaireAcquereur,
  LINK__OPERATION__COLDWELL_BANKER__AGENT: templates.LinkOperationColdwellBankerAgent,
  LINK__OPERATION__COLDWELL_BANKER__CONTRAT_NEGOCIATEUR: templates.LinkOperationColdwellBankerContratNegociateur,
  LINK__OPERATION__COLDWELL_BANKER__MANDANT: templates.LinkOperationColdwellBankerMandant,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME__BAILLEUR:
    templates.LinkOperationDomofranceImmobilierAcheveProgrammeBailleur,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME__COPROPRIETE:
    templates.LinkOperationDomofranceImmobilierAcheveProgrammeCopropriete,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME__FICHE_PROGRAMME:
    templates.LinkOperationDomofranceImmobilierAcheveProgrammeFicheProgramme,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME__LOTS_HABITATION:
    templates.LinkOperationDomofranceImmobilierAcheveProgrammeLotsHabitation,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationDomofranceImmobilierAcheveProgrammeNotaireProgramme,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationDomofranceImmobilierAcheveProgrammeRepresentantBailleur,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME__VENTES:
    templates.LinkOperationDomofranceImmobilierAcheveProgrammeVentes,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__VENTE__ACQUEREUR:
    templates.LinkOperationDomofranceImmobilierAcheveVenteAcquereur,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__VENTE__FICHE_VENTE:
    templates.LinkOperationDomofranceImmobilierAcheveVenteFicheVente,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__VENTE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationDomofranceImmobilierAcheveVenteNotaireAcquereur,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER__PROGRAMME__BAILLEUR:
    templates.LinkOperationDomofranceImmobilierProgrammeBailleur,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER__PROGRAMME__COPROPRIETE:
    templates.LinkOperationDomofranceImmobilierProgrammeCopropriete,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER__PROGRAMME__FICHE_BRS:
    templates.LinkOperationDomofranceImmobilierProgrammeFicheBrs,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER__PROGRAMME__LOTS: templates.LinkOperationDomofranceImmobilierProgrammeLots,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationDomofranceImmobilierProgrammeNotaireProgramme,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationDomofranceImmobilierProgrammeRepresentantBailleur,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER__PROGRAMME__VENTES:
    templates.LinkOperationDomofranceImmobilierProgrammeVentes,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER__VENTE__ACQUEREUR: templates.LinkOperationDomofranceImmobilierVenteAcquereur,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER__VENTE__FICHE_BRS: templates.LinkOperationDomofranceImmobilierVenteFicheBrs,
  LINK__OPERATION__DOMOFRANCE__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationDomofranceImmobilierVenteNotaireAcquereur,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR:
    templates.LinkOperationI3FImmobilierBrsPreliminaireAcquereur,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__COMMERCIALISATEUR:
    templates.LinkOperationI3FImmobilierBrsPreliminaireCommercialisateur,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__CONTRAT_PRELIMINAIRE:
    templates.LinkOperationI3FImmobilierBrsPreliminaireContratPreliminaire,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationI3FImmobilierBrsPreliminaireNotaireAcquereur,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__REPRESENTANT_COMMERCIALISATEUR:
    templates.LinkOperationI3FImmobilierBrsPreliminaireRepresentantCommercialisateur,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__BAILLEUR: templates.LinkOperationI3FImmobilierBrsProgrammeBailleur,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__COOPERATIVE:
    templates.LinkOperationI3FImmobilierBrsProgrammeCooperative,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__COPROPRIETE:
    templates.LinkOperationI3FImmobilierBrsProgrammeCopropriete,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__FICHE_PROGRAMME:
    templates.LinkOperationI3FImmobilierBrsProgrammeFicheProgramme,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS: templates.LinkOperationI3FImmobilierBrsProgrammeLots,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationI3FImmobilierBrsProgrammeNotaireProgramme,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationI3FImmobilierBrsProgrammeRepresentantBailleur,
  LINK__OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__VENTES: templates.LinkOperationI3FImmobilierBrsProgrammeVentes,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__ACQUEREUR:
    templates.LinkOperationI3FImmobilierPslaPreliminaireAcquereur,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__COMMERCIALISATEUR:
    templates.LinkOperationI3FImmobilierPslaPreliminaireCommercialisateur,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__CONTRAT_PRELIMINAIRE:
    templates.LinkOperationI3FImmobilierPslaPreliminaireContratPreliminaire,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationI3FImmobilierPslaPreliminaireNotaireAcquereur,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__REPRESENTANT_COMMERCIALISATEUR:
    templates.LinkOperationI3FImmobilierPslaPreliminaireRepresentantCommercialisateur,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__BAILLEUR: templates.LinkOperationI3FImmobilierPslaProgrammeBailleur,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__COPROPRIETE:
    templates.LinkOperationI3FImmobilierPslaProgrammeCopropriete,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__FICHE_PROGRAMME:
    templates.LinkOperationI3FImmobilierPslaProgrammeFicheProgramme,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS: templates.LinkOperationI3FImmobilierPslaProgrammeLots,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationI3FImmobilierPslaProgrammeNotaireProgramme,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationI3FImmobilierPslaProgrammeRepresentantBailleur,
  LINK__OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__VENTES: templates.LinkOperationI3FImmobilierPslaProgrammeVentes,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__COPROPRIETE:
    templates.LinkOperationI3FImmobilierVefaProgrammeCopropriete,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__FICHE_PROGRAMME:
    templates.LinkOperationI3FImmobilierVefaProgrammeFicheProgramme,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS: templates.LinkOperationI3FImmobilierVefaProgrammeLots,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationI3FImmobilierVefaProgrammeNotaireProgramme,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__PROMOTEURS:
    templates.LinkOperationI3FImmobilierVefaProgrammePromoteurs,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__REPRESENTANT_PROMOTEURS:
    templates.LinkOperationI3FImmobilierVefaProgrammeRepresentantPromoteurs,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__VENTES: templates.LinkOperationI3FImmobilierVefaProgrammeVentes,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__VENTES_ERIGERE:
    templates.LinkOperationI3FImmobilierVefaProgrammeVentesErigere,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__COMMERCIALISATEUR:
    templates.LinkOperationI3FImmobilierVefaReservationCommercialisateur,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__CONTRAT_RESERVATION:
    templates.LinkOperationI3FImmobilierVefaReservationContratReservation,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__NOTAIRE_RESERVATAIRE:
    templates.LinkOperationI3FImmobilierVefaReservationNotaireReservataire,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__REPRESENTANT_COMMERCIALISATEUR:
    templates.LinkOperationI3FImmobilierVefaReservationRepresentantCommercialisateur,
  LINK__OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES:
    templates.LinkOperationI3FImmobilierVefaReservationReservataires,
  LINK__OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__CONSTRUCTEUR:
    templates.LinkOperationImmobilierConstructionMaisonIndividuelleConstructeur,
  LINK__OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__FICHES:
    templates.LinkOperationImmobilierConstructionMaisonIndividuelleFiches,
  LINK__OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__MAITRE_OUVRAGE:
    templates.LinkOperationImmobilierConstructionMaisonIndividuelleMaitreOuvrage,
  LINK__OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__TERRAIN_CONSTRUCTIBLE:
    templates.LinkOperationImmobilierConstructionMaisonIndividuelleTerrainConstructible,
  LINK__OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL:
    templates.LinkOperationImmobilierLocationCommercialBailCommercial,
  LINK__OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL:
    templates.LinkOperationImmobilierLocationCommercialBiensLouesCommercial,
  LINK__OPERATION__IMMOBILIER__LOCATION_CONTRAT__CONDITION_LOCATION:
    templates.LinkOperationImmobilierLocationContratConditionLocation,
  LINK__OPERATION__IMMOBILIER__LOCATION_CONTRAT__GARANTS: templates.LinkOperationImmobilierLocationContratGarants,
  LINK__OPERATION__IMMOBILIER__LOCATION_CONTRAT__LOCATAIRES: templates.LinkOperationImmobilierLocationContratLocataires,
  LINK__OPERATION__IMMOBILIER__LOCATION_PROGRAMME__ADMINISTRATEUR_BIEN:
    templates.LinkOperationImmobilierLocationProgrammeAdministrateurBien,
  LINK__OPERATION__IMMOBILIER__LOCATION_PROGRAMME__BAILLEURS:
    templates.LinkOperationImmobilierLocationProgrammeBailleurs,
  LINK__OPERATION__IMMOBILIER__LOCATION_PROGRAMME__BIENS_LOUES:
    templates.LinkOperationImmobilierLocationProgrammeBiensLoues,
  LINK__OPERATION__IMMOBILIER__LOCATION_PROGRAMME__COPROPRIETE:
    templates.LinkOperationImmobilierLocationProgrammeCopropriete,
  LINK__OPERATION__IMMOBILIER__LOCATION_PROGRAMME__FICHE_PROGRAMME:
    templates.LinkOperationImmobilierLocationProgrammeFicheProgramme,
  LINK__OPERATION__IMMOBILIER__LOCATION_PROGRAMME__LOCATION_WHITEBIRD:
    templates.LinkOperationImmobilierLocationProgrammeLocationWhitebird,
  LINK__OPERATION__IMMOBILIER__LOCATION_PROGRAMME__MANDATAIRE:
    templates.LinkOperationImmobilierLocationProgrammeMandataire,
  LINK__OPERATION__IMMOBILIER__LOCATION_PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationImmobilierLocationProgrammeRepresentantBailleur,
  LINK__OPERATION__IMMOBILIER__LOCATION_VISITE__VISITEUR: templates.LinkOperationImmobilierLocationVisiteVisiteur,
  LINK__OPERATION__IMMOBILIER__LOCATION__ADMINISTRATEUR_BIEN:
    templates.LinkOperationImmobilierLocationAdministrateurBien,
  LINK__OPERATION__IMMOBILIER__LOCATION__AGENCE_DELEGATAIRE: templates.LinkOperationImmobilierLocationAgenceDelegataire,
  LINK__OPERATION__IMMOBILIER__LOCATION__AGENTS: templates.LinkOperationImmobilierLocationAgents,
  LINK__OPERATION__IMMOBILIER__LOCATION__ARRENDADOR: templates.LinkOperationImmobilierLocationArrendador,
  LINK__OPERATION__IMMOBILIER__LOCATION__ARRENDATARIO: templates.LinkOperationImmobilierLocationArrendatario,
  LINK__OPERATION__IMMOBILIER__LOCATION__BAILLEURS: templates.LinkOperationImmobilierLocationBailleurs,
  LINK__OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES: templates.LinkOperationImmobilierLocationBiensLoues,
  LINK__OPERATION__IMMOBILIER__LOCATION__FICHES: templates.LinkOperationImmobilierLocationFiches,
  LINK__OPERATION__IMMOBILIER__LOCATION__GARANTS: templates.LinkOperationImmobilierLocationGarants,
  LINK__OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: templates.LinkOperationImmobilierLocationLocataires,
  LINK__OPERATION__IMMOBILIER__LOCATION__MANDANT_PRELLO: templates.LinkOperationImmobilierLocationMandantPrello,
  LINK__OPERATION__IMMOBILIER__LOCATION__MANDATAIRES: templates.LinkOperationImmobilierLocationMandataires,
  LINK__OPERATION__IMMOBILIER__LOCATION__NOUVEAU_LOCATAIRE: templates.LinkOperationImmobilierLocationNouveauLocataire,
  LINK__OPERATION__IMMOBILIER__LOCATION__PARTENAIRE: templates.LinkOperationImmobilierLocationPartenaire,
  LINK__OPERATION__IMMOBILIER__LOCATION__PRELLO_BAILLEUR: templates.LinkOperationImmobilierLocationPrelloBailleur,
  LINK__OPERATION__IMMOBILIER__LOCATION__REPRESENTANTS: templates.LinkOperationImmobilierLocationRepresentants,
  LINK__OPERATION__IMMOBILIER__LOCATION__REPRESENTANT_BAILLEUR:
    templates.LinkOperationImmobilierLocationRepresentantBailleur,
  LINK__OPERATION__IMMOBILIER__LOCATION__SIGNATAIRE_AGENCE: templates.LinkOperationImmobilierLocationSignataireAgence,
  LINK__OPERATION__IMMOBILIER__LOCATION__VISITES: templates.LinkOperationImmobilierLocationVisites,
  LINK__OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__FICHE_PROGRAMME:
    templates.LinkOperationImmobilierLotissementProgrammeFicheProgramme,
  LINK__OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__LOTISSEUR:
    templates.LinkOperationImmobilierLotissementProgrammeLotisseur,
  LINK__OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__LOTS: templates.LinkOperationImmobilierLotissementProgrammeLots,
  LINK__OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationImmobilierLotissementProgrammeNotaireProgramme,
  LINK__OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__REPRESENTANT_LOTISSEUR:
    templates.LinkOperationImmobilierLotissementProgrammeRepresentantLotisseur,
  LINK__OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__VENTES:
    templates.LinkOperationImmobilierLotissementProgrammeVentes,
  LINK__OPERATION__IMMOBILIER__LOTISSEMENT_VENTE__ACQUEREUR: templates.LinkOperationImmobilierLotissementVenteAcquereur,
  LINK__OPERATION__IMMOBILIER__LOTISSEMENT_VENTE__CONTRAT_VENTE:
    templates.LinkOperationImmobilierLotissementVenteContratVente,
  LINK__OPERATION__IMMOBILIER__LOTISSEMENT_VENTE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationImmobilierLotissementVenteNotaireAcquereur,
  LINK__OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__COPROPRIETE:
    templates.LinkOperationImmobilierProgrammeSocialCopropriete,
  LINK__OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__FICHE_PROGRAMME:
    templates.LinkOperationImmobilierProgrammeSocialFicheProgramme,
  LINK__OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__LOTS: templates.LinkOperationImmobilierProgrammeSocialLots,
  LINK__OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__NOTAIRE_PROGRAMME:
    templates.LinkOperationImmobilierProgrammeSocialNotaireProgramme,
  LINK__OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__PROMOTEUR: templates.LinkOperationImmobilierProgrammeSocialPromoteur,
  LINK__OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__REPRESENTANT_PROMOTEUR:
    templates.LinkOperationImmobilierProgrammeSocialRepresentantPromoteur,
  LINK__OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__VENTES: templates.LinkOperationImmobilierProgrammeSocialVentes,
  LINK__OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE__ACQUEREUR: templates.LinkOperationImmobilierPslaPreliminaireAcquereur,
  LINK__OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE__CONTRAT_PRELIMINAIRE:
    templates.LinkOperationImmobilierPslaPreliminaireContratPreliminaire,
  LINK__OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationImmobilierPslaPreliminaireNotaireAcquereur,
  LINK__OPERATION__IMMOBILIER__PSLA_PROGRAMME__BAILLEUR: templates.LinkOperationImmobilierPslaProgrammeBailleur,
  LINK__OPERATION__IMMOBILIER__PSLA_PROGRAMME__COPROPRIETE: templates.LinkOperationImmobilierPslaProgrammeCopropriete,
  LINK__OPERATION__IMMOBILIER__PSLA_PROGRAMME__FICHE_PROGRAMME:
    templates.LinkOperationImmobilierPslaProgrammeFicheProgramme,
  LINK__OPERATION__IMMOBILIER__PSLA_PROGRAMME__LOTS: templates.LinkOperationImmobilierPslaProgrammeLots,
  LINK__OPERATION__IMMOBILIER__PSLA_PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationImmobilierPslaProgrammeNotaireProgramme,
  LINK__OPERATION__IMMOBILIER__PSLA_PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationImmobilierPslaProgrammeRepresentantBailleur,
  LINK__OPERATION__IMMOBILIER__PSLA_PROGRAMME__VENTES: templates.LinkOperationImmobilierPslaProgrammeVentes,
  LINK__OPERATION__IMMOBILIER__PSLA_PROGRAMME__VENTES_OZANAM:
    templates.LinkOperationImmobilierPslaProgrammeVentesOzanam,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__COPROPRIETE:
    templates.LinkOperationImmobilierVentesProgrammeCopropriete,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__GENERAL: templates.LinkOperationImmobilierVentesProgrammeGeneral,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS: templates.LinkOperationImmobilierVentesProgrammeLots,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_DESIMO:
    templates.LinkOperationImmobilierVentesProgrammeLotsDesimo,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_DUVAL: templates.LinkOperationImmobilierVentesProgrammeLotsDuval,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_IDEAL: templates.LinkOperationImmobilierVentesProgrammeLotsIdeal,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_PVCI_SENS:
    templates.LinkOperationImmobilierVentesProgrammeLotsPvciSens,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationImmobilierVentesProgrammeNotaireProgramme,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__PROMOTEURS: templates.LinkOperationImmobilierVentesProgrammePromoteurs,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__REPRESENTANT_PROMOTEUR:
    templates.LinkOperationImmobilierVentesProgrammeRepresentantPromoteur,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__VENTES: templates.LinkOperationImmobilierVentesProgrammeVentes,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__VENTE_ACHEVE_PVCI_SENS:
    templates.LinkOperationImmobilierVentesProgrammeVenteAchevePvciSens,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__VENTE_VEFA_DESIMO:
    templates.LinkOperationImmobilierVentesProgrammeVenteVefaDesimo,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__VENTE_VEFA_DUVAL:
    templates.LinkOperationImmobilierVentesProgrammeVenteVefaDuval,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__VENTE_VEFA_IDEAL:
    templates.LinkOperationImmobilierVentesProgrammeVenteVefaIdeal,
  LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__VENTE_VEFA_PVCI_SENS:
    templates.LinkOperationImmobilierVentesProgrammeVenteVefaPvciSens,
  LINK__OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES: templates.LinkOperationImmobilierVenteAncienFiches,
  LINK__OPERATION__IMMOBILIER__VENTE_NEUF__COMMERCIALISATEUR:
    templates.LinkOperationImmobilierVenteNeufCommercialisateur,
  LINK__OPERATION__IMMOBILIER__VENTE_NEUF__FICHES: templates.LinkOperationImmobilierVenteNeufFiches,
  LINK__OPERATION__IMMOBILIER__VENTE_NEUF__NOTAIRE_RESERVATAIRE:
    templates.LinkOperationImmobilierVenteNeufNotaireReservataire,
  LINK__OPERATION__IMMOBILIER__VENTE_NEUF__REPRESENTANT_COMMERCIALISATEUR:
    templates.LinkOperationImmobilierVenteNeufRepresentantCommercialisateur,
  LINK__OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES: templates.LinkOperationImmobilierVenteNeufReservataires,
  LINK__OPERATION__IMMOBILIER__VENTE_SOCIAL__ACQUEREUR: templates.LinkOperationImmobilierVenteSocialAcquereur,
  LINK__OPERATION__IMMOBILIER__VENTE_SOCIAL__FICHE_CONTRAT: templates.LinkOperationImmobilierVenteSocialFicheContrat,
  LINK__OPERATION__IMMOBILIER__VENTE_SOCIAL__NOTAIRE_ACQUEREUR:
    templates.LinkOperationImmobilierVenteSocialNotaireAcquereur,
  LINK__OPERATION__IMMOBILIER__VENTE__ACQUEREURS: templates.LinkOperationImmobilierVenteAcquereurs,
  LINK__OPERATION__IMMOBILIER__VENTE__ACQUEREUR_CESSIONNAIRE:
    templates.LinkOperationImmobilierVenteAcquereurCessionnaire,
  LINK__OPERATION__IMMOBILIER__VENTE__AGENCE_DELEGATAIRE: templates.LinkOperationImmobilierVenteAgenceDelegataire,
  LINK__OPERATION__IMMOBILIER__VENTE__AGENTS: templates.LinkOperationImmobilierVenteAgents,
  LINK__OPERATION__IMMOBILIER__VENTE__APPORTEUR: templates.LinkOperationImmobilierVenteApporteur,
  LINK__OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS: templates.LinkOperationImmobilierVenteBiensVendus,
  LINK__OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_AGENCE_DIRECTE:
    templates.LinkOperationImmobilierVenteBiensVendusAgenceDirecte,
  LINK__OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_PRO_SANS_MONO:
    templates.LinkOperationImmobilierVenteBiensVendusProSansMono,
  LINK__OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO: templates.LinkOperationImmobilierVenteBiensVendusSansMono,
  LINK__OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO_TAB:
    templates.LinkOperationImmobilierVenteBiensVendusSansMonoTab,
  LINK__OPERATION__IMMOBILIER__VENTE__BIENS_VISITES: templates.LinkOperationImmobilierVenteBiensVisites,
  LINK__OPERATION__IMMOBILIER__VENTE__COMPRADOR: templates.LinkOperationImmobilierVenteComprador,
  LINK__OPERATION__IMMOBILIER__VENTE__CONTRATO: templates.LinkOperationImmobilierVenteContrato,
  LINK__OPERATION__IMMOBILIER__VENTE__CORRESPONDANT_TRACFIN: templates.LinkOperationImmobilierVenteCorrespondantTracfin,
  LINK__OPERATION__IMMOBILIER__VENTE__CO_MANDATAIRE: templates.LinkOperationImmobilierVenteCoMandataire,
  LINK__OPERATION__IMMOBILIER__VENTE__CO_MANDATAIRE_KW: templates.LinkOperationImmobilierVenteCoMandataireKw,
  LINK__OPERATION__IMMOBILIER__VENTE__DECLARANT_TRACFIN: templates.LinkOperationImmobilierVenteDeclarantTracfin,
  LINK__OPERATION__IMMOBILIER__VENTE__EXPERTO_INMOBILIARIO: templates.LinkOperationImmobilierVenteExpertoInmobiliario,
  LINK__OPERATION__IMMOBILIER__VENTE__INTERVENANTS: templates.LinkOperationImmobilierVenteIntervenants,
  LINK__OPERATION__IMMOBILIER__VENTE__LOTS: templates.LinkOperationImmobilierVenteLots,
  LINK__OPERATION__IMMOBILIER__VENTE__MANAGER_AGENCE: templates.LinkOperationImmobilierVenteManagerAgence,
  LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES: templates.LinkOperationImmobilierVenteMandataires,
  LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES_MEGA_AGENT:
    templates.LinkOperationImmobilierVenteMandatairesMegaAgent,
  LINK__OPERATION__IMMOBILIER__VENTE__NOTAIRES: templates.LinkOperationImmobilierVenteNotaires,
  LINK__OPERATION__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR: templates.LinkOperationImmobilierVenteNotaireAcquereur,
  LINK__OPERATION__IMMOBILIER__VENTE__NOTAIRE_VENDEUR: templates.LinkOperationImmobilierVenteNotaireVendeur,
  LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS: templates.LinkOperationImmobilierVenteOffrants,
  LINK__OPERATION__IMMOBILIER__VENTE__OFFRE: templates.LinkOperationImmobilierVenteOffre,
  LINK__OPERATION__IMMOBILIER__VENTE__OFFRE_MODALITES: templates.LinkOperationImmobilierVenteOffreModalites,
  LINK__OPERATION__IMMOBILIER__VENTE__REDACTEUR: templates.LinkOperationImmobilierVenteRedacteur,
  LINK__OPERATION__IMMOBILIER__VENTE__REPRESENTANTS: templates.LinkOperationImmobilierVenteRepresentants,
  LINK__OPERATION__IMMOBILIER__VENTE__REPRESENTANT_VENDEUR: templates.LinkOperationImmobilierVenteRepresentantVendeur,
  LINK__OPERATION__IMMOBILIER__VENTE__SIGNATAIRE: templates.LinkOperationImmobilierVenteSignataire,
  LINK__OPERATION__IMMOBILIER__VENTE__VENDEDOR: templates.LinkOperationImmobilierVenteVendedor,
  LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS: templates.LinkOperationImmobilierVenteVendeurs,
  LINK__OPERATION__IMMOBILIER__VENTE__VISITE: templates.LinkOperationImmobilierVenteVisite,
  LINK__OPERATION__IMMOBILIER__VENTE__VISITEURS: templates.LinkOperationImmobilierVenteVisiteurs,
  LINK__OPERATION__LFEUR__IMMOBILIER__PROGRAMME__BAILLEUR_SOCIAL:
    templates.LinkOperationLfeurImmobilierProgrammeBailleurSocial,
  LINK__OPERATION__LFEUR__IMMOBILIER__PROGRAMME__COPROPRIETE:
    templates.LinkOperationLfeurImmobilierProgrammeCopropriete,
  LINK__OPERATION__LFEUR__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME:
    templates.LinkOperationLfeurImmobilierProgrammeFicheProgramme,
  LINK__OPERATION__LFEUR__IMMOBILIER__PROGRAMME__LOTS: templates.LinkOperationLfeurImmobilierProgrammeLots,
  LINK__OPERATION__LFEUR__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationLfeurImmobilierProgrammeNotaireProgramme,
  LINK__OPERATION__LFEUR__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationLfeurImmobilierProgrammeRepresentantBailleur,
  LINK__OPERATION__LFEUR__IMMOBILIER__PROGRAMME__VENTES: templates.LinkOperationLfeurImmobilierProgrammeVentes,
  LINK__OPERATION__LFEUR__IMMOBILIER__VENTE__ACQUEREUR: templates.LinkOperationLfeurImmobilierVenteAcquereur,
  LINK__OPERATION__LFEUR__IMMOBILIER__VENTE__FICHE_VENTE: templates.LinkOperationLfeurImmobilierVenteFicheVente,
  LINK__OPERATION__LFEUR__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationLfeurImmobilierVenteNotaireAcquereur,
  LINK__OPERATION__MYNOTARY__CONTRAT_SAAS__CLIENT: templates.LinkOperationMynotaryContratSaasClient,
  LINK__OPERATION__MYNOTARY__CONTRAT_SAAS__COMMERCIAL: templates.LinkOperationMynotaryContratSaasCommercial,
  LINK__OPERATION__MYNOTARY__CONTRAT_SAAS__CONDITIONS: templates.LinkOperationMynotaryContratSaasConditions,
  LINK__OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__COPROPRIETE:
    templates.LinkOperationPodelihaImmobilierProgrammeCopropriete,
  LINK__OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__FICHE_PSLA:
    templates.LinkOperationPodelihaImmobilierProgrammeFichePsla,
  LINK__OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__FICHE_VEFA:
    templates.LinkOperationPodelihaImmobilierProgrammeFicheVefa,
  LINK__OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__LOTS: templates.LinkOperationPodelihaImmobilierProgrammeLots,
  LINK__OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationPodelihaImmobilierProgrammeNotaireProgramme,
  LINK__OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__PROMOTEUR:
    templates.LinkOperationPodelihaImmobilierProgrammePromoteur,
  LINK__OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__REPRESENTANT_PROMOTEUR:
    templates.LinkOperationPodelihaImmobilierProgrammeRepresentantPromoteur,
  LINK__OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__VENTES: templates.LinkOperationPodelihaImmobilierProgrammeVentes,
  LINK__OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR: templates.LinkOperationPodelihaImmobilierVenteAcquereur,
  LINK__OPERATION__PODELIHA__IMMOBILIER__VENTE__FICHE_PSLA: templates.LinkOperationPodelihaImmobilierVenteFichePsla,
  LINK__OPERATION__PODELIHA__IMMOBILIER__VENTE__FICHE_VEFA: templates.LinkOperationPodelihaImmobilierVenteFicheVefa,
  LINK__OPERATION__PODELIHA__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationPodelihaImmobilierVenteNotaireAcquereur,
  LINK__OPERATION__RECRUTEMENT__AGENT__AFFILIE: templates.LinkOperationRecrutementAgentAffilie,
  LINK__OPERATION__RECRUTEMENT__AGENT__AGENCE: templates.LinkOperationRecrutementAgentAgence,
  LINK__OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL: templates.LinkOperationRecrutementAgentAgentCommercial,
  LINK__OPERATION__RECRUTEMENT__AGENT__BENEFICIAIRE: templates.LinkOperationRecrutementAgentBeneficiaire,
  LINK__OPERATION__RECRUTEMENT__AGENT__GENERAL: templates.LinkOperationRecrutementAgentGeneral,
  LINK__OPERATION__RECRUTEMENT__AGENT__PARTENAIRE: templates.LinkOperationRecrutementAgentPartenaire,
  LINK__OPERATION__RECRUTEMENT__AGENT__PRESTATAIRE: templates.LinkOperationRecrutementAgentPrestataire,
  LINK__OPERATION__RECRUTEMENT__AGENT__REPRESENTANT_OPERIO: templates.LinkOperationRecrutementAgentRepresentantOperio,
  LINK__OPERATION__RECRUTEMENT__AGENT__SPONSOR: templates.LinkOperationRecrutementAgentSponsor,
  LINK__OPERATION__RECRUTEMENT__AGENT__SPONSORING: templates.LinkOperationRecrutementAgentSponsoring,
  LINK__OPERATION__RECRUTEMENT__GENERAL__EMPLOYE: templates.LinkOperationRecrutementGeneralEmploye,
  LINK__OPERATION__RECRUTEMENT__GENERAL__SYNDICAT: templates.LinkOperationRecrutementGeneralSyndicat,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__BAILLEUR:
    templates.LinkOperationSdAccessImmobilierProgrammeBailleur,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__COPROPRIETE:
    templates.LinkOperationSdAccessImmobilierProgrammeCopropriete,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__FICHE_BRS:
    templates.LinkOperationSdAccessImmobilierProgrammeFicheBrs,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__LOTS: templates.LinkOperationSdAccessImmobilierProgrammeLots,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationSdAccessImmobilierProgrammeNotaireProgramme,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationSdAccessImmobilierProgrammeRepresentantBailleur,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__VENTES: templates.LinkOperationSdAccessImmobilierProgrammeVentes,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__VENTES_HYBRIDES:
    templates.LinkOperationSdAccessImmobilierProgrammeVentesHybrides,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR: templates.LinkOperationSdAccessImmobilierVenteAcquereur,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__VENTE__FICHE_BRS: templates.LinkOperationSdAccessImmobilierVenteFicheBrs,
  LINK__OPERATION__SD_ACCESS__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationSdAccessImmobilierVenteNotaireAcquereur,
  LINK__OPERATION__SYNDIC__GENERAL__ASL: templates.LinkOperationSyndicGeneralAsl,
  LINK__OPERATION__SYNDIC__GENERAL__COPROPRIETAIRE: templates.LinkOperationSyndicGeneralCoproprietaire,
  LINK__OPERATION__SYNDIC__GENERAL__COPROPRIETE: templates.LinkOperationSyndicGeneralCopropriete,
  LINK__OPERATION__SYNDIC__GENERAL__FICHE: templates.LinkOperationSyndicGeneralFiche,
  LINK__OPERATION__SYNDIC__GENERAL__SYNDIC: templates.LinkOperationSyndicGeneralSyndic,
  LINK__OPERATION__SYNDIC__GENERAL__SYNDICAT: templates.LinkOperationSyndicGeneralSyndicat,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME__BAILLEUR_SOCIAL:
    templates.LinkOperationValloireImmobilierAcheveProgrammeBailleurSocial,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME__COPROPRIETE:
    templates.LinkOperationValloireImmobilierAcheveProgrammeCopropriete,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME__FICHE_PROGRAMME:
    templates.LinkOperationValloireImmobilierAcheveProgrammeFicheProgramme,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME__LOTS:
    templates.LinkOperationValloireImmobilierAcheveProgrammeLots,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME__NOTAIRE_PROGRAMME:
    templates.LinkOperationValloireImmobilierAcheveProgrammeNotaireProgramme,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME__REPRESENTANT_BAILLEUR:
    templates.LinkOperationValloireImmobilierAcheveProgrammeRepresentantBailleur,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME__VENTES:
    templates.LinkOperationValloireImmobilierAcheveProgrammeVentes,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME__VENTES_NEUF:
    templates.LinkOperationValloireImmobilierAcheveProgrammeVentesNeuf,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__VENTE__ACQUEREUR:
    templates.LinkOperationValloireImmobilierAcheveVenteAcquereur,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__VENTE__FICHE_VENTE:
    templates.LinkOperationValloireImmobilierAcheveVenteFicheVente,
  LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__VENTE__NOTAIRE_ACQUEREUR:
    templates.LinkOperationValloireImmobilierAcheveVenteNotaireAcquereur,
  LINK__PROCURATION__AUCUN: templates.LinkProcurationAucun,
  LINK__PROCURATION__PROCURATION: templates.LinkProcurationProcuration,
  LINK__REPRESENTATION__PERSONNE_MORALE: templates.LinkRepresentationPersonneMorale,
  LINK__REQUISITION__REQUISITION_IMMEUBLE: templates.LinkRequisitionRequisitionImmeuble,
  LINK__REQUISITION__REQUISITION_PERSONNE: templates.LinkRequisitionRequisitionPersonne,
  LINK__SERVITUDE__AUCUN: templates.LinkServitudeAucun,
  LINK__SERVITUDE__SERVITUDE: templates.LinkServitudeServitude,
  LINK__SITUATION_MARITALE__AUCUN: templates.LinkSituationMaritaleAucun,
  LINK__SITUATION_MARITALE__DIVORCE: templates.LinkSituationMaritaleDivorce,
  LINK__SITUATION_MARITALE__MARIAGE: templates.LinkSituationMaritaleMariage,
  LINK__SITUATION_MARITALE__NON_DEFINI: templates.LinkSituationMaritaleNonDefini,
  LINK__SITUATION_MARITALE__PACS: templates.LinkSituationMaritalePacs,
  LINK__SITUATION_MARITALE__VEUVAGE: templates.LinkSituationMaritaleVeuvage
};

export function getLegalLinkTemplate(id: string): LegalLinkTemplate;
export function getLegalLinkTemplate(id?: string): LegalLinkTemplate | undefined {
  if (!id) {
    return undefined;
  }

  return legalLinkTemplateMap[id as LegalLinkTemplateId];
}
