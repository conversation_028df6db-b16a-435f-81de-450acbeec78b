// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordBienIndividuelHorsHabitation: LegalRecordTemplate = {
  config: {
    tags: {
      numero_lot: {
        questionId: 'numero_lot',
        format: 'LOT',
        order: 0
      },
      programme_superficie: {
        questionId: 'programme_superficie',
        format: 'AREA',
        order: 1
      }
    },
    recordLinks: [
      {
        id: 'LOTISSEMENT',
        specificTypes: ['COMPOSITION', 'LOTISSEMENT'],
        label: 'Lotissement',
        constraints: {
          min: 0,
          max: 1
        },
        creationQuestion: {
          choices: [
            {
              id: 'other',
              label: 'Oui'
            },
            {
              id: 'empty',
              label: 'Non'
            }
          ],
          label: 'Le bien est-il compris dans un lotissement?'
        }
      }
    ],
    search: ['adresse'],
    duplicate: ['adresse']
  },
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'fonds',
              label: 'Fonds de commerce'
            },
            {
              id: 'mur',
              label: 'Murs'
            },
            {
              id: 'bail',
              label: 'Droit au bail'
            },
            {
              id: 'titre',
              label: 'Titres de sociétés ou de parts'
            }
          ],
          id: 'bien_pro_type',
          label: 'Type de cession',
          multiple: true,
          type: 'SELECT'
        },
        {
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'adresse',
          label: 'Adresse du bien',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_vendeur',
          label: 'Bien construit par le Vendeur',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'residence_statut',
          label: 'Bien situé dans une résidence',
          type: 'SELECT-BINARY'
        },
        {
          id: 'residence_nom',
          label: 'Nom de la résidence',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'designation_statut',
          label: 'Ajouter une désignation supplémentaire du Bien',
          type: 'SELECT-BINARY'
        },
        {
          id: 'designation_terrain',
          label: 'Désignation du terrain supportant les constructions',
          type: 'TEXT'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'designation',
          label: 'Désignation du bien',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'designation_modifiee',
          label: 'Désignation du bien actuelle différente de celle du titre de propriété',
          type: 'SELECT-BINARY'
        },
        {
          id: 'designation_ancienne',
          label: 'Ancienne désignation du bien, telle que figurant dans le titre de propriété',
          type: 'TEXTAREA'
        },
        {
          id: 'designation_ancienne_travaux',
          label: 'Liste des travaux effectués depuis la désignation du titre',
          type: 'TEXTAREA'
        },
        {
          children: [
            {
              id: 'designation_ancienne_factures',
              label: 'Factures',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_assurance_decennale',
              label: 'Assurance décennale',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_assurance_do',
              label: 'Assurance dommage-ouvrage',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_permis',
              label: 'Permis de construire',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_declaration_travaux',
              label: 'Déclaration de travaux',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_declaration_travaux_achevement',
              label: "Déclaration d'achèvement des travaux",
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_certificat_conformite',
              label: 'Certificat de conformité',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'designation_modifiee',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '1325fg20_p36d_o4e2_lk4e_0d5f1380452e',
          label: 'CONDITION_BLOCK_Document_travaux',
          type: 'CONDITION_BLOCK'
        },
        {
          id: 'designation_avenant',
          label: 'Désignation modifiée du bien',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'exclusion_bail',
          label: 'Exclusion de certaines parties réservées par le propriétaire',
          type: 'SELECT-BINARY'
        },
        {
          id: 'exclusion_bail_liste',
          label: 'Désignation des parties réservées',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'nature_bien_immeuble',
              label: 'Immeuble Entier'
            },
            {
              id: 'nature_bien_commercial',
              label: 'Bien commercial ou professionnel'
            },
            {
              id: 'nature_garage',
              label: 'Garage'
            },
            {
              id: 'nature_bien_autre',
              label: 'Autre'
            }
          ],
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'nature_bien',
          label: 'Nature du bien',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'terrain_rural',
          label: 'Terrain nu - usage rural',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                  'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                ]
              }
            ]
          ],
          id: 'designation_precisions',
          label: 'Ajouter des précisions sur la désignation du Bien',
          type: 'SELECT-BINARY'
        },
        {
          id: 'designation_precisions_texte',
          label: 'Précisions sur la désignation du Bien',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'enregistrement_meuble',
          label: 'Enregistrement du Meublé obligatoire',
          type: 'SELECT-BINARY'
        },
        {
          id: 'enregistrement_meuble_numero',
          label: "Numéro d'enregistrement du Meublé",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'garage',
              label: 'Garage - box fermé'
            },
            {
              id: 'parking',
              label: 'Parking'
            }
          ],
          id: 'type_bien_parking_garage',
          label: 'Type de bien',
          type: 'SELECT-BINARY'
        },
        {
          id: 'type_bien_libre',
          label: 'Type de bien',
          type: 'TEXT'
        },
        {
          id: 'systeme_fermeture_commun',
          label: 'Système de fermeture des communs',
          type: 'TEXT'
        },
        {
          id: 'systeme_fermeture_emplacement',
          label: "Système de fermeture de l'emplacement loué",
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'nature_bien',
                value: 'nature_bien_autre',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'nature_bien_autre',
          label: 'Autre nature du bien',
          type: 'TEXT'
        },
        {
          id: 'numero_lot',
          label: 'Numéro de lot',
          type: 'TEXT'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__CDC__IMMOBILIER__PROGRAMME', 'OPERATION__CDC__IMMOBILIER__VENTE']
                  }
                ]
              ],
              title: 'Numéro UG'
            },
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__AQUITANIS__PROGRAMME_ANCIEN',
                      'OPERATION__AQUITANIS__VENTE_ANCIEN',
                      'OPERATION__AQUITANIS__PROGRAMME_BRS',
                      'OPERATION__AQUITANIS__RESERVATION_BRS'
                    ]
                  }
                ]
              ],
              title: 'Référence du lot'
            }
          ],
          id: 'numero_commercialisation_lot',
          label: 'Numéro de commercialisation',
          type: 'TEXT'
        },
        {
          id: 'numero_reference',
          label: 'Référence du lot',
          type: 'TEXT'
        },
        {
          id: 'nombre_piece',
          label: 'Nombre de pièces',
          type: 'NUMBER'
        },
        {
          id: 'nombre_chambres',
          label: 'Nombre de chambres',
          type: 'NUMBER'
        },
        {
          id: 'nombre_sdb',
          label: 'Nombre de salles de bain',
          type: 'NUMBER'
        },
        {
          id: 'nombre_se',
          label: "Nombre de salles d'eau",
          type: 'NUMBER'
        },
        {
          id: 'numero_lotissement',
          label: 'Numéro du lot de lotissement',
          type: 'TEXT'
        },
        {
          id: 'etage',
          label: 'Etage',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'caracteristique_neuf',
              label: 'Neuf'
            },
            {
              id: 'caracteristique_ancien',
              label: 'Ancien'
            },
            {
              id: 'caracteristique_vefa',
              label: "En état futur d'achèvement"
            },
            {
              id: 'caracteristique_renover',
              label: 'A rénover'
            }
          ],
          id: 'caracteristiques',
          label: 'Caractéristiques et état du bien',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'autres_caracteristiques',
          label: "Le bien possède-t-il d'autres caractéristiques ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'autres_caracteristiques_liste',
          label: 'Autres caractéristiques',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'moins_10_ans',
              label: 'Il y a moins de 10 ans',
              icon: 'calendar.svg'
            },
            {
              id: 'apres_1997',
              label: 'Après le 1er juillet 1997 (date du permis)',
              icon: 'calendar.svg'
            },
            {
              id: '1949_1997',
              label: 'Après le 1er janvier 1949',
              icon: 'calendar.svg'
            },
            {
              id: 'avant_1949',
              label: 'Avant 1949',
              icon: 'calendar.svg'
            }
          ],
          id: 'construction',
          label: 'Date de construction',
          type: 'SELECT-PICTURES'
        },
        {
          id: 'numero_immatriculation',
          label: "Numéro d'immatriculation du bien",
          type: 'TEXT'
        },
        {
          id: 'capacite_personne',
          label: 'Capacité du bien (personnes maximum)',
          type: 'NUMBER'
        },
        {
          id: 'usage',
          label: 'Le bien a comme usage',
          type: 'TEXT'
        },
        {
          id: 'destination_libre',
          label: 'Destination du bien',
          type: 'TEXT'
        },
        {
          id: 'programme_typologie',
          label: 'Typologie du Bien',
          type: 'TEXT'
        },
        {
          id: 'programme_ief_bien_numero',
          label: 'Numero du bien',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_ief_parking',
          label: 'Le bien comprend-il un parking',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_parking_nombre',
          label: 'Nombre de parking',
          type: 'NUMBER'
        },
        {
          id: 'programme_ief_parking_numero',
          label: 'Numéro du parking',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_ief_cave',
          label: 'Le bien comprend-il une cave',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_ief_cave_numero',
          label: 'Numéro de la cave',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_ief_cellier',
          label: 'Le bien comprend-il un cellier',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_ief_cellier_numero',
          label: 'Numéro du cellier',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_ief_terrasse',
          label: 'Le bien comprend-il une terrasse ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_ief_terrasse_superficie',
          label: 'Superficie de la terrasse',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_ief_jardin',
          label: 'Le bien comprend-il un jardin ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'tourisme',
              label: 'Tourisme'
            },
            {
              id: 'affaire',
              label: 'Affaire'
            },
            {
              id: 'ehpad',
              label: 'EHPAD'
            },
            {
              id: 'etudiant',
              label: 'Etudiante'
            },
            {
              id: 'senior',
              label: 'Sénior'
            }
          ],
          id: 'lmnp_type',
          label: 'Bien en résidence',
          type: 'SELECT'
        },
        {
          id: 'programme_origine_date',
          label: 'Date de l’origine de propriété',
          type: 'DATE'
        },
        {
          id: 'programme_origine_notaire',
          label: 'Nom et prénom du notaire',
          type: 'TEXT'
        },
        {
          id: 'programme_terrain_superficie',
          label: 'Superficie du terrain',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          id: 'cadastre_parcelle_libre',
          label: 'Parcelle cadastrale',
          type: 'TEXT'
        },
        {
          id: 'cadastre_parcelle_section_libre',
          label: 'Section cadastrale',
          type: 'TEXT'
        },
        {
          id: 'cadastre_parcelle_numero_libre',
          label: 'Numero cadastral',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_volume',
          label: 'Volume',
          type: 'SELECT-BINARY'
        },
        {
          id: 'numero_lot_volume',
          label: 'Numéro de volume',
          type: 'TEXT'
        },
        {
          id: 'quartier',
          label: 'Quartier',
          type: 'TEXT'
        },
        {
          children: [
            {
              id: 'cadastre_parcelles_code_departement',
              label: 'Code département',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_code_topad',
              label: 'Code Commune TOPAD',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_code_insee',
              label: 'Code Commune INSEE',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_commune',
              label: 'Nom commune',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_prefixe',
              label: 'Préfixe',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_section',
              label: 'Section',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_parcelle',
              label: 'Parcelle',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_lieudit',
              label: 'Lieudit',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_h',
              label: 'Hectare',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_a',
              label: 'Are',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_c',
              label: 'Centiare',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_cadastre_non_renove',
              label: 'Référence cadastre non rénové',
              type: 'TEXT'
            }
          ],
          id: 'cadastre_parcelles',
          label: 'Ajouter les références cadastrales',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'Section '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_section'
              },
              {
                type: 'TEXT',
                value: ' n° '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_parcelle'
              }
            ],
            max: 1000,
            min: 1,
            type: 'REF_CADASTRALES'
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cadastre_renove',
          label: 'Des anciennes références cadastrales sont-elles à mentionner ?',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'cadastre_parcelles_renove_code_departement',
              label: 'Code département',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_code_topad',
              label: 'Code Commune TOPAD',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_code_insee',
              label: 'Code Commune INSEE',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_commune',
              label: 'Nom commune',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_prefixe',
              label: 'Préfixe',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_section',
              label: 'Section',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_parcelle',
              label: 'Parcelle',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_lieudit',
              label: 'Lieudit',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_h',
              label: 'Hectare',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_renove_a',
              label: 'Are',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_renove_c',
              label: 'Centiare',
              type: 'NUMBER'
            }
          ],
          id: 'cadastre_parcelles_renove',
          label: 'Anciennes Références cadastrales',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'Section '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_renove_section'
              },
              {
                type: 'TEXT',
                value: ' n° '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_renove_parcelle'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cadastre_division',
          label: "Le bien provient-il de la division d'une parcelle plus grande ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cadastre_division_a_venir',
          label: 'Division cadastrale',
          type: 'SELECT-BINARY'
        },
        {
          id: 'cadastre_division_parcelle_mere',
          label: 'Parcelle mère de la division',
          type: 'TEXT'
        },
        {
          id: 'cadastre_division_geometre_nom',
          label: 'Nom du Géomètre ayant effectué la division',
          type: 'TEXT'
        },
        {
          id: 'cadastre_division_geometre_adresse',
          label: 'Adresse du Géomètre ayant effectué la division',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'location_locaux_equipement',
          label: 'Ajouter des locaux et équipements accessoires',
          type: 'SELECT-BINARY'
        },
        {
          id: 'location_locaux_equipement_liste',
          label: 'Liste des locaux et équipements accessoires',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cuve_stockage_petrolier',
          label: 'Cuve de stockage de produits pétroliers',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'monument_historique',
          label: 'Bien situé dans un périmètre de protection des monuments historiques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'proximite_activites_agricoles',
          label: "Proximité d'activités agricoles - industrielles - artisanales - commerciales",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'espace_boise_classe',
          label: 'Espace boisé classé',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sepulture',
          label: 'Présence de sépulture - cimetière',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_parc_eolienne',
          label: "Présence d'un champ d'éoliennes",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'presence_parc_eolienne',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'parc_eolienne_projet_document',
          label: 'Carte des projets éoliens',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'libre',
              label: 'Libre'
            },
            {
              id: 'loue',
              label: 'Loué'
            }
          ],
          id: 'bien_location_statut',
          label: 'Le bien est vendu',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'bien_location_date',
              label: 'Date du contrat de location',
              type: 'DATE'
            },
            {
              id: 'bien_location_nom',
              label: 'Nom du locataire',
              type: 'TEXT'
            },
            {
              id: 'bien_location_depot_garantie_montant',
              label: 'Montant du dépôt de garantie',
              type: 'PRICE'
            }
          ],
          id: '0d49cd69_0777_4b55_a3d3_be30137e2638',
          label: 'Informations sur la location',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'bien_contrat_affichage',
              label: "Conclusion d'un contrat d'affichage",
              type: 'SELECT-BINARY'
            },
            {
              id: 'bien_contrat_affichage_date',
              label: "Date du contrat d'affichage",
              type: 'DATE'
            },
            {
              id: 'bien_contrat_affichage_societe',
              label: "Société bénéficiaire de l'affichage",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'contrat_panneau_voltaique',
              label: 'Présence de panneaux photovoltaïques',
              type: 'SELECT-BINARY'
            },
            {
              id: 'contrat_panneau_voltaique_installateur_nom',
              label: "Désignation de l'installateur des panneaux",
              type: 'TEXT'
            },
            {
              id: 'contrat_panneau_voltaique_installation_date',
              label: "Date d'installation des panneaux",
              type: 'DATE'
            },
            {
              id: 'contrat_panneau_voltaique_mise_en_service_date',
              label: 'Date de mise en service des panneaux',
              type: 'DATE'
            }
          ],
          id: '74abeff8_9f7d_46e7_9861_1bbf476925ab',
          label: "Contrat d'affichage",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'chaudiere_statut',
              label: "Présence d'une chaudière",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'gaz',
                  label: 'Gaz'
                },
                {
                  id: 'fioul',
                  label: 'Fioul'
                },
                {
                  id: 'autre',
                  label: 'Autre'
                }
              ],
              id: 'chaudiere_statut_type',
              label: 'Type de chaudière',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'chaudiere_statut_type',
                    value: 'autre',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'chaudiere_statut_type_autre',
              label: 'Autre type',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'chaudiere_entretien_statut',
              label: 'Entretien chaudière effectué',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'chaudiere_entretien_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'chaudiere_entretien_attestation',
              label: 'Attestation entretien chaudière',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'chaudiere_entretien_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'chaudiere_entretien_facture',
              label: 'Facture entretien chaudière',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'chaudiere_entretien_vendeur',
              label: 'Entretien effectué avant la vente',
              type: 'SELECT-BINARY'
            }
          ],
          id: '091a3869_16b3_4f9a_90bf_f7bc178097ff',
          label: 'Chaudière',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cuve_fioul_statut',
              label: 'Cuve à fioul',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'operationnelle',
                  label: 'Opérationnelle'
                },
                {
                  id: 'non_neutralisee',
                  label: 'Non fonctionnelle et non neutralisée'
                },
                {
                  id: 'neutralisee',
                  label: 'Non fonctionnelle et neutralisée'
                }
              ],
              id: 'cuve_fioul_etat',
              label: 'La cuve à fioul est',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cuve_fioul_anomalies',
              label: 'Anomalies de la cuve à relater',
              type: 'SELECT-BINARY'
            },
            {
              id: 'cuve_fioul_anomalies_liste',
              label: 'Liste des anomalies',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cuve_fioul_proprietaire',
              label: 'Cuve appartenant au Vendeur',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cuve_fioul_contrat',
              label: "Contrat d'approvisionnement",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cuve_fioul_statut_remboursement',
              label: "Remboursement du fioul restant par l'acquéreur",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'cuve_fioul_contrat',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'cuve_fioul_contrat_document',
              label: "Contrat d'approvisionnement - Fioul",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                    ]
                  }
                ]
              ],
              id: 'cuve_fioul_precisions',
              label: 'Ajouter une précision sur la cuve à fioul',
              type: 'SELECT-BINARY'
            },
            {
              id: 'cuve_fioul_precisions_texte',
              label: 'Précisions sur la cuve à fioul',
              type: 'TEXTAREA'
            }
          ],
          id: 'd6c14d1b_2070_4bc6_8706_db77596510fe',
          label: 'Cuve',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'servitudes',
              label: 'Présence de servitudes',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'servitudes_note',
              label: 'Les servitudes sont-elles reprises dans une note annexée ?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'servitude_type',
              label: 'Type de servitude',
              type: 'TEXT'
            },
            {
              id: 'servitude_notaire_nom',
              label: 'Nom du notaire ayant reçu la servitude',
              type: 'TEXT'
            },
            {
              id: 'servitude_notaire_ville',
              label: 'Ville du notaire',
              type: 'TEXT'
            },
            {
              id: 'servitude_notaire_date',
              label: 'Date de constitution de la servitude',
              type: 'DATE'
            },
            {
              id: 'servitudes_reprise_litterale',
              label: 'Reprise littérale de la servitude',
              type: 'TEXTAREA'
            }
          ],
          id: '3a721c92_1370_4b72_8ee5_088496dcf56b',
          label: 'Servitudes',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_superficie_statut',
          label: 'La superficie du Bien est-elle à indiquer dans la Réservation ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_superficie',
          label: 'Superficie du Bien',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          id: 'enseigne_nom',
          label: 'Nom / Enseigne',
          type: 'TEXT'
        },
        {
          id: 'activite',
          label: 'Activité exercée',
          type: 'TEXT'
        },
        {
          id: 'programme_niveau',
          label: 'Niveau',
          type: 'TEXT'
        },
        {
          id: 'programme_affectation',
          label: 'Affectation',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          id: 'programme_lineaire_vitrine',
          label: 'Linéaire Vitrine',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'mandat_superficie_mandant',
              label: 'Le Mandant'
            },
            {
              id: 'mandat_superficie_mandataire',
              label: 'Le Mandataire'
            },
            {
              id: 'mandat_superficie_diagnostiqueur',
              label: 'Un Diagnostiqueur'
            }
          ],
          id: 'mandat_superficie_fourniture',
          label: 'La superficie est établie par',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'regime_fiscal_location',
          label: 'Régime fiscal spécifique en place',
          type: 'SELECT-BINARY'
        },
        {
          id: 'regime_fiscal_location_liste',
          label: 'Régime fiscal utilisé',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'conventionnement_statut',
          label: "Bien objet d'un conventionnement",
          type: 'SELECT-BINARY'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'annexes_complementaires',
          label: 'Annexes complémentaires',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'habitation',
              label: 'Habitation'
            },
            {
              id: 'mixte',
              label: 'Mixte'
            },
            {
              id: 'commercial',
              label: 'Commercial'
            },
            {
              id: 'professionnel',
              label: 'Professionnel'
            }
          ],
          id: 'usage_gestion',
          label: 'Usage du bien',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'meublee_statut',
          label: 'Bien meublé',
          type: 'SELECT-BINARY'
        }
      ],
      id: '53717f04_8e1f_4aed_a771_03f3c42fdc62',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'asl_presence',
          label: "Présence d'une ASL ou AFUL",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'asl',
              label: 'Association syndicale libre'
            },
            {
              id: 'aful',
              label: 'Association foncière urbaine libre'
            }
          ],
          id: 'asl_aful_binary',
          label: "Type d'association",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'asl_presence_active',
          label: 'ASL ou AFUL encore active',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'f05e0dab_0f7d_4ecb_858a_d1b975583ebb',
      label: 'ASL / AFUL',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'zac_presence',
          label: "Présence d'une ZAC",
          type: 'SELECT-BINARY'
        },
        {
          id: 'zac_denomination',
          label: 'Nom de la ZAC',
          type: 'TEXT'
        },
        {
          id: 'zac_titre',
          label: 'Extrait du titre de propriété relatif à cette ZAC',
          type: 'TEXT'
        }
      ],
      id: '8f1fcd5e_4788_4a22_a1d9_f4f9f20825a8',
      label: "Zone d'aménagement concernée",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'chaudiere_gaz',
                  label: 'Chaudière Gaz'
                },
                {
                  id: 'chaudiere_fioul',
                  label: 'Chaudière Fioul'
                },
                {
                  id: 'cheminee',
                  label: 'Cheminée'
                },
                {
                  id: 'poele_bois',
                  label: 'Poêle à bois'
                },
                {
                  id: 'poele_granules',
                  label: 'Poêle à granulés'
                },
                {
                  id: 'radiateur',
                  label: 'Convecteurs électriques'
                },
                {
                  id: 'pompe',
                  label: 'Pompe à Chaleur'
                },
                {
                  id: 'aucun',
                  label: 'Aucun système de chauffage'
                }
              ],
              id: 'systeme_chauffage_liste',
              label: 'Système de chauffage',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'systeme_chauffage_entretien',
              label: 'Ramonage / Entretien effectué',
              type: 'SELECT-BINARY'
            },
            {
              id: 'systeme_chauffage_entretien_description',
              label: 'Description du dispositif',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'systeme_chauffage_fonctionnement',
              label: 'Dispositif en fonctionnement',
              type: 'SELECT-BINARY'
            },
            {
              id: 'systeme_chauffage_intervention_nom',
              label: "Nom de l'entreprise intervenue",
              type: 'TEXT'
            },
            {
              id: 'systeme_chauffage_intervention_date',
              label: "Date de l'intervention",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'systeme_chauffage_anomalies_vendeur',
              label: 'Prise en charge des anomalies par le Vendeur',
              type: 'SELECT-BINARY'
            }
          ],
          id: '3ed234ae_ae6e_41b4_b262_74c0706432e5',
          label: 'Chauffage',
          type: 'CATEGORY'
        }
      ],
      id: 'e915ac03_ae5b_407b_b189_3d156816d20c',
      label: "Eléments d'équipement",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              id: 'origine_propriete_origine_proprietaire',
              label: 'Noms des propriétaires et biens concernés par cette origine de propriété',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'origine_propriete_acquisition',
                  label: 'Acquisition'
                },
                {
                  id: 'origine_propriete_donation',
                  label: 'Donation'
                },
                {
                  id: 'origine_propriete_succession',
                  label: 'Succession'
                },
                {
                  id: 'origine_propriete_partage',
                  label: 'Partage - Licitation'
                },
                {
                  id: 'origine_propriete_echange',
                  label: 'Echange'
                },
                {
                  id: 'origine_propriete_adjudication',
                  label: 'Adjudication - Vente aux enchères'
                },
                {
                  id: 'origine_propriete_remembrement',
                  label: 'Remembrement'
                }
              ],
              id: 'origine_propriete_origine_liste',
              label: 'Le bien a été reçu par',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'origine_propriete_acquisition',
                  label: 'Acquisition'
                },
                {
                  id: 'origine_propriete_vefa',
                  label: 'VEFA'
                },
                {
                  id: 'origine_propriete_donation',
                  label: 'Donation'
                },
                {
                  id: 'origine_propriete_succession',
                  label: 'Succession'
                },
                {
                  id: 'origine_propriete_partage',
                  label: 'Partage - Licitation'
                },
                {
                  id: 'origine_propriete_echange',
                  label: 'Echange'
                },
                {
                  id: 'origine_propriete_adjudication',
                  label: 'Adjudication - Vente aux enchères'
                },
                {
                  id: 'origine_propriete_remembrement',
                  label: 'Remembrement'
                }
              ],
              id: 'origine_propriete_origine_liste_giboire',
              label: 'Le bien a été reçu par',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_donation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_donation_unique',
              label: 'Propriétaire enfant unique',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'origine_propriete_origine_donation_donateur_decede',
              label: 'Donateur décédé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_donation_unique',
                    value: 'non',
                    type: 'EQUALS'
                  },
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_donation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_donation_deces',
              label: 'Succession du donateur reglée',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_succession_statut',
              label: 'Succession déjà réglée',
              type: 'SELECT-BINARY'
            },
            {
              id: 'origine_propriete_origine_vendeur_nom',
              label: 'Nom des Vendeurs',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_notaire_nom',
              label: 'Nom du notaire',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_notaire_ville',
              label: 'Lieu de résidence du notaire',
              type: 'TEXT',
              uppercase: 'WORD'
            },
            {
              id: 'origine_propriete_origine_defunt_prenom',
              label: 'Prénom du défunt',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_defunt_nom',
              label: 'Nom du défunt',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_defunt_date',
              label: 'Date du décès',
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste_giboire',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_defunt_nom_simple',
              label: 'Nom du défunt',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste_giboire',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_defunt_date_simple',
              label: 'Date de decès',
              type: 'DATE'
            },
            {
              id: 'origine_propriete_origine_date',
              label: "Date de l'acte",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'origine_propriete_origine_publication',
              label: 'Acte publié',
              type: 'SELECT-BINARY'
            },
            {
              id: 'origine_propriete_origine_spf',
              label: 'Service de publicité foncière',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_spf_date',
              label: 'Date de publication',
              type: 'DATE'
            },
            {
              id: 'origine_propriete_origine_volume',
              label: 'Volume',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_volume_numero',
              label: 'Numéro de Volume',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_annee_construction',
              label: 'Année des constructions:',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_adjudication',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_tribunal',
              label: 'Ville du tribunal',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_acquisition',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_acquisition',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_echange',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_echange',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_donation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_donation',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_partage',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_partage',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  },
                  {
                    id: 'origine_propriete_origine_succession_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_succession_pas_reglee',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  },
                  {
                    id: 'origine_propriete_origine_succession_statut',
                    value: 'non',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_succession',
              label: 'Titre de propriété - Attestation de dévolution',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_adjudication',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_adjudication',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_remembrement',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_remembrement',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            }
          ],
          id: 'origine_propriete',
          label: 'Ajouter une origine de propriété',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: ' '
              },
              {
                type: 'VARIABLE',
                value: 'origine_propriete_origine_liste'
              },
              {
                type: 'TEXT',
                value: ' - '
              },
              {
                type: 'VARIABLE',
                value: 'origine_propriete_origine_date'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'scaprim_garage',
          label: 'Le Bien comprend-il un garage attenant ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'scaprim_jardin',
          label: 'Le Bien comprend-il un jardin attenant ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'montant_taxe_fonciere',
          label: 'Montant de la dernière taxe foncière',
          type: 'PRICE'
        },
        {
          id: 'montant_taxe_fonciere_cfp',
          label: 'Montant de la dernière taxe foncière',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'annee_taxe_fonciere',
          label: 'Année de la dernière taxe foncière',
          type: 'YEAR'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'contrats_attaches_statut',
          label: 'Contrats attachés au bien',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'contrat_attaches_list_contrat_attaches_type',
              label: 'Contrat',
              type: 'TEXT'
            },
            {
              id: 'contrat_attaches_list_contrat_attaches_prix',
              label: 'Montant',
              type: 'PRICE'
            },
            {
              id: 'contrat_attaches_list_contrat_attaches_prix_cfp',
              label: 'Montant',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'contrat_frequence_annuelle',
                  label: 'Annuellement'
                },
                {
                  id: 'contrat_frequence_mensuelle',
                  label: 'Mensuellement'
                }
              ],
              id: 'contrat_attaches_list_contrat_attaches_frequence',
              label: 'Fréquence',
              type: 'SELECT'
            },
            {
              id: 'contrat_attaches_list_contrat_attaches_document',
              label: 'Contrat',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'contrats_attaches_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'contrat_attaches_list',
          label: 'Contrats',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'contrat_attaches_list_contrat_attaches_type'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'servitudes',
          label: 'Servitudes',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'servitudes',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'servitudes_liste',
          label: 'Liste des servitudes',
          type: 'TEXTAREA'
        }
      ],
      id: '97a2078a_9383_4866_a87b_b56f808bb81e',
      label: 'Origine de propriété',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            'immeubles situés dans des zones spécifiques (zone protégée par un plan de sauvegarde et de mise en valeur; monuments historiques...) pouvant impliquer des contraintes au pour travaux intérieurs et extérieurs.',
          id: 'situation_particuliere',
          label: 'Ajouter une situation particulière',
          type: 'SELECT-BINARY'
        },
        {
          description: 'Reprendre l’article correspondant dans le titre de propriété',
          id: 'situation_particuliere_description',
          label: 'Description de la situation particulière',
          type: 'TEXT'
        }
      ],
      id: '7642b3a2_6a37_486b_a38b_b78c0d618a34',
      label: 'Règlementations spéciales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dernier_decompte_charges',
          label: 'Dernier décompte de charges',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'occupation_mandat_libre',
              label: 'Libre de toute occupation'
            },
            {
              id: 'occupation_mandat_liberation',
              label: 'Libre à la date'
            },
            {
              id: 'occupation_mandat_loue',
              label: 'Loué selon état locatif figurant en annexe'
            }
          ],
          id: 'occupation_mandat',
          label: 'Le jour de la vente définitive, le bien sera',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'occupation_mandat',
                value: 'occupation_mandat_liberation',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'occupation_mandat_liberation',
          label: 'Date de libération du bien',
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'occupation_mandat',
                value: 'occupation_mandat_loue',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_bail',
          label: 'Contrat de bail',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: "Indiquer 'oui' si le locataire est présent ou déjà parti",
          id: 'occupation_statut',
          label: "Présence d'un locataire",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Occupé à titre gratuit',
                  id: 'occupation_gratuit'
                },
                {
                  label: 'Loué selon un contrat de bail',
                  id: 'location_bail'
                }
              ],
              id: 'occupation_location',
              label: 'Le bien est (ou a été)',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'occupation_location',
                    type: 'EQUALS',
                    value: 'occupation_gratuit'
                  },
                  {
                    id: 'occupation_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'occupation_liste',
              label: 'Liste des occupants',
              type: 'TEXTAREA'
            },
            {
              children: [
                {
                  children: [
                    {
                      choices: [
                        {
                          label: 'Un bail professionnel',
                          id: 'professionnel'
                        },
                        {
                          label: 'Un bail commercial',
                          id: 'commercial'
                        },
                        {
                          label: 'Un bail simple',
                          id: 'simple'
                        },
                        {
                          label: 'Un bail habitation loi 1948',
                          id: '1948'
                        },
                        {
                          label: 'Un bail rural',
                          id: 'rural'
                        },
                        {
                          label: 'Autre type de bail',
                          id: 'bail_autre'
                        }
                      ],
                      id: 'location_bail_liste_location_bail_type',
                      label: 'Type de bail',
                      type: 'SELECT'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'location_bail_liste_location_bail_type',
                            type: 'EQUALS',
                            value: 'bail_autre'
                          },
                          {
                            id: 'occupation_location',
                            type: 'EQUALS',
                            value: 'location_bail'
                          },
                          {
                            id: 'occupation_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'location_bail_liste_location_bail_type_autre',
                      label: 'Précisez le type de bail',
                      type: 'TEXT'
                    },
                    {
                      id: 'location_bail_liste_location_bail_date_signature',
                      label: 'Date de signature du bail',
                      type: 'DATE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_locataires',
                      label: 'Liste des locataires',
                      type: 'TEXTAREA'
                    },
                    {
                      id: 'location_bail_liste_location_bail_loyer',
                      label: 'Montant du loyer',
                      type: 'PRICE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_charges',
                      label: 'Montant des charges',
                      type: 'PRICE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_loyer_cfp',
                      label: 'Montant du loyer',
                      suffix: 'CFP',
                      type: 'PRICE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_charges_cfp',
                      label: 'Montant des charges',
                      suffix: 'CFP',
                      type: 'PRICE'
                    },
                    {
                      id: 'location_bail_liste_contrat_bail',
                      label: 'Contrat de bail',
                      type: 'UPLOAD'
                    }
                  ],
                  id: 'location_bail_liste',
                  label: 'Baux',
                  repetition: {
                    label: [
                      {
                        type: 'VARIABLE',
                        value: 'location_bail_liste_location_bail_locataires'
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                },
                {
                  choices: [
                    {
                      label: 'Sur la totalité du bien',
                      id: 'location_totale'
                    },
                    {
                      label: 'Sur une partie du bien seulement',
                      id: 'location_partielle'
                    }
                  ],
                  id: 'location_partielle_statut',
                  label: 'La location porte',
                  type: 'SELECT'
                }
              ],
              conditions: [
                [
                  {
                    id: 'occupation_location',
                    type: 'EQUALS',
                    value: 'location_bail'
                  },
                  {
                    id: 'occupation_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '822224fa_034c_41dc_b023_d9c341c82c2e',
              label: 'Information sur les baux',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'occupation_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '6a206d5b_856f_4781_b4ab_349a74729875',
          label: 'Occupation du bien',
          type: 'CATEGORY'
        }
      ],
      id: '6f76981d_6ac1_478b_811e_7f37fd764d49',
      label: 'Situation locative',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            recordOnly: true
          },
          id: 'external_cadastre_situation',
          label: 'Plan Cadastral',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'titre_propriete_libre',
          label: 'Titre de propriété',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'etat_recette_depense',
          label: "Dépenses des 12 derniers mois et recettes et dépenses depuis le début d'année",
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'taxe_fonciere',
          label: 'Dernière taxe foncière',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'ddt_libre_doc',
          label: 'Dossier de diagnostics techniques',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'plans_biens',
          label: 'Plan des biens',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'courrier_gestion',
          label: 'Courriers (locataire,syndic,adminsitration...)',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'servitudes',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'servitude_acte',
          label: 'Rappel de servitude',
          type: 'UPLOAD'
        }
      ],
      id: '29df673f_2e8e_4e30_9bdb_1835c7e7219a',
      label: 'Documents Généraux',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_a_jour',
          label: 'Les diagnostics sont à jour',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostics_resultats',
          label: 'Résultat des diagnostics indiqué au compromis',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostics_techniques_diagnostiqueur_unique',
              label: 'Diagnostiqueur unique',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostics_techniques_diagnostiqueur_unique_date',
              label: 'Date unique',
              type: 'SELECT-BINARY'
            },
            {
              id: 'diagnostics_techniques_domofrance_date',
              label: "Date d'établissement du dossier de diagnostic technique",
              type: 'DATE'
            },
            {
              id: 'diagnostics_techniques_diagnostiqueur_unique_nom',
              label: 'Nom du diagnostiqueur',
              type: 'TEXT'
            },
            {
              id: 'diagnostics_techniques_diagnostiqueur_unique_societe',
              label: 'Société du diagnostiqueur',
              type: 'TEXT'
            },
            {
              id: 'diagnostics_techniques_diagnostiqueur_unique_adresse',
              label: 'Adresse du diagnostiqueur',
              type: 'ADDRESS'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'attestation_diagnostiqueur',
              label: 'Attestation du diagnostiqueur',
              type: 'UPLOAD'
            }
          ],
          id: '6da57b54_5d47_422a_9ec7_2a42cce094b4',
          label: 'Diagnostiqueur',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'diag_mls_present',
                  label: 'Le Mandant remet le diagnostic à la signature du mandat'
                },
                {
                  id: 'diag_mls_mandant',
                  label: 'Le Mandant établit le diagnostic avant le compromis'
                },
                {
                  id: 'diag_mls_mandataire',
                  label: "Le Mandant charge le Mandataire d'établir le diagnostic"
                }
              ],
              id: 'amiante_mandat_mls',
              label: 'Concernant le diagnostic amiante',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_amiante_statut',
              label: 'Diagnostic réalisé',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  conditionalTitles: [
                    {
                      conditions: [
                        [
                          {
                            type: 'EQUALS_TEMPLATES',
                            templates: ['OPERATION__CDC__IMMOBILIER__PROGRAMME', 'OPERATION__CDC__IMMOBILIER__VENTE']
                          }
                        ]
                      ],
                      title: 'Date du diagnostic (laisser vide si diag unique)'
                    }
                  ],
                  id: 'diagnostic_amiante_date',
                  label: 'Date du diagnostic',
                  type: 'DATE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_amiante_resultat_simple',
                  label: "Le diagnostic fait apparaitre la présence d'amiante",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      label: "Il n'a pas été repéré des matériaux contenant de l'amiante",
                      id: 'absence'
                    },
                    {
                      label: "Il a été repéré des matériaux contenant de l'amiante de la liste A",
                      id: 'liste_a'
                    },
                    {
                      label: "Il a été repéré des matériaux contenant de l'amiante de la liste B",
                      id: 'liste_b'
                    },
                    {
                      label: "Des locaux ou parties de locaux n'ont pas pu être visités",
                      id: 'non_visite'
                    }
                  ],
                  id: 'diagnostic_amiante_resultat',
                  label: 'Résultat du diagnostic',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  id: 'diagnostic_amiante_resultat_libre',
                  label: 'Résultat du diagnostic',
                  type: 'TEXTAREA'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'construction',
                        value: 'avant_1949',
                        type: 'EQUALS'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        value: 'avant_1949',
                        type: 'EQUALS'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        value: 'avant_1949',
                        type: 'EQUALS'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        value: 'avant_1949',
                        type: 'EQUALS'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'diagnostic_amiante',
                  label: 'Diagnostic Amiante',
                  type: 'UPLOAD'
                },
                {
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'diagnostic_amiante_libre',
                  label: 'Diagnostic Amiante',
                  type: 'UPLOAD'
                }
              ],
              id: 'fac804e4_d1ca_4525_9bc5_20677abbc0ca',
              label: 'CONDITION_BLOCK_Informations sur le diagnostic amiante',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  conditionalTitles: [
                    {
                      conditions: [
                        [
                          {
                            type: 'EQUALS_TEMPLATES',
                            templates: ['OPERATION__CDC__IMMOBILIER__PROGRAMME', 'OPERATION__CDC__IMMOBILIER__VENTE']
                          }
                        ]
                      ],
                      title: 'Nom du diagnostiqueur (laisser vide si diag unique)'
                    }
                  ],
                  id: 'diagnostic_amiante_diagnostiqueur_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostics_techniques_diagnostiqueur_amiante_societe',
                  label: 'Société du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostic_amiante_diagnostiqueur_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: '1949_1997'
                  },
                  {
                    id: 'diagnostics_techniques_diagnostiqueur_unique',
                    type: 'EQUALS',
                    value: 'non'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: '1949_1997'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                      'OPERATION__CDC__IMMOBILIER__VENTE',
                      'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                    ]
                  }
                ],
                [
                  {
                    id: 'construction',
                    value: 'avant_1949',
                    type: 'EQUALS'
                  },
                  {
                    id: 'diagnostics_techniques_diagnostiqueur_unique',
                    type: 'EQUALS',
                    value: 'non'
                  }
                ],
                [
                  {
                    id: 'construction',
                    value: 'avant_1949',
                    type: 'EQUALS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                      'OPERATION__CDC__IMMOBILIER__VENTE',
                      'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                    ]
                  }
                ],
                [
                  {
                    id: 'diagnostics_techniques_diagnostiqueur_unique',
                    type: 'EQUALS',
                    value: 'non'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                      'OPERATION__CDC__IMMOBILIER__VENTE',
                      'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                    ]
                  }
                ]
              ],
              id: '187dfad9_a27b_4dbb_b293_86f8b1323042',
              label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
              type: 'CONDITION_BLOCK'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              }
            ],
            [
              {
                id: 'construction',
                value: 'avant_1949',
                type: 'EQUALS'
              }
            ],
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
              }
            ]
          ],
          id: '16d57586_797e_466e_9bd4_e34ac048e762',
          label: 'Diagnostic Amiante',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_termites_commune',
              label: 'Commune concernée par les termites',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_termites_informatif_effectue',
              label: 'Diagnostic termites tout de même réalisé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'diag_mls_present',
                  label: 'Le Mandant remet le diagnostic à la signature du mandat'
                },
                {
                  id: 'diag_mls_mandant',
                  label: 'Le Mandant établit le diagnostic avant le compromis'
                },
                {
                  id: 'diag_mls_mandataire',
                  label: "Le Mandant charge le Mandataire d'établir le diagnostic"
                }
              ],
              id: 'termites_mandat_mls',
              label: 'Concernant le diagnostic termites',
              type: 'SELECT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_termites_statut',
                  label: 'Diagnostic réalisé',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      conditionalTitles: [
                        {
                          conditions: [
                            [
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE'
                                ]
                              }
                            ]
                          ],
                          title: 'Date du diagnostic (laisser vide si diag unique)'
                        }
                      ],
                      id: 'diagnostic_termites_date',
                      label: 'Date du diagnostic',
                      type: 'DATE'
                    },
                    {
                      choices: [
                        {
                          label: "Absence de traces d'infestation de termite",
                          id: 'absence'
                        },
                        {
                          label: "Présence de traces d'infestation de termite",
                          id: 'presence'
                        }
                      ],
                      id: 'diagnostic_termites_resultat',
                      label: 'Résultat du diagnostic',
                      type: 'SELECT'
                    },
                    {
                      id: 'diagnostic_termites_details_termites_resultat',
                      label: 'Résultat du diagnostic',
                      type: 'TEXTAREA'
                    },
                    {
                      children: [
                        {
                          conditionalTitles: [
                            {
                              conditions: [
                                [
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                      'OPERATION__CDC__IMMOBILIER__VENTE'
                                    ]
                                  }
                                ]
                              ],
                              title: 'Nom du diagnostiqueur (laisser vide si diag unique)'
                            }
                          ],
                          id: 'diagnostic_termites_diagnostiqueur_nom',
                          label: 'Nom du diagnostiqueur',
                          type: 'TEXT'
                        },
                        {
                          id: 'diagnostics_techniques_diagnostiqueur_termites_societe',
                          label: 'Société du diagnostiqueur',
                          type: 'TEXT'
                        },
                        {
                          id: 'diagnostic_termites_diagnostiqueur_adresse',
                          label: 'Adresse du diagnostiqueur',
                          type: 'ADDRESS'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'diagnostics_techniques_diagnostiqueur_unique',
                            type: 'EQUALS',
                            value: 'non'
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_TEMPLATES',
                            templates: [
                              'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                              'OPERATION__CDC__IMMOBILIER__VENTE',
                              'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                            ]
                          }
                        ]
                      ],
                      id: 'cf567502_17e6_455d_a767_745a5bde53e9',
                      label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                      type: 'CONDITION_BLOCK'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'diagnostic_termites_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ]
                      ],
                      filters: {
                        mustBeExcludedInOperationConfig: true
                      },
                      id: 'diagnostic_termites',
                      label: 'Diagnostic Termites',
                      type: 'UPLOAD'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'diagnostic_termites_commune',
                            value: 'oui',
                            type: 'EQUALS'
                          },
                          {
                            type: 'DIFFERENT_TEMPLATES',
                            templates: ['OPERATION__AQUITANIS__VENTE_ANCIEN', 'OPERATION__AQUITANIS__PROGRAMME_ANCIEN']
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_TEMPLATES',
                            templates: ['OPERATION__AQUITANIS__VENTE_ANCIEN', 'OPERATION__AQUITANIS__PROGRAMME_ANCIEN']
                          }
                        ]
                      ],
                      filters: {
                        mustBeIncludedInOperationConfig: true
                      },
                      id: 'diagnostic_termites_libre',
                      label: 'Diagnostic Termites',
                      type: 'UPLOAD'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'programme_zone_termites',
                            recordPath: [
                              'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME',
                              'CONDITIONS_GENERALES',
                              '0'
                            ],
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'diagnostic_termites_programme',
                      label: 'Diagnostic Termites',
                      type: 'UPLOAD'
                    }
                  ],
                  id: '9832495f_5458_45ff_bc01_8fd54ea55188',
                  label: 'Informations sur le diagnostic termites',
                  type: 'CATEGORY'
                }
              ],
              id: '98ba5f54_7544_44bd_b2d6_64ed735f6abc',
              label: 'CONDITION_BLOCK_Zone concernée par les termites',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_termites_informatif',
                  label: 'Contamination par les termites',
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'ecacae2e_84a5_4128_a387_4ac56f047da5',
              label: 'CONDITION_BLOCK_Zone non concernée par les termites',
              type: 'CONDITION_BLOCK'
            }
          ],
          id: 'ea87f61f_8e3b_47b0_b498_acd173ded164',
          label: 'Diagnostic Termites',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'diag_mls_present',
                  label: 'Le Mandant remet le diagnostic à la signature du mandat'
                },
                {
                  id: 'diag_mls_mandant',
                  label: 'Le Mandant établit le diagnostic avant le compromis'
                },
                {
                  id: 'diag_mls_mandataire',
                  label: "Le Mandant charge le Mandataire d'établir le diagnostic"
                }
              ],
              id: 'dpe_mandat_mls',
              label: 'Concernant le DPE',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              description: 'En cas de DPE Vierge la réponse doit être non',
              id: 'dpe_details_statut',
              label: 'Diagnostic réalisé',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'dpe_diagnostiqueur_date',
                  label: 'Date du diagnostic',
                  type: 'DATE'
                },
                {
                  id: 'dpe_consommation_energie',
                  label: 'Montant de la consommation énergétique',
                  type: 'TEXT'
                },
                {
                  description:
                    "Indiquer d'abord la lettre puis éventuellement le montant de la consommation. Exemple : E - 355",
                  id: 'dpe_consommation_energetique',
                  label: 'Performance / consommation énergétique',
                  type: 'TEXT'
                },
                {
                  id: 'dpe_consommation_ges',
                  label: 'Montant du rejet de gaz à effet de serre',
                  type: 'TEXT'
                },
                {
                  id: 'dpe_emission_gaz',
                  label: 'Emission de gaz à effet de serre',
                  type: 'TEXT'
                },
                {
                  id: 'dpe_depense_annuelle',
                  label: "Montant des dépenses annuelles d'énergie",
                  type: 'TEXT'
                },
                {
                  id: 'dpe_depense_annuelle_basse',
                  label: "Montant minimum des dépenses annuelles d'énergie",
                  type: 'PRICE'
                },
                {
                  id: 'dpe_depense_annuelle_haute',
                  label: "Montant maximum des dépenses annuelles d'énergie",
                  type: 'PRICE'
                },
                {
                  id: 'dpe_depense_annuelle_basse_cfp',
                  label: "Montant minimum des dépenses annuelles d'énergie",
                  suffix: 'CFP',
                  type: 'PRICE'
                },
                {
                  id: 'dpe_depense_annuelle_haute_cfp',
                  label: "Montant maximum des dépenses annuelles d'énergie",
                  suffix: 'CFP',
                  type: 'PRICE'
                },
                {
                  id: 'dpe_depense_annuelle_annee',
                  label: "Année(s) de référence pour les dépenses d'énergie",
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'dpe_audit_realise',
                  label: 'Audit énergétique réalisé',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'dpe_audit_date',
                  label: "Date de l'audit énergétique",
                  type: 'DATE'
                },
                {
                  id: 'dpe_audit_diagnostiqueur',
                  label: "Nom du diagnostiqueur ayant réalisé l'audit",
                  type: 'TEXT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'dpe_audit_realise',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'dpe_audit_document',
                  label: 'Audit énergétique',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'dpe_details_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'dpe',
                  label: 'Diagnostic de performance énergétique',
                  type: 'UPLOAD'
                },
                {
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'dpe_libre',
                  label: 'Diagnostic de performance énergétique',
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'dpe_40_m2_statut',
                  label: 'Surface du bien inférieure ou égale à 40m2',
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'dpe_40_m2_statut',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'dpe_40_m2_attestation',
                  label: 'Attestation ADEME',
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'dpe_40_m2_modification',
                  label: 'Modification des résultats du DPE',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'dpe_40_m2_modification_energie',
                  label: 'Etiquette énergie',
                  type: 'TEXT'
                },
                {
                  id: 'dpe_40_m2_modification_gaz',
                  label: 'Etiquette Gaz',
                  type: 'TEXT'
                },
                {
                  children: [
                    {
                      id: 'dpe_diagnostiqueur_nom',
                      label: 'Nom du diagnostiqueur',
                      type: 'TEXT'
                    },
                    {
                      id: 'diagnostics_techniques_diagnostiqueur_dpe_societe',
                      label: 'Société du diagnostiqueur',
                      type: 'TEXT'
                    },
                    {
                      id: 'dpe_diagnostiqueur_adresse',
                      label: 'Adresse du diagnostiqueur',
                      type: 'ADDRESS'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ]
                  ],
                  id: '62e70310_9d3f_4229_9157_d66d9011af8a',
                  label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                  type: 'CONDITION_BLOCK'
                }
              ],
              id: '81af063e_c82e_41e1_b727_622fec2ad031',
              label: 'CONDITION_BLOCK_Informations sur le diagnostic de performance énergétique',
              type: 'CONDITION_BLOCK'
            }
          ],
          id: '152010f2_0e1e_4d8b_b0ca_98f5c77ee59b',
          label: 'Diagnostic de performance Énergétique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'erp_global',
              label:
                'Bien situé dans une zone couverte par un plan de prévention des risques naturels, miniers ou technologiques',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'diag_mls_present',
                  label: 'Le Mandant remet le diagnostic à la signature du mandat'
                },
                {
                  id: 'diag_mls_mandant',
                  label: 'Le Mandant établit le diagnostic avant le compromis'
                },
                {
                  id: 'diag_mls_mandataire',
                  label: "Le Mandant charge le Mandataire d'établir le diagnostic"
                }
              ],
              id: 'erp_mandat_mls',
              label: "Concernant l'état des risques",
              type: 'SELECT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_pprn_commune',
                  label: 'Commune concernée par un plan de prévention des risques naturels prescrit ou approuvé',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_pprn_bien',
                  label: 'Bien concerné par un plan de prévention des risques naturels prescrit ou approuvé',
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'erp_pprn_bien',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'erp_pprn_zonage',
                  label: 'Plan des risques naturels - Zonage règlementaire',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'erp_pprn_bien',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'erp_pprn_extrait_reglement',
                  label: 'Plan des risques naturels  - Extrait du règlement',
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      label: 'Zone non concernée par un plan de prévention des risques naturels',
                      id: 'aucun_plan'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques naturels prescrits",
                      id: 'plan_prescrit'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques naturels anticipés",
                      id: 'plan_anticipe'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques naturels approuvés",
                      id: 'plan_approuve'
                    }
                  ],
                  id: 'ppr_naturels_liste',
                  label: 'Risques naturels',
                  multiple: true,
                  type: 'SELECT'
                }
              ],
              id: 'b564f397_3908_469a_9f6e_d8920937c77f',
              label: 'CONDITION_BLOCK_Plan de prévention des risques naturels',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_pprm_commune',
                  label: 'Commune concernée par un plan de prévention des risques miniers prescrit ou approuvé',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_pprm_bien',
                  label: 'Bien concerné par un plan de prévention des risques miniers prescrit ou approuvé',
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'erp_pprm_bien',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'erp_pprm_zonage',
                  label: 'Plan des risques miniers - Zonage règlementaire',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'erp_pprm_bien',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'erp_pprm_extrait_reglement',
                  label: 'Plan des risques miniers  - Extrait du règlement',
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      label: 'Zone non concernée par un plan de prévention des risques miniers',
                      id: 'aucun_plan'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques miniers prescrits",
                      id: 'plan_prescrit'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques miniers anticipés",
                      id: 'plan_anticipe'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques miniers approuvés",
                      id: 'plan_approuve'
                    }
                  ],
                  id: 'ppr_miniers_liste',
                  label: 'Risques miniers',
                  multiple: true,
                  type: 'SELECT'
                }
              ],
              id: '674a027f_0083_4018_abf5_a37b26180048',
              label: 'CONDITION_BLOCK_Plan de prévention des risques miniers',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_pprt_commune',
                  label: 'Commune concernée par un plan de prévention des risques technologiques prescrit ou approuvé',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_pprt_bien',
                  label: 'Bien concerné par un plan de prévention des risques technologiques prescrit ou approuvé',
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'erp_pprt_bien',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'erp_pprt_zonage',
                  label: 'Plan des risques technologiques - Zonage règlementaire',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'erp_pprt_bien',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'erp_pprt_extrait_reglement',
                  label: 'Plan des risques technologiques  - Extrait du règlement',
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      label: 'Zone non concernée par un plan de prévention des risques technologiques',
                      id: 'aucun_plan'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques technologiques prescrits",
                      id: 'plan_prescrit'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques technologiques anticipés",
                      id: 'plan_anticipe'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques technologiques approuvés",
                      id: 'plan_approuve'
                    }
                  ],
                  id: 'ppr_technologiques_liste',
                  label: 'Risques technologiques',
                  multiple: true,
                  type: 'SELECT'
                }
              ],
              id: '1213d837_455d_4b37_958d_3dba318e54e6',
              label: 'CONDITION_BLOCK_Plan de prévention des risques technologiques',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_sis_statut',
                  label: "Bien situé dans un secteur d'informations des sols (SIS)",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'b11fe4e3_a981_4f68_8725_f7ad6c1de131',
              label: 'CONDITION_BLOCK_Secteur Information sols',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_retrait_cote_commune',
                  label: 'Commune exposée au retrait des côtes',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_retrait_cote_bien',
                  label: 'Bien exposé au retrait des côtes',
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'erp_retrait_cote_bien',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'erp_retrait_cote_bien_fiche',
                  label: 'Retrait de côtes - Extrait des prescriptions applicables',
                  type: 'UPLOAD'
                }
              ],
              id: 'bc3a24aa_ea63_4255_8e5d_48a3d5be01c0',
              label: 'CONDITION_BLOCK_Retrait de côte',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  id: 'zone_sismicite_classe',
                  label: 'Zone de sismicité de la commune',
                  type: 'NUMBER'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'zone_sismicite_classe',
                        type: 'EQUALS',
                        value: 2
                      }
                    ],
                    [
                      {
                        id: 'zone_sismicite_classe',
                        type: 'EQUALS',
                        value: 3
                      }
                    ],
                    [
                      {
                        id: 'zone_sismicite_classe',
                        type: 'EQUALS',
                        value: 4
                      }
                    ],
                    [
                      {
                        id: 'zone_sismicite_classe',
                        type: 'EQUALS',
                        value: 5
                      }
                    ]
                  ],
                  id: 'zone_sismicite_classe_fiche',
                  label: "Fiche d'information sur le risque sismique",
                  type: 'UPLOAD'
                }
              ],
              id: '822640d3_dfb0_44b6_9bb4_894c8a142120',
              label: 'CONDITION_BLOCK_Zone de sismicité',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'potentiel_radon_classe',
                  label: 'Commune à potentiel radon classée niveau 3',
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'potentiel_radon_classe',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'potentiel_radon_classe_fiche',
                  label: 'Fiche information Radon',
                  type: 'UPLOAD'
                },
                {
                  id: 'potentiel_radon_potentiel_radon_classe_libre',
                  label: 'La commune de situation du bien se trouve en zone RADON',
                  type: 'TEXT'
                }
              ],
              id: 'a9f7a960_b2fa_4296_ba69_4305bf5a4a86',
              label: 'CONDITION_BLOCK_Potentiel Radon',
              type: 'CONDITION_BLOCK'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'indemnite_catastrophe',
              label: "Sinistre d'origine catastrophe naturelle survenu sur le bien",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'indemnite_catastrophe',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'indemnite_catastrophe_liste_arretes',
              label: 'Liste des arrêtés portant reconnaissance de l’état de catastrophe naturelle',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'indemnite_catastrophe',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'indemnite_catastrophe_arrete_prefectoral',
              label: 'Dernier arrêté prefectoral',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'indemnite_catastrophe',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'indemnite_catastrophe_systeme_geographique',
              label: 'Informations mises à disposition dans le système d’information géographique',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'indemnite_catastrophe',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'indemnite_catastrophe_code_environnement',
              label: 'Dispositions de l’article L556-2 du Code de l’environnement',
              type: 'UPLOAD'
            },
            {
              id: 'indemnite_catastrophe_description',
              label: 'Description du sinistre',
              type: 'TEXT'
            },
            {
              id: 'indemnite_catastrophe_date',
              label: 'Date de survenance du sinistre',
              type: 'DATE'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'external_georisque',
              label: 'Georisques',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'erp',
              label: 'Etat des risques',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'faible',
                  label: "Zone d'exposition faible"
                },
                {
                  id: 'modere',
                  label: "Zone d'exposition modérée"
                },
                {
                  id: 'forte',
                  label: "Zone d'exposition forte"
                },
                {
                  id: 'inconnue',
                  label: "Zone d'exposition non classée"
                }
              ],
              id: 'zone_argiles_type',
              label: "Classement de la zone concernant l'aléa argile",
              type: 'SELECT'
            },
            {
              id: 'plan_exposition_bruit_zonage',
              label: "Classement de la zone concernant le plan d'exposition au bruit des aérodromes",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_geotechnique',
              label: 'Diagnostic géotechnique réalisé',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'geotechnique_zone_moyen',
                      label: 'Zone à risques moyens'
                    },
                    {
                      id: 'geotechnique_zone_fort',
                      label: 'Zone à risques forts'
                    }
                  ],
                  id: 'diagnostic_geotechnique_zone_type',
                  label: 'Type de zone',
                  type: 'SELECT'
                },
                {
                  id: 'diagnostic_geotechnique_date',
                  label: "Date de réalisation de l'étude géotechnique",
                  type: 'DATE'
                },
                {
                  id: 'diagnostic_geotechnique_diagnostiqueur_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostic_geotechnique_diagnostiqueur_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                },
                {
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'etude_geotechnique',
                  label: 'Etude géotechnique',
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'diagnostic_geotechnique',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '1cdc4ab7_3424_4ad2_8192_bfaaee146525',
              label: 'Diagnostic géotechnique',
              type: 'CATEGORY'
            }
          ],
          id: '5fded18e_757e_4abe_9cb4_7d476b111567',
          label: 'Diagnostics environnementaux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'salubrite_arretes_statut',
              label: "Arrêtés pris sur la salubrité / sécurité de l'immeuble",
              type: 'SELECT-BINARY'
            },
            {
              id: 'salubrite_arretes_contenu',
              label: 'Contenu des arrêtés',
              type: 'TEXT'
            },
            {
              id: 'salubrite_arretes_date',
              label: "Date de l'arrêté",
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'salubrite_arretes_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'salubrite_arretes_document',
              label: "Arrêtes Salubrité / Sécurité de l'immeuble",
              type: 'UPLOAD'
            }
          ],
          id: '0282aa2e_25b7_42eb_af61_ecb81b4941c8',
          label: 'Salubrité et sécurité des immeubles',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'obligation_debroussaillement_statut',
              label: 'Bien concerné par une obligation de débroussaillement',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'obligation_debroussaillement_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'obligation_debroussaillement_doc',
              label: 'Attestation de débroussaillement',
              type: 'UPLOAD'
            }
          ],
          id: 'cb8afbbf_d30a_42d3_8027_480bd43fd4f0',
          label: 'Obligation de débroussaillement',
          type: 'CATEGORY'
        }
      ],
      id: '4cb02da9_9436_4d1b_a7d5_d234a8d90b63',
      label: 'Diagnostics',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'permis_construire_libre',
          label: 'Permis de construire',
          type: 'UPLOAD'
        },
        {
          id: 'construction_date_achevement',
          label: 'Date d’achèvement de la construction',
          type: 'DATE'
        },
        {
          choices: [
            {
              label: 'Un permis de construire',
              id: 'permis'
            },
            {
              label: 'Une déclaration préalable',
              id: 'declaration'
            },
            {
              label: 'Aucune autorisation obtenue',
              id: 'aucune'
            }
          ],
          id: 'construction_autorisation_administration',
          label: "Autorisation d'urbanisme",
          type: 'SELECT'
        },
        {
          children: [
            {
              id: 'construction_autorisation_commune',
              label: 'Commune de délivrance du permis',
              type: 'TEXT'
            },
            {
              id: 'construction_autorisation_date',
              label: "Date de l'autorisation",
              type: 'DATE'
            },
            {
              id: 'construction_autorisation_numero',
              label: "Numéro de l'autorisation",
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'permis_construire_construction',
              label: 'Permis de construire - Construction',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'declaration_prealable_construction',
              label: 'Déclaration préalable - Construction',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_declaration_achevement_statut',
              label: "Déclaration d'achèvement déposée en mairie",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_date',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  }
                ]
              ],
              id: 'carnet_information_construction_statut',
              label: "Carnet d'information du logement effectué",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'carnet_information_construction_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_date',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  }
                ]
              ],
              id: 'carnet_information_construction_document',
              label: "Carnet d'information du logement",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_rga_zone_concernee',
              label: 'Bien situé dans une zone à risque Argile "Moyen" ou "Fort"',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_rga_attestation_etablie',
              label: 'Attestation "Retrait / Gonflement Argile" établie',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_rga_attestation_etablie',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'construction_rga_document',
              label: 'Attestation "Retrait / Gonflement Argile"',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'construction_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'construction_declaration_achevement_date',
                  label: "Date de la déclaration d'achèvement",
                  type: 'DATE'
                },
                {
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'declaration_achevement_construction',
                  label: "Déclaration d'achèvement - Construction",
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'construction_certificat_conformite_statut',
                  label: 'Certificat de conformité ou de non contestation obtenu',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'construction_certificat_conformite_refus',
                  label: 'Refus de la conformité',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'construction_certificat_conformite_refus_date',
                  label: 'Date du refus de la conformité',
                  type: 'DATE'
                },
                {
                  id: 'construction_certificat_conformite_refus_motif',
                  label: 'Motif du refus de la conformité',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'construction_certificat_conformite_refus_regularisation',
                  label: 'Le refus a été régularisé depuis',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'permis',
                      label: "Dépôt d'un permis modificatif"
                    },
                    {
                      id: 'travaux',
                      label: 'Travaux de régularisation'
                    }
                  ],
                  id: 'construction_certificat_conformite_refus_regularisation_objet',
                  label: 'Régularisation du refus de conformité',
                  type: 'SELECT'
                },
                {
                  id: 'construction_certificat_conformite_refus_objet',
                  label: 'Objet de la régularisation',
                  type: 'TEXT'
                },
                {
                  id: 'construction_certificat_conformite_refus_permis_date',
                  label: 'Date de dépôt du permis modificatif',
                  type: 'DATE'
                },
                {
                  id: 'construction_certificat_conformite_refus_permis_delivrance_date',
                  label: 'Date de délivrance du permis modificatif',
                  type: 'DATE'
                },
                {
                  children: [
                    {
                      id: 'construction_certificat_conformite_date',
                      label: 'Date de délivrance du certificat',
                      type: 'DATE'
                    },
                    {
                      filters: {
                        mustBeIncludedInOperationConfig: true
                      },
                      id: 'certificat_conformite_construction',
                      label: 'Certificat de conformité - Construction',
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'construction_certificat_conformite_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'construction_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: '20979ce6_3398_49ff_b9de_438c970dbb26',
                  label: 'CONDITION_BLOCK_Certificat de conformité',
                  type: 'CONDITION_BLOCK'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_declaration_achevement_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '784765f5_83ea_4854_8e16_8202e2cddb4d',
              label: "CONDITION_BLOCK_Déclaration d'achèvement",
              type: 'CONDITION_BLOCK'
            }
          ],
          id: '520380cd_9583_4b4c_a74c_fd767e03996b',
          label: "CONDITION_BLOCK_Information sur l'autorisation",
          type: 'CONDITION_BLOCK'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_regularisation',
          label: 'Régularisation des travaux a posteriori par le Vendeur',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_professionnel_statut',
          label: 'Travaux effectués par un professionnel',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_initiale_do_statut',
          label: "Souscription d'une assurance Dommage-Ouvrage",
          type: 'SELECT-BINARY'
        },
        {
          id: 'construction_initiale_do_organisme',
          label: "Etablissement d'assurance de la Dommage-Ouvrage",
          type: 'TEXT'
        },
        {
          id: 'construction_initiale_do_organisme_date',
          label: "Date de l'assurance Dommage-Ouvrage",
          type: 'DATE'
        },
        {
          id: 'construction_initiale_liste_intervenant',
          label: 'Liste des intervenant à la construction',
          type: 'TEXT'
        },
        {
          children: [
            {
              children: [
                {
                  id: 'construction_professionnel_list_construction_professionnel_nom',
                  label: 'Nom du professionnel',
                  type: 'TEXT'
                },
                {
                  id: 'construction_professionnel_list_construction_professionnel_adresse',
                  label: 'Adresse du professionnel',
                  type: 'ADDRESS'
                },
                {
                  id: 'construction_professionnel_list_construction_professionnel_travaux',
                  label: 'Travaux réalisés',
                  type: 'TEXT'
                }
              ],
              id: 'construction_professionnel_list',
              label: 'Professionnels',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'construction_professionnel_list_construction_professionnel_nom'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'assurance_decennale_construction',
              label: 'Assurance Décennale - Construction',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'assurance_dommage_construction',
              label: 'Assurance Dommage-Ouvrage - Construction',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'factures_entreprises_construction',
              label: 'Factures entreprises - Construction',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'moins_10_ans'
              },
              {
                id: 'construction_professionnel_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'b0da0554_907a_440b_8a7d_2b1f91d55721',
          label: 'CONDITION_BLOCK_Informations sur les professionnels',
          type: 'CONDITION_BLOCK'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_travaux_proprietaire',
          label: 'Un particulier a effectué lui-même certains travaux',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'construction_particulier_travaux',
              label: 'Travaux réalisés',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_particulier_assurance_dommage_statut',
              label: "Souscription d'une assurance dommage ouvrage par le particulier",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_particulier_assurance_dommage_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'construction_travaux_proprietaire',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'construction_particulier_assurance_dommage',
              label: 'Assurance Dommage-Ouvrage Particulier non professionnel',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'moins_10_ans'
              },
              {
                id: 'construction_travaux_proprietaire',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'c77c7537_6582_4aa5_bb55_4d23845b767b',
          label: 'CONDITION_BLOCK_Informations sur les travaux',
          type: 'CONDITION_BLOCK'
        }
      ],
      conditions: [
        [
          {
            id: 'construction',
            type: 'EQUALS',
            value: 'moins_10_ans'
          }
        ]
      ],
      id: '50236d26_14b7_42fb_ba5f_d43504a0beb6',
      label: 'Construction initiale',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              },
              {
                id: 'construction_vendeur',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'apres_1997'
              },
              {
                id: 'construction_vendeur',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                value: 'avant_1949',
                type: 'EQUALS'
              },
              {
                id: 'construction_vendeur',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'permis_construire_libre_plus_10',
          label: 'Permis de construire',
          type: 'UPLOAD'
        },
        {
          id: 'construction_initiale_plus_10_pc_numero',
          label: 'Numéro du permis de construire',
          type: 'TEXT'
        },
        {
          id: 'construction_initiale_plus_10_pc_mairie',
          label: 'Commune du dépôt du permis de construire',
          type: 'TEXT'
        },
        {
          id: 'construction_initiale_plus_10_pc_date',
          label: 'Date du dépôt du permis de construire',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_initiale_plus_10_daact',
          label: "Dépôt de la déclaration d'achèvement et de conformité des travaux",
          type: 'SELECT-BINARY'
        },
        {
          id: 'construction_initiale_plus_10_daact_date',
          label: "Date de dépôt de la déclaration d'achèvement",
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              },
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'apres_1997'
              },
              {
                id: 'construction_initiale_plus_10_daact',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              },
              {
                id: 'construction',
                value: 'avant_1949',
                type: 'EQUALS'
              },
              {
                id: 'construction_initiale_plus_10_daact',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              },
              {
                id: 'construction_initiale_plus_10_daact',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'apres_1997'
              },
              {
                id: 'construction',
                value: 'avant_1949',
                type: 'EQUALS'
              },
              {
                id: 'construction_initiale_plus_10_daact',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'apres_1997'
              },
              {
                id: 'construction_initiale_plus_10_daact',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                value: 'avant_1949',
                type: 'EQUALS'
              },
              {
                id: 'construction_initiale_plus_10_daact',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'declaration_achevement_construction_plus_10',
          label: "Déclaration d'achèvement - Construction",
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_initiale_plus_10_conformite',
          label: 'Obtention de la conformité des travaux ou non contestation à la conformité',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              },
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'apres_1997'
              },
              {
                id: 'construction_initiale_plus_10_conformite',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              },
              {
                id: 'construction',
                value: 'avant_1949',
                type: 'EQUALS'
              },
              {
                id: 'construction_initiale_plus_10_conformite',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              },
              {
                id: 'construction_initiale_plus_10_conformite',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'apres_1997'
              },
              {
                id: 'construction',
                value: 'avant_1949',
                type: 'EQUALS'
              },
              {
                id: 'construction_initiale_plus_10_conformite',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'apres_1997'
              },
              {
                id: 'construction_initiale_plus_10_conformite',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'construction',
                value: 'avant_1949',
                type: 'EQUALS'
              },
              {
                id: 'construction_initiale_plus_10_conformite',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'construction_initiale_plus_10_conformite_doc',
          label: 'Conformité des travaux ou non contestation à la conformité',
          type: 'UPLOAD'
        },
        {
          id: 'construction_initiale_plus_10_conformite_date',
          label: "Date d'obtention de la conformité des travaux ou non contestation à la conformité",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_initiale_plus_10_conformite_refus',
          label: 'Refus de la conformité des travaux',
          type: 'SELECT-BINARY'
        },
        {
          id: 'construction_initiale_plus_10_conformite_refus_motif',
          label: 'Motif du refus',
          type: 'TEXT'
        }
      ],
      conditions: [
        [
          {
            id: 'construction',
            type: 'EQUALS',
            value: '1949_1997'
          }
        ],
        [
          {
            id: 'construction',
            type: 'EQUALS',
            value: 'apres_1997'
          }
        ],
        [
          {
            id: 'construction',
            value: 'avant_1949',
            type: 'EQUALS'
          }
        ]
      ],
      id: 'ebec3b87_7e4f_4284_82f3_3ac2f3c7092f',
      label: 'Construction Initiale',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_statut',
          label: 'Présence de travaux',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_simple_dix_ans',
          label: 'Travaux de moins de 10 ans',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'exterieur',
              label: "L'aspect extérieur du bâti"
            },
            {
              id: 'structure',
              label: 'La structure du bâtiment'
            },
            {
              id: 'aucun',
              label: 'Aucun des deux'
            }
          ],
          id: 'travaux_simple_objet',
          label: 'Les travaux affectent',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_simple_urbanisme',
          label: "Travaux faisant l'objet d'une autorisation d'urbanisme",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_simple_pro',
          label: 'Travaux réalisés par un professionnel',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'travaux_list_travaux_date_achevement',
              label: 'Date d’achèvement des travaux',
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_dossier_urbanisme',
              label: "Dossier d'urbanisme - travaux",
              type: 'UPLOAD'
            },
            {
              id: 'travaux_list_travaux_titre',
              label: 'Titre des travaux',
              type: 'TEXT'
            },
            {
              id: 'travaux_list_travaux_description',
              label: 'Descriptif des travaux',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  label: 'Un permis de construire',
                  id: 'permis'
                },
                {
                  label: 'Une déclaration préalable',
                  id: 'declaration'
                },
                {
                  label: 'Aucune autorisation obtenue',
                  id: 'aucune'
                },
                {
                  label: "Travaux sans autorisation spécifique / travaux à l'identique",
                  id: 'non_necessaire'
                }
              ],
              id: 'travaux_list_travaux_autorisation_administration',
              label: "Autorisations d'urbanisme",
              type: 'SELECT'
            },
            {
              children: [
                {
                  id: 'travaux_list_travaux_autorisation_date',
                  label: "Date de l'autorisation",
                  type: 'DATE'
                },
                {
                  id: 'travaux_list_travaux_autorisation_numero',
                  label: "Numéro de l'autorisation",
                  type: 'TEXT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_permis_construire',
                  label: 'Permis de construire',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_declaration_prealable',
                  label: 'Déclaration préalable',
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'travaux_list_travaux_declaration_achevement_statut',
                  label: "Déclaration d'achèvement déposée en mairie",
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      conditions: [
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'travaux_list_travaux_declaration_achevement_date',
                      label: "Date de la déclaration d'achèvement",
                      type: 'DATE'
                    },
                    {
                      id: 'travaux_list_declaration_achevement',
                      label: "Déclaration d'achèvement",
                      type: 'UPLOAD'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'travaux_list_travaux_certificat_conformite_statut',
                      label: 'Certificat de conformité ou de non contestation obtenu',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'travaux_list_travaux_certificat_conformite_date',
                          label: 'Date de délivrance du certificat',
                          type: 'DATE'
                        },
                        {
                          id: 'travaux_list_certificat_conformite',
                          label: 'Certificat de conformité',
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_certificat_conformite_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_certificat_conformite_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_certificat_conformite_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'travaux_list_936ae7ff_b95a_4d66_8504_9e6ec2d07e78',
                      label: 'CONDITION_BLOCK_Certificat de conformité',
                      type: 'CONDITION_BLOCK'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_list_travaux_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_list_travaux_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_051291d2_b342_41cf_a295_4b7b680c2bd7',
                  label: "CONDITION_BLOCK_Déclaration d'achèvement",
                  type: 'CONDITION_BLOCK'
                }
              ],
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'travaux_list_travaux_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_4eb1dad8_5cba_4485_9388_0e39ccb57392',
              label: "CONDITION_BLOCK_Information sur l'autorisation",
              type: 'CONDITION_BLOCK'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_regularisation',
              label: 'Régularisation des travaux a posteriori par le Vendeur',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_date_achevement',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_travaux_energetique',
              label: 'Les travaux ont-ils porté sur une rénovation énergétique ?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_date_achevement',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  },
                  {
                    id: 'travaux_list_travaux_energetique',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_carnet_information_travaux_statut',
              label: "Carnet d'information du logement effectué",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'travaux_list_carnet_information_travaux_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'travaux_list_travaux_date_achevement',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  },
                  {
                    id: 'travaux_list_travaux_energetique',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_carnet_information_travaux_document',
              label: "Carnet d'information du logement",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_rga_renovation',
              label: 'Travaux portant sur une rénovation complète du Bien',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_rga_zone_concernee',
              label: 'Bien situé dans une zone à risque Argile "Moyen" ou "Fort"',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_rga_attestation_etablie',
              label: 'Attestation "Retrait / Gonflement Argile" établie',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_rga_attestation_etablie',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_travaux_rga_document',
              label: 'Attestation "Retrait / Gonflement Argile"',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_professionnel_statut',
              label: 'Travaux réalisés par un professionnel',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_non_professionnel_statut',
              label: 'Travaux réalisés par le Vendeur lui-même',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_professionnel_do',
              label: "Souscription d'une assurance dommage-ouvrage",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_professionnel_possession_garanties',
              label: 'Le Vendeur est-il en possession des assurances et garantie profesionnelles des entreprises ?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'travaux_list_travaux_professionnel_possession_garanties_absence_nom',
              label: "Nom de l'entreprise intervenue pour les travaux",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_professionnel_possession_garanties_absence_liquidation',
              label: 'Entreprise encore active',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_particulier_assurance_dommage_statut',
              label: "Souscription d'une assurance dommage ouvrage par le particulier",
              type: 'SELECT-BINARY'
            },
            {
              id: 'travaux_list_travaux_particulier_assurance_dommage_nom',
              label: "Nom de l'entreprise d'assurance",
              type: 'TEXT'
            },
            {
              id: 'travaux_list_travaux_particulier_assurance_dommage_numero',
              label: "Numéro du contrat d'assurance",
              type: 'TEXT'
            },
            {
              id: 'travaux_list_travaux_particulier_assurance_dommage_date',
              label: "Date de souscription du contrat d'assurance",
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_particulier_assurance_dommage_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_ctravaux_particulier_assurance_dommage',
              label: 'Assurance Dommage-Ouvrage',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  id: 'travaux_list_travaux_professionnel_nom',
                  label: 'Nom du professionnel',
                  type: 'TEXT'
                },
                {
                  id: 'travaux_list_travaux_professionnel_adresse',
                  label: 'Adresse du professionnel',
                  type: 'ADDRESS'
                },
                {
                  id: 'travaux_list_facture_entreprise_travaux',
                  label: "Factures de l'entreprise",
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_date_achevement',
                        type: 'NOT_OLDER_THAN_N_MONTHS',
                        value: '120'
                      },
                      {
                        id: 'travaux_list_travaux_professionnel_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_assurance_decennale_travaux',
                  label: 'Assurance décennale',
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_professionnel_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_ccbc7436_21cb_4a32_b14d_fa0779a6f886',
              label: 'CONDITION_BLOCK_Informations sur le professionnel',
              type: 'CONDITION_BLOCK'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_facture',
              label: 'Des factures sont-elles annexées ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_facture',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_travaux_facture_doc',
              label: 'Factures travaux',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'travaux_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'travaux_list',
          label: 'Travaux',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: ' '
              },
              {
                type: 'VARIABLE',
                value: 'travaux_list_travaux_titre'
              },
              {
                type: 'TEXT',
                value: ' - '
              },
              {
                type: 'VARIABLE',
                value: 'travaux_list_travaux_date_achevement'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        }
      ],
      id: '970a0c7a_9488_4af0_8899_0033470b4ba1',
      label: 'Travaux',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'visite_prix',
          label: 'Prix du bien',
          type: 'PRICE'
        },
        {
          id: 'visite_honoraires',
          label: 'Montant des honoraires',
          type: 'PRICE'
        },
        {
          id: 'programme_prix_vente_ttc',
          label: 'Prix de vente TTC',
          type: 'PRICE'
        },
        {
          description: 'Le prix TTC sera automatiquement calculé',
          id: 'programme_prix_vente',
          label: 'Prix de vente Hors Taxe',
          type: 'PRICE'
        },
        {
          id: 'programme_prix_vente_libre',
          label: 'Prix de vente Libre',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'occupation_sociale',
                recordPath: ['OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__FICHE_VENTE', 'VENTE', '0'],
                type: 'DIFFERENT',
                value: 'non'
              }
            ]
          ],
          id: 'programme_prix_vente_occupe',
          label: 'Prix de vente Occupé',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_prix_vente_remise',
          label: 'Remise commerciale',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'programme_prix_vente_remise',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'programme_prix_vente_remise_montant',
          label: 'Montant de la remise commerciale',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'tva_20',
              label: '20 %'
            },
            {
              id: 'tva_5',
              label: '5,5 %'
            }
          ],
          id: 'programme_prix_tva',
          label: 'Taux de TVA applicable',
          type: 'SELECT'
        },
        {
          id: 'programme_prix_brs',
          label: 'Prix de vente',
          type: 'PRICE'
        },
        {
          id: 'programme_prix_mise_vente',
          label: 'Prix de mise en vente',
          type: 'PRICE'
        },
        {
          id: 'programme_brs_montant',
          label: 'Montant mensuel de la redevance BRS',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'disponibilite_libre',
              label: 'Libre'
            },
            {
              id: 'disponibilite_reserve',
              label: 'Réservé'
            },
            {
              id: 'disponibilite_prereserve',
              label: 'Préréservé'
            }
          ],
          id: 'programme_disponibilite',
          label: 'Disponibilité du bien',
          type: 'SELECT'
        },
        {
          id: 'programme_psla_montant_a',
          label: 'Montant annuel de la fraction de la redevance partie locative (A)',
          type: 'PRICE'
        },
        {
          id: 'programme_psla_montant_b',
          label: 'Montant annuel de la fraction de la redevance partie acquisitive (B)',
          type: 'PRICE'
        },
        {
          id: 'programme_psla_montant_mensuel_a',
          label: 'Montant mensuel de la fraction de la redevance partie locative (A)',
          type: 'PRICE'
        },
        {
          id: 'programme_psla_montant_mensuel_b',
          label: 'Montant mensuel de la fraction de la redevance partie acquisitive (B)',
          type: 'PRICE'
        },
        {
          id: 'programme_montant_honoraires_ttc',
          label: 'Montant des honoraires TTC',
          type: 'PRICE'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'programme_plan',
          label: 'Plan de masse',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'plan_lot',
          label: 'Plan du lot',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'tableau_surface',
          label: 'Tableau de surface',
          type: 'UPLOAD'
        }
      ],
      id: '8d33763f_3521_423f_8f3f_99af151882d6',
      label: 'Informations sur la vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                id: 'cadastre_division',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dossier_division',
          label: 'Dossier de division',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'servitudes_note',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'note_servitude',
          label: 'Notes servitudes',
          type: 'UPLOAD'
        }
      ],
      id: 'af3e3b49_2ef2_4cbd_9061_7d792971adef',
      label: 'Documents',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              id: 'mandat_date',
              label: 'Date de signature du Mandat initial',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'simple',
                  label: 'de location simple'
                },
                {
                  id: 'semi',
                  label: 'de location semi-exclusif'
                },
                {
                  id: 'exclusif',
                  label: 'de location exclusif'
                },
                {
                  id: 'gestion',
                  label: 'de gestion'
                }
              ],
              id: 'mandat_type',
              label: 'Type de Mandat',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type',
                    value: 'gestion',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'mandat_numero_gestion',
              label: 'Numéro du mandat',
              register: {
                contracts: [
                  'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                  'IMMOBILIER_LOCATION_MANDAT_SAISONNIER',
                  'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_EXCLUSIF_EFFICITY',
                  'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_SIMPLE_EFFICITY'
                ],
                type: 'MANAGEMENT'
              },
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type',
                    value: 'gestion',
                    type: 'DIFFERENT'
                  }
                ]
              ],
              id: 'mandat_numero_transaction',
              label: 'Numéro du mandat',
              register: {
                contracts: [
                  'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                  'IMMOBILIER_LOCATION_MANDAT_SAISONNIER',
                  'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_EXCLUSIF_EFFICITY',
                  'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_SIMPLE_EFFICITY'
                ],
                type: 'TRANSACTION'
              },
              type: 'TEXT'
            },
            {
              children: [
                {
                  id: 'honoraires_location_bailleur_programme',
                  label: "Montant des honoraires d'entremise et de négociation à la charge du Bailleur",
                  type: 'PRICE'
                },
                {
                  id: 'honoraires_prestation_redaction_bailleur_programme',
                  label:
                    'Montant des honoraires de visite, constitution de dossier, rédaction du bail à charge du Bailleur',
                  type: 'PRICE'
                },
                {
                  id: 'honoraires_prestation_etat_des_lieux_bailleur_programme',
                  label: "Montant des honoraires d'établissement de l'état des lieux à charge du Bailleur",
                  type: 'PRICE'
                }
              ],
              id: 'dcb062dc_79f0_4515_8c37_35b218e3e6b2',
              label: 'Prestations à la charge du Bailleur',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'honoraires_location_locataire_autre',
                  label: 'Montant des honoraires de location à la charge du Locataire',
                  type: 'TEXTAREA'
                },
                {
                  description: 'Ces honoraires ne peuvent pas dépasser ceux charge Bailleur',
                  id: 'honoraires_prestation_redaction_locataire_programme',
                  label:
                    'Montant des honoraires de visite, constitution de dossier, rédaction du bail, à charge du Locataire',
                  type: 'PRICE'
                },
                {
                  description: 'Ces honoraires ne peuvent pas dépasser ceux charge Bailleur',
                  id: 'honoraires_prestation_etat_des_lieux_locataire_programme',
                  label: "Montant des honoraires d'établissement de l'état des lieux, à charge du Locataire",
                  type: 'PRICE'
                }
              ],
              id: '7774c1c1_b2b5_4ad0_84b9_44db55e390bd',
              label: 'Prestations à la charge du Locataire',
              type: 'CATEGORY'
            }
          ],
          id: '0bad379d_89a0_44bf_8f31_fce5ed998238',
          label: 'Mandat',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'loyer_initial',
              label: 'Montant du loyer (hors charges et hors complément de loyer)',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'loyer_complement',
              label: 'Un complément de loyer est il appliqué ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'loyer_complement',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'loyer_complement_montant',
              label: 'Montant du complément de loyer',
              type: 'PRICE'
            },
            {
              conditions: [
                [
                  {
                    id: 'loyer_complement',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'loyer_complement_justifications',
              label: "Justifications d'application du complément de loyer",
              type: 'TEXTAREA'
            },
            {
              id: 'charges_montant',
              label: 'Montant des charges',
              type: 'PRICE'
            }
          ],
          id: '912fb86e_6197_42ee_9edd_dae420e3e9e0',
          label: 'Loyer',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'cable',
                  label: 'Raccordement au câble'
                },
                {
                  id: 'adsl',
                  label: "Raccordement à l'ADSL"
                },
                {
                  id: 'fibre',
                  label: 'Raccordement à la fibre'
                },
                {
                  id: 'aucun',
                  label: 'Aucun équipement'
                },
                {
                  id: 'autre',
                  label: 'Autre équipement'
                }
              ],
              id: 'equipements',
              label: 'Le bien loué est équipé des éléments technologiques suivant',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              choices: [
                {
                  id: 'individuel',
                  label: 'Individuel'
                },
                {
                  id: 'collectif',
                  label: 'Collectif'
                }
              ],
              id: 'production_eau_chaude',
              label: "Production d'eau chaude",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'individuel',
                  label: 'Individuel'
                },
                {
                  id: 'collectif',
                  label: 'Collectif'
                }
              ],
              id: 'production_chauffage',
              label: 'Production de chauffage',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'etat_des_lieux',
              label: "Un état des lieux d'entrée a-t-il déjà été effectué entre les parties ?",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'location_travaux',
              label: 'Des travaux ont-ils été effectués depuis le dernier locataire ?',
              type: 'SELECT-BINARY'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'etat_lieux_document',
              label: 'Etat des lieux',
              type: 'UPLOAD'
            }
          ],
          id: '5481db9c_d4a9_4822_ae62_7fe13a0e82a0',
          label: 'Location',
          type: 'CATEGORY'
        }
      ],
      id: '19cfa41f_7a57_4647_b13f_7f612ef4d65d',
      label: 'Location',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  label: '1 étoile',
                  id: '1'
                },
                {
                  label: '2 étoiles',
                  id: '2'
                },
                {
                  label: '3 étoiles',
                  id: '3'
                },
                {
                  label: '4 étoiles',
                  id: '4'
                },
                {
                  label: '5 étoiles',
                  id: '5'
                },
                {
                  label: 'Aucun classement',
                  id: 'aucun'
                }
              ],
              id: 'etat_descriptif_categorie_classement',
              label: 'Catégorie de classement',
              type: 'SELECT'
            },
            {
              id: 'etat_descriptif_arrete_prefectoral',
              label: 'Arrété préfectoral du',
              type: 'DATE'
            }
          ],
          id: 'ddb25979_9d86_4412_ab24_c94f64f618c8',
          label: 'Renseignements Généraux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Neuve',
                  id: 'neuve'
                },
                {
                  label: 'Récente',
                  id: 'recente'
                },
                {
                  label: 'Ancienne',
                  id: 'ancienne'
                }
              ],
              id: 'etat_descriptif_type_construction',
              label: 'Type de construction',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  label: 'Une maison',
                  id: 'maison'
                },
                {
                  label: 'Indépendante',
                  id: 'independant'
                },
                {
                  label: 'Avec jardin',
                  id: 'jardin'
                },
                {
                  label: 'Un studio',
                  id: 'studio'
                },
                {
                  label: 'Un appartement',
                  id: 'appartement'
                }
              ],
              id: 'etat_descriptif_type_bien',
              label: 'Type de bien',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'etat_descriptif_plusieurs_logement',
              label: 'Meublé situé dans immeuble avec plusieurs logements',
              type: 'SELECT-BINARY'
            },
            {
              id: 'etat_descriptif_plusieurs_logement_nombre',
              label: 'Nombres de logements',
              type: 'NUMBER'
            },
            {
              choices: [
                {
                  label: 'Un appartement',
                  id: 'appartement'
                },
                {
                  label: 'Une villa',
                  id: 'villa'
                },
                {
                  label: 'Occupées partiellement par le propriétaire',
                  id: 'proprietaire'
                },
                {
                  label: "Occupées par d'autres locataires",
                  id: 'locataire'
                }
              ],
              id: 'etat_descriptif_plusieurs_logement_situation',
              label: 'Pièces situées dans',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'etat_descriptif_handicap',
              label: 'Meublé accessibles aux personnes handicapées',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'Chauffage central',
                  id: 'chauffage'
                },
                {
                  label: 'Climatisation',
                  id: 'climatisation'
                },
                {
                  label: "Rafraîchissement d'air",
                  id: 'rafraichissement'
                }
              ],
              id: 'etat_descriptif_chauffage',
              label: 'Chauffage / Climatisation',
              multiple: true,
              type: 'SELECT'
            },
            {
              id: 'etat_descriptif_superficie',
              label: 'Superficie totale',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_nombre_pieces',
              label: "Nombres de pièces d'habitation",
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_nombre_salle_eau',
              label: "Nombre de salle d'eau",
              type: 'NUMBER'
            },
            {
              choices: [
                {
                  label: 'Cuisine séparée',
                  id: 'cuisine_separee'
                },
                {
                  label: 'Coin-cuisine dans la pièce principale',
                  id: 'cuisine'
                },
                {
                  label: "Existence d'une entrée",
                  id: 'entree'
                }
              ],
              id: 'etat_descriptif_pieces_caracteristique',
              label: 'Caractéristiques des pièces',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  label: 'Jardin privatif',
                  id: 'jardin'
                },
                {
                  label: 'Parc privatif',
                  id: 'parc'
                },
                {
                  label: 'Cour privative',
                  id: 'cour'
                },
                {
                  label: 'Garage privatif',
                  id: 'garage'
                },
                {
                  label: 'Emplacement de voiture privatif',
                  id: 'emplacement'
                },
                {
                  label: 'Terrasse privative',
                  id: 'terrasse'
                },
                {
                  label: 'Loggia privative',
                  id: 'loggia'
                },
                {
                  label: 'Balcon privatif',
                  id: 'balcon'
                }
              ],
              id: 'etat_descriptif_jouissance_privative',
              label: 'Jouissance privative',
              multiple: true,
              type: 'SELECT'
            },
            {
              id: 'etat_descriptif_jouissance_terrasse_superficie',
              label: 'Superficie de la terrasse',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_jouissance_terrasse_vue',
              label: 'Vue de la terrasse',
              type: 'TEXT'
            },
            {
              id: 'etat_descriptif_jouissance_loggia_superficie',
              label: 'Superficie de la loggia',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_jouissance_loggia_vue',
              label: 'Vue de la loggia',
              type: 'TEXT'
            },
            {
              id: 'etat_descriptif_jouissance_balcon_superficie',
              label: 'Superficie du balcon',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_jouissance_balcon_vue',
              label: 'Vue du balcon',
              type: 'TEXT'
            }
          ],
          id: 'bdaa927c_447c_4c40_8344_90ac43093fb1',
          label: 'Principales caractéristiques',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Isolé',
                  id: 'isole'
                },
                {
                  label: 'Dans une ferme',
                  id: 'ferme'
                },
                {
                  label: 'Dans un hameau',
                  id: 'hameau'
                },
                {
                  label: 'Dans un village',
                  id: 'village'
                },
                {
                  label: 'Dans une ville',
                  id: 'ville'
                }
              ],
              id: 'etat_descriptif_situation_meuble',
              label: 'Le meublé est',
              type: 'SELECT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_ski',
                  label: 'Proximité de pistes de ski',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_ski_distance',
                  label: 'Distance des pistes de ski',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_mer',
                  label: 'Proximité de la mer',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_mer_distance',
                  label: 'Distance de la mer',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_lac',
                  label: "Proximité d'un lac",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_lac_distance',
                  label: 'Distance du lac',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_plage',
                  label: "Proximité d'une plage",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_plage_distance',
                  label: 'Distance de la plage',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_foret',
                  label: "Proximité d'une forêt",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_foret_distance',
                  label: 'Distance de la forêt',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_riviere',
                  label: "Proximité d'une rivière",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_riviere_distance',
                  label: 'Distance de la rivière',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_port',
                  label: "Proximité d'un port",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_port_distance',
                  label: 'Distance du port',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_centre_distance',
                  label: 'Distance du centre ville',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_autres',
                  label: "Autres centres d'intérêt",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_autres_distance',
                  label: "Liste et distance des autres centres d'intérêt",
                  type: 'TEXTAREA'
                }
              ],
              id: 'da0ef845_2680_4582_be3f_68d867b4b34f',
              label: "Distance des centres d'intérêt touristique",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'etat_descriptif_situation_proximite_sncf_distance',
                  label: 'Distance gare SNCF',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_gare_distance',
                  label: 'Distance gare routière',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_aeroport_distance',
                  label: 'Distance aéroport',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_medecin_distance',
                  label: 'Distance Médecin',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_hopital_distance',
                  label: 'Distance Hôpital',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_supermarche_distance',
                  label: 'Distance Centre commercial/supermarché',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_restaurant_distance',
                  label: 'Distance restaurant',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_epicerie_distance',
                  label: 'Distance épicerie',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_laverie_distance',
                  label: 'Distance laverie',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_services_autres',
                  label: 'Autres services',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_services_autres_distance',
                  label: 'Distance autres services',
                  suffix: 'km',
                  type: 'NUMBER'
                }
              ],
              id: 'c0f290d0_e59a_46bb_b740_fbdab700e91f',
              label: 'Distance des principaux services',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_inconvenient_bruits',
                  label: 'Bruits',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_inconvenient_bruits_liste',
                  label: 'Liste des bruits',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_inconvenient_odeurs',
                  label: 'Odeurs',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_inconvenient_odeurs_liste',
                  label: 'Liste des odeurs',
                  type: 'TEXT'
                }
              ],
              id: '16863a48_362d_49e1_be4e_b274c992ca05',
              label: 'Inconvénients de voisinage',
              type: 'CATEGORY'
            }
          ],
          id: '6003a06a_b486_48ac_add3_798a504cee69',
          label: 'Situation dans la localité',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'etat_descriptif_etat_entretien',
              label: "Etat d'entretien général",
              type: 'TEXT'
            },
            {
              children: [
                {
                  children: [
                    {
                      id: 'etat_desciptif_piece_agencement_nom_piece',
                      label: 'Nom de la pièce',
                      type: 'TEXT'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_surface_piece',
                      label: 'Surface',
                      suffix: 'm2',
                      type: 'NUMBER'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_fenetres_piece',
                      label: 'Nombre de Fenêtres',
                      type: 'NUMBER'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_lits_piece',
                      label: 'Nombres de Lits',
                      type: 'NUMBER'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_mobilier_piece',
                      label: 'Mobilier',
                      type: 'TEXT'
                    },
                    {
                      choices: [
                        {
                          label: 'Nord',
                          id: 'nord'
                        },
                        {
                          label: 'Sud',
                          id: 'sud'
                        },
                        {
                          label: 'Est',
                          id: 'est'
                        },
                        {
                          label: 'Ouest',
                          id: 'ouest'
                        }
                      ],
                      id: 'etat_desciptif_piece_agencement_exposition_piece',
                      label: 'Exposition',
                      type: 'SELECT'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_vue_piece',
                      label: 'Vue',
                      type: 'TEXT'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'etat_desciptif_piece_agencement_independance_piece',
                      label: 'Indépendance de la pièce',
                      type: 'SELECT-BINARY'
                    }
                  ],
                  id: 'etat_desciptif_piece_agencement',
                  label: 'Ajouter une pièce',
                  repetition: {
                    label: [
                      {
                        type: 'TEXT',
                        value: ' '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'etat_desciptif_piece_agencement_nom_piece'
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                }
              ],
              id: '14ec7d3c_4a39_4829_9aa1_1e46ad00baae',
              label: 'Agencement des pièces',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Evier avec eau chaude/froide',
                      id: 'evier'
                    },
                    {
                      label: 'VMC',
                      id: 'vmc'
                    },
                    {
                      label: 'Hotte aspirante',
                      id: 'hotte'
                    },
                    {
                      label: 'Table de cuisson',
                      id: 'cuisson'
                    },
                    {
                      label: 'Four',
                      id: 'four'
                    },
                    {
                      label: 'Four à micro-ondes',
                      id: 'microonde'
                    },
                    {
                      label: 'Réfrigérateur',
                      id: 'refrigerateur'
                    },
                    {
                      label: 'Congélateur',
                      id: 'congelateur'
                    },
                    {
                      label: 'Lave-vaisselle',
                      id: 'lave'
                    },
                    {
                      label: 'Batterie de cuisine',
                      id: 'batterie'
                    },
                    {
                      label: 'Autocuiseur',
                      id: 'autocuiseur'
                    }
                  ],
                  id: 'etat_descriptif_agencement_cuisine',
                  label: 'Equipements de la cuisine',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_feux',
                  label: 'Nombres de feux',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      label: 'Gaz de ville',
                      id: 'gaz'
                    },
                    {
                      label: 'Bouteille de gaz',
                      id: 'bouteille'
                    },
                    {
                      label: 'Electricité',
                      id: 'electricite'
                    },
                    {
                      label: 'Mixte',
                      id: 'mixte'
                    }
                  ],
                  id: 'etat_descriptif_agencemement_cuisine_plaque',
                  label: 'Alimentation de la plaque',
                  type: 'SELECT'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_refrigerateur_contenance',
                  label: 'Contenance du Réfrigérateur',
                  suffix: 'litres',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_agencemement_cuisine_refrigerateur_compartiment',
                  label: 'Réfrigérateur avec compartiment conservation',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_congelateur_contenance',
                  label: 'Contenance du congélateur',
                  suffix: 'litres',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_batterie_descriptif',
                  label: 'Batterie de cuisine descriptif',
                  type: 'TEXT'
                }
              ],
              id: 'd838d158_65e3_4ebd_8db8_a816faaa3fd2',
              label: 'Agencement de la cuisine',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  children: [
                    {
                      id: 'etat_desciptif_sanitaire_lavabos_nombre',
                      label: 'Nombre de lavabos',
                      type: 'NUMBER'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'etat_desciptif_sanitaire_douche',
                      label: 'Présence douche',
                      type: 'SELECT-BINARY'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'etat_desciptif_sanitaire_baignoire',
                      label: 'Présence baignoire avec douche',
                      type: 'SELECT-BINARY'
                    }
                  ],
                  id: 'etat_desciptif_sanitaire',
                  label: "Ajouter une salle d'eau",
                  repetition: {
                    label: [
                      {
                        type: 'TEXT',
                        value: " Salle d'eau"
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                },
                {
                  id: 'etat_desciptif_sanitaire_wc',
                  label: 'Nombre de WC intérieur au meublé',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_desciptif_sanitaire_wc_independant',
                  label: "Nombre de WC intérieur au meublé et indépendant de la salle d'eau",
                  type: 'NUMBER'
                }
              ],
              id: '57339077_c75b_47d7_8c92_a653cfd4131e',
              label: 'Equipements sanitaires',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Téléphone dans le logement',
                      id: 'telephone'
                    },
                    {
                      label: 'Téléphone à proximité',
                      id: 'telephone_proximite'
                    },
                    {
                      label: 'Accès internet haut debit',
                      id: 'internet'
                    },
                    {
                      label: 'TV couleur',
                      id: 'tv'
                    },
                    {
                      label: 'Lecteur DVD',
                      id: 'lecteur'
                    },
                    {
                      label: 'Chaîne Hi-Fi avec radio',
                      id: 'radio'
                    },
                    {
                      label: 'Lave-linge électrique',
                      id: 'lave_linge'
                    },
                    {
                      label: 'Sèche-Linge électrique',
                      id: 'seche_linge'
                    },
                    {
                      label: 'Etendoir à linge',
                      id: 'etendoir'
                    },
                    {
                      label: 'Fer à repasser',
                      id: 'fer'
                    },
                    {
                      label: 'Sèche-cheveux',
                      id: 'seche_cheveux'
                    },
                    {
                      label: 'Aspirateur',
                      id: 'aspirateur'
                    },
                    {
                      label: 'Autres équipements',
                      id: 'autre'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien',
                  label: 'Equipements du bien',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  id: 'etat_descriptif_equipement_bien_telephone_numero',
                  label: 'Numéro de téléphone',
                  type: 'PHONE'
                },
                {
                  id: 'etat_descriptif_equipement_bien_telephone_distance',
                  label: 'Distance du Téléphone',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_lave_linge',
                  label: 'Lave-linge particulier au logement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_seche_linge',
                  label: 'Sèche-linge particulier au logement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_etendoir',
                  label: 'Etendoir à linge intérieur au logement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_planche',
                  label: 'Planche à repasser',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_equipement_bien_autre',
                  label: 'Autre équipements à noter',
                  type: 'TEXT'
                },
                {
                  id: 'etat_descriptif_equipement_bien_loisirs_attaches',
                  label: 'Equipements de loisirs attachés',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_documentation_pratique',
                  label: 'Documentation pratique remise au locataire',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_documentation_touristique',
                  label: 'Documentation touristique remise au locataire',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_animaux',
                  label: 'Animaux domestiques acceptés',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_blanchisserie',
                  label: 'Service quotidien de blanchisserie',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_menage',
                  label: 'Service quotidien de ménage',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '6247a7ae_7ecb_4e42_912b_062aa621df1f',
              label: 'Equipements divers',
              type: 'CATEGORY'
            }
          ],
          id: '2437a447_8f18_4cda_af0e_7d2d3e0e58f3',
          label: 'Description du meublé',
          type: 'CATEGORY'
        }
      ],
      id: 'b3bda1af_9465_44d7_a27f_e2758ce7660d',
      label: 'Etat descriptif',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              label: 'En région Wallonne',
              id: 'wallonne'
            },
            {
              label: 'En région Bruxelles-Capitale',
              id: 'bruxelles'
            }
          ],
          id: 'belgique_situation_bien',
          label: 'Situation du bien',
          type: 'SELECT'
        }
      ],
      id: 'dce87622_6cab_4898_b351_a2f9fc503622',
      label: 'Informations Générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'belgique_revenu_cadastral',
          label: 'Revenu cadastral du bien',
          type: 'PRICE'
        }
      ],
      id: 'e612c1d4_0eef_4f4e_bdd1_72ccdc5ff833',
      label: 'Revenu cadastral',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes',
          label: 'Présence de servitudes',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes_titre',
          label: 'Servitudes contenue au titre de propriété',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_presence_servitudes_titre_liste',
          label: 'Liste des servitudes',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes_vendeur',
          label: 'Servitudes octroyée par le Vendeur',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_presence_servitudes_vendeur_liste',
          label: 'Liste des servitudes',
          type: 'TEXT'
        }
      ],
      id: '77c20963_306a_47c8_9193_c69d5278f782',
      label: 'Servitudes',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_publicitaire_presence',
          label: 'Panneau publicitaire',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_publicitaire_presence_preemption',
          label: 'Le contrat contient un droit de préemption',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_presence',
          label: 'Panneaux photovoltaïques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_proprietaire',
          label: 'Vendeur propriétaire des panneaux photovoltaïques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_presence_vente',
          label: 'Panneaux compris dans la vente',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_certificat',
          label: 'Vendeur bénéficiaire de certificats verts',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_panneau_photovoltaiques_certificat_liste',
          label: 'Liste de certificats verts',
          type: 'TEXTAREA'
        }
      ],
      id: '1c7271de_c766_44f8_8fe0_ed425ea5f22a',
      label: 'Panneaux / Enseignes',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_diu_presence',
              label: 'Bien concerné par un DIU',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_diu_liste',
              label: 'Liste des travaux concernés',
              type: 'TEXTAREA'
            }
          ],
          id: '542f15d5_43dd_4102_9f3b_afeeb92254d5',
          label: "Dossier d'intervention ultérieur",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_date_1981',
              label: "Installation électrique datant d'avant le 01/10/1981",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'PV de contrôle établi',
                  id: 'etabli'
                },
                {
                  label: 'PV de contrôle non établi',
                  id: 'non_etabli'
                },
                {
                  label: 'Dispense de contrôle',
                  id: 'dispense'
                }
              ],
              id: 'belgique_pv_controle_electricite_statut',
              label: "Contrôle de l'installation électrique",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_rapport',
              label: 'Rapport de visite remis',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_pv_controle_electricite_date',
              label: 'Date de réalisation du PV de contrôle',
              type: 'DATE'
            },
            {
              id: 'belgique_pv_controle_electricite_nom',
              label: 'Nom de la société ayant réalisé le PV',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_conformite',
              label: 'Installation conforme',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'Démolition du bâtiment',
                  id: 'demolition'
                },
                {
                  label: "Rénovation de l'installation",
                  id: 'renovation'
                }
              ],
              id: 'belgique_pv_controle_electricite_dispense_raison',
              label: 'Raison de la dispense',
              type: 'SELECT'
            }
          ],
          id: 'd8be18e0_5ff2_4bd7_918e_8884a3ae6c89',
          label: 'Electricité',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_amiante_presence',
              label: 'Amiante utilisée lors de la construction ou présence dans certains éléments',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_amiante_liste',
              label: "Situation de l'amiante dans le bien",
              type: 'TEXT'
            }
          ],
          id: '2a2abc8b_90dc_459e_93e0_2ec4588dc795',
          label: 'Amiante',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_peb_effectue',
              label: 'PEB effectué',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_peb_numero',
              label: 'Numéro du PEB',
              type: 'TEXT'
            },
            {
              id: 'belgique_peb_expert',
              label: "Nom de l'expert",
              type: 'TEXT'
            },
            {
              id: 'belgique_peb_date',
              label: "Date d'établissement du PEB",
              type: 'DATE'
            },
            {
              id: 'belgique_peb_classe',
              label: 'Classe énergétique du bien',
              type: 'TEXT'
            }
          ],
          id: '1fbfe90b_3ced_4e05_ab76_9a5dddca0c2f',
          label: 'Performance énergétique du bâtiment',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'belgique_compteur_eau_numero',
              label: "Numéro de compteur d'eau",
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_gaz_numero',
              label: 'Numéro de compteur Gaz',
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_gaz_ean',
              label: 'Code EAN Gaz',
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_electrique_numero',
              label: 'Numéro de compteur électricité',
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_electrique_ean',
              label: 'Code EAN électricité',
              type: 'TEXT'
            }
          ],
          id: 'a65bfec9_90ba_4106_be6f_7a1ab2a56329',
          label: 'Compteurs',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_toiture',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_toiture',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_toiture',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: 'f58a1cd2_f713_47c0_99a9_b81e2650cae1',
              label: 'Toiture(s)',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_menuiserie',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_menuiserie',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_menuiserie',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: 'e6329985_fa50_4601_b67b_e9451c733c6c',
              label: 'Menuiserie extérieure',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_electrique',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_electrique',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_electrique',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '4f30fca9_6486_4953_a7a3_780a1c7e6d12',
              label: 'Installation electrique',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_sanitaire',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_sanitaire',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_sanitaire',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: 'dec56dc0_3fef_4330_aaab_3d93a225d339',
              label: 'Sanitaires',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_chauffage',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_chauffage',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_chauffage',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: 'f4ad99e5_eeed_4bdd_8f0d_f663ac02d31d',
              label: 'Chauffage central',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_revetement_sol',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_revetement_sol',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_revetement_sol',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: 'd996fd0b_ddbe_40df_8e46_e6db3650b070',
              label: 'Revêtement sols',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_revetement_mur',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_revetement_mur',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_revetement_mur',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: 'e694e312_51eb_4fcb_96d9_b2d16994331e',
              label: 'Revêtements murs',
              type: 'CATEGORY'
            }
          ],
          id: '8f3dc320_3762_48d5_9352_0d46bd0f2745',
          label: 'Etat des Postes principaux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_urbanisme_statut',
              label: "Bien concerné par un permis d'urbanisme",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_permis_urbanisme_date',
              label: 'Date du permis',
              type: 'DATE'
            },
            {
              id: 'belgique_permis_urbanisme_lieu',
              label: 'Lieu de délivrance du permis',
              type: 'TEXT'
            },
            {
              id: 'belgique_permis_urbanisme_numero',
              label: 'Numéro de permis',
              type: 'TEXT'
            }
          ],
          id: '23535424_679d_491b_9f5f_d71b4fe9c1ed',
          label: "Permis d'urbanisme",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_prime_wallonnie',
              label: 'Prime de la région Wallonne utilisée',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_prime_wallonnie_date',
              label: 'Date de la prime',
              type: 'DATE'
            },
            {
              id: 'belgique_prime_wallonnie_type',
              label: 'Type de la prime',
              type: 'TEXT'
            },
            {
              id: 'belgique_prime_wallonnie_montant',
              label: 'Montant de la prime',
              type: 'PRICE'
            }
          ],
          id: '917ecb7a_3075_4f95_9c6d_c163aae99d85',
          label: 'Prime',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_location',
              label: "Bien objet d'un permis de location",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_permis_location_date',
              label: 'Date du permis de location',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_logement_inoccupe',
              label: 'Bien objet d’un PV de constat de logement inoccupé',
              type: 'SELECT-BINARY'
            }
          ],
          id: 'fc4e259d_3bac_4fbe_ae41_48df6c48be18',
          label: 'Habitation durable',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'belgique_usage_bien',
              label: 'Affectation du bien',
              type: 'TEXT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_infraction_urbanistique',
                  label: 'Bien en infraction urbanistique ou environnementale',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_infraction_urbanistique_liste',
                  label: "Type d'infraction",
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_infraction_urbanistique_presence',
                  label: 'Infraction présente lors du transfert de propriété',
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'b9b643aa_7f97_4cbd_b475_24ca0745f8cd',
              label: 'Infraction',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_insalubrite',
                  label: 'Bien déclaré inhabitable ou insalubre',
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'b1c9f0bb_8101_4e17_9764_3cf516378bd2',
              label: 'Insalubrité',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_inoccupe',
                  label: 'Immeuble déclaré inoccupé ou abandonné',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '116cc5ae_e67e_45e7_85f2_7512e15980f3',
              label: 'Inoccupation',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_alignement',
                  label: "Bien dans le périmètre d'un plan d'alignement",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'a5378f54_f0fc_4ce1_a2b3_6e6335a89182',
              label: 'Alignement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_travaux_presence',
                  label: 'Présence de travaux nécessitant un permis',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_responsabilite_decennale',
                  label: 'Bien concerné par la responsabilité décennale',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_travaux_juillet_2018',
                  label: 'Travaux soumis à permis délivré après le 1er juillet 2018',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'belgique_travaux_list_belgique_travaux_permis_obtenu',
                      label: 'Permis obtenu',
                      type: 'SELECT-BINARY'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_titre',
                      label: 'Titre des Travaux',
                      type: 'TEXT'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_description',
                      label: 'Description des travaux',
                      type: 'TEXTAREA'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_date_achevement',
                      label: "Date d'achèvement des travaux",
                      type: 'DATE'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'belgique_travaux_list_belgique_travaux_permis_obtenu',
                            value: 'non',
                            type: 'EQUALS'
                          }
                        ]
                      ],
                      id: 'belgique_travaux_list_belgique_travaux_aministie',
                      label: 'Régime de régularisation / amnistie',
                      type: 'TEXTAREA'
                    }
                  ],
                  id: 'belgique_travaux_list',
                  label: 'Ajouter Travaux',
                  repetition: {
                    label: [
                      {
                        type: 'TEXT',
                        value: ' '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'belgique_travaux_list_belgique_travaux_titre'
                      },
                      {
                        type: 'TEXT',
                        value: ' - '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'belgique_travaux_list_belgique_travaux_date_achevement'
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                }
              ],
              id: '8913374a_8716_4072_9bb4_db1fc13cf80c',
              label: 'Travaux',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_statut',
                  label: 'Bien soumis à Lotissement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_permis_delivrance',
                  label: "Permis d'urbanisation délivré",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_lotissement_notaire_nom',
                  label: "Notaire ayant reçu l'acte de division",
                  type: 'TEXT'
                },
                {
                  id: 'belgique_lotissement_notaire_date',
                  label: "Date de l'acte de division",
                  type: 'DATE'
                },
                {
                  id: 'belgique_lotissement_destination_vendu',
                  label: 'Destination du bien vendu',
                  type: 'TEXT'
                },
                {
                  id: 'belgique_lotissement_destination_conserver',
                  label: 'Destination du bien conservé par le vendeur',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_cs_avis',
                  label: "Condition suspensive d'absence d'avis défavorable",
                  type: 'SELECT-BINARY'
                }
              ],
              id: '6b5de402_9382_4233_8b63_4db96660eef8',
              label: 'Lotissement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_eau_usee_presence',
                  label: "Bien équipé d'un système d'épuration des eaux usées",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_eau_usee_repartition_clause',
                  label: 'Ajouter la répartition des frais sur demande des intercommunales',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '8600082b_cd84_4bd6_a2b2_8ba70aa3aef3',
              label: 'Equipement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_zone_inondable',
                  label: 'Bien situé en zone inondable',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_zone_inondable_inonde',
                  label: 'Bien a subi une inondation',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_zone_inondable_etendue',
                  label: "Étendue de l'inondation",
                  type: 'TEXT'
                }
              ],
              id: 'b2720c74_a7c8_48eb_bf08_57e23230efc1',
              label: 'Zones inondables',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_mesure_expropriation',
                  label: "Mesure d'expropriation de l'immeuble",
                  type: 'SELECT-BINARY'
                }
              ],
              id: '71fb7415_3e37_499a_9a5c_69572d7c2838',
              label: 'Expropriation',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_litige',
                  label: "Litige relatif à l'immeuble",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_litiges_liste',
                  label: 'Description des litiges',
                  type: 'TEXT'
                }
              ],
              id: 'e4b8a9a7_0984_4306_a2c0_2d07de590f67',
              label: 'Litige',
              type: 'CATEGORY'
            }
          ],
          id: '8081fa0d_b69a_4f1c_bdfc_4d5eb7d09283',
          label: 'Situation urbanistique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Raccordement antérieur au 1er Juin 2021',
                  id: 'raccordement_ante_2021'
                },
                {
                  label: 'Raccordement postérieur au 1er Juin 2021',
                  id: 'raccordement_post_2021'
                },
                {
                  label: 'Immeuble sur plan',
                  id: 'plan'
                },
                {
                  label: "Local où l'eau est fournie au public",
                  id: 'public'
                }
              ],
              id: 'belgique_certibeau_statut',
              label: 'Raccordement à la distribution publique de l’eau',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise',
              label: 'CertIBEau réalisé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_a_realiser',
              label: 'Le CertIBEau devra être réalisé',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_certibeau_resultat',
              label: 'Résultat du CertIBEau',
              type: 'TEXTAREA'
            },
            {
              id: 'belgique_certibeau_realise_date',
              label: "Date d'établissement du CertIBEau",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise_conformite',
              label: 'CertIBEau délivré conforme',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise_conformite_modification',
              label: "Modifications réalisés depuis l'établissement du CertIBEau",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_certibeau_realise_conformite_modification_liste',
              label: 'Liste des modifications intervenues',
              type: 'TEXTAREA'
            }
          ],
          id: '6bb0fbf1_23a2_4968_9c01_7e6da36612b5',
          label: 'CertIBeau',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_detecteur_incendie_statut',
              label: "Bien équipé de détecteur d'incendie",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_detecteur_incendie_nombre',
              label: 'Nombre de détecteur',
              type: 'NUMBER'
            },
            {
              id: 'belgique_detecteur_incendie_pieces',
              label: 'Pièces équipées',
              type: 'TEXT'
            }
          ],
          id: 'fe4a9948_2679_419d_bf87_15d02374ec9b',
          label: 'Détecteur incendie',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_presence',
              label: "Présence d'une citerne",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'GAZ',
                  id: 'gaz'
                },
                {
                  label: 'MAZOUT',
                  id: 'mazout'
                }
              ],
              id: 'belgique_citerne_type',
              label: 'Type de citerne',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_capacite_3000',
              label: 'Capacité de la citerne supérieure à 3.000 litres',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'Aérienne',
                  id: 'aerienne'
                },
                {
                  label: 'Enterrée',
                  id: 'enterree'
                }
              ],
              id: 'belgique_citerne_enterree_aerienne',
              label: 'Situation de la citerne',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_attestation',
              label: 'Attestation de conformité / certificat de visite de contrôle',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_citerne_mazout_capacite',
              label: 'Capacité de la citerne mazout',
              type: 'TEXT'
            }
          ],
          id: '3c53ff50_ecc8_4fc8_bcd4_f3aa7e72b4e3',
          label: 'Citerne mazout / gaz',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_environnement_statut',
              label: "Bien objet d'un permis d'environnement / déclaration de classe 3",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'PERMIS',
                  id: 'permis'
                },
                {
                  label: 'DECLARATION',
                  id: 'declaration'
                }
              ],
              id: 'belgique_permis_environnement_statut_type',
              label: 'Type',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_permis_environnement_type',
              label: "Objet du permis d'environnement",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_environnement_activite',
              label: 'Activité exercée imposant un tel permis ou déclaration',
              type: 'SELECT-BINARY'
            }
          ],
          id: '5c431dc0_98f3_424c_8c28_32de828e108f',
          label: "Permis d'environnement",
          type: 'CATEGORY'
        }
      ],
      id: '07b31b40_c9a8_4c56_bbd2_0716798dc640',
      label: 'Informations Administratives',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__BIEN__INDIVIDUEL_HORS_HABITATION',
  label: 'Bien individuel - Hors habitation',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__BIEN__INDIVIDUEL_HORS_HABITATION',
  specificTypes: ['BIEN', 'INDIVIDUEL_HORS_HABITATION'],
  type: 'RECORD'
};
