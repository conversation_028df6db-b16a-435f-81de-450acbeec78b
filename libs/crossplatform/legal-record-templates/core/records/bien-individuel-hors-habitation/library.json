{"questionTemplates": {"CONSTRUCTION": {"type": "SELECT-PICTURES", "choices": [{"id": "moins_10_ans", "label": "Il y a moins de 10 ans", "icon": "calendar.svg"}, {"id": "apres_1997", "label": "Après le 1er juillet 1997 (date du permis)", "icon": "calendar.svg"}, {"id": "1949_1997", "label": "Après le 1er janvier 1949", "icon": "calendar.svg"}, {"id": "avant_1949", "label": "Avant 1949", "icon": "calendar.svg"}]}, "OCCUPATION": {"type": "SELECT", "choices": [{"label": "Occupé à titre gratuit", "id": "occupation_gratuit"}, {"label": "<PERSON><PERSON> selon un contrat de bail", "id": "location_bail"}]}, "BAIL_TYPE": {"type": "SELECT", "choices": [{"label": "Un bail professionnel", "id": "professionnel"}, {"label": "Un bail commercial", "id": "commercial"}, {"label": "Un bail simple", "id": "simple"}, {"label": "Un bail habitation loi 1948", "id": "1948"}, {"label": "Un bail rural", "id": "rural"}, {"label": "Autre type de bail", "id": "bail_autre"}]}, "LOCATION_PARTIELLE": {"type": "SELECT", "choices": [{"label": "Sur la totalité du bien", "id": "location_totale"}, {"label": "Sur une partie du bien seulement", "id": "location_partielle"}]}, "AMIANTE": {"type": "SELECT", "choices": [{"label": "Il n'a pas été repéré des matériaux contenant de l'amiante", "id": "absence"}, {"label": "Il a été repéré des matériaux contenant de l'amiante de la liste A", "id": "liste_a"}, {"label": "Il a été repéré des matériaux contenant de l'amiante de la liste B", "id": "liste_b"}, {"label": "Des locaux ou parties de locaux n'ont pas pu être visités", "id": "non_visite"}], "multiple": true}, "TERMITES": {"type": "SELECT", "choices": [{"label": "Absence de traces d'infestation de termite", "id": "absence"}, {"label": "Présence de traces d'infestation de termite", "id": "presence"}]}, "RISQUES_NATURELS": {"type": "SELECT", "choices": [{"label": "Zone non concernée par un plan de prévention des risques naturels", "id": "aucun_plan"}, {"label": "Dans le périmètre d'un plan de prévention des risques naturels prescrits", "id": "plan_prescrit"}, {"label": "Dans le périmètre d'un plan de prévention des risques naturels anticipés", "id": "plan_anticipe"}, {"label": "Dans le périmètre d'un plan de prévention des risques naturels approuvés", "id": "plan_approuve"}], "multiple": true}, "RISQUES_MINIERS": {"type": "SELECT", "choices": [{"label": "Zone non concernée par un plan de prévention des risques miniers", "id": "aucun_plan"}, {"label": "Dans le périmètre d'un plan de prévention des risques miniers prescrits", "id": "plan_prescrit"}, {"label": "Dans le périmètre d'un plan de prévention des risques miniers anticipés", "id": "plan_anticipe"}, {"label": "Dans le périmètre d'un plan de prévention des risques miniers approuvés", "id": "plan_approuve"}], "multiple": true}, "RISQUES_TECHNOLOGIQUES": {"type": "SELECT", "choices": [{"label": "Zone non concernée par un plan de prévention des risques technologiques", "id": "aucun_plan"}, {"label": "Dans le périmètre d'un plan de prévention des risques technologiques prescrits", "id": "plan_prescrit"}, {"label": "Dans le périmètre d'un plan de prévention des risques technologiques anticipés", "id": "plan_anticipe"}, {"label": "Dans le périmètre d'un plan de prévention des risques technologiques approuvés", "id": "plan_approuve"}], "multiple": true}, "TRAVAUX_AUTORISATION": {"type": "SELECT", "choices": [{"label": "Un permis de construire", "id": "permis"}, {"label": "Une déclaration préalable", "id": "declaration"}, {"label": "Aucune autorisation obtenue", "id": "aucune"}, {"label": "Travaux sans autorisation spécifique / travaux à l'identique", "id": "non_necessaire"}]}, "CONSTRUCTION_AUTORISATION": {"type": "SELECT", "choices": [{"label": "Un permis de construire", "id": "permis"}, {"label": "Une déclaration préalable", "id": "declaration"}, {"label": "Aucune autorisation obtenue", "id": "aucune"}]}, "CONTRAT_FREQUENCE": {"type": "SELECT", "choices": [{"id": "contrat_frequence_annuelle", "label": "<PERSON><PERSON><PERSON>"}, {"id": "contrat_frequence_mensuelle", "label": "Mensuellement"}]}, "UPLAOD": {"type": "SELECT", "choices": []}, "TVA": {"type": "SELECT", "choices": [{"id": "tva_20", "label": "20 %"}, {"id": "tva_5", "label": "5,5 %"}]}, "DISPONIBILITE": {"type": "SELECT", "choices": [{"id": "disponibilite_libre", "label": "Libre"}, {"id": "disponibilite_reserve", "label": "Réservé"}, {"id": "disponibilite_prereserve", "label": "Préréservé"}]}, "ORIGINE": {"type": "SELECT", "choices": [{"id": "origine_propriete_acquisition", "label": "Acquisition"}, {"id": "origine_propriete_donation", "label": "Donation"}, {"id": "origine_propriete_succession", "label": "Succession"}, {"id": "origine_propriete_partage", "label": "Partage - Licitation"}, {"id": "origine_propriete_echange", "label": "Echange"}, {"id": "origine_propriete_adjudication", "label": "Adjudication - Vente aux enchères"}, {"id": "origine_propriete_remembrement", "label": "Remembrement"}]}, "CARACTERISTIQUES": {"type": "SELECT", "choices": [{"id": "caracteristique_neuf", "label": "<PERSON><PERSON><PERSON>"}, {"id": "caracteristique_ancien", "label": "Ancien"}, {"id": "caracteristique_vefa", "label": "En état futur d'achèvement"}, {"id": "caracteristique_renover", "label": "A rénover"}]}, "OCCUPATION_MANDAT": {"type": "SELECT", "choices": [{"id": "occupation_mandat_libre", "label": "Libre de toute occupation"}, {"id": "occupation_mandat_liberation", "label": "Libre à la date"}, {"id": "occupation_mandat_loue", "label": "Loué selon état locatif figurant en annexe"}]}, "MANDAT_SUPERFICIE": {"type": "SELECT", "choices": [{"id": "mandat_superficie_mandant", "label": "Le Mandant"}, {"id": "mandat_superficie_mandataire", "label": "Le Mandataire"}, {"id": "mandat_superficie_diagnostiqueur", "label": "Un Diagnostiqueur"}]}, "NATURE_BIEN": {"type": "SELECT", "choices": [{"id": "nature_bien_immeuble", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "nature_bien_commercial", "label": "Bien commercial ou professionnel"}, {"id": "nature_garage", "label": "Garage"}, {"id": "nature_bien_autre", "label": "<PERSON><PERSON>"}]}, "USAGE": {"type": "SELECT", "choices": []}, "DIAG_MLS": {"type": "SELECT", "choices": [{"id": "diag_mls_present", "label": "Le Mandant remet le diagnostic à la signature du mandat"}, {"id": "diag_mls_mandant", "label": "Le Mandant établit le diagnostic avant le compromis"}, {"id": "diag_mls_mandataire", "label": "Le Mandant charge le Mandataire d'établir le diagnostic"}]}, "UPLOAD] [@UPLOAD": {"type": "SELECT", "choices": []}, "ZONE_ARGILE": {"type": "SELECT", "choices": [{"id": "faible", "label": "Zone d'exposition faible"}, {"id": "modere", "label": "Zone d'exposition modérée"}, {"id": "forte", "label": "Zone d'exposition forte"}, {"id": "inconnue", "label": "Zone d'exposition non classée"}]}, "TRAVAUX_SIMPLE_OBJET_LISTE": {"type": "SELECT", "choices": [{"id": "exterieur", "label": "L'aspect extérieur du bâti"}, {"id": "structure", "label": "La structure du bâtiment"}, {"id": "aucun", "label": "Aucun des deux"}]}, "PRICE_CFP": {"type": "PRICE", "suffix": "CFP"}, "MANDAT_TYPE": {"type": "SELECT", "choices": [{"id": "simple", "label": "de location simple"}, {"id": "semi", "label": "de location semi-exclusif"}, {"id": "exclusif", "label": "de location exclusif"}, {"id": "gestion", "label": "de gestion"}]}, "EQUIPEMENTS": {"type": "PICK_LIST", "choices": [{"id": "cable", "label": "Raccordement au câble"}, {"id": "adsl", "label": "Raccordement à l'ADSL"}, {"id": "fibre", "label": "Raccordement à la fibre"}, {"id": "aucun", "label": "Aucun équipement"}, {"id": "autre", "label": "Autre équipement"}], "multiple": true}, "INDIVIDUEL_COLLECTIF": {"type": "SELECT", "choices": [{"id": "individuel", "label": "Individuel"}, {"id": "collectif", "label": "Collectif"}]}, "PARKING_GARAGE": {"type": "SELECT-BINARY", "choices": [{"id": "garage", "label": "Garage - box fermé"}, {"id": "parking", "label": "Parking"}]}, "ORIGINE_GIBOIRE": {"type": "SELECT", "choices": [{"id": "origine_propriete_acquisition", "label": "Acquisition"}, {"id": "origine_propriete_vefa", "label": "VEFA"}, {"id": "origine_propriete_donation", "label": "Donation"}, {"id": "origine_propriete_succession", "label": "Succession"}, {"id": "origine_propriete_partage", "label": "Partage - Licitation"}, {"id": "origine_propriete_echange", "label": "Echange"}, {"id": "origine_propriete_adjudication", "label": "Adjudication - Vente aux enchères"}, {"id": "origine_propriete_remembrement", "label": "Remembrement"}]}, "LOUE_LIBRE": {"type": "SELECT-BINARY", "choices": [{"id": "libre", "label": "Libre"}, {"id": "loue", "label": "<PERSON><PERSON>"}]}, "CONFORMITE_REGULARISATION": {"type": "SELECT", "choices": [{"id": "permis", "label": "Dépôt d'un permis modificatif"}, {"id": "travaux", "label": "Travaux de régularisation"}]}, "CESSION_PRO_TYPE": {"type": "SELECT", "choices": [{"id": "fonds", "label": "Fonds de commerce"}, {"id": "mur", "label": "Murs"}, {"id": "bail", "label": "<PERSON><PERSON> au bail"}, {"id": "titre", "label": "Titres de sociétés ou de parts"}], "multiple": true}, "ASL_AFUL": {"type": "SELECT", "choices": [{"id": "asl", "label": "Association syndicale libre"}, {"id": "aful", "label": "Association foncière urbaine libre"}]}, "MEUBLE_SITUATION": {"type": "SELECT", "choices": [{"label": "Isolé", "id": "isole"}, {"label": "Dans une ferme", "id": "ferme"}, {"label": "Dans un hameau", "id": "hameau"}, {"label": "Dans un village", "id": "village"}, {"label": "Dans une ville", "id": "ville"}]}, "CLASSEMENT_CATEGORIE": {"type": "SELECT", "choices": [{"label": "1 étoile", "id": "1"}, {"label": "2 étoiles", "id": "2"}, {"label": "3 étoiles", "id": "3"}, {"label": "4 étoiles", "id": "4"}, {"label": "5 étoiles", "id": "5"}, {"label": "Aucun classement", "id": "aucun"}]}, "ETAT_DESCRIPTIF_TYPE_CONSTRUCTION": {"type": "SELECT", "choices": [{"label": "Neuve", "id": "neuve"}, {"label": "Ré<PERSON><PERSON>", "id": "recente"}, {"label": "Ancienne", "id": "ancienne"}]}, "ETAT_DESCRIPTIF_TYPE_BIEN": {"type": "SELECT", "choices": [{"label": "Une maison", "id": "maison"}, {"label": "Indépendante", "id": "independant"}, {"label": "Avec jardin", "id": "jardin"}, {"label": "Un studio", "id": "studio"}, {"label": "Un appartement", "id": "appartement"}], "multiple": true}, "ETAT_DESCRIPTIF_PIECES_SITUATION": {"type": "SELECT", "choices": [{"label": "Un appartement", "id": "appartement"}, {"label": "Une villa", "id": "villa"}, {"label": "Occupées partiellement par le propriétaire", "id": "proprietaire"}, {"label": "Occupées par d'autres locataires", "id": "locataire"}], "multiple": true}, "PIECES_CARACTERISTIQUES": {"type": "SELECT", "choices": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "id": "cuisine_separee"}, {"label": "Coin-cuisine dans la pièce principale", "id": "cuisine"}, {"label": "Existence d'une entrée", "id": "entree"}], "multiple": true}, "JOUISSANCE_PRIVATIVE": {"type": "SELECT", "choices": [{"label": "Jardin privatif", "id": "jardin"}, {"label": "Parc privatif", "id": "parc"}, {"label": "Cour privative", "id": "cour"}, {"label": "Garage privatif", "id": "garage"}, {"label": "Emplacement de voiture privatif", "id": "emplacement"}, {"label": "Terrasse privative", "id": "terrasse"}, {"label": "Loggia privative", "id": "loggia"}, {"label": "Balcon privatif", "id": "balcon"}], "multiple": true}, "EXPOSITION": {"type": "SELECT", "choices": [{"label": "Nord", "id": "nord"}, {"label": "Sud", "id": "sud"}, {"label": "Est", "id": "est"}, {"label": "Ouest", "id": "ouest"}]}, "ETAT_DESCRIPTIF_AGENCEMENT_CUISINE": {"type": "SELECT", "choices": [{"label": "Evier avec eau chaude/froide", "id": "evier"}, {"label": "VMC", "id": "vmc"}, {"label": "<PERSON><PERSON> aspi<PERSON>e", "id": "hotte"}, {"label": "Table de cuisson", "id": "cuisson"}, {"label": "Four", "id": "four"}, {"label": "Four à micro-ondes", "id": "microonde"}, {"label": "Réfrigérateur", "id": "refrigerateur"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "id": "congelateur"}, {"label": "Lave-vais<PERSON>le", "id": "lave"}, {"label": "Batterie de cuisine", "id": "batterie"}, {"label": "Autocuiseur", "id": "autocuiseur"}], "multiple": true}, "ALIMENTATION_PLAQUE": {"type": "SELECT", "choices": [{"label": "Gaz de ville", "id": "gaz"}, {"label": "Bouteille de gaz", "id": "bouteille"}, {"label": "Electricité", "id": "electricite"}, {"label": "Mixte", "id": "mixte"}]}, "ETAT_DESCRIPTIF_EQUIPEMENT_BIEN": {"type": "SELECT", "choices": [{"label": "Téléphone dans le logement", "id": "telephone"}, {"label": "Téléphone à proximité", "id": "telephone_proximite"}, {"label": "Accès internet haut debit", "id": "internet"}, {"label": "TV couleur", "id": "tv"}, {"label": "Lecteur DVD", "id": "lecteur"}, {"label": "Chaîne Hi-Fi avec radio", "id": "radio"}, {"label": "Lave-linge électrique", "id": "lave_linge"}, {"label": "Sèche-Linge électrique", "id": "seche_linge"}, {"label": "Etendoir à linge", "id": "etendoir"}, {"label": "Fer à repasser", "id": "fer"}, {"label": "Sèche-cheveux", "id": "seche_cheveux"}, {"label": "As<PERSON>rateur", "id": "aspirateur"}, {"label": "Autres équipements", "id": "autre"}], "multiple": true}, "ETAT_DESCRIPTIF_CHAUFFAGE": {"type": "SELECT", "choices": [{"label": "Chauffage central", "id": "chauffage"}, {"label": "Climatisation", "id": "climatisation"}, {"label": "Rafraîchissement d'air", "id": "rafraichissement"}], "multiple": true}, "STATUT_PV_ELECTRIQUE": {"type": "SELECT", "choices": [{"label": "PV de contrôle établi", "id": "etabli"}, {"label": "PV de contrôle non établi", "id": "non_etabli"}, {"label": "Dispense de contrôle", "id": "dispense"}]}, "ELECTRICITE_DISPENSE": {"type": "SELECT", "choices": [{"label": "Démolition du bâtiment", "id": "demolition"}, {"label": "Rénovation de l'installation", "id": "renovation"}]}, "BELGIQUE_CERTIBEAU_STATUT": {"type": "SELECT", "choices": [{"label": "Raccordement antérieur au 1er Juin 2021", "id": "raccordement_ante_2021"}, {"label": "Raccordement postérieur au 1er Juin 2021", "id": "raccordement_post_2021"}, {"label": "Immeuble sur plan", "id": "plan"}, {"label": "Local où l'eau est fournie au public", "id": "public"}]}, "GAZ_MAZOUT": {"type": "SELECT-BINARY", "choices": [{"label": "GAZ", "id": "gaz"}, {"label": "MAZOUT", "id": "mazout"}]}, "PERMIS_DECLARATION": {"type": "SELECT-BINARY", "choices": [{"label": "PERMIS", "id": "permis"}, {"label": "DECLARATION", "id": "declaration"}]}, "ETAT_POSTE_PRINCIPAUX": {"type": "SELECT", "choices": [{"label": "<PERSON><PERSON><PERSON>", "id": "neuf"}, {"label": "Excellent", "id": "excellent"}, {"label": "Remis à neuf", "id": "remis_neuf"}, {"label": "Bon état", "id": "bon_etat"}, {"label": "A rafraîchir", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "A rénover", "id": "renover"}, {"label": "A réhabiliter", "id": "rehabiliter"}, {"label": "A démolir", "id": "demolir"}, {"label": "<PERSON><PERSON>", "id": "autre"}]}, "AERIENNE_ENTERREE": {"type": "SELECT", "choices": [{"label": "Aérienne", "id": "aerienne"}, {"label": "Enterrée", "id": "enterree"}]}, "BELGIQUE_SITUATION_BIEN": {"type": "SELECT", "choices": [{"label": "En région Wallonne", "id": "wallonne"}, {"label": "En région Bruxelles-Capitale", "id": "bruxelles"}]}, "LISTE_CHAUFFAGE": {"type": "SELECT", "choices": [{"id": "chaudiere_gaz", "label": "Chaudière G<PERSON>"}, {"id": "chaudiere_fioul", "label": "<PERSON><PERSON><PERSON>"}, {"id": "cheminee", "label": "Cheminée"}, {"id": "poele_bois", "label": "<PERSON><PERSON><PERSON> à bois"}, {"id": "poele_granules", "label": "Poêle à granulés"}, {"id": "radiateur", "label": "Convecteurs électriques"}, {"id": "pompe", "label": "Pompe à Chaleur"}, {"id": "aucun", "label": "Aucun système de chauffage"}], "multiple": true}, "CHAUDIERE_TYPE": {"type": "SELECT", "choices": [{"id": "gaz", "label": "Gaz"}, {"id": "fioul", "label": "<PERSON><PERSON><PERSON>"}, {"id": "autre", "label": "<PERSON><PERSON>"}]}, "ETAT_CUVE_FIOUL": {"type": "SELECT", "choices": [{"id": "operationnelle", "label": "Opérationnelle"}, {"id": "non_neutralisee", "label": "Non fonctionnelle et non neutralisée"}, {"id": "neutralisee", "label": "Non fonctionnelle et neutralisée"}]}, "USAGE_GESTION": {"type": "SELECT", "choices": [{"id": "habitation", "label": "Habitation"}, {"id": "mixte", "label": "Mixte"}, {"id": "commercial", "label": "Commercial"}, {"id": "professionnel", "label": "Professionnel"}]}, "LMNP_TYPE": {"type": "SELECT", "choices": [{"id": "tourisme", "label": "Tourisme"}, {"id": "affaire", "label": "<PERSON>e"}, {"id": "ehpad", "label": "EHPAD"}, {"id": "etudiant", "label": "Etudiante"}, {"id": "senior", "label": "Sénior"}]}}, "conditions": {"OCCUPATION": [{"id": "occupation_statut", "type": "EQUALS", "value": "oui"}], "OCCUPATION_GRATUIT": [{"id": "occupation_location", "type": "EQUALS", "value": "occupation_gratuit"}], "LOCATION": [{"id": "occupation_location", "type": "EQUALS", "value": "location_bail"}], "LOCATION_BAIL_TYPE_AUTRE": [{"id": "location_bail_type", "type": "EQUALS", "value": "bail_autre"}], "MOINS_10_ANS": [{"id": "construction", "type": "EQUALS", "value": "moins_10_ans"}], "1949_1997": [{"id": "construction", "type": "EQUALS", "value": "1949_1997"}], "DIAGNOSTIQUEUR_UNIQUE": [{"id": "diagnostics_techniques_diagnostiqueur_unique", "type": "EQUALS", "value": "oui"}], "PAS_DIAGNOSTIQUEUR_UNIQUE": [{"id": "diagnostics_techniques_diagnostiqueur_unique", "type": "EQUALS", "value": "non"}], "DPE": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}], "AMIANTE": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}], "TERMITES": [{"id": "diagnostic_termites_statut", "type": "EQUALS", "value": "oui"}], "DIAGNOSTIC_GEOTECHNIQUE": [{"id": "diagnostic_geotechnique", "type": "EQUALS", "value": "oui"}], "TRAVAUX": [{"id": "travaux_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_PERMIS": [{"id": "travaux_autorisation_administration", "type": "EQUALS", "value": "permis"}], "TRAVAUX_DECLARATION": [{"id": "travaux_autorisation_administration", "type": "EQUALS", "value": "declaration"}], "CONSTRUCTION_PERMIS": [{"id": "construction_autorisation_administration", "type": "EQUALS", "value": "permis"}], "CONSTRUCTION_DECLARATION": [{"id": "construction_autorisation_administration", "type": "EQUALS", "value": "declaration"}], "TRAVAUX_DECLARATION_ACHEVEMENT": [{"id": "travaux_declaration_achevement_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_CERTIFICAT_CONFORMITE": [{"id": "travaux_certificat_conformite_statut", "type": "EQUALS", "value": "oui"}], "CONSTRUCTION_DECLARATION_ACHEVEMENT": [{"id": "construction_declaration_achevement_statut", "type": "EQUALS", "value": "oui"}], "CONSTRUCTION_CERTIFICAT_CONFORMITE": [{"id": "construction_certificat_conformite_statut", "type": "EQUALS", "value": "oui"}], "CONSTRUCTION_INITIALE_PROFESSIONNEL": [{"id": "construction_professionnel_statut", "type": "EQUALS", "value": "oui"}], "CONSTRUCTION_PARTICULIER": [{"id": "construction_travaux_proprietaire", "type": "EQUALS", "value": "oui"}], "CONSTRUCTION_ASSURANCE_PARTICULIER": [{"id": "construction_particulier_assurance_dommage_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_PROFESSIONNEL": [{"id": "travaux_professionnel_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_MOINS_10_ANS": [{"id": "travaux_date_achevement", "type": "NOT_OLDER_THAN_N_MONTHS", "value": "120"}], "CONTRATS_ATTACHES": [{"id": "contrats_attaches_statut", "value": "oui", "type": "EQUALS"}], "SERVITUDES": [{"id": "servitudes", "value": "oui", "type": "EQUALS"}], "OP_DONATION": [{"id": "origine_liste", "value": "origine_propriete_donation", "type": "EQUALS"}], "OP_DONATION_UNIQUE": [{"id": "origine_donation_unique", "value": "non", "type": "EQUALS"}], "OP_SUCCESSION": [{"id": "origine_liste", "value": "origine_propriete_succession", "type": "EQUALS"}], "OP_ADJUDICATION": [{"id": "origine_liste", "value": "origine_propriete_adjudication", "type": "EQUALS"}], "OP_ACQUISITION": [{"id": "origine_liste", "value": "origine_propriete_acquisition", "type": "EQUALS"}], "OP_ECHANGE": [{"id": "origine_liste", "value": "origine_propriete_echange", "type": "EQUALS"}], "OP_PARTAGE": [{"id": "origine_liste", "value": "origine_propriete_partage", "type": "EQUALS"}], "OP_SUCCESSION_TERMINEE": [{"id": "origine_succession_statut", "value": "oui", "type": "EQUALS"}], "OP_SUCCESSION_NON_TERMINEE": [{"id": "origine_succession_statut", "value": "non", "type": "EQUALS"}], "OP_REMEMBREMENT": [{"id": "origine_liste", "value": "origine_propriete_remembrement", "type": "EQUALS"}], "AVANT_1949": [{"id": "construction", "value": "avant_1949", "type": "EQUALS"}], "OCCUPATION_MANDAT_LIBERATION": [{"id": "occupation_mandat", "value": "occupation_mandat_liberation", "type": "EQUALS"}], "OCCUPATION_MANDAT_LOUE": [{"id": "occupation_mandat", "value": "occupation_mandat_loue", "type": "EQUALS"}], "NATURE_AUTRE": [{"id": "nature_bien", "value": "nature_bien_autre", "type": "EQUALS"}], "TRAVAUX_ASSURANCE_PARTICULIER": [{"id": "travaux_particulier_assurance_dommage_statut", "value": "oui", "type": "EQUALS"}], "DIVISION": [{"id": "cadastre_division", "value": "oui", "type": "EQUALS"}], "SERVITUDE_ANNEXE": [{"id": "servitudes_note", "value": "oui", "type": "EQUALS"}], "ZONE_TERMITES": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__AQUITANIS__VENTE_ANCIEN", "OPERATION__AQUITANIS__PROGRAMME_ANCIEN"]}, {"id": "diagnostic_termites_commune", "value": "oui", "type": "EQUALS"}], "FACTURE_TRAVAUX": [{"id": "travaux_facture", "value": "oui", "type": "EQUALS"}], "MANDAT_TYPE_GESTION": [{"id": "mandat_type", "value": "gestion", "type": "EQUALS"}], "MANDAT_TYPE_TRANSACTION": [{"id": "mandat_type", "value": "gestion", "type": "DIFFERENT"}], "COMPLEMENT": [{"id": "loyer_complement", "type": "EQUALS", "value": "oui"}], "CONSTRUCTION_2023": [{"id": "construction_autorisation_date", "type": "DATE_NOT_OLDER_THAN", "value": "2022-12-31"}], "TRAVAUX_2023": [{"id": "travaux_date_achevement", "type": "DATE_NOT_OLDER_THAN", "value": "2022-12-31"}], "TRAVAUX_ENERGETIQUE": [{"id": "travaux_energetique", "value": "oui", "type": "EQUALS"}], "CIL_CONSTRUCTION": [{"id": "carnet_information_construction_statut", "value": "oui", "type": "EQUALS"}], "CIL_TRAVAUX": [{"id": "carnet_information_travaux_statut", "value": "oui", "type": "EQUALS"}], "AUDIT": [{"id": "dpe_audit_realise", "value": "oui", "type": "EQUALS"}], "OP_SUCCESSION_SIMPLE": [{"id": "origine_liste_giboire", "value": "origine_propriete_succession", "type": "EQUALS"}], "APRES_1997": [{"id": "construction", "type": "EQUALS", "value": "apres_1997"}], "CONSTRUCTION_VENDEUR": [{"id": "construction_vendeur", "value": "oui", "type": "EQUALS"}], "ACHEVEMENT_PLUS_10": [{"id": "construction_initiale_plus_10_daact", "value": "oui", "type": "EQUALS"}], "CONFORMITE_PLUS_10": [{"id": "construction_initiale_plus_10_conformite", "value": "oui", "type": "EQUALS"}], "ZONE_RGA_CONSTRUCTION_CONCERNED": [{"id": "construction_rga_attestation_etablie", "value": "oui", "type": "EQUALS"}], "ZONE_RGA_TRAVAUX_CONCERNED": [{"id": "travaux_rga_attestation_etablie", "value": "oui", "type": "EQUALS"}], "SECURITE_SALUBRITE": [{"id": "salubrite_arretes_statut", "value": "oui", "type": "EQUALS"}], "DIAGNOSTIQUEUR_LIBRE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__CDC__IMMOBILIER__VENTE", "OPERATION__MA_GESTION_LOCATIVE__LOCATION"]}], "VENTE_SOCIAL_OCCUPE": [{"id": "occupation_sociale", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__FICHE_VENTE", "VENTE", "0"], "type": "DIFFERENT", "value": "non"}], "ATTESTATION_40_M2": [{"id": "dpe_40_m2_statut", "value": "oui", "type": "EQUALS"}], "ZONE_TERMITES_PROGRAMME_CDC": [{"id": "programme_zone_termites", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "PERMIS_NON_OBTENU": [{"id": "belgique_travaux_permis_obtenu", "value": "non", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_TOITURE": [{"id": "belgique_poste_toiture", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_MENUISERIE": [{"id": "belgique_poste_menuiserie", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_ELECTRIQUE": [{"id": "belgique_poste_electrique", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_SANITAIRE": [{"id": "belgique_poste_sanitaire", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_CHAUFFAGE": [{"id": "belgique_poste_chauffage", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_REVETEMENT_SOL": [{"id": "belgique_poste_revetement_sol", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_REVETEMENT_MUR": [{"id": "belgique_poste_revetement_mur", "value": "autre", "type": "EQUALS"}], "DEBROUSSAILLEMENT": [{"id": "obligation_debroussaillement_statut", "value": "oui", "type": "EQUALS"}], "ENTRETIEN_CHAUDIERE": [{"id": "chaudiere_entretien_statut", "value": "oui", "type": "EQUALS"}], "CUVE_CONTRAT": [{"id": "cuve_fioul_contrat", "value": "oui", "type": "EQUALS"}], "CHAUDIERE_AUTRE": [{"id": "chaudiere_statut_type", "value": "autre", "type": "EQUALS"}], "EOLIENNE": [{"id": "presence_parc_eolienne", "type": "EQUALS", "value": "oui"}], "OPERATION_TEMPLATE_AFFICHAGE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__CDC__IMMOBILIER__VENTE"]}], "TEMPLATE_PRECISION_DEROGATION": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP"]}], "PPRN_SIMPLE": [{"id": "erp_pprn_bien", "type": "EQUALS", "value": "oui"}], "PPRM_SIMPLE": [{"id": "erp_pprm_bien", "type": "EQUALS", "value": "oui"}], "PPRT_SIMPLE": [{"id": "erp_pprt_bien", "type": "EQUALS", "value": "oui"}], "RETRAIT_COTE": [{"id": "erp_retrait_cote_bien", "type": "EQUALS", "value": "oui"}], "RADON": [{"id": "potentiel_radon_classe", "type": "EQUALS", "value": "oui"}], "INDEMINTE_CATASTROPHE": [{"id": "indemnite_catastrophe", "type": "EQUALS", "value": "oui"}], "SISMICITE_2": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 2}], "SISMICITE_3": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 3}], "SISMICITE_4": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 4}], "SISMICITE_5": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 5}], "ZONE_TERMITES_OBLIGATOIRE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AQUITANIS__VENTE_ANCIEN", "OPERATION__AQUITANIS__PROGRAMME_ANCIEN"]}], "NUMERO_UG": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__CDC__IMMOBILIER__VENTE"]}], "NUMERO_REFERENCE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AQUITANIS__PROGRAMME_ANCIEN", "OPERATION__AQUITANIS__VENTE_ANCIEN", "OPERATION__AQUITANIS__PROGRAMME_BRS", "OPERATION__AQUITANIS__RESERVATION_BRS"]}], "PRIX_REMISE": [{"id": "programme_prix_vente_remise", "type": "EQUALS", "value": "oui"}], "TEMPLATE_GESTION": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__BENEDIC__DOSSIER_DE_LOCATION_BENEDIC"]}], "DESIGNATION_TRAVAUX": [{"id": "designation_modifiee", "type": "EQUALS", "value": "oui"}], "PREPARATION_EFFICITY": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["EFFICITY__TRANSACTION__PREPARATION_COMPROMIS"]}]}}