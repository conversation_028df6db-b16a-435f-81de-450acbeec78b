// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordBienMonoproprieteHabitation: LegalRecordTemplate = {
  config: {
    tags: {
      programme_superficie: {
        questionId: 'programme_superficie',
        format: 'AREA',
        order: 0
      }
    },
    recordLinks: [
      {
        id: 'ENSEMBLE_IMMOBILIER',
        specificTypes: ['COMPOSITION', 'ENSEMBLE_IMMOBILIER'],
        label: 'Ensemble immobilier',
        constraints: {
          max: 1,
          min: 1
        },
        creation: {
          autoCreate: false
        }
      }
    ],
    search: ['numero_logement'],
    duplicate: ['numero_logement']
  },
  form: [
    {
      children: [
        {
          filters: {
            qualificationQuestions: true
          },
          id: 'adresse',
          label: 'Adresse du Bien',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'Non Tendue'
            },
            {
              id: 'oui',
              label: 'Tendue'
            },
            {
              id: 'tres_tendue',
              label: 'Très Tendue'
            }
          ],
          description: 'Code postal détecté dans une zone non tendue, tendue ou très tendue',
          id: 'zone_tendue',
          label: 'Commune située dans une zone tendue',
          type: 'SELECT'
        },
        {
          filters: {
            qualificationQuestions: true
          },
          id: 'numero_logement',
          label: 'Numéro du logement',
          type: 'TEXT'
        },
        {
          id: 'nombre_piece',
          label: 'Nombre de pièces',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'caracteristique_neuf',
              label: 'Neuf'
            },
            {
              id: 'caracteristique_ancien',
              label: 'Ancien'
            },
            {
              id: 'caracteristique_vefa',
              label: "En état futur d'achèvement"
            },
            {
              id: 'caracteristique_renover',
              label: 'A rénover'
            }
          ],
          id: 'caracteristiques',
          label: 'Caractéristiques et état du bien',
          type: 'SELECT'
        },
        {
          id: 'programme_superficie',
          label: 'Superficie du Bien',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'mandat_superficie_mandant',
              label: 'Le Mandant'
            },
            {
              id: 'mandat_superficie_mandataire',
              label: 'Le Mandataire'
            }
          ],
          id: 'mandat_superficie_fourniture',
          label: 'La superficie est établie par',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'moins_10_ans',
              label: 'Il y a moins de 10 ans',
              icon: 'calendar.svg'
            },
            {
              id: 'apres_1997',
              label: 'Après le 1er juillet 1997 (date du permis)',
              icon: 'calendar.svg'
            },
            {
              id: '1949_1997',
              label: 'Après le 1er janvier 1949',
              icon: 'calendar.svg'
            },
            {
              id: 'avant_1949',
              label: 'Avant 1949',
              icon: 'calendar.svg'
            }
          ],
          id: 'construction',
          label: "L'immeuble a été construit",
          type: 'SELECT-PICTURES'
        },
        {
          choices: [
            {
              label: 'Habitation exlusivement',
              id: 'usage_habitation'
            },
            {
              label: 'Mixte professionnel et Habitation',
              id: 'usage_mixte'
            },
            {
              label: 'Hors habitation',
              id: 'hors_habitation'
            }
          ],
          id: 'usage',
          label: 'Le logement a comme usage',
          type: 'SELECT'
        },
        {
          id: 'etage',
          label: 'Etage',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'detecteur_fumee',
          label: 'Détecteur de fumée',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_typologie',
          label: 'Typologie du Bien',
          type: 'TEXT'
        },
        {
          id: 'numero_immatriculation',
          label: "Numéro d'immatriculation du bien",
          type: 'TEXT'
        },
        {
          id: 'capacite_personne',
          label: 'Capacité du bien (personnes maximum)',
          type: 'NUMBER'
        },
        {
          id: 'numero_fiscal',
          label: "Numéro d'identification fiscal du logement",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'enregistrement_meuble',
          label: 'Enregistrement du Meublé obligatoire',
          type: 'SELECT-BINARY'
        },
        {
          id: 'enregistrement_meuble_numero',
          label: "Numéro d'enregistrement du Meublé",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'habitation',
              label: 'Habitation'
            },
            {
              id: 'mixte',
              label: 'Mixte'
            },
            {
              id: 'commercial',
              label: 'Commercial'
            },
            {
              id: 'professionnel',
              label: 'Professionnel'
            }
          ],
          id: 'usage_gestion',
          label: 'Usage du bien',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'meublee_statut',
          label: 'Bien meublé',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'a6c30482_5122_46e8_a057_6f08a6a2b351',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'individuel',
                  label: 'Individuel'
                },
                {
                  id: 'collectif',
                  label: 'Collectif'
                }
              ],
              id: 'mode_chauffage_production',
              label: 'Modalités de production chauffage',
              type: 'SELECT'
            },
            {
              id: 'mode_chauffage_production_repartition',
              label: 'Modalités de répartition de la consommation du locataire',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'chauffage_electricite',
                  label: 'Electricité'
                },
                {
                  id: 'chauffage_gaz',
                  label: 'Gaz'
                },
                {
                  id: 'chauffage_collectif',
                  label: 'Collectif'
                },
                {
                  id: 'chauffage_autre',
                  label: 'Autre'
                }
              ],
              id: 'mode_chauffage',
              label: 'Mode de chauffage général',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'mode_chauffage',
                    value: 'chauffage_autre',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'mode_chauffage_autre',
              label: 'Autre mode chauffage',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'chauffage_electricite',
                  label: 'Electricité'
                },
                {
                  id: 'chauffage_gaz',
                  label: 'Gaz'
                },
                {
                  id: 'chauffage_collectif',
                  label: 'Collectif'
                },
                {
                  id: 'chauffage_autre',
                  label: 'Autre'
                }
              ],
              id: 'eau_mode_chauffage',
              label: 'Eau chaude',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'individuel',
                  label: 'Individuel'
                },
                {
                  id: 'collectif',
                  label: 'Collectif'
                }
              ],
              id: 'eau_mode_chauffage_production',
              label: 'Modalité de production eau chaude',
              type: 'SELECT'
            },
            {
              id: 'mode_chauffage_production_eau_chaude_repartition',
              label: 'Modalités de répartition de la consommation du locataire - eau chaude',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'eau_mode_chauffage',
                    value: 'chauffage_autre',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'eau_mode_chauffage_autre',
              label: 'Autre mode eau chaude',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'chauffage_electricite',
                  label: 'Electricité'
                },
                {
                  id: 'chauffage_gaz',
                  label: 'Gaz'
                },
                {
                  id: 'chauffage_collectif',
                  label: 'Collectif'
                },
                {
                  id: 'chauffage_autre',
                  label: 'Autre'
                }
              ],
              id: 'cuisson_mode_chauffage',
              label: 'Cuisson',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'cuisson_mode_chauffage',
                    value: 'chauffage_autre',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'cuisson_mode_chauffage_autre',
              label: 'Autre mode cuisson',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'element_chauffage_equipement_autre',
              label: "Autre élément d'équipement à mentionner",
              type: 'SELECT-BINARY'
            },
            {
              id: 'element_chauffage_equipement_autre_liste',
              label: 'Liste des éléments',
              type: 'TEXT'
            }
          ],
          id: '6cd4b99c_aa26_4778_808b_be26b285c820',
          label: 'Mode de chauffage',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'compteur_electrique',
              label: 'Numéro du compteur Électrique',
              type: 'TEXT'
            },
            {
              id: 'compteur_gaz',
              label: 'Numéro du compteur de Gaz',
              type: 'TEXT'
            }
          ],
          id: '377a3dc0_baaf_47f2_9863_3ede363dea9e',
          label: 'Compteurs',
          type: 'CATEGORY'
        }
      ],
      id: 'f97fcd2b_6924_4e1d_b13e_b6be79451cd1',
      label: 'Etat des lieux',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            recordOnly: true
          },
          id: 'designation',
          label: 'Désignation du bien',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'designation_modifiee',
          label: 'Désignation du bien actuelle différente de celle du titre de propriété',
          type: 'SELECT-BINARY'
        },
        {
          id: 'designation_ancienne',
          label: 'Ancienne désignation du bien, telle que figurant dans le titre de propriété',
          type: 'TEXTAREA'
        },
        {
          id: 'designation_ancienne_travaux',
          label: 'Liste des travaux effectués depuis la désignation du titre',
          type: 'TEXTAREA'
        },
        {
          children: [
            {
              id: 'designation_ancienne_factures',
              label: 'Factures',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_assurance_decennale',
              label: 'Assurance décennale',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_assurance_do',
              label: 'Assurance dommage-ouvrage',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_permis',
              label: 'Permis de construire',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_declaration_travaux',
              label: 'Déclaration de travaux',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_declaration_travaux_achevement',
              label: "Déclaration d'achèvement des travaux",
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_certificat_conformite',
              label: 'Certificat de conformité',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'designation_modifiee',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '1325fg20_p36d_o4e2_lk4e_0d5f1380452e',
          label: 'CONDITION_BLOCK_Document_travaux',
          type: 'CONDITION_BLOCK'
        },
        {
          id: 'designation_avenant',
          label: 'Désignation modifiée du bien',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'exclusion_bail',
          label: 'Exclusion de certaines parties réservées par le propriétaire',
          type: 'SELECT-BINARY'
        },
        {
          id: 'exclusion_bail_liste',
          label: 'Désignation des parties réservées',
          type: 'TEXT'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'surface_habitable',
          label: 'Surface habitable',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'surface_habitable_professionnel',
          label: 'Mesurage réalisé par un professionnel',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'surface_habitable_professionnel_nom',
              label: 'Nom du professionnel',
              type: 'TEXT'
            },
            {
              id: 'surface_habitable_professionnel_adresse',
              label: 'Adresse du professionnel',
              type: 'ADDRESS'
            },
            {
              id: 'surface_habitable_professionnel_date',
              label: 'Date du mesurage',
              type: 'DATE'
            }
          ],
          conditions: [
            [
              {
                id: 'surface_habitable_professionnel',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '57633ae0_10ba_4d79_bdca_c876e398202f',
          label: 'CONDITION_BLOCK_Mesurage',
          type: 'CONDITION_BLOCK'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'location_locaux_equipement',
          label: 'Ajouter des locaux ou équipements accessoires',
          type: 'SELECT-BINARY'
        },
        {
          id: 'location_locaux_equipement_liste',
          label: 'Liste des locaux ou équipements accessoires',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'location_locaux_equipement_precisions',
          label: 'Ajouter des précisions sur les équipements',
          type: 'SELECT-BINARY'
        },
        {
          id: 'location_locaux_equipement_precisions_liste',
          label: 'Précisions sur les équipements',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'indigne',
          label: "Zone d'habitat indigne (permis de louer)",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'autorisation',
                  label: 'Une autorisation préalable'
                },
                {
                  id: 'declaration',
                  label: 'Une déclaration postérieure'
                }
              ],
              id: 'indigne_dualite',
              label: 'La location néccessite une',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'indigne_autorisation',
              label: 'La location nécessite-t-elle une autorisation préalable ?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'autorisation_location_date',
              label: "Date de l'autorisation de louer",
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'indigne_autorisation',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    id: 'indigne_dualite',
                    value: 'autorisation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'autorisation_location',
              label: 'Autorisation de louer',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'indigne_declaration',
              label: 'La location nécessite-t-elle une déclaration préalable ?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'declaration_prealable_location',
              label: 'Déclaration préalable de location',
              type: 'UPLOAD'
            }
          ],
          id: '647ba590_8cab_469e_858b_7de8fcc51332',
          label: "Zone d'habitat indigne",
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'regime_fiscal_location',
          label: 'Régime fiscal spécifique en place',
          type: 'SELECT-BINARY'
        },
        {
          id: 'regime_fiscal_location_liste',
          label: 'Régime fiscal utilisé',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'conventionnement_statut',
          label: "Bien objet d'un conventionnement",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'servitudes',
              label: 'Présence de servitudes',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'servitudes_note',
              label: 'Les servitudes sont-elles reprises dans une note annexée ?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'servitude_type',
              label: 'Type de servitude',
              type: 'TEXT'
            },
            {
              conditions: [[]],
              id: 'servitudes_liste',
              label: 'Liste des servitudes',
              type: 'TEXTAREA'
            },
            {
              id: 'servitudes_reprise_litterale',
              label: 'Reprise littérale de la servitude',
              type: 'TEXTAREA'
            }
          ],
          id: '874ca296_a1bc_47d5_8437_1c75aa8ec85a',
          label: 'Servitudes',
          type: 'CATEGORY'
        }
      ],
      id: 'd6542a02_0510_45c3_8454_6c4fc7e9f5a6',
      label: 'Description du bien',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cheminee',
          label: 'Cheminée / Poêle',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cheminee_fonctionnelle_statut',
              label: 'Cheminée / Poêle fonctionnel',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'cheminee_fonctionnelle_ramonage',
                  label: "Ramonage il y a moins d'un an",
                  type: 'SELECT-BINARY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'cheminee',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cheminee_fonctionnelle_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'a80968d8_8207_4940_b3ac_17a4d1f0c0bf',
              label: 'CONDITION_BLOCK_Ramonage',
              type: 'CONDITION_BLOCK'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'cheminee',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                    ]
                  }
                ]
              ],
              id: 'cheminee_precisions',
              label: 'Ajouter une précision sur la cheminée/poêle',
              type: 'SELECT-BINARY'
            },
            {
              id: 'cheminee_precisions_texte',
              label: 'Précisions sur la cheminée/poêle',
              type: 'TEXTAREA'
            }
          ],
          conditions: [
            [
              {
                id: 'cheminee',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '2ca44e1a_fa53_4cf5_9997_46fa0610587a',
          label: 'CONDITION_BLOCK_Informations sur le pôele / la cheminée',
          type: 'CONDITION_BLOCK'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'loue_anterieurement_moins_18_mois',
          label: 'Bien loué lors des 18 derniers mois',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'loue_anterieurement_travaux',
              label: 'Des travaux ont-il été effectué depuis la fin du dernier contrat de bail ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'loue_anterieurement_moins_18_mois',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'loue_anterieurement_travaux',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'loue_anterieurement_travaux_liste',
              label: 'Précisez le montant et la nature de ces travaux',
              type: 'TEXTAREA'
            },
            {
              children: [
                {
                  id: 'loyer_dernier_montant',
                  label: 'Montant du loyer payé par le dernier locataire',
                  type: 'PRICE'
                },
                {
                  id: 'loyer_derniere_date_paiement',
                  label: 'Date de versement du dernier loyer',
                  type: 'DATE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'loyer_dernier_revision_statut',
                  label: "Ancien loyer a fait l'objet d'une révision",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'loyer_derniere_date_revision',
                  label: 'Date de dernière révision du loyer',
                  type: 'DATE'
                }
              ],
              id: 'ade12a16_11c3_4093_a9a0_870c087df3c8',
              label: 'Montant des derniers loyers',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'loue_anterieurement_moins_18_mois',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: '13450e3c_f60f_40da_b9b5_b1bb33a0a7da',
          label: 'CONDITION_BLOCK_Précédente location',
          type: 'CONDITION_BLOCK'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'saisonnier_residence',
          label: 'Le bien constitue-t-il la résidence principale du Bailleur ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'saisonnier_residence',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'saisonnier_residence_obligatoire',
          label: 'La commune de situation du bien a-t-elle instauré une déclaration préalable à la location ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'saisonnier_classement',
          label: "Le bien fait-il l'objet d'un classement en meublé de tourisme ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'individuel',
              label: 'Individuel'
            },
            {
              id: 'collectif',
              label: 'Collectif'
            }
          ],
          id: 'production_eau_chaude',
          label: "Production d'eau chaude",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'individuel',
              label: 'Individuel'
            },
            {
              id: 'collectif',
              label: 'Collectif'
            }
          ],
          id: 'production_chauffage',
          label: 'Production de chauffage',
          type: 'SELECT'
        }
      ],
      id: '2b19bbdd_d2df_485f_a493_1a52a4af383c',
      label: 'Informations particulières',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'zone_loyer_reference_montant',
          label: 'Montant du loyer de référence (par m2)',
          type: 'PRICE'
        },
        {
          id: 'zone_loyer_reference_montant_majore',
          label: 'Montant du loyer de référence majoré (par m2)',
          type: 'PRICE'
        }
      ],
      id: '007f5bb0_d4bb_4bf5_bc37_862536f79511',
      label: 'Loyer de référence',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dernier_decompte_charges',
          label: 'Dernier décompte de charges',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'occupation_mandat_libre',
              label: 'Libre de toute occupation'
            },
            {
              id: 'occupation_mandat_liberation',
              label: 'Libre à la date'
            },
            {
              id: 'occupation_mandat_loue',
              label: 'Loué selon état locatif figurant en annexe'
            }
          ],
          id: 'occupation_mandat',
          label: 'Le jour de la vente définitive, le bien sera',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'occupation_mandat',
                value: 'occupation_mandat_liberation',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'occupation_mandat_liberation',
          label: 'Date de libération du bien',
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'occupation_mandat',
                value: 'occupation_mandat_loue',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeExcludedInOperationConfig: true
          },
          id: 'mandat_bail',
          label: 'Contrat de bail',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'occupation_statut',
          label: 'Le bien est-il occupé par une personne autre que le propriétaire ou sa famille ?',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Occupé à titre gratuit',
                  id: 'occupation_gratuit'
                },
                {
                  label: 'Loué selon un contrat de bail',
                  id: 'location_bail'
                }
              ],
              id: 'occupation_location',
              label: 'Le bien est actuellement',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'occupation_location',
                    type: 'EQUALS',
                    value: 'occupation_gratuit'
                  },
                  {
                    id: 'occupation_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'occupation_liste',
              label: 'Liste des occupants',
              type: 'TEXTAREA'
            },
            {
              children: [
                {
                  children: [
                    {
                      choices: [
                        {
                          label: "Un bail d'habitation",
                          id: 'habitation'
                        },
                        {
                          label: 'Un bail professionnel',
                          id: 'professionnel'
                        },
                        {
                          label: 'Un bail commercial',
                          id: 'commercial'
                        },
                        {
                          label: 'Un bail simple',
                          id: 'simple'
                        },
                        {
                          label: 'Un bail habitation loi 1948',
                          id: '1948'
                        },
                        {
                          label: 'Un bail rural',
                          id: 'rural'
                        },
                        {
                          label: 'Autre type de bail',
                          id: 'bail_autre'
                        }
                      ],
                      id: 'location_bail_liste_location_bail_type',
                      label: 'Type de bail',
                      type: 'SELECT'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'location_bail_liste_location_bail_type',
                            type: 'EQUALS',
                            value: 'bail_autre'
                          },
                          {
                            id: 'occupation_location',
                            type: 'EQUALS',
                            value: 'location_bail'
                          },
                          {
                            id: 'occupation_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'location_bail_liste_location_bail_type_autre',
                      label: 'Précisez le type de bail',
                      type: 'TEXT'
                    },
                    {
                      id: 'location_bail_liste_location_bail_date_signature',
                      label: 'Date de signature du bail',
                      type: 'DATE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_locataires',
                      label: 'Liste des locataires',
                      type: 'TEXTAREA'
                    },
                    {
                      id: 'location_bail_liste_location_bail_loyer',
                      label: 'Montant du loyer',
                      type: 'PRICE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_charges',
                      label: 'Montant des charges',
                      type: 'PRICE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_loyer_cfp',
                      label: 'Montant du loyer',
                      suffix: 'CFP',
                      type: 'PRICE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_charges_cfp',
                      label: 'Montant des charges',
                      suffix: 'CFP',
                      type: 'PRICE'
                    },
                    {
                      filters: {
                        mustBeIncludedInOperationConfig: true
                      },
                      id: 'location_bail_liste_contrat_bail',
                      label: 'Contrat de bail',
                      type: 'UPLOAD'
                    }
                  ],
                  id: 'location_bail_liste',
                  label: 'Baux',
                  repetition: {
                    label: [
                      {
                        type: 'VARIABLE',
                        value: 'location_bail_liste_location_bail_type'
                      },
                      {
                        type: 'TEXT',
                        value: '-'
                      },
                      {
                        type: 'VARIABLE',
                        value: 'location_bail_liste_location_bail_type_autre'
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                },
                {
                  choices: [
                    {
                      label: 'Sur la totalité du bien',
                      id: 'location_totale'
                    },
                    {
                      label: 'Sur une partie du bien seulement',
                      id: 'location_partielle'
                    }
                  ],
                  id: 'location_partielle_statut',
                  label: 'La location porte',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'avantage_fiscal_statut',
                  label: "Présence d'un dispositif fiscal",
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'avantage_fiscal_dispositif',
                      label: 'Dispositif fiscal utilisé',
                      type: 'TEXT'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'avantage_fiscal_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'occupation_location',
                        type: 'EQUALS',
                        value: 'location_bail'
                      },
                      {
                        id: 'occupation_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'b39dc913_fc9d_4ac4_b60e_3208d60bd224',
                  label: 'Avantage fiscal',
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'occupation_location',
                    type: 'EQUALS',
                    value: 'location_bail'
                  },
                  {
                    id: 'occupation_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '7c08fa7c_60ce_4636_8820_0431abf5a603',
              label: 'Information sur les baux',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'occupation_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '575d1a68_0e8d_447c_ba95_8b7534fa6112',
          label: 'Occupation du bien',
          type: 'CATEGORY'
        }
      ],
      id: '7a64789e_7d8d_4899_b910_77027ef84352',
      label: 'Situation locative',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'titre_propriete_libre',
          label: 'Titre de propriété',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'etat_recette_depense',
          label: "Dépenses des 12 derniers mois et recettes et dépenses depuis le début d'année",
          type: 'UPLOAD'
        },
        {
          id: 'taxe_fonciere',
          label: 'Dernière taxe foncière',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'ddt_libre_doc',
          label: 'Dossier de diagnostics techniques',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'plans_biens',
          label: 'Plan des biens',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'courrier_gestion',
          label: 'Courriers (locataire,syndic,adminsitration...)',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'cheminee',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'cheminee_fonctionnelle_ramonage',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'cheminee_fonctionnelle_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'cheminee_facture_ramonage',
          label: 'Facture ramonage',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['IMMOBILIER_LOCATION_BAIL_SAISONNIER']
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'saisonnier_etat_descriptif',
          label: 'Etat descriptif type du logement',
          type: 'UPLOAD'
        }
      ],
      id: '9dbe38d6_299a_4db9_bb71_e1596107880c',
      label: 'Documents Généraux',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_a_jour',
          label: 'Les diagnostics sont à jour',
          type: 'SELECT-BINARY'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'attestation_surface',
          label: 'Attestation de surface',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              label: "Un seul dossier de diagnostic est réalisé pour tout l'immeuble",
              id: 'immeuble_entier_dossier_diagnostic_unique'
            },
            {
              label: 'Un dossier de diagnostic est réalisé pour chaque logement',
              id: 'immeuble_entier_dossier_diagnostic_pluralite'
            }
          ],
          id: 'immeuble_entier_diagnostic',
          label: 'Concernant le dossier de diagnostic technique',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostics_resultats',
          label: 'Résultat des diagnostics est indiqué dans le compromis',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostics_techniques_diagnostiqueur_unique',
                  label: 'Diagnostiqueur unique',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostics_techniques_diagnostiqueur_unique_date',
                  label: 'Date unique',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'diagnostics_techniques_diagnostiqueur_unique_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostics_techniques_diagnostiqueur_unique_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                },
                {
                  id: 'attestation_diagnostiqueur',
                  label: 'Attestation du diagnostiqueur',
                  type: 'UPLOAD'
                },
                {
                  id: 'diagnostics_techniques_domofrance_date',
                  label: "Date d'établissement du dossier de diagnostic technique",
                  type: 'DATE'
                }
              ],
              id: 'd672614a_0795_42eb_b6a4_af90a6be442a',
              label: 'Information sur le diagnostiqueur',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_plomb_statut',
                  label: 'Diagnostic réalisé',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'diagnostic_plomb_date',
                      label: 'Date du diagnostic',
                      type: 'DATE'
                    },
                    {
                      choices: [
                        {
                          label: "Il n'a pas été repéré de présence de plomb",
                          id: 'absence'
                        },
                        {
                          label: 'Il a été repéré des mesures de plomb de classe 0',
                          id: 'classe_0'
                        },
                        {
                          label: 'Il a été repéré des mesures de plomb de classe 1',
                          id: 'classe_1'
                        },
                        {
                          label: 'Il a été repéré des mesures de plomb de classe 2',
                          id: 'classe_2'
                        },
                        {
                          label: 'Il a été repéré des mesures de plomb de classe 3',
                          id: 'classe_3'
                        }
                      ],
                      id: 'diagnostic_plomb_resultat',
                      label: 'Résultat du diagnostic',
                      multiple: true,
                      type: 'SELECT'
                    },
                    {
                      id: 'diagnostic_plomb_resultat_libre',
                      label: 'Résultat du diagnostic',
                      type: 'TEXTAREA'
                    },
                    {
                      id: 'diagnostic_plomb',
                      label: 'Diagnostic Plomb',
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostic_plomb_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostic_plomb_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'diagnostic_plomb_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: 'e424a9ed_e15d_4185_a46c_427fefad94aa',
                  label: 'CONDITION_BLOCK_Informations sur le diagnostic plomb',
                  type: 'CONDITION_BLOCK'
                },
                {
                  children: [
                    {
                      id: 'diagnostic_plomb_diagnostiqueur_nom',
                      label: 'Nom du diagnostiqueur',
                      type: 'TEXT'
                    },
                    {
                      id: 'diagnostic_plomb_diagnostiqueur_adresse',
                      label: 'Adresse du diagnostiqueur',
                      type: 'ADDRESS'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ],
                    [
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ]
                  ],
                  id: '8dcef19e_0d3d_4701_9e22_a926b1611813',
                  label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                  type: 'CONDITION_BLOCK'
                },
                {
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'diagnostic_plomb_libre',
                  label: 'Diagnostic Plomb',
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'avant_1949'
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ]
              ],
              id: 'f922532a_cc46_4d06_b000_d899a7ebab17',
              label: 'Diagnostic Plomb',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_amiante_statut',
                  label: 'Diagnostic réalisé',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'diagnostic_amiante_date',
                      label: 'Date du diagnostic',
                      type: 'DATE'
                    },
                    {
                      choices: [
                        {
                          label: "Il n'a pas été repéré des matériaux contenant de l'amiante",
                          id: 'absence'
                        },
                        {
                          label: "Il a été repéré des matériaux contenant de l'amiante de la liste A",
                          id: 'liste_a'
                        },
                        {
                          label: "Il a été repéré des matériaux contenant de l'amiante de la liste B",
                          id: 'liste_b'
                        },
                        {
                          label: "Des locaux ou parties de locaux n'ont pas pu être visités",
                          id: 'non_visite'
                        }
                      ],
                      id: 'diagnostic_amiante_resultat',
                      label: 'Résultat du diagnostic',
                      multiple: true,
                      type: 'SELECT'
                    },
                    {
                      id: 'diagnostic_amiante_resultat_libre',
                      label: 'Résultat du diagnostic',
                      type: 'TEXTAREA'
                    },
                    {
                      id: 'diagnostic_amiante',
                      label: 'Diagnostic Amiante',
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: '5558c8cb_d498_4576_8611_53de4d8336de',
                  label: 'CONDITION_BLOCK_Informations sur le diagnostic amiante',
                  type: 'CONDITION_BLOCK'
                },
                {
                  children: [
                    {
                      id: 'diagnostic_amiante_diagnostiqueur_nom',
                      label: 'Nom du diagnostiqueur',
                      type: 'TEXT'
                    },
                    {
                      id: 'diagnostic_amiante_diagnostiqueur_adresse',
                      label: 'Adresse du diagnostiqueur',
                      type: 'ADDRESS'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ],
                    [
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ]
                  ],
                  id: 'e97c214c_cde7_4bc5_9fa4_164d40f134c6',
                  label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                  type: 'CONDITION_BLOCK'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: '1949_1997'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'avant_1949'
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ]
              ],
              id: 'ffa53660_32d0_4381_8265_39e17098e759',
              label: 'Diagnostic Amiante',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_gaz_presence_installation',
                  label: "Présence d'une installation de gaz",
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_gaz_vielle_installation',
                      label: 'Installation de plus de 15 ans',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          choices: [
                            {
                              id: 'non',
                              label: 'NON'
                            },
                            {
                              id: 'oui',
                              label: 'OUI'
                            }
                          ],
                          id: 'diagnostic_gaz_statut',
                          label: 'Diagnostic réalisé',
                          type: 'SELECT-BINARY'
                        },
                        {
                          children: [
                            {
                              id: 'diagnostic_gaz_date',
                              label: 'Date du diagnostic',
                              type: 'DATE'
                            },
                            {
                              choices: [
                                {
                                  label: "L'installation de gaz ne comporte aucune anomalie",
                                  id: 'sans'
                                },
                                {
                                  label: 'L’installation comporte des anomalies de type A1',
                                  id: '1'
                                },
                                {
                                  label: 'L’installation comporte des anomalies de type A2',
                                  id: '2'
                                },
                                {
                                  label: 'L’installation comporte des anomalies de type DGI',
                                  id: '3'
                                },
                                {
                                  label: 'L’installation comporte des anomalies de type 32c',
                                  id: '4'
                                }
                              ],
                              id: 'diagnostic_gaz_resultat',
                              label: 'Résultat du diagnostic',
                              multiple: true,
                              type: 'SELECT'
                            },
                            {
                              id: 'diagnostic_gaz',
                              label: 'Diagnostic Gaz',
                              type: 'UPLOAD'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_gaz_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_gaz_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ]
                          ],
                          id: '6a61397d_5b27_4d8e_859d_baead28c3e52',
                          label: 'CONDITION_BLOCK_Informations sur le diagnostic gaz',
                          type: 'CONDITION_BLOCK'
                        },
                        {
                          children: [
                            {
                              id: 'diagnostic_gaz_details_infos_diagnostiqueur_nom',
                              label: 'Nom du diagnostiqueur',
                              type: 'TEXT'
                            },
                            {
                              id: 'diagnostic_gaz_details_infos_diagnostiqueur_adresse',
                              label: 'Adresse du diagnostiqueur',
                              type: 'ADDRESS'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_gaz_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_gaz_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ]
                          ],
                          id: '47453f65_ca14_477e_b4d2_ddc61168ab8f',
                          label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                          type: 'CONDITION_BLOCK'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'construction',
                            value: 'moins_10_ans',
                            type: 'DIFFERENT'
                          },
                          {
                            id: 'diagnostic_gaz_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_gaz_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'construction',
                            value: 'moins_10_ans',
                            type: 'DIFFERENT'
                          },
                          {
                            id: 'diagnostic_gaz_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_gaz_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'construction',
                            value: 'moins_10_ans',
                            type: 'DIFFERENT'
                          },
                          {
                            id: 'diagnostic_gaz_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_gaz_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ]
                      ],
                      id: '4387fd76_ce6e_49af_a688_597cfeb11a2a',
                      label: "CONDITION_BLOCK_Informations sur l'installation au gaz",
                      type: 'CONDITION_BLOCK'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        value: 'moins_10_ans',
                        type: 'DIFFERENT'
                      },
                      {
                        id: 'diagnostic_gaz_presence_installation',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: '3de4abd1_6984_4756_a89f_20038cebcf69',
                  label: 'CONDITION_BLOCK_Installation au gaz',
                  type: 'CONDITION_BLOCK'
                }
              ],
              id: '390bede5_fe4a_4a96_a80f_125a29caf27a',
              label: 'Diagnostic Gaz',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_electrique_presence_installation',
                  label: "Présence d'une installation électrique",
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_electrique_vielle_installation',
                      label: 'Installation de plus de 15 ans',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          choices: [
                            {
                              id: 'non',
                              label: 'NON'
                            },
                            {
                              id: 'oui',
                              label: 'OUI'
                            }
                          ],
                          id: 'diagnostic_electrique_statut',
                          label: 'Diagnostic électrique réalisé',
                          type: 'SELECT-BINARY'
                        },
                        {
                          children: [
                            {
                              id: 'diagnostic_electrique_date',
                              label: 'Date du diagnostic',
                              type: 'DATE'
                            },
                            {
                              choices: [
                                {
                                  label: "L'installation électrique ne comporte aucune anomalie",
                                  id: 'sans'
                                },
                                {
                                  label: '1. L’appareil général de commande et de protection et de son accessibilité',
                                  id: '1'
                                },
                                {
                                  label:
                                    '2. La protection différentielle à l’origine de l’installation électrique et sa sensibilité appropriée aux conditions de mise à la terre',
                                  id: '2'
                                },
                                {
                                  label: '3. La  prise de terre et l’installation de mise à la terre',
                                  id: '3'
                                },
                                {
                                  label:
                                    '4. La protection contre les surintensités adaptée à la section des conducteurs',
                                  id: '4'
                                },
                                {
                                  label:
                                    '5. La liaison équipotentielle dans les locaux contenant une baignoire ou une douche',
                                  id: '5'
                                },
                                {
                                  label:
                                    '6. Les règles liées aux zones dans les locaux contenant une baignoire ou une douche',
                                  id: '6'
                                },
                                {
                                  label: '7. Des matériels électriques présentant des risques de contacts directs',
                                  id: '7'
                                },
                                {
                                  label: '8.1 Des matériels électriques vétustes, inadaptés à l’usage',
                                  id: '8_1'
                                },
                                {
                                  label: '8.2 Des conducteurs non protégés mécaniquement',
                                  id: '8_2'
                                },
                                {
                                  label:
                                    '9. Des appareils d’utilisation situés dans les parties communes et alimentés depuis la partie privative',
                                  id: '9'
                                },
                                {
                                  label: '10. La piscine privée ou le bassin de fontaine',
                                  id: '10'
                                }
                              ],
                              id: 'diagnostic_electrique_anomalies',
                              label: 'Anomalies détectées',
                              multiple: true,
                              type: 'SELECT'
                            },
                            {
                              choices: [
                                {
                                  id: 'non',
                                  label: 'NON'
                                },
                                {
                                  id: 'oui',
                                  label: 'OUI'
                                }
                              ],
                              conditions: [
                                [
                                  {
                                    id: 'construction',
                                    value: 'moins_10_ans',
                                    type: 'DIFFERENT'
                                  },
                                  {
                                    id: 'diagnostic_electrique_presence_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_statut',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_vielle_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'construction',
                                    value: 'moins_10_ans',
                                    type: 'DIFFERENT'
                                  },
                                  {
                                    id: 'diagnostic_electrique_presence_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_statut',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_vielle_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'construction',
                                    value: 'moins_10_ans',
                                    type: 'DIFFERENT'
                                  },
                                  {
                                    id: 'diagnostic_electrique_presence_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_statut',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'construction',
                                    value: 'moins_10_ans',
                                    type: 'DIFFERENT'
                                  },
                                  {
                                    id: 'diagnostic_electrique_presence_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_vielle_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'construction',
                                    value: 'moins_10_ans',
                                    type: 'DIFFERENT'
                                  },
                                  {
                                    id: 'diagnostic_electrique_presence_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'diagnostic_electrique_statut',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_vielle_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'diagnostic_electrique_statut',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'diagnostic_electrique_vielle_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ]
                              ],
                              id: 'diagnostic_electrique_anomalies_autres_renvoi',
                              label: 'Autres anomalies : se référer au diagnostic',
                              type: 'SELECT-BINARY'
                            },
                            {
                              choices: [
                                {
                                  label: "L'installation électrique ne fait l'objet d'aucune constatations",
                                  id: 'sans'
                                },
                                {
                                  label: '1. Des installations non couvertes par le présent diagnostic',
                                  id: '1'
                                },
                                {
                                  label: '2. Des points de contrôles n’ayant pu être vérifiés',
                                  id: '2'
                                },
                                {
                                  label: '3. Des constatations concernant l’installation et son environnement',
                                  id: '3'
                                }
                              ],
                              id: 'diagnostic_electrique_constatations',
                              label: 'Autres Constatations',
                              multiple: true,
                              type: 'SELECT'
                            },
                            {
                              choices: [
                                {
                                  id: 'non',
                                  label: 'NON'
                                },
                                {
                                  id: 'oui',
                                  label: 'OUI'
                                }
                              ],
                              conditions: [
                                [
                                  {
                                    id: 'construction',
                                    value: 'moins_10_ans',
                                    type: 'DIFFERENT'
                                  },
                                  {
                                    id: 'diagnostic_electrique_presence_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_statut',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_vielle_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'construction',
                                    value: 'moins_10_ans',
                                    type: 'DIFFERENT'
                                  },
                                  {
                                    id: 'diagnostic_electrique_presence_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_statut',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_vielle_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'construction',
                                    value: 'moins_10_ans',
                                    type: 'DIFFERENT'
                                  },
                                  {
                                    id: 'diagnostic_electrique_presence_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_statut',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'construction',
                                    value: 'moins_10_ans',
                                    type: 'DIFFERENT'
                                  },
                                  {
                                    id: 'diagnostic_electrique_presence_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_vielle_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'construction',
                                    value: 'moins_10_ans',
                                    type: 'DIFFERENT'
                                  },
                                  {
                                    id: 'diagnostic_electrique_presence_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'diagnostic_electrique_statut',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    id: 'diagnostic_electrique_vielle_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'diagnostic_electrique_statut',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    id: 'diagnostic_electrique_vielle_installation',
                                    type: 'EQUALS',
                                    value: 'oui'
                                  },
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ],
                                [
                                  {
                                    type: 'EQUALS_CONTRACT_MODELS',
                                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                                  },
                                  {
                                    type: 'EQUALS_TEMPLATES',
                                    templates: [
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                    ]
                                  }
                                ]
                              ],
                              id: 'diagnostic_electrique_constatation_autres_renvoi',
                              label: 'Autres constatations : se référer au diagnostic',
                              type: 'SELECT-BINARY'
                            },
                            {
                              id: 'diagnostic_electrique',
                              label: 'Diagnostic Electrique',
                              type: 'UPLOAD'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_electrique_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_electrique_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ]
                          ],
                          id: '239f3da4_a87f_45d3_b518_96c281762049',
                          label: 'CONDITION_BLOCK_Informations sur le diagnostic électrique',
                          type: 'CONDITION_BLOCK'
                        },
                        {
                          children: [
                            {
                              id: 'diagnostic_electrique_details_infos_diagnostiqueur_nom',
                              label: 'Nom du diagnostiqueur',
                              type: 'TEXT'
                            },
                            {
                              id: 'diagnostic_electrique_details_infos_diagnostiqueur_adresse',
                              label: 'Adresse du diagnostiqueur',
                              type: 'ADDRESS'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'construction',
                                value: 'moins_10_ans',
                                type: 'DIFFERENT'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ]
                          ],
                          id: '8d171b49_fab7_4dae_ba75_4539542eb62c',
                          label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                          type: 'CONDITION_BLOCK'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'construction',
                            value: 'moins_10_ans',
                            type: 'DIFFERENT'
                          },
                          {
                            id: 'diagnostic_electrique_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_electrique_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'construction',
                            value: 'moins_10_ans',
                            type: 'DIFFERENT'
                          },
                          {
                            id: 'diagnostic_electrique_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_electrique_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'construction',
                            value: 'moins_10_ans',
                            type: 'DIFFERENT'
                          },
                          {
                            id: 'diagnostic_electrique_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_electrique_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ]
                      ],
                      id: '1d47168a_d679_4d47_9d66_eceb38c47425',
                      label: "CONDITION_BLOCK_Informations sur l'installation électrique",
                      type: 'CONDITION_BLOCK'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        value: 'moins_10_ans',
                        type: 'DIFFERENT'
                      },
                      {
                        id: 'diagnostic_electrique_presence_installation',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: '6ab5df0b_43f2_4bd3_8d15_db6ae28bc52f',
                  label: 'CONDITION_BLOCK_Installation électrique',
                  type: 'CONDITION_BLOCK'
                }
              ],
              id: '69f9f445_7830_49a5_be15_4db2438b3a5c',
              label: 'Diagnostic Électrique',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_termites_commune',
                  label: 'Commune concernée par les termites',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_termites_statut',
                      label: 'Diagnostic réalisé',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'diagnostic_termites_date',
                          label: 'Date du diagnostic',
                          type: 'DATE'
                        },
                        {
                          choices: [
                            {
                              label: "Absence de traces d'infestation de termite",
                              id: 'absence'
                            },
                            {
                              label: "Présence de traces d'infestation de termite",
                              id: 'presence'
                            }
                          ],
                          id: 'diagnostic_termites_resultat',
                          label: 'Résultat du diagnostic',
                          type: 'SELECT'
                        },
                        {
                          children: [
                            {
                              id: 'diagnostic_termites_diagnostiqueur_nom',
                              label: 'Nom du diagnostiqueur',
                              type: 'TEXT'
                            },
                            {
                              id: 'diagnostic_termites_diagnostiqueur_adresse',
                              label: 'Adresse du diagnostiqueur',
                              type: 'ADDRESS'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ]
                          ],
                          id: '6ceb41a1_cff0_49db_9dd4_85d0b2b28707',
                          label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                          type: 'CONDITION_BLOCK'
                        },
                        {
                          id: 'diagnostic_termites',
                          label: 'Diagnostic Termites',
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'diagnostic_termites_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_termites_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_termites_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_termites_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_termites_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_termites_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ]
                      ],
                      id: '81c81080_5b46_4757_980c_f074e5611233',
                      label: 'Informations sur le diagnostic termites',
                      type: 'CATEGORY'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostic_termites_commune',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: '77d4af9a_18cf_4c4f_bd8b_4a7461d499c2',
                  label: 'CONDITION_BLOCK_Zone concernée par les termites',
                  type: 'CONDITION_BLOCK'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_termites_informatif',
                      label: 'Contamination par les termites',
                      type: 'SELECT-BINARY'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostic_termites_commune',
                        type: 'EQUALS',
                        value: 'non'
                      }
                    ]
                  ],
                  id: 'e5ab7cc5_e733_4832_b646_d165e06acf86',
                  label: 'CONDITION_BLOCK_Zone non concernée par les termites',
                  type: 'CONDITION_BLOCK'
                }
              ],
              id: '527d1720_c244_4cb8_9839_1ecdb0e037a9',
              label: 'Diagnostic Termites',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_merule_commune',
                  label: "Bien susceptible d'être concerné par la mérule",
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_merule_statut',
                      label: 'Diagnostic réalisé',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'diagnostic_merule_date',
                          label: 'Date du diagnostic',
                          type: 'DATE'
                        },
                        {
                          choices: [
                            {
                              label: 'Absence de traces visibles de champignons lignivores',
                              id: 'absence'
                            },
                            {
                              label: 'Présence de traces visibles de champignons lignivores',
                              id: 'presence'
                            }
                          ],
                          id: 'diagnostic_merule_resultat',
                          label: 'Résultat du diagnostic',
                          type: 'SELECT'
                        },
                        {
                          children: [
                            {
                              id: 'diagnostic_merule_diagnostiqueur_nom',
                              label: 'Nom du diagnostiqueur',
                              type: 'TEXT'
                            },
                            {
                              id: 'diagnostic_merule_diagnostiqueur_adresse',
                              label: 'Adresse du diagnostiqueur',
                              type: 'ADDRESS'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_merule_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_merule_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_merule_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_merule_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ]
                          ],
                          id: '96e00c87_8227_42a7_9e09_a66ecf580e31',
                          label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                          type: 'CONDITION_BLOCK'
                        },
                        {
                          id: 'diagnostic_merule',
                          label: 'Diagnostic Mérule',
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'diagnostic_merule_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_merule_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_merule_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_merule_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_merule_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_merule_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ]
                      ],
                      id: '3f5672e4_e395_4005_aa31_4de996778fdf',
                      label: 'CONDITION_BLOCK_Informations sur le diagnostic mérule',
                      type: 'CONDITION_BLOCK'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostic_merule_commune',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: '447dfc78_32af_437e_8188_9fe3a474fbec',
                  label: 'CONDITION_BLOCK_Zone concernée par la mérule',
                  type: 'CONDITION_BLOCK'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_merule_informatif',
                      label: 'Contamination par la mérule',
                      type: 'SELECT-BINARY'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostic_merule_commune',
                        type: 'EQUALS',
                        value: 'non'
                      }
                    ]
                  ],
                  id: 'a35f222a_a0c9_4649_a076_91b3be594d3f',
                  label: 'CONDITION_BLOCK_Zone non concernée par la mérule',
                  type: 'CONDITION_BLOCK'
                }
              ],
              id: '5c7b2983_b191_41ea_8883_8bf605cb0bc3',
              label: 'Diagnostic Mérules',
              type: 'CATEGORY'
            }
          ],
          id: '61272b83_98c1_4e62_9c3a_486c27ad429e',
          label: 'CONDITION_BLOCK_Information sur le dossier de diagnostic technique',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              description: 'En cas de DPE Vierge la réponse doit être non',
              id: 'dpe_details_statut',
              label: 'Diagnostic réalisé',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'dpe_diagnostiqueur_date',
                  label: 'Date du diagnostic',
                  type: 'DATE'
                },
                {
                  description:
                    "Indiquer d'abord la lettre puis éventuellement le montant de la consommation. Exemple : E - 355",
                  id: 'dpe_consommation_energetique',
                  label: 'Performance / consommation énergétique',
                  type: 'TEXT'
                },
                {
                  id: 'dpe_emission_gaz',
                  label: 'Emission de gaz à effet de serre',
                  type: 'TEXT'
                },
                {
                  id: 'dpe_depense_annuelle',
                  label: "Montant des dépenses annuelles d'énergie",
                  type: 'TEXT'
                },
                {
                  id: 'dpe_depense_annuelle_annee',
                  label: "Année(s) de référence pour les dépenses d'énergie",
                  type: 'TEXT'
                },
                {
                  id: 'dpe',
                  label: 'Diagnostic de performance énergétique',
                  type: 'UPLOAD'
                },
                {
                  children: [
                    {
                      id: 'dpe_diagnostiqueur_nom',
                      label: 'Nom du diagnostiqueur',
                      type: 'TEXT'
                    },
                    {
                      id: 'dpe_diagnostiqueur_adresse',
                      label: 'Adresse du diagnostiqueur',
                      type: 'ADDRESS'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      },
                      {
                        id: 'dpe_details_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'dpe_details_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ]
                  ],
                  id: 'a69f6ce0_a56c_4c53_a303_91499b9e1334',
                  label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                  type: 'CONDITION_BLOCK'
                }
              ],
              conditions: [
                [
                  {
                    id: 'dpe_details_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ]
              ],
              id: 'ed8c054a_8257_4b96_a551_e19c09aad22a',
              label: 'CONDITION_BLOCK_Informations sur le diagnostic de performance énergétique',
              type: 'CONDITION_BLOCK'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'dpe_libre',
              label: 'Diagnostic de performance énergétique',
              type: 'UPLOAD'
            }
          ],
          id: '2a1be32b_97db_4d40_9636_82f42d22799d',
          label: 'Diagnostic de performance Énergétique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'erp_global',
              label:
                'Bien situé dans une zone couverte par un plan de prévention des risques naturels, miniers ou technologiques',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'indemnite_catastrophe',
              label: "Sinistre d'origine catastrophe naturelle survenu sur le bien",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'indemnite_catastrophe',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'indemnite_catastrophe_liste_arretes',
              label: 'Liste des arrêtés portant reconnaissance de l’état de catastrophe naturelle',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'indemnite_catastrophe',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'indemnite_catastrophe_arrete_prefectoral',
              label: 'Dernier arrêté prefectoral',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'indemnite_catastrophe',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'indemnite_catastrophe_systeme_geographique',
              label: 'Informations mises à disposition dans le système d’information géographique',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'indemnite_catastrophe',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'indemnite_catastrophe_code_environnement',
              label: 'Dispositions de l’article L556-2 du Code de l’environnement',
              type: 'UPLOAD'
            },
            {
              id: 'indemnite_catastrophe_description',
              label: 'Description du sinistre',
              type: 'TEXT'
            },
            {
              id: 'indemnite_catastrophe_date',
              label: 'Date de survenance du sinistre',
              type: 'DATE'
            },
            {
              id: 'erp',
              label: 'Etat des risques',
              type: 'UPLOAD'
            },
            {
              id: 'georisque',
              label: 'Géorisques',
              type: 'UPLOAD'
            }
          ],
          id: '10031fae_bc1c_4081_989c_a9830eb7704f',
          label: 'Diagnostics environnementaux',
          type: 'CATEGORY'
        }
      ],
      id: '86fcc38c_0ee1_4ff7_9da8_2b306f7fb45a',
      label: 'Diagnostics',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  label: '1 étoile',
                  id: '1'
                },
                {
                  label: '2 étoiles',
                  id: '2'
                },
                {
                  label: '3 étoiles',
                  id: '3'
                },
                {
                  label: '4 étoiles',
                  id: '4'
                },
                {
                  label: '5 étoiles',
                  id: '5'
                },
                {
                  label: 'Aucun classement',
                  id: 'aucun'
                }
              ],
              id: 'etat_descriptif_categorie_classement',
              label: 'Catégorie de classement',
              type: 'SELECT'
            },
            {
              id: 'etat_descriptif_arrete_prefectoral',
              label: 'Arrété préfectoral du',
              type: 'DATE'
            }
          ],
          id: 'cda61c3d_9f46_43af_b3fb_46b137b2d063',
          label: 'Renseignements Généraux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Neuve',
                  id: 'neuve'
                },
                {
                  label: 'Récente',
                  id: 'recente'
                },
                {
                  label: 'Ancienne',
                  id: 'ancienne'
                }
              ],
              id: 'etat_descriptif_type_construction',
              label: 'Type de construction',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  label: 'Une maison',
                  id: 'maison'
                },
                {
                  label: 'Indépendante',
                  id: 'independant'
                },
                {
                  label: 'Avec jardin',
                  id: 'jardin'
                },
                {
                  label: 'Un studio',
                  id: 'studio'
                },
                {
                  label: 'Un appartement',
                  id: 'appartement'
                }
              ],
              id: 'etat_descriptif_type_bien',
              label: 'Type de bien',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'etat_descriptif_plusieurs_logement',
              label: 'Meublé situé dans immeuble avec plusieurs logements',
              type: 'SELECT-BINARY'
            },
            {
              id: 'etat_descriptif_plusieurs_logement_nombre',
              label: 'Nombres de logements',
              type: 'NUMBER'
            },
            {
              choices: [
                {
                  label: 'Un appartement',
                  id: 'appartement'
                },
                {
                  label: 'Une villa',
                  id: 'villa'
                },
                {
                  label: 'Occupées partiellement par le propriétaire',
                  id: 'proprietaire'
                },
                {
                  label: "Occupées par d'autres locataires",
                  id: 'locataire'
                }
              ],
              id: 'etat_descriptif_plusieurs_logement_situation',
              label: 'Pièces situées dans',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'etat_descriptif_handicap',
              label: 'Meublé accessibles aux personnes handicapées',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'Chauffage central',
                  id: 'chauffage'
                },
                {
                  label: 'Climatisation',
                  id: 'climatisation'
                },
                {
                  label: "Rafraîchissement d'air",
                  id: 'rafraichissement'
                }
              ],
              id: 'etat_descriptif_chauffage',
              label: 'Chauffage / Climatisation',
              multiple: true,
              type: 'SELECT'
            },
            {
              id: 'etat_descriptif_superficie',
              label: 'Superficie totale',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_nombre_pieces',
              label: "Nombres de pièces d'habitation",
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_nombre_salle_eau',
              label: "Nombre de salle d'eau",
              type: 'NUMBER'
            },
            {
              choices: [
                {
                  label: 'Cuisine séparée',
                  id: 'cuisine_separee'
                },
                {
                  label: 'Coin-cuisine dans la pièce principale',
                  id: 'cuisine'
                },
                {
                  label: "Existence d'une entrée",
                  id: 'entree'
                }
              ],
              id: 'etat_descriptif_pieces_caracteristique',
              label: 'Caractéristiques des pièces',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  label: 'Jardin privatif',
                  id: 'jardin'
                },
                {
                  label: 'Parc privatif',
                  id: 'parc'
                },
                {
                  label: 'Cour privative',
                  id: 'cour'
                },
                {
                  label: 'Garage privatif',
                  id: 'garage'
                },
                {
                  label: 'Emplacement de voiture privatif',
                  id: 'emplacement'
                },
                {
                  label: 'Terrasse privative',
                  id: 'terrasse'
                },
                {
                  label: 'Loggia privative',
                  id: 'loggia'
                },
                {
                  label: 'Balcon privatif',
                  id: 'balcon'
                }
              ],
              id: 'etat_descriptif_jouissance_privative',
              label: 'Jouissance privative',
              multiple: true,
              type: 'SELECT'
            },
            {
              id: 'etat_descriptif_jouissance_terrasse_superficie',
              label: 'Superficie de la terrasse',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_jouissance_terrasse_vue',
              label: 'Vue de la terrasse',
              type: 'TEXT'
            },
            {
              id: 'etat_descriptif_jouissance_loggia_superficie',
              label: 'Superficie de la loggia',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_jouissance_loggia_vue',
              label: 'Vue de la loggia',
              type: 'TEXT'
            },
            {
              id: 'etat_descriptif_jouissance_balcon_superficie',
              label: 'Superficie du balcon',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_jouissance_balcon_vue',
              label: 'Vue du balcon',
              type: 'TEXT'
            }
          ],
          id: '777c202d_0ebe_4e6f_8539_a76482e4836d',
          label: 'Principales caractéristiques',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Isolé',
                  id: 'isole'
                },
                {
                  label: 'Dans une ferme',
                  id: 'ferme'
                },
                {
                  label: 'Dans un hameau',
                  id: 'hameau'
                },
                {
                  label: 'Dans un village',
                  id: 'village'
                },
                {
                  label: 'Dans une ville',
                  id: 'ville'
                }
              ],
              id: 'etat_descriptif_situation_meuble',
              label: 'Le meublé est',
              type: 'SELECT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_ski',
                  label: 'Proximité de pistes de ski',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_ski_distance',
                  label: 'Distance des pistes de ski',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_mer',
                  label: 'Proximité de la mer',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_mer_distance',
                  label: 'Distance de la mer',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_lac',
                  label: "Proximité d'un lac",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_lac_distance',
                  label: 'Distance du lac',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_plage',
                  label: "Proximité d'une plage",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_plage_distance',
                  label: 'Distance de la plage',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_foret',
                  label: "Proximité d'une forêt",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_foret_distance',
                  label: 'Distance de la forêt',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_riviere',
                  label: "Proximité d'une rivière",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_riviere_distance',
                  label: 'Distance de la rivière',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_port',
                  label: "Proximité d'un port",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_port_distance',
                  label: 'Distance du port',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_centre_distance',
                  label: 'Distance du centre ville',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_autres',
                  label: "Autres centres d'intérêt",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_autres_distance',
                  label: "Liste et distance des autres centres d'intérêt",
                  type: 'TEXTAREA'
                }
              ],
              id: 'a15ae250_dd1c_42de_bccf_e225b40cc4eb',
              label: "Distance des centres d'intérêt touristique",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'etat_descriptif_situation_proximite_sncf_distance',
                  label: 'Distance gare SNCF',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_gare_distance',
                  label: 'Distance gare routière',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_aeroport_distance',
                  label: 'Distance aéroport',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_medecin_distance',
                  label: 'Distance Médecin',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_hopital_distance',
                  label: 'Distance Hôpital',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_supermarche_distance',
                  label: 'Distance Centre commercial/supermarché',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_restaurant_distance',
                  label: 'Distance restaurant',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_epicerie_distance',
                  label: 'Distance épicerie',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_laverie_distance',
                  label: 'Distance laverie',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_services_autres',
                  label: 'Autres services',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_services_autres_distance',
                  label: 'Distance autres services',
                  suffix: 'km',
                  type: 'NUMBER'
                }
              ],
              id: '67e547fc_b8da_456b_bc98_5c485b4cca10',
              label: 'Distance des principaux services',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_inconvenient_bruits',
                  label: 'Bruits',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_inconvenient_bruits_liste',
                  label: 'Liste des bruits',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_inconvenient_odeurs',
                  label: 'Odeurs',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_inconvenient_odeurs_liste',
                  label: 'Liste des odeurs',
                  type: 'TEXT'
                }
              ],
              id: '954bc23c_8900_45a0_b394_08b93ccfecf4',
              label: 'Inconvénients de voisinage',
              type: 'CATEGORY'
            }
          ],
          id: 'd113d6e6_b859_41f1_8af1_f47784c2fda9',
          label: 'Situation dans la localité',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'etat_descriptif_etat_entretien',
              label: "Etat d'entretien général",
              type: 'TEXT'
            },
            {
              children: [
                {
                  children: [
                    {
                      id: 'etat_desciptif_piece_agencement_nom_piece',
                      label: 'Nom de la pièce',
                      type: 'TEXT'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_surface_piece',
                      label: 'Surface',
                      suffix: 'm2',
                      type: 'NUMBER'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_fenetres_piece',
                      label: 'Nombre de Fenêtres',
                      type: 'NUMBER'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_lits_piece',
                      label: 'Nombres de Lits',
                      type: 'NUMBER'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_mobilier_piece',
                      label: 'Mobilier',
                      type: 'TEXT'
                    },
                    {
                      choices: [
                        {
                          label: 'Nord',
                          id: 'nord'
                        },
                        {
                          label: 'Sud',
                          id: 'sud'
                        },
                        {
                          label: 'Est',
                          id: 'est'
                        },
                        {
                          label: 'Ouest',
                          id: 'ouest'
                        }
                      ],
                      id: 'etat_desciptif_piece_agencement_exposition_piece',
                      label: 'Exposition',
                      type: 'SELECT'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_vue_piece',
                      label: 'Vue',
                      type: 'TEXT'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'etat_desciptif_piece_agencement_independance_piece',
                      label: 'Indépendance de la pièce',
                      type: 'SELECT-BINARY'
                    }
                  ],
                  id: 'etat_desciptif_piece_agencement',
                  label: 'Ajouter une pièce',
                  repetition: {
                    label: [
                      {
                        type: 'TEXT',
                        value: ' '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'etat_desciptif_piece_agencement_nom_piece'
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                }
              ],
              id: 'ca4b1515_dae1_4e94_9add_c8ee2e4fe5df',
              label: 'Agencement des pièces',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Evier avec eau chaude/froide',
                      id: 'evier'
                    },
                    {
                      label: 'VMC',
                      id: 'vmc'
                    },
                    {
                      label: 'Hotte aspirante',
                      id: 'hotte'
                    },
                    {
                      label: 'Table de cuisson',
                      id: 'cuisson'
                    },
                    {
                      label: 'Four',
                      id: 'four'
                    },
                    {
                      label: 'Four à micro-ondes',
                      id: 'microonde'
                    },
                    {
                      label: 'Réfrigérateur',
                      id: 'refrigerateur'
                    },
                    {
                      label: 'Congélateur',
                      id: 'congelateur'
                    },
                    {
                      label: 'Lave-vaisselle',
                      id: 'lave'
                    },
                    {
                      label: 'Batterie de cuisine',
                      id: 'batterie'
                    },
                    {
                      label: 'Autocuiseur',
                      id: 'autocuiseur'
                    }
                  ],
                  id: 'etat_descriptif_agencement_cuisine',
                  label: 'Equipements de la cuisine',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_feux',
                  label: 'Nombres de feux',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      label: 'Gaz de ville',
                      id: 'gaz'
                    },
                    {
                      label: 'Bouteille de gaz',
                      id: 'bouteille'
                    },
                    {
                      label: 'Electricité',
                      id: 'electricite'
                    },
                    {
                      label: 'Mixte',
                      id: 'mixte'
                    }
                  ],
                  id: 'etat_descriptif_agencemement_cuisine_plaque',
                  label: 'Alimentation de la plaque',
                  type: 'SELECT'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_refrigerateur_contenance',
                  label: 'Contenance du Réfrigérateur',
                  suffix: 'litres',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_agencemement_cuisine_refrigerateur_compartiment',
                  label: 'Réfrigérateur avec compartiment conservation',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_congelateur_contenance',
                  label: 'Contenance du congélateur',
                  suffix: 'litres',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_batterie_descriptif',
                  label: 'Batterie de cuisine descriptif',
                  type: 'TEXT'
                }
              ],
              id: '43807e35_78d3_45f9_8da0_ec65934ad305',
              label: 'Agencement de la cuisine',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  children: [
                    {
                      id: 'etat_desciptif_sanitaire_lavabos_nombre',
                      label: 'Nombre de lavabos',
                      type: 'NUMBER'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'etat_desciptif_sanitaire_douche',
                      label: 'Présence douche',
                      type: 'SELECT-BINARY'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'etat_desciptif_sanitaire_baignoire',
                      label: 'Présence baignoire avec douche',
                      type: 'SELECT-BINARY'
                    }
                  ],
                  id: 'etat_desciptif_sanitaire',
                  label: "Ajouter une salle d'eau",
                  repetition: {
                    label: [
                      {
                        type: 'TEXT',
                        value: " Salle d'eau"
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                },
                {
                  id: 'etat_desciptif_sanitaire_wc',
                  label: 'Nombre de WC intérieur au meublé',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_desciptif_sanitaire_wc_independant',
                  label: "Nombre de WC intérieur au meublé et indépendant de la salle d'eau",
                  type: 'NUMBER'
                }
              ],
              id: '59102100_ce9b_4d45_b581_6abaef64c629',
              label: 'Equipements sanitaires',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Téléphone dans le logement',
                      id: 'telephone'
                    },
                    {
                      label: 'Téléphone à proximité',
                      id: 'telephone_proximite'
                    },
                    {
                      label: 'Accès internet haut debit',
                      id: 'internet'
                    },
                    {
                      label: 'TV couleur',
                      id: 'tv'
                    },
                    {
                      label: 'Lecteur DVD',
                      id: 'lecteur'
                    },
                    {
                      label: 'Chaîne Hi-Fi avec radio',
                      id: 'radio'
                    },
                    {
                      label: 'Lave-linge électrique',
                      id: 'lave_linge'
                    },
                    {
                      label: 'Sèche-Linge électrique',
                      id: 'seche_linge'
                    },
                    {
                      label: 'Etendoir à linge',
                      id: 'etendoir'
                    },
                    {
                      label: 'Fer à repasser',
                      id: 'fer'
                    },
                    {
                      label: 'Sèche-cheveux',
                      id: 'seche_cheveux'
                    },
                    {
                      label: 'Aspirateur',
                      id: 'aspirateur'
                    },
                    {
                      label: 'Autres équipements',
                      id: 'autre'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien',
                  label: 'Equipements du bien',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  id: 'etat_descriptif_equipement_bien_telephone_numero',
                  label: 'Numéro de téléphone',
                  type: 'PHONE'
                },
                {
                  id: 'etat_descriptif_equipement_bien_telephone_distance',
                  label: 'Distance du Téléphone',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_lave_linge',
                  label: 'Lave-linge particulier au logement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_seche_linge',
                  label: 'Sèche-linge particulier au logement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_etendoir',
                  label: 'Etendoir à linge intérieur au logement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_planche',
                  label: 'Planche à repasser',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_equipement_bien_autre',
                  label: 'Autre équipements à noter',
                  type: 'TEXT'
                },
                {
                  id: 'etat_descriptif_equipement_bien_loisirs_attaches',
                  label: 'Equipements de loisirs attachés',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_documentation_pratique',
                  label: 'Documentation pratique remise au locataire',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_documentation_touristique',
                  label: 'Documentation touristique remise au locataire',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_animaux',
                  label: 'Animaux domestiques acceptés',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_blanchisserie',
                  label: 'Service quotidien de blanchisserie',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_menage',
                  label: 'Service quotidien de ménage',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '94aefac0_62fe_4fe3_9b52_41ad204c149b',
              label: 'Equipements divers',
              type: 'CATEGORY'
            }
          ],
          id: 'd6e17894_4f2f_40d1_8be4_1cbe83012d91',
          label: 'Description du meublé',
          type: 'CATEGORY'
        }
      ],
      id: '7693f9b6_c40c_4a1c_b83f_094af3ce5cbc',
      label: 'Etat descriptif',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'belgique_revenu_cadastral',
          label: 'Revenu cadastral du bien',
          type: 'PRICE'
        }
      ],
      id: '3365abc5_98f7_4b3a_a306_c3bc0d5fc315',
      label: 'Revenu cadastral',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes',
          label: 'Présence de servitudes',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes_titre',
          label: 'Servitudes contenue au titre de propriété',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_presence_servitudes_titre_liste',
          label: 'Liste des servitudes',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes_vendeur',
          label: 'Servitudes octroyée par le Vendeur',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_presence_servitudes_vendeur_liste',
          label: 'Liste des servitudes',
          type: 'TEXT'
        }
      ],
      id: '9c740b4f_ed45_4d98_9982_d6343268cacc',
      label: 'Servitudes',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_publicitaire_presence',
          label: 'Panneau publicitaire',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_publicitaire_presence_preemption',
          label: 'Le contrat contient un droit de préemption',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_presence',
          label: 'Panneaux photovoltaïques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_proprietaire',
          label: 'Vendeur propriétaire des panneaux photovoltaïques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_presence_vente',
          label: 'Panneaux compris dans la vente',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_certificat',
          label: 'Vendeur bénéficiaire de certificats verts',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_panneau_photovoltaiques_certificat_liste',
          label: 'Liste de certificats verts',
          type: 'TEXTAREA'
        }
      ],
      id: '23cba686_c30a_41d5_89c6_50e12a051a47',
      label: 'Panneaux / Enseignes',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_diu_presence',
              label: 'Bien concerné par un DIU',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_diu_liste',
              label: 'Liste des travaux concernés',
              type: 'TEXTAREA'
            }
          ],
          id: '0dcf867f_083d_432b_a447_655dd1fc6ea9',
          label: "Dossier d'intervention ultérieur",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_date_1981',
              label: "Installation électrique datant d'avant le 01/10/1981",
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'belgique_pv_controle_electricite_statut',
              label: "Contrôle de l'installation électrique",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_rapport',
              label: 'Rapport de visite remis',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_pv_controle_electricite_date',
              label: 'Date de réalisation du PV de contrôle',
              type: 'DATE'
            },
            {
              id: 'belgique_pv_controle_electricite_nom',
              label: 'Nom de la société ayant réalisé le PV',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_conformite',
              label: 'Installation conforme',
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'belgique_pv_controle_electricite_dispense_raison',
              label: 'Raison de la dispense',
              type: 'SELECT'
            }
          ],
          id: '5af2da2c_9aa8_4a90_9fe8_ae43afa8630f',
          label: 'Electricité',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_amiante_presence',
              label: 'Amiante utilisée lors de la construction ou présence dans certains éléments',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_amiante_liste',
              label: "Situation de l'amiante dans le bien",
              type: 'TEXT'
            }
          ],
          id: '87c5efdb_d8bc_4fb5_bbdb_02540c368d0c',
          label: 'Amiante',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_peb_effectue',
              label: 'PEB effectué',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_peb_numero',
              label: 'Numéro du PEB',
              type: 'TEXT'
            },
            {
              id: 'belgique_peb_expert',
              label: "Nom de l'expert",
              type: 'TEXT'
            },
            {
              id: 'belgique_peb_date',
              label: "Date d'établissement du PEB",
              type: 'DATE'
            },
            {
              id: 'belgique_peb_classe',
              label: 'Classe énergétique du bien',
              type: 'TEXT'
            }
          ],
          id: '6d8b7e91_b7ca_492f_ab5e_a0c31b7ef82c',
          label: 'Performance énergétique du bâtiment',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'belgique_compteur_eau_numero',
              label: "Numéro de compteur d'eau",
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_gaz_numero',
              label: 'Numéro de compteur Gaz',
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_gaz_ean',
              label: 'Code EAN Gaz',
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_electrique_numero',
              label: 'Numéro de compteur électricité',
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_electrique_ean',
              label: 'Code EAN électricité',
              type: 'TEXT'
            }
          ],
          id: '9383504e_3af2_4179_8763_960c5bf7dd1a',
          label: 'Compteurs',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_toiture',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_toiture',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '50480d57_293b_44a4_a9ea_4eb37f435ab4',
              label: 'Toiture(s)',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_menuiserie',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_menuiserie',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '3f758d8b_a62a_40dd_921c_cf341f4181e7',
              label: 'Menuiserie extérieure',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_electrique',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_electrique',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '523d7ca6_850a_4898_ac12_8bc7ebeb9a47',
              label: 'Installation electrique',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_sanitaire',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_sanitaire',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '47378088_41c6_4c4b_84ba_7848ef6ba988',
              label: 'Sanitaires',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_chauffage',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_chauffage',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '040bdf8b_b263_4e4e_a8f7_e376d3423ef2',
              label: 'Chauffage central',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_revetement_sol',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_revetement_sol',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '929fb2dd_69e3_4d49_b02f_f307124bfcd2',
              label: 'Revêtement sols',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_revetement_mur',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_revetement_mur',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: 'ba0cbb6d_ee50_4639_ad1b_1769e10e777f',
              label: 'Revêtements murs',
              type: 'CATEGORY'
            }
          ],
          id: 'be140e99_17f2_4641_b19c_174dbd05c7c9',
          label: 'Etat des Postes principaux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_urbanisme_statut',
              label: "Bien concerné par un permis d'urbanisme",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_permis_urbanisme_date',
              label: 'Date du permis',
              type: 'DATE'
            },
            {
              id: 'belgique_permis_urbanisme_lieu',
              label: 'Lieu de délivrance du permis',
              type: 'TEXT'
            },
            {
              id: 'belgique_permis_urbanisme_numero',
              label: 'Numéro de permis',
              type: 'TEXT'
            }
          ],
          id: '6cd17b28_b1c9_4a50_926e_705ffafe8903',
          label: "Permis d'urbanisme",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_prime_wallonnie',
              label: 'Prime de la région Wallonne utilisée',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_prime_wallonnie_date',
              label: 'Date de la prime',
              type: 'DATE'
            },
            {
              id: 'belgique_prime_wallonnie_type',
              label: 'Type de la prime',
              type: 'TEXT'
            },
            {
              id: 'belgique_prime_wallonnie_montant',
              label: 'Montant de la prime',
              type: 'PRICE'
            }
          ],
          id: '441a6e3d_d1a7_489f_82c5_011df47aff4d',
          label: 'Prime',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_location',
              label: "Bien objet d'un permis de location",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_permis_location_date',
              label: 'Date du permis de location',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_logement_inoccupe',
              label: 'Bien objet d’un PV de constat de logement inoccupé',
              type: 'SELECT-BINARY'
            }
          ],
          id: '4fe110fa_a5e2_4b10_8f03_e46b8e37d9c9',
          label: 'Habitation durable',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'belgique_usage_bien',
              label: 'Affectation du bien',
              type: 'TEXT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_infraction_urbanistique',
                  label: 'Bien en infraction urbanistique ou environnementale',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_infraction_urbanistique_liste',
                  label: "Type d'infraction",
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_infraction_urbanistique_presence',
                  label: 'Infraction présente lors du transfert de propriété',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '588b66ec_0f26_4456_8b44_7eba2bd916c1',
              label: 'Infraction',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_insalubrite',
                  label: 'Bien déclaré inhabitable ou insalubre',
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'ab58a9ef_e235_45ac_855c_43bcea33076e',
              label: 'Insalubrité',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_inoccupe',
                  label: 'Immeuble déclaré inoccupé ou abandonné',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '79f99283_e974_4243_bffd_2f3345eb6308',
              label: 'Inoccupation',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_alignement',
                  label: "Bien dans le périmètre d'un plan d'alignement",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'aa9966fc_fd8a_4e5f_91f4_5088c6909605',
              label: 'Alignement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_travaux_presence',
                  label: 'Présence de travaux nécessitant un permis',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_responsabilite_decennale',
                  label: 'Bien concerné par la responsabilité décennale',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_travaux_juillet_2018',
                  label: 'Travaux soumis à permis délivré après le 1er juillet 2018',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'belgique_travaux_list_belgique_travaux_permis_obtenu',
                      label: 'Permis obtenu',
                      type: 'SELECT-BINARY'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_titre',
                      label: 'Titre des Travaux',
                      type: 'TEXT'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_description',
                      label: 'Description des travaux',
                      type: 'TEXTAREA'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_date_achevement',
                      label: "Date d'achèvement des travaux",
                      type: 'DATE'
                    },
                    {
                      conditions: [[]],
                      id: 'belgique_travaux_list_belgique_travaux_aministie',
                      label: 'Régime de régularisation / amnistie',
                      type: 'TEXTAREA'
                    }
                  ],
                  id: 'belgique_travaux_list',
                  label: 'Ajouter Travaux',
                  repetition: {
                    label: [
                      {
                        type: 'TEXT',
                        value: ' '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'belgique_travaux_list_belgique_travaux_titre'
                      },
                      {
                        type: 'TEXT',
                        value: ' - '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'belgique_travaux_list_belgique_travaux_date_achevement'
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                }
              ],
              id: '0929b195_4bcf_4a1c_906b_45b733a48c81',
              label: 'Travaux',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_statut',
                  label: 'Bien soumis à Lotissement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_permis_delivrance',
                  label: "Permis d'urbanisation délivré",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_lotissement_notaire_nom',
                  label: "Notaire ayant reçu l'acte de division",
                  type: 'TEXT'
                },
                {
                  id: 'belgique_lotissement_notaire_date',
                  label: "Date de l'acte de division",
                  type: 'DATE'
                },
                {
                  id: 'belgique_lotissement_destination_vendu',
                  label: 'Destination du bien vendu',
                  type: 'TEXT'
                },
                {
                  id: 'belgique_lotissement_destination_conserver',
                  label: 'Destination du bien conservé par le vendeur',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_cs_avis',
                  label: "Condition suspensive d'absence d'avis défavorable",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'c05a3f6c_1829_4315_98c3_5a6009a0aa6c',
              label: 'Lotissement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_eau_usee_presence',
                  label: "Bien équipé d'un système d'épuration des eaux usées",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_eau_usee_repartition_clause',
                  label: 'Ajouter la répartition des frais sur demande des intercommunales',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '9003a7cc_d5e2_4491_8188_c684d441ff15',
              label: 'Equipement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_zone_inondable',
                  label: 'Bien situé en zone inondable',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_zone_inondable_inonde',
                  label: 'Bien a subi une inondation',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_zone_inondable_etendue',
                  label: "Étendue de l'inondation",
                  type: 'TEXT'
                }
              ],
              id: '3e0b69e9_f600_48ee_8e4e_c16965236c70',
              label: 'Zones inondables',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_mesure_expropriation',
                  label: "Mesure d'expropriation de l'immeuble",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'aa9b6b44_5976_42fe_93ff_72280cb9aa65',
              label: 'Expropriation',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_litige',
                  label: "Litige relatif à l'immeuble",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_litiges_liste',
                  label: 'Description des litiges',
                  type: 'TEXT'
                }
              ],
              id: '3844c679_54d3_46e0_a57a_2632776ed81a',
              label: 'Litige',
              type: 'CATEGORY'
            }
          ],
          id: 'e5ec41a1_28ce_4286_83e9_286fc1d991e2',
          label: 'Situation urbanistique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [],
              id: 'belgique_certibeau_statut',
              label: 'Raccordement à la distribution publique de l’eau',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise',
              label: 'CertIBEau réalisé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_a_realiser',
              label: 'Le CertIBEau devra être réalisé',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_certibeau_resultat',
              label: 'Résultat du CertIBEau',
              type: 'TEXTAREA'
            },
            {
              id: 'belgique_certibeau_realise_date',
              label: "Date d'établissement du CertIBEau",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise_conformite',
              label: 'CertIBEau délivré conforme',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise_conformite_modification',
              label: "Modifications réalisés depuis l'établissement du CertIBEau",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_certibeau_realise_conformite_modification_liste',
              label: 'Liste des modifications intervenues',
              type: 'TEXTAREA'
            }
          ],
          id: 'b673feca_0085_42b2_a02a_96f9cfd5e57c',
          label: 'CertIBeau',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_detecteur_incendie_statut',
              label: "Bien équipé de détecteur d'incendie",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_detecteur_incendie_nombre',
              label: 'Nombre de détecteur',
              type: 'NUMBER'
            },
            {
              id: 'belgique_detecteur_incendie_pieces',
              label: 'Pièces équipées',
              type: 'TEXT'
            }
          ],
          id: '3bacc869_3928_46ab_9fbc_1d5c12e4864d',
          label: 'Détecteur incendie',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_presence',
              label: "Présence d'une citerne",
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'belgique_citerne_type',
              label: 'Type de citerne',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_capacite_3000',
              label: 'Capacité de la citerne supérieure à 3.000 litres',
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'belgique_citerne_enterree_aerienne',
              label: 'Situation de la citerne',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_attestation',
              label: 'Attestation de conformité / certificat de visite de contrôle',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_citerne_mazout_capacite',
              label: 'Capacité de la citerne mazout',
              type: 'TEXT'
            }
          ],
          id: 'af4a7466_b7b5_4ae8_aeca_79384df47238',
          label: 'Citerne mazout / gaz',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_environnement_statut',
              label: "Bien objet d'un permis d'environnement / déclaration de classe 3",
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'belgique_permis_environnement_statut_type',
              label: 'Type',
              type: 'SELECT'
            },
            {
              id: 'belgique_permis_environnement_type',
              label: "Objet du permis d'environnement",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_environnement_activite',
              label: 'Activité exercée imposant un tel permis ou déclaration',
              type: 'SELECT-BINARY'
            }
          ],
          id: '0e021ae1_5fc0_460f_999d_880948cd3239',
          label: "Permis d'environnement",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_copropriete_acte_base_fourniture',
              label: 'Acte de base fourni',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_copropriete_pv_ag_fourniture',
              label: "PV d'assemblée général fourni",
              type: 'SELECT-BINARY'
            }
          ],
          id: '6599aaf1_2b7c_4117_b688_8a195d002bab',
          label: 'Copropriété',
          type: 'CATEGORY'
        }
      ],
      id: '2fd4f01b_91d8_4e61_b646_7a8e0f9a7647',
      label: 'Informations Administratives',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__BIEN__MONOPROPRIETE_HABITATION',
  label: 'Bien en monopropriété - Habitation',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__BIEN__MONOPROPRIETE_HABITATION',
  specificTypes: ['BIEN', 'MONOPROPRIETE_HABITATION'],
  type: 'RECORD'
};
