// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationImmobilierLocationMandat: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_PRELLO_FORCE_MAJEURE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'prello_force_majeure',
          label: 'Force Majeure',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_PRELLO_LISTE_ENTRETIEN'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'prello_liste_entretien',
          label: "Liste des prestations d'entretien",
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_PRELLO_CONCIERGERIE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'prello_conciergerie',
          label: 'Liste service conciergerie',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'prello_appel_charge',
          label: 'Appel de charges mensuelles',
          type: 'UPLOAD'
        }
      ],
      id: '11ab3f19_40a6_4e8d_b3b0_190ea63fa01e',
      label: 'Documents',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__AJP__DOSSIER_DE_LOCATION_AJP']
              }
            ]
          ],
          id: 'mandat_carte_gestion',
          label: 'Utiliser la carte de gestion',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'mandat_type_statut_location',
              label: 'Un mandat de location ou de gestion'
            },
            {
              id: 'mandat_type_statut_administration',
              label: "Un mandat d'administration pour une location saisonnière"
            },
            {
              id: 'mandat_type_statut_recherche',
              label: "Un mandat de recherche d'une location"
            }
          ],
          id: 'mandat_type_statut',
          label: 'Type de Mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'simple',
              label: 'de location simple'
            },
            {
              id: 'semi',
              label: 'de location semi-exclusif'
            },
            {
              id: 'exclusif',
              label: 'de location exclusif'
            }
          ],
          id: 'mandat_type_statut_pas_gestion',
          label: 'Type de Mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'simple',
              label: 'de location simple'
            },
            {
              id: 'semi',
              label: 'de location semi-exclusif'
            },
            {
              id: 'exclusif',
              label: 'de location exclusif'
            },
            {
              id: 'gestion',
              label: 'de gestion'
            }
          ],
          id: 'mandat_type',
          label: 'Type de Mandat',
          type: 'SELECT'
        },
        {
          choices: [],
          id: 'mandat_type_hermes',
          label: 'Type de mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'simple',
              label: 'Simple'
            },
            {
              id: 'exclusif',
              label: 'Exclusif'
            }
          ],
          id: 'mandat_type_simple',
          label: 'Type de Mandat',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'mandat_semi_agence_unique',
              label: 'Aucun honoraires ne sont dus si le Bailleur trouve lui même un Locataire'
            },
            {
              id: 'mandat_semi_honoraire_reduits',
              label: 'Les honoraires sont réduits de moitié si le Bailleur trouve lui même un locataire'
            }
          ],
          id: 'mandat_semi_condition',
          label: 'Conditions de la semi-exclusivité :',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_location_89',
          label: 'Location soumise à la loi du 6 Juillet 1989 (location meublée ou nue)',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'professionnel',
              label: 'Bail professionnel'
            },
            {
              id: 'commercial',
              label: 'Bail commercial'
            },
            {
              id: 'saisonnier',
              label: 'Location Saisonnière'
            },
            {
              id: 'fermage',
              label: 'Bail rural / fermage'
            },
            {
              id: 'libre',
              label: 'Secteur libre : résidence secondaire, garage...'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_location_89',
                type: 'DIFFERENT',
                value: 'oui'
              }
            ]
          ],
          id: 'mandat_regime_location_hors_habitation',
          label: 'Régime de la location',
          type: 'SELECT'
        },
        {
          id: 'mandat_regime_location_hors_habitation_autre_texte',
          label: 'Type de location',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'recherche_simple',
              label: 'de recherche simple'
            },
            {
              id: 'recherche_semi',
              label: 'de recherche semi-exclusif'
            },
            {
              id: 'recherche_exclusif',
              label: 'de recherche exclusif'
            }
          ],
          id: 'mandat_recherche_type',
          label: 'Type de Mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'mandat_recherche_semi_agence_unique',
              label:
                "Aucun honoraires ne sont dus si le Locataire trouve lui même une location, l'Agence est le seul intermédiaire professionnel"
            },
            {
              id: 'mandat_recherche_semi_honoraire_reduits',
              label: 'Les honoraires sont réduits de moitié si le Locataire trouve lui même une location'
            }
          ],
          id: 'mandat_recherche_semi_condition',
          label: 'Conditions de la semi-exclusivité :',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'transaction',
              label: 'de Transaction'
            },
            {
              id: 'gestion',
              label: 'de Gestion'
            }
          ],
          id: 'mandat_registre',
          label: 'Numéro à prendre dans le registre',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_registre',
                value: 'gestion',
                type: 'EQUALS'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'UNIS_LOCATION_HABITATION_MANDAT_LOCATION_UNIS',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_CIVIL',
                  'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE'
                ]
              }
            ],
            [
              {
                id: 'mandat_type',
                value: 'gestion',
                type: 'EQUALS'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: [
                  'UNIS_LOCATION_HABITATION_MANDAT_LOCATION_UNIS',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_CIVIL',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_PARAHOTELIER',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_SAISONNIER',
                  'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                  'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                  'AJP__LOCATION__MANDAT_DE_GESTION_AJP',
                  'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION'
                ]
              }
            ],
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'UNIS_LOCATION_HABITATION_MANDAT_GESTION_UNIS',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_SAISONNIER',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_PARAHOTELIER',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_CONVENTION_LOCATION_SAISONNIERE',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_CONVENTION_LOCATION_SAISONNIERE_PARAHOTELIER',
                  'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                  'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
                  'AJP__LOCATION__MANDAT_DE_GESTION_AJP',
                  'BENEDIC__LOCATION__MANDAT_DE_GESTION'
                ]
              }
            ]
          ],
          id: 'mandat_numero_gestion',
          label: 'Numéro du mandat',
          placeholder: 'entrez votre numéro de mandat',
          register: {
            type: 'MANAGEMENT',
            contracts: [
              'IMMOBILIER_LOCATION_MANDAT_LOCATION',
              'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
              'IMMOBILIER_LOCATION_MANDAT_SAISONNIER',
              'LOCATION__MANDAT_DE_LOCATION_SAISONNIERE_ANGLAIS',
              'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_EXCLUSIF_EFFICITY',
              'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_SIMPLE_EFFICITY',
              'IMMOBILIER_LOCATION_MANDAT_DROIT_COMMUN',
              'KELLER_WILLIAMS_IMMOBILIER_LOCATION_HABITATION_COMANDAT_LOCATION',
              'UNIS_LOCATION_HABITATION_MANDAT_GESTION_UNIS',
              'UNIS_LOCATION_HABITATION_MANDAT_LOCATION_UNIS',
              'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_SAISONNIER',
              'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_CIVIL',
              'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_PARAHOTELIER',
              'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
              'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
              'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
              'AJP__LOCATION__MANDAT_DE_GESTION_AJP',
              'AJP__LOCATION__MANDAT_DE_LOCATION'
            ]
          },
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_registre',
                value: 'transaction',
                type: 'EQUALS'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'UNIS_LOCATION_HABITATION_MANDAT_LOCATION_UNIS',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_CIVIL',
                  'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                  'AJP__LOCATION__MANDAT_DE_LOCATION'
                ]
              }
            ],
            [
              {
                id: 'mandat_type',
                value: 'exclusif',
                type: 'EQUALS'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: [
                  'UNIS_LOCATION_HABITATION_MANDAT_LOCATION_UNIS',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_CIVIL',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_PARAHOTELIER',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_SAISONNIER',
                  'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                  'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_EXCLUSIF_EFFICITY'
                ]
              }
            ],
            [
              {
                id: 'mandat_type',
                value: 'semi',
                type: 'EQUALS'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: [
                  'UNIS_LOCATION_HABITATION_MANDAT_LOCATION_UNIS',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_CIVIL',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_PARAHOTELIER',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_SAISONNIER',
                  'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                  'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_EXCLUSIF_EFFICITY'
                ]
              }
            ],
            [
              {
                id: 'mandat_type',
                value: 'simple',
                type: 'EQUALS'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: [
                  'UNIS_LOCATION_HABITATION_MANDAT_LOCATION_UNIS',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_CIVIL',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_PARAHOTELIER',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_SAISONNIER',
                  'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                  'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_EXCLUSIF_EFFICITY'
                ]
              }
            ],
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_EXCLUSIF_EFFICITY',
                  'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_SIMPLE_EFFICITY',
                  'KELLER_WILLIAMS_IMMOBILIER_LOCATION_HABITATION_COMANDAT_LOCATION',
                  'PROPRIETES_PRIVEES_IMMOBILIER_LOCATION_MANDAT_LOCATION',
                  'AJP__LOCATION__MANDAT_DE_LOCATION'
                ]
              }
            ]
          ],
          id: 'mandat_numero_transaction',
          label: 'Numéro du mandat',
          placeholder: 'entrez votre numéro de mandat',
          register: {
            type: 'TRANSACTION',
            contracts: [
              'IMMOBILIER_LOCATION_MANDAT_LOCATION',
              'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
              'IMMOBILIER_LOCATION_MANDAT_SAISONNIER',
              'LOCATION__MANDAT_DE_LOCATION_SAISONNIERE_ANGLAIS',
              'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_EXCLUSIF_EFFICITY',
              'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_SIMPLE_EFFICITY',
              'IMMOBILIER_LOCATION_MANDAT_DROIT_COMMUN',
              'KELLER_WILLIAMS_IMMOBILIER_LOCATION_HABITATION_COMANDAT_LOCATION',
              'UNIS_LOCATION_HABITATION_MANDAT_LOCATION_UNIS',
              'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_SAISONNIER',
              'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_CIVIL',
              'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_PARAHOTELIER',
              'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
              'AJP__LOCATION__MANDAT_DE_LOCATION'
            ]
          },
          type: 'TEXT'
        },
        {
          id: 'mandat_recherche_numero',
          label: 'Numéro du mandat',
          placeholder: 'entrez votre numéro de mandat',
          register: {
            type: 'TRANSACTION',
            contracts: [
              'IMMOBILIER_LOCATION_MANDAT_RECHERCHE',
              'LOCATION__MANDAT_DE_RECHERCHE_ANGLAIS',
              'KELLER_WILLIAMS_IMMOBILIER_LOCATION_HABITATION_COMANDAT_RECHERCHE_LOCATION'
            ]
          },
          type: 'TEXT'
        },
        {
          id: 'mandat_comandataire_numero',
          label: 'Numéro du mandat du comandataire',
          type: 'NUMBER'
        },
        {
          id: 'mandat_numero_date_prise',
          label: 'Date de prise du numéro du mandat',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_anglais',
          label: 'Mandat en anglais',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'principale_vide',
              label: 'Résidence principale - Vide'
            },
            {
              id: 'principale_meuble',
              label: 'Résidence principale - Meublée'
            },
            {
              id: 'secondaire_vide',
              label: 'Résidence secondaire - Vide'
            },
            {
              id: 'secondaire_meuble',
              label: 'Résidence secondaire - Meublée'
            },
            {
              id: 'mixte',
              label: 'Mixte habitation / professionnel'
            }
          ],
          id: 'mandat_usage_era',
          label: 'Usage du bien',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'habitation',
              label: 'Résidence principale habitation nue ou meublée'
            },
            {
              id: 'mixte',
              label: 'Résidence principale mixte habitation et professionnelle'
            },
            {
              id: 'saisonnier',
              label: 'Location saisonnière'
            },
            {
              id: 'professionnel',
              label: 'Location exclusivement professionnelle / commerciale'
            },
            {
              id: 'secondaire',
              label: 'Résidence secondaire'
            },
            {
              id: 'autre',
              label: 'Autre type'
            }
          ],
          id: 'destination_complete',
          label: 'Usage du bien',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'habitation',
              label: 'Habitation'
            },
            {
              id: 'commercial',
              label: 'Commercial'
            }
          ],
          id: 'usage_habitation_commercial',
          label: 'Usage du bien',
          type: 'SELECT'
        },
        {
          id: 'destination_mixte_activite',
          label: 'Liste des activités professionnelles autorisées',
          type: 'TEXT'
        },
        {
          id: 'destination_complete_autre',
          label: 'Autre usage',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'meublee_statut',
          label: 'Location meublée',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_sous_location',
          label: 'Le Mandant est le locataire du bien',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_sous_location_proprietaire',
          label: 'Nom du propriétaire du bien',
          type: 'TEXT'
        },
        {
          id: 'mandat_recherche_designation',
          label: 'Désignation du bien à rechercher',
          type: 'TEXTAREA'
        },
        {
          id: 'mandat_recherche_geographique',
          label: 'Secteur géographique de recherche',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_recherche_duree_type',
          label: 'Le type et la durée du bail sont-ils indiqués dans le mandant de recherche ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_recherche_loyer_maximum',
          label: 'Montant maximum du loyer mensuel recherché',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            "Les coordonnées pourront toujours être renseignées dans la fiche mais n'apparaîtront pas dans le contrat.",
          id: 'mandat_coordonnees',
          label: "Les coordonnées de l'intermédiaire doivent-elles être indiquées dans le mandat ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'mandat_gestion_duree_mois',
              label: 'en mois'
            },
            {
              id: 'mandat_gestion_duree_autre',
              label: 'selon une autre durée libre'
            }
          ],
          id: 'mandat_gestion_duree',
          label: 'La durée du mandat du gestion est exprimée',
          type: 'SELECT'
        },
        {
          id: 'mandat_duree',
          label: 'Durée du mandat (en mois)',
          type: 'NUMBER'
        },
        {
          description:
            "Interdiction faite au mandant de traiter avec tout candidat locataire ayant été présenté par le mandataire ou ayant visité le bien avec lui ; également période où le mandataire peut demander l'identité du locataire trouvé par le mandant.",
          id: 'mandat_duree_interdiction',
          label: "Durée de l'interdiction faite au Mandant après le terme du mandat",
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          id: 'mandat_duree_an',
          label: 'Durée du mandat (année)',
          suffix: 'année',
          type: 'NUMBER'
        },
        {
          id: 'mandat_duree_autre',
          label: 'Durée du mandat (texte libre)',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_reconduction',
          label: 'Le mandat est-il reconductible par tacite reconduction ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_duree_recondution',
          label: 'Durée de la reconduction',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          id: 'mandat_duree_recondution_totale',
          label: 'Durée totale du mandat',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          id: 'mandat_duree_recondution_totale_annee',
          label: 'Durée totale du mandat',
          suffix: 'année',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_duree_date_prise_effet_statut',
          label: "Ajouter une date de prise d'effet du mandat",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_duree_date_prise_effet_date',
          label: "Date d'effet du mandat",
          type: 'DATE'
        },
        {
          id: 'delegation_mandat_date',
          label: "Date d'échéance du mandat initial",
          type: 'DATE'
        },
        {
          id: 'mandat_depart',
          label: 'Date de début du mandat',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'tres',
              label: 'Très tendue'
            },
            {
              id: 'tendu',
              label: 'Tendue'
            },
            {
              id: 'autre',
              label: 'Autre zone'
            }
          ],
          id: 'zone_situation_bien',
          label: 'Zone de situation du bien',
          type: 'PICK_LIST'
        },
        {
          id: 'mandat_moyen_diffusion',
          label: 'Moyen de diffusion des annonces',
          type: 'TEXT'
        },
        {
          id: 'date_limite_non_reconduction',
          label: 'Date limite de non-reconduction',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'presentation',
              label: 'Présenter les biens'
            },
            {
              id: 'visite',
              label: 'Visiter les biens'
            },
            {
              id: 'publicite',
              label: 'Publicité'
            },
            {
              id: 'ensemble',
              label: 'Ensemble des pouvoirs'
            }
          ],
          id: 'pouvoir_delegue',
          label: 'Pouvoirs du délégué :',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          id: 'facture_numero',
          label: 'Numéro de facture',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'simplicite',
              label: 'Simplicité'
            },
            {
              id: 'totale',
              label: 'Garantie Totale'
            }
          ],
          id: 'offre_simplicite_totale',
          label: 'Prestations supplémentaires, offre :',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'loue_actuellement',
          label: 'Le bien est loué actuellement',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'loue_18_derniers_mois',
          label: 'Le bien était loué les 18 derniers mois',
          type: 'SELECT-BINARY'
        },
        {
          id: 'loue_actuellement_duree',
          label: 'Durée du bail',
          type: 'TEXT'
        },
        {
          id: 'loue_actuellement_loyer',
          label: 'Montant du loyer mensuel hors charges',
          type: 'PRICE'
        },
        {
          id: 'loue_actuellement_date_effet',
          label: "Date d'effet du bail",
          type: 'DATE'
        },
        {
          id: 'loue_actuellement_date_liberation',
          label: 'Date de liberation du bien',
          type: 'DATE'
        },
        {
          id: 'loue_actuellement_nom_locataire',
          label: 'Nom du locataire',
          type: 'TEXT'
        },
        {
          id: 'loue_actuellement_date_signature',
          label: 'Date de signature du bail',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'loue_actuellement_disponibilite',
          label: 'Ajouter la date de disponibilité du bien',
          type: 'SELECT-BINARY'
        },
        {
          id: 'loue_actuellement_disponibilite_date',
          label: 'Date de disponibilité',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'bailleur_precisions',
          label: 'Ajouter des précisions sur le Bailleur',
          type: 'SELECT-BINARY'
        },
        {
          id: 'bailleur_precisions_texte',
          label: 'Précisions clause Bailleur',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'regime_fiscal_statut',
          label: 'Option du bailleur pour un régime fiscal',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'duflot',
              label: 'Duflot / Pinel'
            },
            {
              id: 'scellier',
              label: 'Scellier'
            },
            {
              id: 'robien',
              label: 'Robien'
            },
            {
              id: 'borloo',
              label: 'Borloo'
            },
            {
              id: 'besson',
              label: 'Besson'
            },
            {
              id: 'perissol',
              label: 'Périssol'
            },
            {
              id: 'historique',
              label: 'Immeubles Historiques'
            },
            {
              id: 'malraux',
              label: 'Malraux'
            },
            {
              id: 'girardin',
              label: 'Girardin'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          id: 'regime_fiscal_liste',
          label: 'Régime fiscal applicable',
          multiple: true,
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'scellier',
              label: 'Scellier Classique'
            },
            {
              id: 'scellier_intermediaire',
              label: 'Scellier Intermédiaire'
            },
            {
              id: 'borloo',
              label: 'Borloo Populaire'
            },
            {
              id: 'duflot',
              label: 'Duflot'
            },
            {
              id: 'normandie',
              label: 'de Normandie'
            },
            {
              id: 'autre',
              label: 'Autre'
            },
            {
              id: 'aucun',
              label: 'aucun'
            }
          ],
          id: 'regime_defiscalisation_liste',
          label: 'Régime de défiscalisation',
          multiple: true,
          type: 'SELECT'
        },
        {
          id: 'regime_fiscal_liste_autre',
          label: 'Autre régime fiscal',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'conventionnement_statut',
          label: "Le Bien a fait l'objet d'un conventionnement (Etat ou ANAH)",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'financement_ptz',
          label: 'Le Bien a été financé par un Prêt Taux Zéro',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'a_jour',
              label: 'DPE postérieur au 1er juillet 2021'
            },
            {
              id: 'en_cours',
              label: 'DPE réalisé avant le 1er Juillet 2021'
            },
            {
              id: 'aucun',
              label: 'Aucun DPE fourni'
            },
            {
              id: 'non_soumis',
              label: 'Bien non concerné par le DPE'
            }
          ],
          id: 'dpe_statut',
          label: 'Concernant le DPE',
          type: 'SELECT'
        },
        {
          id: 'date_liberation',
          label: 'Date de libération du bien',
          type: 'DATE'
        },
        {
          id: 'support_publicite',
          label: 'Supports de publicité utilisés',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'ttc',
              label: 'En TTC'
            },
            {
              id: 'ht',
              label: 'En HT'
            },
            {
              id: 'double',
              label: 'TTC et HT'
            }
          ],
          id: 'mandat_vente_honoraires_affichage',
          label: 'Affichage des honoraires',
          type: 'PICK_LIST'
        },
        {
          id: 'mandat_pourcentage_remuneration_loyer',
          label: 'Pourcentage de rémunération à prélever sur les loyers',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_delegation_statut',
          label: 'Délégation possible pour location et relocation',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_delegation_nom',
          label: 'Intermédiaire concerné',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'offre_simplicite_totale',
                value: 'totale',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_ASSURANCE_GLI_GARANTIE_TOTALE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'assurance_garantie_totale',
          label: 'Assurance des loyer impayés GARANTIE TOTALE',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'loue_actuellement',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'contrat_bail_document',
          label: 'Contrat de bail',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'loue_actuellement',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'dossier_locataire_document',
          label: 'Dossier Locataire',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'loue_actuellement',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'etat_lieux_document',
          label: 'Etat des lieux',
          type: 'UPLOAD'
        },
        {
          children: [
            {
              id: 'delegation_clause_particuliere_liste_delegation_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'delegation_clause_particuliere_liste_delegation_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          conditions: [],
          id: 'delegation_clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'delegation_clause_particuliere_liste_delegation_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          children: [
            {
              id: 'mandat_gestion_date_acquisition',
              label: "Date d'acquisition du bien ou des lots",
              type: 'DATE'
            },
            {
              id: 'mandat_gestion_notaire_acquisition_nom',
              label: "Nom du notaire ayant reçu l'acte",
              type: 'TEXT'
            },
            {
              id: 'mandat_gestion_notaire_acquisition_adresse',
              label: 'Adresse du notaire',
              type: 'ADDRESS'
            },
            {
              id: 'mandat_gestion_regime_matrimonial',
              label: 'Régime matrimonial au jour du mandat',
              type: 'TEXT'
            }
          ],
          id: 'd2cbaf8a_bccb_4034_bafb_a46e3dd8f280',
          label: "Informations relatives à l'acquisition",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'action_local_commercial_statut',
              label: 'Location portant sur un local commercial',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'bail',
                  label: 'Signature du bail'
                },
                {
                  id: 'actes',
                  label: 'Signature du bail et des autres actes'
                }
              ],
              id: 'action_local_commercial_signature',
              label: "Pouvoir donné à l'agence pour",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'action_gestion_travaux',
              label: 'Mandataire assure la gestion des travaux',
              type: 'SELECT-BINARY'
            },
            {
              id: 'action_gestion_travaux_montant',
              label: 'Montant maximum des travaux à gérer par le Mandataire',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'action_gestion_travaux_augmentation',
              label: 'Montant des travaux amené à augmenter',
              type: 'SELECT-BINARY'
            },
            {
              id: 'action_gestion_travaux_augmentation_montant',
              label: "Montant d'augmentation des travaux",
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'action_gestion_prestation_courante',
              label: 'Ajouter des prestations courantes',
              type: 'SELECT-BINARY'
            },
            {
              id: 'action_gestion_prestation_courante_liste',
              label: 'Liste des prestations courantes',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  id: 'travaux',
                  label: 'Gestion des gros travaux'
                },
                {
                  id: 'sinistre',
                  label: 'Gestion des sinistres'
                },
                {
                  id: 'representation',
                  label: 'Représentation en AG'
                },
                {
                  id: 'declaration',
                  label: 'Aide à la déclaration foncière'
                },
                {
                  id: 'subvention',
                  label: 'Demande de subvention ANAH'
                },
                {
                  id: 'charges',
                  label: 'Gestion des charges'
                },
                {
                  id: 'personnel',
                  label: "Gestion du personnel de l'immeuble"
                },
                {
                  id: 'assurance',
                  label: 'Gestion des assurances'
                },
                {
                  id: 'autre',
                  label: 'Autres prestations'
                }
              ],
              description: "Vous devez être immatriculé à l'ORIAS pour gérer les assurances",
              id: 'action_gestion_prestation_liste',
              label: 'Prestations supplémentaires à accomplir par le Mandataire',
              multiple: 'true',
              type: 'SELECT'
            },
            {
              id: 'action_gestion_prestation_liste_contenu',
              label: 'Liste des autres prestations supplémentaires',
              type: 'TEXT'
            },
            {
              id: 'mandat_gestion_rapport',
              label: "La rapport de gestion s'effectue tous les",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'honoraires_gestion_pourcentage',
                  label: 'selon un pourcentage des sommes encaissées par le mandataire'
                },
                {
                  id: 'honoraires_gestion_autre',
                  label: 'selon une autre méthode à préciser en texte libre'
                }
              ],
              id: 'mandat_gestion_calcul_honoraires',
              label: 'Les honoraires de gestion sont calculés',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'pourcentage',
                  label: 'En pourcentage'
                },
                {
                  id: 'forfait',
                  label: 'Selon un forfait'
                }
              ],
              id: 'mandat_gestion_calcul_forfait_pourcentage',
              label: 'Honoraires de gestion calculés',
              type: 'SELECT-BINARY'
            },
            {
              id: 'mandat_gestion_honoraires_pourcentage',
              label: 'Pourcentage des honoraires de gestion',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'mandat_gestion_honoraires_pourcentage_ht',
              label: 'Pourcentage des honoraires de gestion HT',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'mandat_gestion_reversement_jour',
              label: 'Jour de versement des sommes dues au Mandat',
              type: 'TEXT'
            },
            {
              id: 'mandat_gestion_honoraires_forfait',
              label: 'Montant du forfait des honoraires Hors Taxe',
              type: 'PRICE'
            },
            {
              id: 'mandat_gestion_honoraires_assiette',
              label: 'Assiette de prélèvement des honoraires',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_gestion_calcul_honoraires',
                    value: 'honoraires_gestion_autre',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'mandat_gestion_honoraires_autre',
              label: 'Précisez le mode de calcul des honoraires de gestion',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'honoraires_gestion_charge_bailleur',
                  label: 'du Bailleur'
                },
                {
                  id: 'honoraires_gestion_charge_locataire',
                  label: 'du Locataire'
                },
                {
                  id: 'honoraires_gestion_charge_double',
                  label: 'du Bailleur et du Locataire'
                }
              ],
              id: 'mandat_saisonnier_honoraires_charge',
              label: "Les honoraires d'agence sont à la charge",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'honoraires_gestion_versement_prelevement',
                  label: 'par prélèvement sur les sommes encaissées'
                },
                {
                  id: 'honoraires_gestion_versement_mensuel',
                  label: 'tous les mois'
                },
                {
                  id: 'honoraires_gestion_versement_autre',
                  label: 'autre'
                }
              ],
              id: 'mandat_gestion_honoraires_versement',
              label: 'Les honoraires de gestion sont versés',
              type: 'SELECT'
            },
            {
              id: 'mandat_saisonnier_honoraires_montant',
              label: "Montant des honoraires d'agence",
              type: 'PRICE'
            },
            {
              id: 'mandat_saisonnier_honoraires_bailleur_montant',
              label: "Montant des honoraires d'agence charge bailleur",
              type: 'PRICE'
            },
            {
              id: 'mandat_saisonnier_honoraires_locataire_montant',
              label: "Montant des honoraires d'agence charge locataire",
              type: 'PRICE'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_gestion_honoraires_versement',
                    value: 'honoraires_gestion_versement_mensuel',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'mandat_gestion_honoraires_versement_mensuel',
              label: 'Jour du mois du versement',
              type: 'NUMBER'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_gestion_honoraires_versement',
                    value: 'honoraires_gestion_versement_autre',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'mandat_gestion_honoraires_versement_autre',
              label: 'Précisez les modalités de versement des honoraires de gestion',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'modalite_honoraires_virement',
                  label: 'Par virement'
                },
                {
                  id: 'modalite_honoraires_cheque',
                  label: 'Par chèque'
                },
                {
                  id: 'modalite_honoraires_liquide',
                  label: 'En liquide'
                }
              ],
              id: 'mandat_gestion_modalite_honoraires',
              label: 'Modalité de paiement des honoraires de gestion',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'mandat_gestion_depot_conservation',
              label: 'Le Mandataire conservera les dépôts de garantie',
              type: 'SELECT-BINARY'
            },
            {
              id: 'mandat_gestion_arret_compte',
              label: 'Arrêt des comptes à la fin de',
              type: 'TEXT'
            },
            {
              children: [
                {
                  id: 'mandat_gestion_pourcentage_ht',
                  label: 'Pourcentage de rémunération HT (sur les sommes encaissées pour le compte du Mandant)',
                  suffix: '%',
                  type: 'NUMBER'
                },
                {
                  id: 'mandat_gestion_pourcentage_ttc',
                  label: 'Pourcentage de rémunération TTC (sur les sommes encaissées pour le compte du Mandant)',
                  suffix: '%',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'habitation',
                      label: "Locaux d'habitation"
                    },
                    {
                      id: 'commercial',
                      label: 'Locaux commerciaux ou bureaux'
                    },
                    {
                      id: 'civil',
                      label: 'Locaux relevant du Code Civil'
                    },
                    {
                      id: 'autre',
                      label: 'Autres honoraires particuliers'
                    }
                  ],
                  id: 'mandat_gestion_type_location',
                  label: 'Le Mandat de gestion porte sur',
                  multiple: true,
                  type: 'SELECT'
                }
              ],
              id: 'f72d058a_a912_4937_bec6_ad312c4a95b4',
              label: 'Honoraires de base forfaitaires',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'mandat_gestion_pourcentage_commercial_location_ht',
                  label: 'Pourcentage de rémunération HT en ce qui concerne les honoraires de location',
                  suffix: '%',
                  type: 'NUMBER'
                },
                {
                  id: 'mandat_gestion_pourcentage_commercial_redaction_ht',
                  label: 'Pourcentage de rémunération HT en ce qui concerne les honoraires de rédaction',
                  suffix: '%',
                  type: 'NUMBER'
                }
              ],
              id: '6e9e6cd9_a5f6_4a45_9219_16d017eb8532',
              label: 'Honoraires de location - Locaux commerciaux et bureaux',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'mandat_gestion_pourcentage_code_civil_location_ht',
                  label: 'Pourcentage de rémunération HT en ce qui concerne les honoraires de location',
                  suffix: '%',
                  type: 'NUMBER'
                },
                {
                  id: 'mandat_gestion_pourcentage_code_civil_redaction_ht',
                  label: 'Pourcentage de rémunération HT en ce qui concerne les honoraires de rédaction',
                  suffix: '%',
                  type: 'NUMBER'
                }
              ],
              id: 'b73ea73b_3949_403f_9086_a5b33a7ddc45',
              label: 'Honoraires de location - Locaux relevant du Code Civil',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'icc',
                      label: 'ICC - Indice du coût de la construction'
                    },
                    {
                      id: 'irl',
                      label: 'IRL - Indice de référence des loyers'
                    },
                    {
                      id: 'ilat',
                      label: 'ILAT - Indice des loyers des activités tertiaires'
                    }
                  ],
                  id: 'mandat_gestion_indice_revision_liste',
                  label: 'Indice retenu pour révision des honoraires',
                  type: 'SELECT'
                }
              ],
              id: '2e8e1d0e_78d2_409e_bf48_5819b0d832d6',
              label: 'Autre honoraires particuliers',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'mandat_gestion_assurance_gli',
                  label: "Souscription à l'assurance Loyers Impayés",
                  type: 'SELECT-BINARY'
                },
                {
                  conditionalTitles: [
                    {
                      conditions: [
                        [
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['BENEDIC__LOCATION__MANDAT_DE_GESTION']
                          }
                        ]
                      ],
                      title: 'Taux de la GLI'
                    }
                  ],
                  id: 'mandat_gestion_assurance_gli_montant_pourcentage',
                  label: 'Limite du montant du quittancement - Assurance Loyers Impayés',
                  suffix: '%',
                  type: 'NUMBER'
                },
                {
                  id: 'mandat_gestion_assurance_gli_montant_frais_administratif_montant',
                  label: 'Montant des frais administratif (montant HT/mois/lot principal)',
                  type: 'PRICE'
                },
                {
                  id: 'mandat_gestion_assurance_gli_nom',
                  label: 'Nom du contrat',
                  type: 'TEXT'
                },
                {
                  id: 'mandat_gestion_assurance_gli_numero',
                  label: 'Numéro du contrat',
                  type: 'TEXT'
                },
                {
                  id: 'mandat_gestion_assurance_gli_organisme',
                  label: "Organisme d'assurance",
                  type: 'TEXT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_gestion_assurance_gli',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'mandat_gestion_assurance_gli_doc',
                  label: 'Bulletin de souscription - Assurance Loyers Impayés',
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'mandat_gestion_assurance_pno',
                  label: "Souscription à l'assurance Propriétaire non Occupant",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'mandat_gestion_assurance_pno_montant_pourcentage',
                  label: 'Limite du montant du quittancement - Assurance Propriétaire non Occupant',
                  suffix: '%',
                  type: 'NUMBER'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_gestion_assurance_pno',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'mandat_gestion_assurance_pno_doc',
                  label: 'Bulletin de souscription - Propriétaire non Occupant',
                  type: 'UPLOAD'
                }
              ],
              id: '291061c0_11ab_422b_937e_aae1fd159b55',
              label: 'Assurances',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'frais_autres_revenus_fonciers_statut',
                  label: "Ajouter des frais d'aide à la déclaration de revenus fonciers",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'frais_autres_revenus_fonciers',
                  label: 'Aide à la déclaration des revenus fonciers',
                  type: 'PRICE'
                },
                {
                  id: 'frais_autres_conge_bailleur',
                  label: "Délivrance d'un congé bailleur",
                  type: 'PRICE'
                },
                {
                  id: 'frais_autres_suivi_travaux',
                  label: 'Suivi des travaux',
                  type: 'PRICE'
                },
                {
                  id: 'frais_autres_suivi_travaux_pourcentage',
                  label: 'Pourcentage du montant des travaux facturés',
                  suffix: '%',
                  type: 'NUMBER'
                }
              ],
              id: '21f0f4a6_8715_4e55_bc88_08202429e5cd',
              label: 'Autres frais particuliers',
              type: 'CATEGORY'
            }
          ],
          id: '40126bb3_e732_47e5_a89a_2cf683c81931',
          label: 'Informations sur le mandat de gestion',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'honoraires_loi_89',
              label: "Ajouter des honoraires loi 89 sur les baux d'habitation",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'forfait',
                  label: 'Par un montant forfaitaire'
                },
                {
                  id: 'pourcentage',
                  label: 'Par un pourcentage'
                },
                {
                  id: 'autre',
                  label: 'Par une autre répartition (en texte libre)'
                }
              ],
              id: 'honoraires_pourcentage_forfait',
              label: 'Honoraires de location indiqués',
              type: 'SELECT'
            },
            {
              id: 'honoraires_pourcentage_libre',
              label: 'Pourcentage des honoraires',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'honoraires_pourcentage_libre_assiette',
              label: 'Assiette des honoraires',
              type: 'TEXT'
            },
            {
              id: 'mandat_montant_honoraires_libre',
              label: 'Montant des honoraires',
              type: 'PRICE'
            },
            {
              id: 'mandat_montant_honoraires_libre_texte',
              label: 'Honoraires libres',
              type: 'TEXT'
            },
            {
              id: 'mandat_montant_honoraires_pourcentage_charge',
              label: 'Honoraires à la charge',
              type: 'TEXT'
            },
            {
              id: 'mandat_montant_honoraires_forfait_charge',
              label: 'Honoraires à la charge',
              type: 'TEXT'
            },
            {
              id: 'mandat_montant_honoraires_detail',
              label: 'Liste des prestations incluses dans les honoraires',
              type: 'TEXT'
            },
            {
              id: 'honoraires_recherche_prestation_redaction_locataire',
              label:
                'Montant des honoraires de visite, constitution de dossier, rédaction du bail, à charge du Locataire, par mètres carrés',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'mandat_gestion_location_statut',
              label: 'Les honoraires de location sont-ils indiqués au mandat de gestion ?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'modalite_mention_honoraires_location_fixes',
                  label: 'Par des montants chiffrés'
                },
                {
                  id: 'modalite_mention_honoraires_location_autre',
                  label: 'Par une autre répartition (en texte libre)'
                }
              ],
              id: 'mandat_honoraires_mention',
              label: 'Le montant des honoraires de location est indiqué',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'bailleur',
                  label: 'Charge Bailleur'
                },
                {
                  id: 'locataire',
                  label: 'Charge Locataire'
                },
                {
                  id: 'partages',
                  label: 'Charge partagée Bailleur et Locataire'
                }
              ],
              id: 'mandat_honoraires_charge_liste',
              label: 'Charge des honoraires de location',
              type: 'SELECT'
            },
            {
              id: 'honoraires_prestation_repartition_montant',
              label: 'Montant des honoraires partagés entre Bailleur et Locataire',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'honoraires_prestation_repartition',
              label: 'Honoraires répartis entre Bailleur et Locataire',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'honoraires_etat_des_lieux_statut',
              label: "Etat des lieux établi par l'agence",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'honoraires_etat_des_lieux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    type: 'DIFFERENT_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_LOCATION_BAIL',
                      'LOCATION__BAIL_HABITATION_ANGLAIS',
                      'AJP__LOCATION__BAIL_HABITATION',
                      'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                      'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                      'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                      'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                      'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                      'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                      'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                      'AJP__LOCATION__MANDAT_DE_LOCATION',
                      'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                      'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                      'AJP__LOCATION__MANDAT_DE_GESTION_AJP',
                      'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION'
                    ]
                  }
                ]
              ],
              id: 'honoraires_recherche_prestation_etat_des_lieux',
              label: "Montant des honoraires d'établissement de l'état des lieux, par mètres carrés",
              type: 'PRICE'
            },
            {
              children: [
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_honoraires_mention',
                        value: 'modalite_mention_honoraires_location_autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'honoraires_location_bailleur_autre',
                  label: 'Montant des honoraires à la charge du Bailleur',
                  type: 'TEXTAREA'
                },
                {
                  id: 'honoraires_location_bailleur_libre',
                  label: 'Montant des honoraires à la charge du Bailleur',
                  type: 'PRICE'
                },
                {
                  id: 'honoraires_location_bailleur',
                  label: "Honoraires d'entremise et de négociation",
                  type: 'PRICE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  description: '8/10/12 € par m2 en fonction de la zone automatiquement détectée',
                  id: 'honoraires_prestation_redaction_bailleur_automatique',
                  label:
                    'Appliquer automatiquement le plafond légal pour les honoraires de visite, constitution de dossier, rédaction du bail',
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'honoraires_prestation_redaction_bailleur_automatique',
                        type: 'DIFFERENT',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_LOCATION_BAIL',
                          'LOCATION__BAIL_HABITATION_ANGLAIS',
                          'AJP__LOCATION__BAIL_HABITATION',
                          'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                          'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                          'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                          'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                          'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                          'AJP__LOCATION__MANDAT_DE_LOCATION',
                          'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                          'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                          'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
                          'AJP__LOCATION__MANDAT_DE_GESTION_AJP'
                        ]
                      }
                    ],
                    [
                      {
                        type: 'DIFFERENT_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_LOCATION_BAIL',
                          'LOCATION__BAIL_HABITATION_ANGLAIS',
                          'AJP__LOCATION__BAIL_HABITATION',
                          'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                          'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                          'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                          'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                          'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                          'AJP__LOCATION__MANDAT_DE_LOCATION',
                          'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                          'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                          'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
                          'AJP__LOCATION__MANDAT_DE_GESTION_AJP'
                        ]
                      }
                    ]
                  ],
                  id: 'honoraires_prestation_redaction_bailleur',
                  label: 'Honoraires de visite, constitution de dossier, rédaction du bail',
                  type: 'PRICE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'honoraires_etat_des_lieux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_LOCATION_BAIL',
                          'LOCATION__BAIL_HABITATION_ANGLAIS',
                          'AJP__LOCATION__BAIL_HABITATION',
                          'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                          'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                          'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                          'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                          'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                          'AJP__LOCATION__MANDAT_DE_LOCATION',
                          'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                          'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                          'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
                          'AJP__LOCATION__MANDAT_DE_GESTION_AJP'
                        ]
                      }
                    ]
                  ],
                  description: '3€ par m2',
                  id: 'honoraires_prestation_etat_des_lieux_bailleur_automatique',
                  label: "Appliquer automatiquement le plafond légal pour les honoraires d'état des lieux",
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'honoraires_etat_des_lieux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'honoraires_prestation_etat_des_lieux_bailleur_automatique',
                        type: 'DIFFERENT',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_LOCATION_BAIL',
                          'LOCATION__BAIL_HABITATION_ANGLAIS',
                          'AJP__LOCATION__BAIL_HABITATION',
                          'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                          'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                          'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                          'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                          'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                          'AJP__LOCATION__MANDAT_DE_LOCATION',
                          'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                          'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                          'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
                          'AJP__LOCATION__MANDAT_DE_GESTION_AJP'
                        ]
                      }
                    ],
                    [
                      {
                        id: 'honoraires_etat_des_lieux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'DIFFERENT_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_LOCATION_BAIL',
                          'LOCATION__BAIL_HABITATION_ANGLAIS',
                          'AJP__LOCATION__BAIL_HABITATION',
                          'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                          'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                          'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                          'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                          'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                          'AJP__LOCATION__MANDAT_DE_LOCATION',
                          'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                          'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                          'AJP__LOCATION__MANDAT_DE_GESTION_AJP',
                          'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION'
                        ]
                      }
                    ]
                  ],
                  id: 'honoraires_prestation_etat_des_lieux_bailleur',
                  label: "Honoraires d'établissement de l'état des lieux",
                  type: 'PRICE'
                }
              ],
              id: 'dcb062dc_79f0_4515_8c37_35b218e3e6b2',
              label: 'Prestations à la charge du Bailleur',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_honoraires_mention',
                        value: 'modalite_mention_honoraires_location_autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'honoraires_location_locataire_autre',
                  label: 'Honoraires de location',
                  type: 'TEXTAREA'
                },
                {
                  id: 'honoraires_location_locataire_libre',
                  label: 'Montant des honoraires à la charge du Locataire',
                  type: 'PRICE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  description: '8/10/12 € par m2 en fonction de la zone automatiquement détectée',
                  id: 'honoraires_prestation_redaction_locataire_automatique',
                  label:
                    'Appliquer automatiquement le plafond légal pour les honoraires de visite, constitution de dossier, rédaction du bail',
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'honoraires_prestation_redaction_locataire_automatique',
                        type: 'DIFFERENT',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_LOCATION_BAIL',
                          'LOCATION__BAIL_HABITATION_ANGLAIS',
                          'AJP__LOCATION__BAIL_HABITATION',
                          'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                          'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                          'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                          'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                          'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                          'AJP__LOCATION__MANDAT_DE_LOCATION',
                          'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                          'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                          'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
                          'AJP__LOCATION__MANDAT_DE_GESTION_AJP'
                        ]
                      }
                    ],
                    [
                      {
                        type: 'DIFFERENT_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_LOCATION_BAIL',
                          'LOCATION__BAIL_HABITATION_ANGLAIS',
                          'AJP__LOCATION__BAIL_HABITATION',
                          'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                          'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                          'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                          'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                          'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                          'AJP__LOCATION__MANDAT_DE_LOCATION',
                          'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                          'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                          'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
                          'AJP__LOCATION__MANDAT_DE_GESTION_AJP'
                        ]
                      }
                    ]
                  ],
                  description: 'Ces honoraires ne peuvent pas dépasser ceux charge Bailleur',
                  id: 'honoraires_prestation_redaction_locataire',
                  label: 'Honoraires de visite, constitution de dossier, rédaction du bail',
                  type: 'PRICE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'honoraires_etat_des_lieux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_LOCATION_BAIL',
                          'LOCATION__BAIL_HABITATION_ANGLAIS',
                          'AJP__LOCATION__BAIL_HABITATION',
                          'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                          'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                          'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                          'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                          'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                          'AJP__LOCATION__MANDAT_DE_LOCATION',
                          'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                          'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                          'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
                          'AJP__LOCATION__MANDAT_DE_GESTION_AJP'
                        ]
                      }
                    ]
                  ],
                  description: '3€ par m2',
                  id: 'honoraires_prestation_etat_des_lieux_locataire_automatique',
                  label: "Appliquer automatiquement le plafond légal pour les honoraires d'état des lieux",
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'honoraires_etat_des_lieux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'honoraires_prestation_etat_des_lieux_locataire_automatique',
                        type: 'DIFFERENT',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_LOCATION_BAIL',
                          'LOCATION__BAIL_HABITATION_ANGLAIS',
                          'AJP__LOCATION__BAIL_HABITATION',
                          'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                          'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                          'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                          'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                          'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                          'AJP__LOCATION__MANDAT_DE_LOCATION',
                          'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                          'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                          'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
                          'AJP__LOCATION__MANDAT_DE_GESTION_AJP'
                        ]
                      }
                    ],
                    [
                      {
                        id: 'honoraires_etat_des_lieux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'DIFFERENT_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_LOCATION_BAIL',
                          'LOCATION__BAIL_HABITATION_ANGLAIS',
                          'AJP__LOCATION__BAIL_HABITATION',
                          'MA_GESTION_LOCATIVE__LOCATION__BAIL_HABITATION',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
                          'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
                          'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
                          'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
                          'IMMOBILIER_LOCATION_MANDAT_LOCATION',
                          'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
                          'AJP__LOCATION__MANDAT_DE_LOCATION',
                          'ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE',
                          'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                          'AJP__LOCATION__MANDAT_DE_GESTION_AJP',
                          'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION'
                        ]
                      }
                    ]
                  ],
                  description: 'Ces honoraires ne peuvent pas dépasser ceux charge Bailleur',
                  id: 'honoraires_prestation_etat_des_lieux_locataire',
                  label: "Honoraires d'établissement de l'état des lieux",
                  type: 'PRICE'
                }
              ],
              id: '7774c1c1_b2b5_4ad0_84b9_44db55e390bd',
              label: 'Prestations à la charge du Locataire',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'honoraires_precisions',
                  label: 'Ajouter des précisions sur les honoraires',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'honoraires_precisions_texte',
                  label: 'Précisions sur la clause Honoraires',
                  type: 'TEXTAREA'
                }
              ],
              id: '77749i9i_gj3z_4ad0_47y6_44db55e395tg',
              label: 'Honoraires de location',
              type: 'CATEGORY'
            }
          ],
          id: 'a02046b4_18d3_4286_91f5_28579c907cdb',
          label: 'Honoraires de location du Mandataire',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'systematique',
              label: 'après chaque visite'
            },
            {
              id: 'hebdomadaire',
              label: 'chaque semaine'
            },
            {
              id: 'frequence_autre',
              label: 'autre'
            }
          ],
          id: 'mandat_frequence',
          label: 'A quelle fréquence seront effectués les comptes rendus des visites ?',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_frequence',
                type: 'EQUALS',
                value: 'frequence_autre'
              }
            ]
          ],
          id: 'mandat_frequence_autre_detail',
          label: 'Précisez les modalités de comptes rendus',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'mandat_gestion_travaux_libre',
              label: "Tous les travaux peuvent être réalisés par l'Agence, sans accord du Mandant"
            },
            {
              id: 'mandat_gestion_travaux_plafonds',
              label: "Les travaux peuvent être réalisés par l'Agence en dessous d'un certain plafond"
            },
            {
              id: 'mandat_gestion_travaux_encadre',
              label: "Tous les travaux nécessitent l'accord préalable du Mandant"
            }
          ],
          id: 'mandat_gestion_travaux',
          label: 'Concernant les travaux à réaliser pendant la durée mandat',
          type: 'SELECT'
        },
        {
          id: 'mandat_gestion_travaux_plafond',
          label: 'Montant du plafond de travaux',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_gestion_gli',
          label: 'Le Mandataire propose-t-il au Mandant de souscrire une Garantie des loyers impayés?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_gestion_gli_approuve',
          label: 'Le Mandant accepte-t-il de souscrire à cette garantie ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_gestion_pno',
          label: 'Le Mandataire propose-t-il au Mandant de souscrire une assurance Propriétaire Non Occupant?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_gestion_pno_approuve',
          label: 'Le Mandant accepte-t-il de souscrire à cette assurance ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_gestion_dispositif_fiscal',
          label:
            "Les biens objets du mandat de gestion font-ils l'objet d'un dispositif fiscal ou d'une convention avec l'état ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_gestion_dispositif_description',
          label: 'Type de dispositif ou de convention',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'gestion',
              label: 'Intendance et gestion'
            },
            {
              id: 'intendance',
              label: 'Intendance'
            }
          ],
          id: 'prello_service',
          label: 'Service demandé par le Bailleur',
          type: 'PICK_LIST'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              description: 'Honoraires libres non soumis aux règlementations des baux habitation',
              id: 'honoraires_derogatoires_loi_89',
              label: 'Ajouter des honoraires hors loi 89',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'pourcentage',
                  label: 'En pourcentage'
                },
                {
                  id: 'forfait',
                  label: 'Selon un forfait'
                }
              ],
              id: 'honoraires_derogatoire_indication',
              label: 'Honoraires indiqués',
              type: 'SELECT-BINARY'
            },
            {
              id: 'honoraires_derogatoire_forfait_montant',
              label: 'Montant du forfait',
              type: 'PRICE'
            },
            {
              id: 'honoraires_derogatoire_pourcentage_montant',
              label: "Pourcentage d'honoraires",
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'honoraires_derogatoire_assiette',
              label: 'Assiette des honoraires',
              type: 'TEXT'
            },
            {
              id: 'honoraires_derogatoire_charge',
              label: 'Débiteur des honoraires',
              type: 'TEXT'
            },
            {
              id: 'honoraires_derogatoire_edl_montant',
              label: "Montant des honoraires d'état des lieux",
              type: 'PRICE'
            },
            {
              id: 'honoraires_derogatoire_edl_charge',
              label: "Débiteur des honoraires d'état des lieux",
              type: 'TEXT'
            }
          ],
          id: '510fd0cb_29ec_4269_9b72_eff1e3ef344f',
          label: 'Honoraires hors loi 89',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'vacation',
                  label: 'A la vacation Horaire'
                },
                {
                  id: 'forfait',
                  label: 'Selon un forfait'
                },
                {
                  id: 'bareme',
                  label: 'Selon le barème annexé'
                }
              ],
              id: 'honoraires_prestations_supplementaire_sinistre',
              label: 'Honoraires de gestion de dossier de sinistres /contentieux locataires indiqués',
              type: 'SELECT'
            },
            {
              id: 'honoraires_prestations_supplementaire_locataire_montant',
              label: 'Montant HT des frais de dossier contentieux locataire',
              type: 'PRICE'
            },
            {
              id: 'honoraires_prestations_supplementaire_sinistre_montant',
              label: 'Montant HT des frais de dossier sinistre',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'honoraires_prestations_supplementaire_bareme',
              label: 'Renvoi au barème pour les autres prestations',
              type: 'SELECT-BINARY'
            },
            {
              description: 'texte libre',
              id: 'honoraires_prestations_supplementaire_representation_ag',
              label: 'Honoraires de représentation en AG',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'honoraires_prestations_supplementaire_declaration',
              label: 'Détailler les honoraires pour aide à déclaration de revenus',
              type: 'SELECT-BINARY'
            },
            {
              id: 'honoraires_prestations_supplementaire_declaration_montant',
              label: 'Honoraires aide déclaration de revenus fonciers',
              type: 'PRICE'
            },
            {
              id: 'honoraires_prestations_supplementaire_assurance',
              label: "Frais HT de gestion des contrats d'assurance",
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'honoraires_prestations_supplementaire_ajout',
              label: "Détailler d'autres prestations",
              type: 'SELECT-BINARY'
            },
            {
              id: 'honoraires_prestations_supplementaire_ajout_liste',
              label: 'Liste des autres prestations',
              type: 'TEXTAREA'
            }
          ],
          id: '046d480c_6401_41e2_b678_213b09cd905d',
          label: 'Honoraires des prestations supplémentaires',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'garant_gli',
                  label: 'Accord pour assurance Garant Me - GLI'
                },
                {
                  id: 'garant_pno',
                  label: 'Accord pour assurance Garant Me - PNO'
                },
                {
                  id: 'garant_gli_refus',
                  label: 'Refus pour assurance Garant Me - GLI'
                },
                {
                  id: 'garant_pno_refus',
                  label: 'Refus pour assurance Garant Me - PNO'
                },
                {
                  id: 'filhet_gli',
                  label: 'Accord pour assurance Filhet - GLI'
                },
                {
                  id: 'filhet_pno',
                  label: 'Accord pour assurance Filhet - PNO'
                },
                {
                  id: 'filhet_gli_refus',
                  label: 'Refus pour assurance Filhet - GLI'
                },
                {
                  id: 'filhet_pno_refus',
                  label: 'Refus pour assurance Filhet - PNO'
                },
                {
                  id: 'autre_gli',
                  label: 'Autre Assurance GLI souscrite'
                },
                {
                  id: 'autre_pno',
                  label: 'Autre Assurance PNO souscrite'
                },
                {
                  id: 'vacance',
                  label: 'Assurance Vacance Locative'
                },
                {
                  id: 'autre_mandataire',
                  label: 'Autre assurance proposée par le Mandataire'
                },
                {
                  id: 'autre_mandant',
                  label: 'Autre assurance souscrite par le Mandat'
                },
                {
                  id: 'refus_total',
                  label: 'Aucune assurance souscrite'
                }
              ],
              id: 'assurance_orpi',
              label: "Contrats d'assurance",
              multiple: 'true',
              type: 'SELECT'
            },
            {
              id: 'assurance_orpi_garantme_gli_numero',
              label: "Numéro de l'assurance Garant Me - GLI",
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_garantme_gli_montant',
              label: "Montant de l'assurance Garant Me - GLI",
              type: 'PRICE'
            },
            {
              id: 'assurance_orpi_garantme_pno_numero',
              label: "Numéro de l'assurance Garant Me - PNO",
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_garantme_pno_montant',
              label: "Montant de l'assurance Garant Me - PNO",
              type: 'PRICE'
            },
            {
              id: 'assurance_orpi_filhet_gli_numero',
              label: "Numéro de l'assurance Filhet - GLI",
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_filhet_gli_montant',
              label: "Montant de l'assurance Filhet - GLI",
              type: 'PRICE'
            },
            {
              id: 'assurance_orpi_filhet_pno_numero',
              label: "Numéro de l'assurance Filhet - PNO",
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_filhet_pno_montant',
              label: "Montant de l'assurance Filhet - PNO",
              type: 'PRICE'
            },
            {
              id: 'assurance_orpi_gli_autre_nom',
              label: 'Nom du contrat - Assurance GLI',
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_gli_autre_organisme',
              label: "Nom de l'organisme - Assurance GLI",
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_gli_autre_numero',
              label: 'Numéro de contrat - Assurance GLI',
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_gli_autre_montant',
              label: 'Montant du contrat - Assurance GLI',
              type: 'PRICE'
            },
            {
              id: 'assurance_orpi_pno_autre_nom',
              label: 'Nom du contrat - Assurance PNO',
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_pno_autre_organisme',
              label: "Nom de l'organisme - Assurance PNO",
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_pno_autre_numero',
              label: 'Numéro de contrat - Assurance PNO',
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_pno_autre_montant',
              label: 'Montant du contrat - Assurance PNO',
              type: 'PRICE'
            },
            {
              id: 'assurance_orpi_vacance_nom',
              label: 'Nom du contrat - Assurance Vacance Locative',
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_vacance_organisme',
              label: "Nom de l'organisme - Assurance Vacance Locative",
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_vacance_numero',
              label: 'Numéro de contrat - Assurance Vacance Locative',
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_vacance_montant',
              label: 'Montant du contrat - Assurance Vacance Locative',
              type: 'PRICE'
            },
            {
              id: 'assurance_orpi_assurance_agence',
              label: "Autre assurance proposée par l'Agence",
              type: 'TEXT'
            },
            {
              id: 'assurance_orpi_assurance_mandant',
              label: 'Autre assurance souscrite directement par le Mandant',
              type: 'TEXT'
            }
          ],
          id: 'ef9f323a_f98d_490a_99a7_41f18fcd2b90',
          label: 'Assurance',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'reddition_frequence',
              label: 'Fréquence des comptes rendus',
              type: 'TEXT'
            },
            {
              id: 'reddition_date_prise_effet',
              label: "Date de prise d'effet du mandat",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'virement',
                  label: 'Par virement'
                },
                {
                  id: 'cheque',
                  label: 'Par chèque'
                },
                {
                  id: 'autre',
                  label: 'Autre'
                }
              ],
              id: 'reddition_mode_paiement',
              label: 'Mode de paiement',
              type: 'SELECT'
            },
            {
              id: 'reddition_mode_paiement_autre',
              label: 'Autre mode de paiement',
              type: 'TEXT'
            },
            {
              id: 'reddition_periodicite_paiement',
              label: 'Périodicité du paiement',
              type: 'TEXT'
            }
          ],
          id: '6809950c_5d20_4a1f_a8f8_77a03d45d3cf',
          label: 'Reddition des comptes',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'initiale',
                  label: 'Une durée initiale avec renouvellements annuels'
                },
                {
                  id: 'trois_ans',
                  label: 'Trois ans avec renouvellements triénnaux'
                },
                {
                  id: 'globale',
                  label: 'Durée globale et résiliation ponctuelle'
                }
              ],
              id: 'gestion_duree_orpi',
              label: 'Durée du mandat de gestion indiquée',
              type: 'SELECT'
            },
            {
              id: 'gestion_duree_orpi_indication',
              label: 'Durée du mandat',
              type: 'TEXT'
            },
            {
              id: 'gestion_duree_orpi_preavis',
              label: 'Durée du préavis',
              type: 'TEXT'
            }
          ],
          id: '1ad2c107_2cf3_44b0_a751_af1aad6f675b',
          label: 'Durée',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'mandant_statut',
              label: 'Le client agit dans le cadre de ses activités personnelles',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'mandat_signature_hors_etablissement',
              label: 'Mandat signé hors établissement',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    conditions: [
                      {
                        id: 'mandant_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION']
                  }
                ],
                [
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION']
                  }
                ]
              ],
              description: "Si 'non' le DIP doit obligatoirement être généré et signé préalablement au mandat",
              id: 'mandant_dip_integre_statut',
              label: "Document d'Informations Précontractuelles intégré au début du mandat",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'mandant_retractation',
              label: "Présence d'un droit de rétractation (Mandat signé à distance ou hors établissement)",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'mandant_execution',
              label: 'Exécution immédiate du mandat',
              type: 'SELECT-BINARY'
            }
          ],
          id: 'dccf695e_1dce_4447_8596_9dac279e8bc3',
          label: 'Droit de la consommation',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'notification_electronique',
          label: 'Accord du Mandant pour notification électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'accord_clause_hitbo',
          label: 'Accord du Mandant pour utilisation de ses données personnelles',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_debut',
          label: 'Date de début du mandat',
          type: 'DATE'
        },
        {
          id: 'mandat_fin',
          label: 'Date de fin du mandat',
          type: 'DATE'
        },
        {
          id: 'mandat_modalites_visite',
          label: 'Modalités de visite du bien',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_signature_hors_agence',
          label: 'Mandat signé hors agence',
          type: 'SELECT-BINARY'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_ACCEPTATION_TREVI_TRACFIN'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'belgique_tracfin_client',
          label: 'Politique d’acceptation du client - blanchiment de capitaux et du financement du terrorisme',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_TREVI_LISTE_DOCUMENT_MANDAT'
          },
          id: 'belgique_trevi_liste_document',
          label: 'Liste des documents à fournir',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_comettant_autorisation_anticipee',
          label: "Le commettant autorise l'agence à débuter sa mission",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_jours_heures_visite',
          label: 'Jours et heures préférentielles pour les visites de l’immeuble',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_panneau_publicitaire',
          label: 'Panneaux publicitaire en façade',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_information_cadastre',
          label: 'Accord pour récupération des informations cadastrales',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'mandat_date',
              label: 'Date de signature du Mandat initial',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'mandat_pouvoir',
              label: "Le mandant donne-t-il directement pouvoir au mandataire à l'effet de signer le contrat de bail ?",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'recommande_electronique',
              label: 'Les parties acceptent-elles les notifications par voie électronique ?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'signature_electronique_mandat',
              label: 'Signature électronique',
              type: 'SELECT-BINARY'
            },
            {
              id: 'mandat_avenant_texte',
              label: "Objet de l'avenant au mandat",
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'signature_electronique_avenant_mandat',
              label: 'Signature électronique',
              type: 'SELECT-BINARY'
            }
          ],
          id: 'ec25d977_7f30_4daf_a911_0f6d1f7b5fd2',
          label: 'Cloture du mandat',
          type: 'CATEGORY'
        }
      ],
      id: '15bf5595_242f_455a_80e4_ca39d250b294',
      label: 'Informations générales sur le mandat',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_dpe_etabli',
          label: 'DPE déjà réalisé',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_dpe_etabli',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mandat_dpe_etabli_mandataire',
          label: "Le Mandataire est chargé de l'établir, aux frais du mandant",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_dpe_etabli',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mandat_dpe_date',
          label: 'Date de réalisation du DPE',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_erp_etabli',
          label: 'Etat des risques déjà réalisé',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_erp_etabli',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mandat_erp_etabli_mandataire',
          label: "Le Mandataire est chargé de l'établir, aux frais du mandant",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_crep_etabli',
          label: "Constat d'exposition au plomb déjà réalisé",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_crep_etabli',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mandat_crep_etabli_mandataire',
          label: "Le Mandataire est chargé de l'établir, aux frais du mandant",
          type: 'SELECT-BINARY'
        }
      ],
      id: 'bfdc0c36_53a9_480c_8bb9_4681af5dba0a',
      label: 'Diagnostics',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'mandat_modalite_diffusion_annonce',
          label: 'Modalité de diffusion des annonces',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_modalite_diffusion_annonce_reseau',
          label: 'Diffusion via un réseau particulier',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_modalite_diffusion_annonce_reseau_liste',
          label: 'Réseau utilisé',
          type: 'TEXT'
        },
        {
          id: 'mandat_actions_exclusif_unis_liste',
          label: 'Actions à réaliser dans le cadre du mandat exclusif',
          type: 'TEXTAREA'
        },
        {
          id: 'mandat_actions_exclusif_unis_reddition_modalite',
          label: 'Modalités de réddition de compte des actions',
          type: 'TEXT'
        },
        {
          id: 'mandat_actions_exclusif_unis_reddition_periodicite',
          label: 'Périodicité de réddition de compte',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'visites',
              label: 'Visites'
            },
            {
              id: 'negociation',
              label: 'Négociation'
            },
            {
              id: 'reception',
              label: 'Réception et communication des offres de vente'
            },
            {
              id: 'autre',
              label: 'Autres actions à préciser'
            }
          ],
          id: 'mandat_actions_simple_unis',
          label: 'Réalisation des actions suivantes',
          multiple: true,
          type: 'SELECT'
        },
        {
          id: 'mandat_actions_simple_unis_autres',
          label: 'Autres actions à réaliser',
          type: 'TEXTAREA'
        },
        {
          children: [
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'taylor_offshore_leaks',
              label: 'Offshore Leaks Database - Bailleur',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'taylor_offshore_leaks_locataire',
              label: 'Offshore Leaks Database - Locataire',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'dossier',
                      label: "Réalisation d'un dossier de présentation"
                    },
                    {
                      id: 'annonce',
                      label: "Affichage de l'annonce en vitrine"
                    },
                    {
                      id: 'panonceau',
                      label: "Pose d'un panonceau sur les biens"
                    },
                    {
                      id: 'diffusion',
                      label: "Diffusion de l'annonce sur sites spécialisés"
                    },
                    {
                      id: 'publication',
                      label: "Diffusion de l'annonce dans certaines publications"
                    },
                    {
                      id: 'distribution',
                      label: "Distribution d'un mailing de présentation"
                    },
                    {
                      id: 'autre',
                      label: 'Autres actions'
                    }
                  ],
                  id: 'taylor_actions_liste',
                  label: 'Actions à réaliser',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'distribution',
                      label: "Distribution d'un mailing à des prospects"
                    },
                    {
                      id: 'autre',
                      label: 'Autres actions'
                    }
                  ],
                  id: 'taylor_actions_recherche_liste',
                  label: 'Actions à réaliser',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'photo',
                      label: "Réalisation d'un dossier photos"
                    },
                    {
                      id: 'video',
                      label: "Réalisation d'un dossier vidéo"
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'taylor_actions_liste',
                        type: 'CONTAINS',
                        value: 'dossier'
                      }
                    ]
                  ],
                  id: 'taylor_actions_dossier',
                  label: 'Dossier comportant',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'taylor',
                      label: 'Diffusion sur John Taylor.com'
                    },
                    {
                      id: 'autre',
                      label: "Diffusion sur d'autres sites"
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'taylor_actions_liste',
                        type: 'CONTAINS',
                        value: 'diffusion'
                      }
                    ]
                  ],
                  id: 'taylor_actions_site',
                  label: 'Sites internet utilisés',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'taylor_actions_liste',
                        type: 'CONTAINS',
                        value: 'diffusion'
                      },
                      {
                        id: 'taylor_actions_site',
                        type: 'CONTAINS',
                        value: 'autre'
                      }
                    ]
                  ],
                  id: 'taylor_actions_site_autre',
                  label: 'Autre sites internet',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'photo',
                      label: 'Photos'
                    },
                    {
                      id: 'video',
                      label: 'Vidéo'
                    },
                    {
                      id: 'geolocalisation',
                      label: 'Géolocalisation'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'taylor_actions_liste',
                        type: 'CONTAINS',
                        value: 'diffusion'
                      }
                    ]
                  ],
                  id: 'taylor_actions_site_publication',
                  label: 'Dossier à réaliser pour publication',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  id: 'taylor_actions_publication_liste',
                  label: "Liste des publications pour diffusion de l'annonce",
                  type: 'TEXTAREA'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'taylor_actions_liste',
                        type: 'CONTAINS',
                        value: 'autre'
                      }
                    ],
                    [
                      {
                        id: 'taylor_actions_recherche_liste',
                        type: 'CONTAINS',
                        value: 'autre'
                      }
                    ]
                  ],
                  id: 'taylor_actions_autres_liste',
                  label: 'Autres actions à réaliser',
                  type: 'TEXT'
                },
                {
                  id: 'taylor_actions_periodicite',
                  label: "Périodicité d'information du client par le mandataire",
                  type: 'TEXT'
                }
              ],
              id: '61becb60_dd83_4ff3_9b30_3d46c7ae4ef8',
              label: 'Actions de communication',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'visite',
                      label: 'Compte rendu à chaque visite'
                    },
                    {
                      id: 'offre',
                      label: 'Transmission des offres dès réception'
                    }
                  ],
                  id: 'taylor_suivi_information',
                  label: "Mode d'information du Mandant",
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'taylor_suivi_recherche_information',
                  label: 'Compte rendu après chaque visite',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'mail',
                      label: 'Email'
                    },
                    {
                      id: 'autre',
                      label: 'Autres moyens'
                    }
                  ],
                  id: 'taylor_suivi_transmission',
                  label: 'Transmission des informations par',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'taylor_suivi_transmission',
                        type: 'CONTAINS',
                        value: 'autre'
                      }
                    ]
                  ],
                  id: 'taylor_suivi_transmission_autre',
                  label: 'Autre modalités de transmission',
                  type: 'TEXT'
                }
              ],
              id: '6c164df4_45f2_4dd5_865f_2d9538a33e88',
              label: 'Suivi des actions de communication',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'taylor_execution_anticipee',
                  label: "Demande d'excécution anticipée du mandat",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'taylor_donnees_utilisation',
                  label: 'Accord du Mandant pour utilisation de ses données personnelles',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'taylor_donnees_transmission',
                  label: 'Accord du Mandant pour transmission de ses données personnelles',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '3fa5fa67_f9f6_4260_a174_d084c1a1caa6',
              label: 'Signature du mandat',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_anglais',
                        type: 'DIFFERENT',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_DIP_GESTION'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'taylor_dip_gestion_doc',
                  label: "Document d'informations précontractuelles",
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_anglais',
                        type: 'DIFFERENT',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_DIP_LOCATION'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'taylor_dip_location_doc',
                  label: "Document d'informations précontractuelles",
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_anglais',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_DIP_GESTION_ANGLAIS'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'taylor_dip_gestion_anglais_doc',
                  label: "Document d'informations précontractuelles - Anglais",
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_anglais',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_DIP_LOCATION_ANGLAIS'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'taylor_dip_location_anglais_doc',
                  label: "Document d'informations précontractuelles - Anglais",
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_anglais',
                        type: 'DIFFERENT',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_TAYLOR_RETRACTATION'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'taylor_retractation_doc',
                  label: 'Formulaire de rétractation',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_anglais',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_TAYLOR_RETRACTATION_ANGLAIS'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'taylor_retractation_anglais_doc',
                  label: 'Formulaire de rétractation - Anglais',
                  type: 'UPLOAD'
                }
              ],
              id: 'e90fb056_4d4f_43a3_a84a_4b613563d0b3',
              label: 'Droit de la consommation',
              type: 'CATEGORY'
            }
          ],
          id: '3805a374_5e4c_4a67_b0ea_b53eb6a166a3',
          label: 'CONDITION_BLOCK_John Taylor',
          type: 'CONDITION_BLOCK'
        }
      ],
      id: '87a0b11f_b82e_4819_bdbc_4185991039a1',
      label: 'Actions du Mandataire',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'delegation_mandat_honoraire_libre',
          label: 'Rémunération du Délégataire :',
          type: 'TEXT'
        },
        {
          id: 'delegation_date_fin',
          label: 'Date de fin de la délégation :',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'delegation_partage_moitie',
              label: 'A la moitié des honoraires prévus'
            },
            {
              id: 'delegation_partage_autre',
              label: 'A une autre répartition des honoraires '
            }
          ],
          id: 'delegation_mandat_honoraire',
          label: 'En cas de réalisation de la transaction, le Délégataire aura droit',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'delegation_mandat_honoraire',
                value: 'delegation_partage_autre',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'delegation_mandat_partage_autre',
          label: 'Modalités de partage des honoraires',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'delegation_clause_particuliere',
          label: 'La délégation de mandat comporte-t-elle une clause particulière supplémentaire ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'delegation_mandat_electronique',
          label: 'La délégation est signée de manière électronique ?',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'fl4756yg_fj4e_pl2e_01f3_ddrt3faemfo4',
      label: 'Délégation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'clause_particuliere',
          label: 'Ajouter une clause particulière supplémentaire',
          type: 'SELECT-BINARY'
        },
        {
          id: 'clause_particuliere_libre',
          label: 'Clause particulière',
          type: 'TEXT'
        },
        {
          children: [
            {
              id: 'clause_particuliere_liste_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'clause_particuliere_liste_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          conditions: [
            [
              {
                id: 'clause_particuliere',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'clause_particuliere_liste_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        }
      ],
      id: '697758dd_50b8_49b8_8181_dd393faef0e9',
      label: 'Clause particulière',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_clause_particuliere',
          label: 'Ajouter une clause particulière supplémentaire',
          type: 'SELECT-BINARY'
        },
        {
          id: 'avenant_clause_particuliere_liste_contenu',
          label: 'Contenu de la clause',
          type: 'TEXTAREA'
        },
        {
          children: [
            {
              id: 'avenant_clause_particuliere_liste_avenant_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'avenant_clause_particuliere_liste_avenant_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          id: 'avenant_clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'avenant_clause_particuliere_liste_avenant_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        }
      ],
      id: '04b3b377_5e1b_4709_87c8_8ee7926cb381',
      label: 'Clause particulière - Avenant',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'delegation_clauses_particulieres',
          label: 'Clause particulière supplémentaire',
          type: 'SELECT-BINARY'
        },
        {
          id: 'delegation_clause_particuliere_liste_contenu',
          label: 'Contenu de la clause',
          type: 'TEXTAREA'
        },
        {
          children: [
            {
              id: 'delegation_clause_particuliere_liste_delegation_clause_particuliere_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'delegation_clause_particuliere_liste_delegation_clause_particuliere_clause',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          id: 'delegation_clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'delegation_clause_particuliere_liste_avenant_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 0
          },
          type: 'REPEAT'
        }
      ],
      id: '6c969ce5_e481_4a1f_b2ce_e0c99231379e',
      label: 'Clause particulière - Délégation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'attestation_proprietaire_anglais',
          label: 'Traduction en anglais',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'attestation_proprietaire_signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'f4048927_88cb_4945_95a7_1703110edfca',
      label: 'Attestation Propriétaire',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION']
              },
              {
                id: 'mandant_dip_integre_statut',
                type: 'EQUALS',
                value: 'non'
              },
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'mandant_dip_integre_statut',
                type: 'EQUALS',
                value: 'non'
              },
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION']
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'orpi_dip',
          label: "Document d'information précontractuelle",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_retractation',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_ORPI_RETRACTATION'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'orpi_retractation',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandat_recherche_type',
                value: 'recherche_exclusif',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandat_recherche_type',
                value: 'recherche_semi',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandat_recherche_type',
                value: 'recherche_simple',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_LOCATION_RECHERCHE'
          },
          id: 'dip_recherche',
          label: "Document d'informations précontractuelles - Recherche Location",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandat_type',
                value: 'gestion',
                type: 'DIFFERENT'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'UNIS_LOCATION_HABITATION_MANDAT_LOCATION_UNIS',
                  'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION'
                ]
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_LOCATION'
          },
          id: 'dip_location',
          label: "Document d'informations précontractuelles - Location",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandat_type',
                value: 'gestion',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'UNIS_LOCATION_HABITATION_MANDAT_GESTION_UNIS',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_SAISONNIER',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_MANDAT_BAIL_PARAHOTELIER',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_CONVENTION_LOCATION_SAISONNIERE',
                  'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_CONVENTION_LOCATION_SAISONNIERE_PARAHOTELIER',
                  'ORPI_IMMOBILIER_LOCATION_MANDAT_GESTION',
                  'MA_GESTION_LOCATIVE__LOCATION__MANDAT_GESTION',
                  'AJP__LOCATION__MANDAT_DE_GESTION_AJP',
                  'BENEDIC__LOCATION__MANDAT_DE_GESTION'
                ]
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_GESTION'
          },
          id: 'dip_gestion',
          label: "Document d'informations précontractuelles - Gestion",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandat_saisonnier_honoraires_charge',
                value: 'honoraires_gestion_charge_bailleur',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandat_saisonnier_honoraires_charge',
                value: 'honoraires_gestion_charge_locataire',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_ADMINISTRATION'
          },
          id: 'dip_administration',
          label: "Document d'informations précontractuelles - Administration",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_retractation',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
          },
          id: 'mandat_retractation',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT_PRELLO'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_retractation_prello',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_retractation',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT_UNIS'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_retractation_unis',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          children: [
            {
              conditions: [
                [
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mandat_signature_hors_etablissement',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'mandat_retractation_era',
              label: 'Formulaire de rétractation',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['ERA_IMMOBILIER_LOCATION_MANDAT_UNIQUE']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_DIP_EXCLUSIF_ERA'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'mandat_dip_era',
              label: "Document d'informations précontractuelles",
              type: 'UPLOAD'
            }
          ],
          id: '4be27087_f221_4ac4_994e_bab2b4da0c5c',
          label: 'CONDITION_BLOCK_ERA',
          type: 'CONDITION_BLOCK'
        }
      ],
      conditions: [
        [
          {
            id: 'mandant_statut',
            type: 'EQUALS',
            value: 'oui'
          }
        ]
      ],
      id: '9568f530_bc35_4ff2_bb1d_52693c48cdaa',
      label: 'Droit de la consommation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_RETRACTATION_EFFICITY_2'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'retractation_efficity_2',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        }
      ],
      id: '7d9e128f_8473_479a_baf7_0a77ee241c29',
      label: 'Documents',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
          },
          id: 'formulaire_retractation_pp',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        }
      ],
      id: 'PP0e64250c_c7b2_459a_804c_d52a23fcbfcc',
      label: 'Droit de la consommation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_KW_ABONDANCE_ORGANIGRAMME'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_abondance_organigramme',
          label: 'Organigramme',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION']
              },
              {
                id: 'mandant_dip_integre_statut',
                value: 'non',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_dip_integre_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION']
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_dip_location',
          label: 'Informations Précontractuelles',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_retractation',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_KW'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_formulaire_retractation',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_KW'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_commercial_formulaire_retractation',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_KW'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_formulaire_retractation_dip',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          conditions: [[]],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_KW'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_formulaire_retractation_tapuscrit',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        }
      ],
      id: '4ca15969_3dac_447d_94e6_eae3a20f4241',
      label: 'Droit de la consommation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'signature_electronique_apport_affaire',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '0d993fea_543b_4f7e_90c9_255a9f7e32df',
      label: "Convention d'apporteur d'affaires",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'delegation',
                  label: 'Obtention de la délégation'
                },
                {
                  id: 'accord',
                  label: 'Accord du Bailleur'
                }
              ],
              id: 'partenariat_remise_bien',
              label: 'Remise du Bien après',
              type: 'SELECT'
            }
          ],
          id: 'f5bdf3a5_24a9_481e_9dd5_6e6bcd48b013',
          label: 'Le Partenariat',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'partenariat_mandat_gestion_signature',
              label: 'Partenaire signe les mandats de gestion directement',
              type: 'SELECT-BINARY'
            }
          ],
          id: '597e96ac_4442_4ddd_bdf4_75dd05f54c76',
          label: 'Propriété des mandats de gestion',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'partenariat_date_depart',
              label: 'Date de départ de la convention',
              type: 'DATE'
            }
          ],
          id: '2dd78447_4b83_430b_9112_76c3f8d68f75',
          label: 'Durée de la convention',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'annee',
                  label: 'Par année'
                },
                {
                  id: 'trimestre',
                  label: 'Par trimestre'
                }
              ],
              id: 'partenariat_frequence_retrocession',
              label: 'Fréquence des rétrocessions',
              type: 'SELECT'
            }
          ],
          id: '8983bc56_68a2_41a3_8e84_2d38d12a40f5',
          label: 'Rétrocessions',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'signature_electronique_convention_partenariat',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '015f5d3c_76ac_41f4_99ac_2e2b17145718',
      label: 'Convention de partenariat',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__IMMOBILIER__LOCATION__MANDAT',
  label: 'Mandat',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__OPERATION__IMMOBILIER__LOCATION__MANDAT',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'MANDAT'],
  type: 'RECORD'
};
