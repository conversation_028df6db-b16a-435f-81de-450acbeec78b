// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationImmobilierVenteAncienMandat: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_cnil',
          label: 'Le Client autorise la collecte de ses données personnelles',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_mandat_ajout',
          label: 'Ajouter un Avenant au Mandat de vente',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            "Si 'oui' permet de remplir certaines parties du mandat avant de l'imprimer puis de compléter le reste à la main lors de la signature.",
          id: 'mandat_tapuscrit',
          label: 'Mandat complété en tout ou partie à la main après impression',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'libre_type',
              label: 'Le type de mandat (simple,exclusif...)'
            },
            {
              id: 'libre_prix',
              label: 'Le prix de vente'
            },
            {
              id: 'libre_honoraires',
              label: "Les honoraires de l'Agence"
            },
            {
              id: 'libre_duree',
              label: 'La durée du Mandat'
            },
            {
              id: 'libre_actions',
              label: 'La liste des actions commerciales'
            },
            {
              id: 'aucune_clause',
              label: 'Aucune clause'
            }
          ],
          description:
            "Les clauses sélectionnées devront être complétées via le formulaire puis les autres seront complétées à la main après impression. Si rien n'est sélectionné tout le contrat devra être complété à la main après impression.",
          id: 'mandat_libre_liste',
          label: 'Clauses complétées par ordinateur',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'mandat_vente',
              label: 'Vente'
            },
            {
              id: 'mandat_recherche',
              label: 'Recherche'
            }
          ],
          id: 'mandat_statut',
          label: 'Le mandat est-il un mandat de vente ou de recherche ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_nom',
          label: 'Nom du mandat spécial',
          type: 'TEXT'
        },
        {
          id: 'mandat_offre_fin_date',
          label: "Date de fin de l'offre mandat spécial",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'image',
              label: 'Image'
            },
            {
              id: 'annexe',
              label: 'Annexe'
            }
          ],
          id: 'mandat_papier_mode',
          label: 'Mode',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'oui',
              label: 'Exclusif'
            },
            {
              id: 'non',
              label: 'Simple'
            }
          ],
          id: 'coldwell_mandat_statut',
          label: 'Type de mandat',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'exclusif',
              label: 'Exclusif'
            },
            {
              id: 'semi',
              label: 'Semi-exclusif'
            },
            {
              id: 'simple',
              label: 'Simple'
            }
          ],
          id: 'mandat_type',
          label: 'Type de mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'confiance',
              label: 'Confiance +'
            },
            {
              id: 'exclusif',
              label: 'Exclusif'
            },
            {
              id: 'semi',
              label: 'Semi-exclusif'
            },
            {
              id: 'simple',
              label: 'Simple'
            }
          ],
          id: 'mandat_type_confiance',
          label: 'Type de mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'exclusif',
              label: 'Exclusif'
            },
            {
              id: 'privilege',
              label: 'Privilège'
            },
            {
              id: 'simple',
              label: 'Sans Exclusivité'
            }
          ],
          id: 'mandat_type_hermes',
          label: 'Type de mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'confiance',
              label: 'Confiance'
            },
            {
              id: 'semi',
              label: 'Semi-exclusif'
            },
            {
              id: 'sans',
              label: 'Sans exclusivité'
            }
          ],
          id: 'mandat_type_iparticulier',
          label: 'Type de mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'exclusif',
              label: 'Gagnant (exclusif)'
            },
            {
              id: 'serenite',
              label: 'Sérénité (semi-exclusif)'
            },
            {
              id: 'simple',
              label: 'Initial (simple)'
            }
          ],
          id: 'mandat_type_cote_particuliers',
          label: 'Type de mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'simple',
              label: 'Simple'
            },
            {
              id: 'semi',
              label: 'Semi-exclusif'
            },
            {
              id: 'exclusif',
              label: 'Exclusif'
            },
            {
              id: 'succes',
              label: 'Succès'
            }
          ],
          id: 'mandat_type_pp',
          label: 'Type de mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'multichoix',
              label: 'Multichoix'
            },
            {
              id: 'exclusif',
              label: 'Exclusif'
            },
            {
              id: 'happy',
              label: 'Happy'
            }
          ],
          id: 'mandat_type_pp_papier',
          label: 'Type de mandat à générer',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'simple',
              label: 'simple'
            },
            {
              id: 'exclusif',
              label: 'exclusif'
            },
            {
              id: 'succes',
              label: 'succès'
            }
          ],
          id: 'mandat_type_success_pp',
          label: 'Type de mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'simple',
              label: 'Simple'
            },
            {
              id: 'exclusif',
              label: 'Exclusif'
            }
          ],
          id: 'mandat_type_simple_exclusif',
          label: 'Type de mandat',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'recherche_simple',
              label: 'Simple'
            },
            {
              id: 'recherche_exclusif',
              label: 'Exclusif'
            }
          ],
          id: 'mandat_type_recherche_pp',
          label: 'Type de mandat',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'exclusif',
              label: '30 jours nulle part ailleurs (exclusif)'
            },
            {
              id: 'partenaire',
              label: 'Partenaire'
            },
            {
              id: 'simple',
              label: 'Simple'
            }
          ],
          id: 'mandat_type_imoconseil',
          label: 'Type de mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'exclusif',
              label: 'Exclusif'
            },
            {
              id: 'partenaire',
              label: 'Partenaire'
            },
            {
              id: 'simple',
              label: 'Simple'
            }
          ],
          id: 'mandat_type_axo',
          label: 'Type de mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'type_bail_commercial',
              label: 'Bail Commercial'
            },
            {
              id: 'type_bail_pro',
              label: 'Bail Professionnel'
            }
          ],
          id: 'mandat_type_bail',
          label: 'Type de bail à céder',
          type: 'SELECT'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_VENTE_ANCIEN_DELEGATION_MANDAT']
                  }
                ]
              ],
              title: 'Numéro de mandat initial'
            }
          ],
          id: 'mandat_numero',
          label: 'Numéro de mandat',
          placeholder: 'entrez votre numéro de mandat',
          register: {
            type: 'TRANSACTION',
            contracts: [
              'IMMOBILIER_VENTE_ANCIEN_MANDAT',
              'SELECTION_HABITAT__TRANSACTION__MANDAT_DE_VENTE',
              'AJP__VENTE__MANDAT_VENTE_AJP',
              'ERA_IMMOBILIER_VENTE_MANDAT_UNIQUE',
              'ERA_IMMOBILIER_VENTE_MANDAT_SERENITE',
              'IMMOBILIER_VENTE_ANCIEN_MANDAT_ANGLAIS',
              'IMMOBILIER_VENTE_ANCIEN_MANDAT_RECHERCHE',
              'TRANSACTION__MANDAT_DE_COMMERCIALISATION',
              'TRANSACTION__MANDAT_DE_RECHERCHE_ANGLAIS',
              'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_VENTE_FONDS_COMMERCE',
              'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_RECHERCHE_FONDS_COMMERCE',
              'AGENCE_DIRECTE_IMMOBILIER_MANDAT_EXCLUSIF',
              'AGENCE_DIRECTE_IMMOBILIER_MANDAT_EXCLUSIF_HORS_ETABLISSEMENT',
              'AGENCE_DIRECTE_IMMOBILIER_MANDAT_SIMPLE',
              'AGENCE_DIRECTE_IMMOBILIER_MANDAT_RECHERCHE',
              'COLDWELL_BANKER_IMMOBILIER_MANDAT_COLDWELL',
              'FOLLIOT_IMMOBILIER_MANDAT_SEMI_EXCLUSIF',
              'KELLER_WILLIAMS_IMMOBILIER_MANDAT_RECHERCHE_KELLER',
              'KELLER_WILLIAMS_IMMOBILIER_MANDAT_SUCCESS',
              'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_KELLER',
              'KELLER_WILLIAMS_IMMOBILIER_COMMERCIAL_MANDAT_RECHERCHE_FONDS_KELLER',
              'KELLER_WILLIAMS_IMMOBILIER_COMMERCIAL_MANDAT_SUCCESS_COMMERCIAL',
              'KELLER_WILLIAMS_IMMOBILIER_COMMERCIAL_MANDAT_VENTE_FONDS_KELLER',
              'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_RECHERCHE_VIAGER_SIMPLE_KW',
              'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_SUCCESS_VIAGER',
              'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VIAGER_MUTUALISE',
              'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_SUCCESS_VIAGER_A_TERME',
              'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW',
              'IMMOBILIER_VENTE_VIAGER_MANDAT_RECHERCHE_VIAGER',
              'IMMOBILIER_VENTE_VIAGER_MANDAT_VENTE_VIAGER',
              'PIERRE_REVENTE_IMMOBILIER_MANDAT_EXCLUSIF',
              'PIERRE_REVENTE_IMMOBILIER_MANDAT_NON_EXCLUSIF',
              'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_KW_ANGLAIS',
              'ORPI_IMMOBILIER_VENTE_MANDAT_ORPI',
              'LGM_IMMOBILIER_MANDAT_LGM',
              'KW_ABONDANCE_IMMOBILIER_SUCCESS_ABONDANCE',
              'KW_ABONDANCE_IMMOBILIER_MANDAT_SIMPLE_ABONDANCE',
              'KW_ABONDANCE_IMMOBILIER_MANDAT_COEXCLUSIF',
              'EFFICITY_IMMOBILIER_MANDAT_EXCLUSIF_EFFICITY',
              'EFFICITY_IMMOBILIER_MANDAT_SIMPLE_EFFICITY',
              'LGM_IMMOBILIER_MANDAT_RECHERCHE_LGM',
              'LGM_IMMOBILIER_MANDAT_VIAGER',
              'I_PARTICULIERS_IMMOBILIER_PROFESSIONNEL_MANDAT_VENTE',
              'I_PARTICULIERS_IMMOBILIER_HABITATION_MANDAT_VENTE',
              'I_PARTICULIERS_IMMOBILIER_SUISSE_MANDAT_COURTAGE',
              'IMMOBILIER_VENTE_ANCIEN_MANDAT_SIMPLIFIE',
              'EFFICITY_IMMOBILIER_MANDAT_RECHERCHE_EFFICITY',
              'EFFICITY_IMMOBILIER_MANDAT_VENTE_ANGLAIS_EFFICITY',
              'IMMOBILIER_POLYNESIE_VENTE_MANDAT_SUCCESS',
              'IMMOBILIER__POLYNESIE__VENTE__MANDAT_SUCCESS_VAPP',
              'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE',
              'IMMOBILIER_POLYNESIE_VENTE_MANDAT_RECHERCHE',
              'ISM_IMMOBILIER_MANDAT_ISM',
              'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_RECHERCHE',
              'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_SIMPLE',
              'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_SUCCESS',
              'PVCI_SENS_IMMOBILIER_VENTE_SEULE_MANDAT_VENTE',
              'PVCI_SENS_IMMOBILIER_VENTE_SEULE_MANDAT_ANGLAIS',
              'GIBOIRE_IMMOBILIER_VENTE_MANDAT_VENTE',
              'GIBOIRE_IMMOBILIER_VENTE_MANDAT_GIBOIRE',
              'GIBOIRE_IMMOBILIER_VENTE_MANDAT_RECHERCHE',
              'IMOCONSEIL_IMMOBILIER_VENTE_MANDAT_VENTE_PLURIEL',
              'IMOCONSEIL_IMMOBILIER_VENTE_CONTRAT_PRELIMINAIRE_ACHAT',
              'COTE_PARTICULIERS_IMMOBILIER_VENTE_MANDAT_SERENITE',
              'MY_CHEZ_MOI_IMMOBILIER_VENTE_MANDAT',
              'AXO_ACTIF_IMMOBILIER_HABITATION_MANDAT_TROIS_OPTIONS',
              'AXO_ACTIF_IMMOBILIER_HABITATION_MANDAT_RECHERCHE',
              'HERMES_IMMOBILIER_TRANSACTION_PRO_MANDAT_VENTE',
              'HERMES_IMMOBILIER_TRANSACTION_PRO_MANDAT_RECHERCHE',
              'JOHN_TAYLOR_IMMOBILIER_VENTE_HABITATION_MANDAT_VENTE',
              'JOHN_TAYLOR_IMMOBILIER_VENTE_HABITATION_MANDAT_COEXCLUSIF',
              'JOHN_TAYLOR_IMMOBILIER_VENTE_HABITATION_DELEGATION_MANDAT',
              'JOHN_TAYLOR_IMMOBILIER_VENTE_HABITATION_MANDAT_RECHERCHE',
              'ORPI_IMMOBILIER_VENTE_MANDAT_120_JOURS',
              'ORPI_IMMOBILIER_VENTE_MANDAT_SIMPLE',
              'ORPI_IMMOBILIER_VENTE_MANDAT_RECHERCHE',
              'FRANCE_FORESTRY_IMMOBILIER_MANDAT_VENTE_FRANCE_FORESTRY',
              'EFFICITY__IMMOBILIER__MANDAT_DE_VENTE_LOCAL_COMMERCIAL',
              'BEAUX_VILLAGES__IMMOBILIER__MANDAT_VENTE',
              'CANNISIMMO__IMMOBILIER__MANDAT_VENTE',
              'SANTONI__IMMOBILIER__MANDAT_VENTE',
              'SEXTANT__TRANSACTION__MANDAT_DE_VENTE_LMNP'
            ]
          },
          type: 'TEXT'
        },
        {
          id: 'mandat_numero_initial',
          label: 'Numéro de mandat initial',
          type: 'TEXT'
        },
        {
          id: 'mandat_comandataire_numero',
          label: 'Numéro du mandat du comandataire',
          type: 'NUMBER'
        },
        {
          id: 'mandat_numero_dossier',
          label: 'Numéro de Dossier',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_anglais',
          label: 'Mandat en Anglais',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_activite_societe',
          label: 'Activité de la société mandante',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_type_exclusif_exclusion',
          label: "Exclure une agence de la clause d'exclusivité",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_type_exclusif_exclusion_agence_nom',
          label: "Nom de l'agence à exclure",
          type: 'TEXT'
        },
        {
          id: 'mandat_type_exclusif_exclusion_agence_adresse',
          label: "Adresse de l'agence à exclure",
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_type_exclusif_reduction',
          label: "Honoraires réduits en cas de présentation d'un Acquéreur par le Vendeur",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_type_exclusion_acquereur',
          label: "Exclusion d'un ou plusieurs acquéreurs",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_type_exclusion_acquereur_nom',
          label: 'Nom du / des acquéreurs exclus',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_procuration_mandat',
          label: 'Un ou plusieurs mandants donne procuration',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'prix',
              label: 'Le prix de vente'
            },
            {
              id: 'honoraires',
              label: 'Les honoraires'
            },
            {
              id: 'option',
              label: 'Options choisies par le Mandant'
            },
            {
              id: 'aucune_clause',
              label: 'Aucune clause'
            }
          ],
          id: 'mandat_libre_liste_kw',
          label: 'Quelles clauses sont complétées avant le rendez-vous ?',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'mandat_semi_agence_unique',
              label: 'Aucun honoraires ne sont dus si le Vendeur trouve lui même un Acquéreur'
            },
            {
              id: 'mandat_semi_honoraire_reduits',
              label: 'Les honoraires sont réduits de moitié si le Vendeur trouve lui même un Acquéreur'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_type',
                value: 'semi',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mandat_semi_condition',
          label: 'Conditions de la semi-exclusivité',
          type: 'SELECT'
        },
        {
          id: 'mandat_signature_date',
          label: 'Date de signature du mandat',
          type: 'DATE'
        },
        {
          id: 'mandat_lotissement_numero_permis',
          label: "Numéro du permis d'aménager",
          type: 'TEXT'
        },
        {
          id: 'mandat_lotissement_date_permis',
          label: 'Date de délivrance du permis',
          type: 'DATE'
        },
        {
          id: 'mandat_lotissement_nom',
          label: 'Nom du lotissement',
          type: 'TEXT'
        },
        {
          id: 'mandat_lotissement_type',
          label: 'Type de programme',
          type: 'TEXT'
        },
        {
          id: 'mandat_lotissement_parcelles',
          label: 'Nombres de parcelles du lotissement',
          type: 'NUMBER'
        },
        {
          id: 'mandat_lotissement_adresse',
          label: 'Adresse du lotissement',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_pierre_honoraire_statut',
          label: 'Le contrat prévoit-il des honoraires de négociation ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_pierre_montant_nego',
          label: 'Montant des honoraires de négociations',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_recherche_investissement',
          label: "S'agit-il d'un mandat de recherche d'investissement ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_recherche_description',
          label: 'Description des Biens à rechercher',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_recherche_adresse_statut',
          label: "Indiquer l'adresse des biens à rechercher",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_recherche_adresse',
          label: 'Adresse des biens à rechercher',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_recherche_designation_precise',
          label: 'Le bien est clairement identifié au mandat',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'fonds',
              label: 'Fonds de commerce'
            },
            {
              id: 'mur_location',
              label: 'Location de Murs'
            },
            {
              id: 'mur_acquisition',
              label: 'Acquisition de Murs'
            },
            {
              id: 'droit_bail',
              label: 'Location avec droit au bail'
            },
            {
              id: 'droit_bail_pure',
              label: 'Location pure'
            },
            {
              id: 'bureaux_location',
              label: 'Location de bureaux'
            },
            {
              id: 'bureaux_acquisition',
              label: 'Acquisition de bureaux'
            },
            {
              id: 'locaux_industriels',
              label: 'Locaux Industriels'
            },
            {
              id: 'autre',
              label: 'Autres biens'
            }
          ],
          id: 'mandat_recherche_hermes',
          label: 'Type de Biens à rechercher',
          multiple: 'true',
          type: 'SELECT'
        },
        {
          id: 'mandat_recherche_description_autre',
          label: 'Autre type de biens',
          type: 'TEXT'
        },
        {
          id: 'mandat_recherche_secteur',
          label: 'Secteur géographique de recherche',
          type: 'TEXT'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['HERMES_IMMOBILIER_TRANSACTION_PRO_MANDAT_RECHERCHE']
                  }
                ]
              ],
              title: 'Activité(s) envisagée(s)'
            }
          ],
          id: 'mandat_recherche_criteres',
          label: 'Critères des biens à rechercher',
          type: 'TEXT'
        },
        {
          id: 'mandat_recherche_prix_maximum',
          label: 'Montant maximum du prix de vente',
          type: 'PRICE'
        },
        {
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'mandat_recherche_prix_maximum_cfp',
          label: 'Montant maximum du prix de vente',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_recherche_prix_honoraires_inclus',
          label: 'Montant du prix incluant les honoraires',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'drome',
              label: 'Drôme'
            },
            {
              id: 'ardeche',
              label: 'Ardèche'
            },
            {
              id: 'rhone',
              label: 'Rhône'
            },
            {
              id: 'rhone_alpes',
              label: 'Rhône Alpes'
            },
            {
              id: 'france',
              label: 'France'
            },
            {
              id: 'autre',
              label: 'Autres compléments'
            }
          ],
          id: 'mandat_recherche_localisation_hermes',
          label: 'Localisation de recherche',
          multiple: 'true',
          type: 'SELECT'
        },
        {
          id: 'mandat_recherche_localisation_hermes_autre',
          label: 'Compléments de recherche',
          type: 'TEXT'
        },
        {
          id: 'commercialisation_description_projet',
          label: 'Description du projet immobilier du Mandant',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'commercialisation_proprietaire_terrain',
          label: 'Mandant déjà propriétaire du terrain objet du projet immobilier',
          type: 'SELECT-BINARY'
        },
        {
          id: 'commercialisation_proprietaire_terrain_notaire',
          label: 'Nom du notaire ayant reçu la vente',
          type: 'TEXT'
        },
        {
          id: 'commercialisation_proprietaire_terrain_date_vente',
          label: 'Date de signature de la vente',
          type: 'DATE'
        },
        {
          id: 'commercialisation_proprietaire_terrain_date_promesse',
          label: 'Date de signature de la promesse de vente',
          type: 'DATE'
        },
        {
          id: 'commercialisation_proprietaire_terrain_adresse',
          label: 'Adresse du terrain',
          type: 'TEXT'
        },
        {
          id: 'commercialisation_proprietaire_terrain_contenance',
          label: 'Contenance du terrain',
          type: 'TEXT'
        },
        {
          id: 'commercialisation_proprietaire_terrain_cadastre',
          label: 'Cadastre du terrain',
          type: 'TEXT'
        },
        {
          id: 'commercialisation_autorisation_depot_date',
          label: "Date de dépôt de l'autorisation d'urbanisme",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'commercialisation_autorisation_obtention',
          label: 'Permis obtenu',
          type: 'SELECT-BINARY'
        },
        {
          id: 'commercialisation_autorisation_obtention_date',
          label: "Date de délivrance de l'autorisation",
          type: 'DATE'
        },
        {
          id: 'commercialisation_autorisation_obtention_numero',
          label: "Numéro de l'autorisation",
          type: 'TEXT'
        },
        {
          id: 'commercialisation_autorisation_obtention_pv_affichage',
          label: "Liste des PV d'affichage",
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'commercialisation_autorisation_purge',
          label: 'Permis purgé de toute recours',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'commercialisation_chantier_ouvert',
          label: 'Chantier déjà ouvert',
          type: 'SELECT-BINARY'
        },
        {
          id: 'commercialisation_chantier_ouvert_date',
          label: "Date d'ouverture du chantier",
          type: 'DATE'
        },
        {
          id: 'commercialisation_achevement_date',
          label: "Date prévisionnelle d'achèvement",
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['TRANSACTION__MANDAT_DE_COMMERCIALISATION']
              }
            ]
          ],
          id: 'commercialisation_note_technique',
          label: 'Note technique sommaire',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['TRANSACTION__MANDAT_DE_COMMERCIALISATION']
              }
            ]
          ],
          id: 'commercialisation_plan_masse',
          label: 'Plan de masse prévisionnel',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['TRANSACTION__MANDAT_DE_COMMERCIALISATION']
              }
            ]
          ],
          id: 'commercialisation_liste_lot',
          label: 'Liste des lots à commercialiser',
          type: 'UPLOAD'
        },
        {
          id: 'mandat_recherche_bouquet_maximum',
          label: 'Montant maximum du bouquet',
          type: 'PRICE'
        },
        {
          id: 'mandat_recherche_rente_maximum',
          label: 'Montant maximum de la rente mensuelle',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'mandat_commercial_local_commercial',
              label: 'Locaux Commerciaux'
            },
            {
              id: 'mandat_commercial_local_professionnels',
              label: 'Locaux Professionnels'
            },
            {
              id: 'mandat_commercial_fonds_commerce',
              label: 'Cession de fonds de commerce'
            },
            {
              id: 'mandat_commercial_cession_titre',
              label: 'Cession de titres'
            },
            {
              id: 'mandat_commercial_droit_bail',
              label: 'Cession de droit au bail'
            }
          ],
          id: 'mandat_commercial_nature_bien',
          label: 'Nature des biens ou droits à vendre',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'mandat_commercial_fonds_commerce',
              label: 'Cession de fonds de commerce'
            },
            {
              id: 'mandat_commercial_cession_titre',
              label: 'Cession de titres'
            },
            {
              id: 'mandat_commercial_autre',
              label: 'Autres'
            }
          ],
          id: 'mandat_commercial_nature_biens',
          label: 'Nature des biens ou droits à vendre',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'mandat_commercial_murs',
              label: 'Murs commerciaux'
            },
            {
              id: 'mandat_commercial_fonds_commerce',
              label: 'Cession de fonds de commerce'
            },
            {
              id: 'mandat_commercial_local',
              label: 'Local/bureaux'
            },
            {
              id: 'mandat_commercial_autre',
              label: 'Autres'
            }
          ],
          id: 'mandat_commercial_nature_biens_iparticuliers',
          label: 'Nature des biens ou droits à vendre',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_commercial_nature_biens_iparticuliers',
                value: 'mandat_commercial_autre',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mandat_commercial_autre_bien_iparticuliers',
          label: 'Autre type de biens à vendre :',
          type: 'TEXT'
        },
        {
          id: 'mandat_commercial_nature_autre',
          label: 'Description du bien à vendre',
          type: 'TEXTAREA'
        },
        {
          id: 'mandat_delegation_liste_bien',
          label: 'Descriptions des biens à vendre',
          type: 'TEXTAREA'
        },
        {
          id: 'mandat_delegation_prix',
          label: 'Prix de vente / Ventilation du prix',
          type: 'TEXTAREA'
        },
        {
          id: 'mandat_adresse_bien',
          label: 'Adresse du bien à vendre :',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'tabac',
              label: 'Tabac'
            },
            {
              id: 'fdj',
              label: 'Française des Jeux'
            },
            {
              id: 'presse',
              label: 'Presse'
            },
            {
              id: 'licence',
              label: 'Licence IV'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          id: 'mandat_fonds_tabac',
          label: 'Type de fonds à vendre',
          multiple: true,
          type: 'SELECT'
        },
        {
          id: 'mandat_fonds_tabac_autre',
          label: 'Autre type de fonds de commerce',
          type: 'TEXT'
        },
        {
          id: 'mandat_nombre_contrat_travail',
          label: 'Nombre de contrat de travail en cours lié au fonds',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'fonds',
              label: 'Fonds de Commerce'
            },
            {
              id: 'droit_bail',
              label: 'Droit au bail'
            },
            {
              id: 'pas_porte',
              label: 'Pas de porte'
            },
            {
              id: 'parts',
              label: 'Actions / parts sociales'
            }
          ],
          id: 'mandat_pp_business_type_bien',
          label: 'Le mandat de vente porte sur les biens suivants',
          type: 'SELECT'
        },
        {
          id: 'mandat_commercial_activite_enseigne',
          label: "Enseigne / Nom commercial utilisé pour l'exercice de l'activité",
          type: 'TEXT'
        },
        {
          id: 'mandat_commercial_activite',
          label: 'Activité exercée',
          type: 'TEXT'
        },
        {
          id: 'mandat_commercial_activite_adresse',
          label: "Adresse d'exercice de l'activité / du Bien",
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_commercial_bail',
          label: 'Le fonds comprend-il le droit au bail ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_business_debut_bail',
          label: 'Date de début du bail',
          type: 'DATE'
        },
        {
          id: 'mandat_business_duree_bail',
          label: 'Durée du bail',
          type: 'TEXT'
        },
        {
          id: 'mandat_business_bail_renouvellement',
          label: 'Date de renouvellement du bail',
          type: 'DATE'
        },
        {
          id: 'mandat_business_fin_bail',
          label: 'Date de fin du bail',
          type: 'DATE'
        },
        {
          id: 'mandat_business_designation_bien',
          label: 'Désignation du bien vendu',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'acquisition',
              label: 'Acquisition'
            },
            {
              id: 'creation',
              label: 'Création'
            }
          ],
          id: 'mandat_pp_business_acquisition',
          label: 'Le mandant est propriétaire du bien par',
          type: 'SELECT'
        },
        {
          id: 'mandat_pp_business_date_acquisition',
          label: "Date d'acquisition ou de création",
          type: 'DATE'
        },
        {
          id: 'prix_vente_mandat',
          label: 'Prix de vente',
          type: 'PRICE'
        },
        {
          description: 'Indiquer le montant hors honoraires acquéreur le cas échéant',
          id: 'mandat_commercial_fonds_montant',
          label: 'Prix de vente',
          type: 'PRICE'
        },
        {
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'prix_vente_mandat_cfp',
          label: 'Prix de vente',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'prix_vente_mandat_chf',
          label: 'Prix de vente',
          suffix: 'CHF',
          type: 'PRICE'
        },
        {
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'mandat_commercial_fonds_montant_cfp',
          label: 'Prix de vente',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'ttc',
              label: 'Toutes Taxes Comprises'
            },
            {
              id: 'ht',
              label: 'Hors Taxes'
            }
          ],
          id: 'prix_vente_tva',
          label: 'Ce prix est exprimé',
          type: 'SELECT'
        },
        {
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'prix',
          label: 'Prix de commercialisation du bien',
          type: 'PRICE'
        },
        {
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'prix_cfp',
          label: 'Prix de commercialisation du bien',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_vente_tva_statut',
          label: 'Le prix de vente est soumis à TVA',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_coordonnees',
          label: "Affichage des coordonnées de l'intermédiaire",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            "Les coordonnées pourront toujours être renseignées dans la fiche mais n'apparaîtront pas dans le contrat.",
          id: 'mandat_coordonnees_parties',
          label: 'Affichage des coordonnées des parties',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_cadastre',
          label: 'Affichage des références cadastrales',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_box_sus',
          label: "Possibilité d'acquérir un box en plus",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_box_designation',
          label: 'Désignation du box',
          type: 'TEXT'
        },
        {
          id: 'mandat_box_prix',
          label: 'Prix du box',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'avenant_age',
              label: "Modification de l'âge, des infos financières et des honoraires"
            },
            {
              id: 'avenant_prix',
              label: 'Modification des infos financières et des honoraires'
            },
            {
              id: 'avenant_honoraires',
              label: 'Modification montant des honoraires'
            }
          ],
          id: 'mandat_avenant_type_viager_kw',
          label: 'La modification du mandat porte sur',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_avenant_libre_modification',
          label: 'Ajouter du texte de modification libre',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'avenant_prix',
              label: 'Modification du prix de vente'
            },
            {
              id: 'avenant_debiteur',
              label: 'Modification débiteur des honoraires'
            },
            {
              id: 'avenant_honoraires',
              label: 'Modification montant des honoraires'
            },
            {
              id: 'avenant_libre',
              label: 'Modification libre'
            }
          ],
          id: 'mandat_avenant_type',
          label: 'La modification du mandat porte sur',
          multiple: true,
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'avenant_prix',
              label: 'Sur le prix'
            },
            {
              id: 'avenant_honoraires',
              label: 'Sur les honoraires'
            },
            {
              id: 'avenant_libre',
              label: 'Avenant en texte libre'
            }
          ],
          id: 'mandat_avenant_type_pp',
          label: 'La modification du mandat porte sur',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'prix',
              label: 'Honoraires/prix idéal vendeur/de départ'
            },
            {
              id: 'date',
              label: 'Date de vente interactive'
            },
            {
              id: 'libre',
              label: 'Modification Libre'
            }
          ],
          id: 'mandat_avenant_type_encheres',
          label: 'La modification du mandat porte sur',
          multiple: true,
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'prix',
              label: 'Modification du prix de recherche'
            },
            {
              id: 'bien',
              label: 'Modification du bien recherché'
            },
            {
              id: 'libre',
              label: 'Modification Libre'
            }
          ],
          id: 'mandat_avenant_recherche',
          label: 'La modification du mandat de recherche porte sur',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          id: 'mandat_avenant_libre_texte',
          label: "Texte de l'avenant libre",
          type: 'TEXTAREA'
        },
        {
          id: 'mandat_recherche_description_avenant',
          label: 'Modification - Description des Biens à rechercher',
          type: 'TEXTAREA'
        },
        {
          id: 'mandat_recherche_secteur_avenant',
          label: 'Modification - Secteur géographique de recherche',
          type: 'TEXT'
        },
        {
          id: 'mandat_recherche_criteres_avenant',
          label: 'Modification - Critères des biens à rechercher',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_avenant_statut',
          label: 'Un avenant au mandat a-t-il été signé ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_avenant_numero',
          label: "Numéro de l'avenant au mandat",
          type: 'TEXT'
        },
        {
          id: 'mandat_avenant_date',
          label: "Date de signature de l'avenant",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_clause_particuliere',
          label: 'Ajouter une clause particulière supplémentaire',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'avenant_clause_particuliere_liste_avenant_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'avenant_clause_particuliere_liste_avenant_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          id: 'avenant_clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'avenant_clause_particuliere_liste_avenant_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 0
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pierre_mandat_recherche',
          label: 'Un mandat de recherche a-t-il été signé ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'pierre_mandat_recherche_numero',
          label: 'Numero du mandat de recherche',
          type: 'TEXT'
        },
        {
          id: 'pierre_mandat_recherche_date',
          label: 'Date de signature du mandat de recherche',
          type: 'DATE'
        },
        {
          id: 'pierre_mandat_recherche_mandataire',
          label: 'Dénomination sociale du mandataire',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pierre_mandat_agence',
          label: "Applique-t-on des frais d'agence sur ce dossier ?",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'pierre_mandat_agence',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'pierre_mandat_recherche',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pierre_mandat_frais_agence',
          label: "Pourcentage des frais d'agence sur le prix TTC",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'statut_location_libre',
              label: 'Libres'
            },
            {
              id: 'statut_location_loue',
              label: 'Loués'
            }
          ],
          id: 'mandat_statut_location',
          label: 'Le jour de la vente, les biens seront',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'statut_location_libre',
              label: 'Libres'
            },
            {
              id: 'statut_location_loue',
              label: 'Loués'
            },
            {
              id: 'statut_location_viager_libre',
              label: 'Viager Libre'
            },
            {
              id: 'statut_location_viager_occupé',
              label: 'Viager Occupé'
            }
          ],
          id: 'mandat_statut_location_imoconseil',
          label: 'Le jour de la vente, les biens seront',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_statut_location',
                value: 'statut_location_loue',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mandat_etat_locatif',
          label: 'Etat locatif',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_notaire_choisi',
          label: 'Le Vendeur a-t-il déjà choisi un notaire ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_notaire_vendeur',
          label: 'Nom du notaire vendeur',
          type: 'TEXT'
        },
        {
          id: 'mandat_notaire_vendeur_adresse',
          label: 'Adresse du notaire',
          type: 'ADDRESS'
        },
        {
          id: 'mandat_notaire_vendeur_telephone',
          label: 'Téléphone du notaire',
          type: 'PHONE'
        },
        {
          id: 'mandat_notaire_vendeur_mail',
          label: 'Adresse mail du notaire',
          type: 'EMAIL'
        },
        {
          id: 'bail_commercial_debut',
          label: 'Date de début du bail',
          type: 'DATE'
        },
        {
          id: 'bail_commercial_fin',
          label: 'Date de fin du bail',
          type: 'DATE'
        },
        {
          id: 'bail_commercial_loyer',
          label: 'Montant du loyer annuel',
          type: 'PRICE'
        },
        {
          id: 'bail_commercial_loyer_principal',
          label: 'Montant du loyer annuel principal',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'mensuel',
              label: 'paiement par mensualités'
            },
            {
              id: 'trimestriel',
              label: 'paiement trimestriel'
            },
            {
              id: 'echoir',
              label: 'paiement à terme echu'
            },
            {
              id: 'avance',
              label: "paiement d'avance"
            }
          ],
          id: 'bail_commercial_modalite',
          label: 'Modalité de paiement du loyer',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__PROPRIETES_PRIVEES__IMMOBILIER']
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'annexe_mandat_vip',
          label: 'Annexe au Mandat VIP',
          templateId: 'annexeMandatVip.pdf',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__PROPRIETES_PRIVEES__IMMOBILIER']
              }
            ]
          ],
          id: 'annexe_mandat_reussite',
          label: 'Annexe au Mandat REUSSITE',
          templateId: 'annexeMandatReussite.pdf',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__PROPRIETES_PRIVEES__IMMOBILIER']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_ANNEXE_MANDAT_ULTRA_VIP'
          },
          id: 'annexe_mandat_ultravip',
          label: 'Annexe au Mandat ULTRA VIP +',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_RETRACTATION_IPARTICULIER'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'retractation_iparticulier',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_RETRACTATION_IMOCONSEIL'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'formulaire_retractation_imoconseil',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_RETRACTATION_AXO'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'formulaire_retractation_axo',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_mandat_cpa',
          label: 'Ajouter un Avenant au Mandat de vente',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'a_jour',
              label: 'DPE postérieur au 1er juillet 2021'
            },
            {
              id: 'en_cours',
              label: 'DPE réalisé avant le 1er Juillet 2021'
            },
            {
              id: 'aucun',
              label: 'Aucun DPE fourni'
            },
            {
              id: 'non_soumis',
              label: 'Bien non concerné par le DPE'
            }
          ],
          id: 'dpe_statut',
          label: 'Concernant le DPE',
          type: 'SELECT'
        },
        {
          children: [
            {
              conditions: [
                [
                  {
                    id: 'mandat_type_pp_papier',
                    type: 'EQUALS',
                    value: 'multichoix'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_MULTICHOIX'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'mandat_pp_multichoix_document',
              label: 'Mandat Multichoix',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type_pp_papier',
                    type: 'EQUALS',
                    value: 'multichoix'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_MULTICHOIX_DIP'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'mandat_pp_multichoix_dip_document',
              label: "Document d'informations précontractuelles",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type_pp_papier',
                    type: 'EQUALS',
                    value: 'happy'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_HAPPY'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'mandat_pp_happy_document',
              label: 'Mandat Happy',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type_pp_papier',
                    type: 'EQUALS',
                    value: 'happy'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_HAPPY_DIP'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'mandat_pp_happy_dip_document',
              label: "Document d'informations précontractuelles",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type_pp_papier',
                    type: 'EQUALS',
                    value: 'exclusif'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_EXCLUSIF'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'mandat_pp_exclusif_document',
              label: 'Mandat Exclusif',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type_pp_papier',
                    type: 'EQUALS',
                    value: 'exclusif'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_EXCLUSIF_DIP'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'mandat_exclusif_dip_document',
              label: "Document d'informations précontractuelles",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type_simple_exclusif',
                    type: 'EQUALS',
                    value: 'simple'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES__TRANSACTION__MANDAT_DE_RECHERCHE_PROCEDURE_PAPIER']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_RECHERCHE_SIMPLE'
              },
              id: 'mandat_pp_recherche_simple',
              label: 'Mandat de recherche simple',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type_simple_exclusif',
                    type: 'EQUALS',
                    value: 'simple'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES__TRANSACTION__MANDAT_DE_RECHERCHE_PROCEDURE_PAPIER']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_RECHERCHE_SIMPLE_DIP'
              },
              id: 'mandat_pp_recherche_simple_dip',
              label: "Document d'informations précontractuelles - Mandat de recherche simple",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type_simple_exclusif',
                    type: 'EQUALS',
                    value: 'exclusif'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES__TRANSACTION__MANDAT_DE_RECHERCHE_PROCEDURE_PAPIER']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_RECHERCHE_EXCLUSIF'
              },
              id: 'mandat_pp_recherche_exclusif',
              label: 'Mandat de recherche exclusif',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_type_simple_exclusif',
                    type: 'EQUALS',
                    value: 'exclusif'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES__TRANSACTION__MANDAT_DE_RECHERCHE_PROCEDURE_PAPIER']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_RECHERCHE_EXCLUSIF_DIP'
              },
              id: 'mandat_pp_recherche_exclusif_dip',
              label: "Document d'informations précontractuelles - Mandat de recherche exclusif",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES__TRANSACTION__MANDAT_VIP_PROCEDURE_PAPIER']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_VIP'
              },
              id: 'mandat_pp_vip_document',
              label: 'Mandat de vente VIP',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES__TRANSACTION__MANDAT_VIP_PROCEDURE_PAPIER']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_MANDAT_PP_VIP_DIP'
              },
              id: 'mandat_pp_vip_document_dip',
              label: "Document d'informations précontractuelles - Mandat VIP",
              type: 'UPLOAD'
            }
          ],
          id: '8bc37io3_fu4f_ps3z_po23_a675557bgu97',
          label: 'Mandat',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              conditions: [
                [
                  {
                    id: 'mandat_honoraires_charge_simple',
                    type: 'EQUALS',
                    value: 'vendeur'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_LIA_PP_VENDEUR'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'lia_pp_vendeur_document',
              label: 'LIA Charge Vendeur',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandat_honoraires_charge_simple',
                    type: 'EQUALS',
                    value: 'acquereur'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_LIA_PP_ACQUEREUR'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'lia_pp_acquereur_document',
              label: 'LIA Charge Acquéreur',
              type: 'UPLOAD'
            }
          ],
          id: '8bc3mljs_xcd1_pml3_lk30_a675557btyu7',
          label: 'LIA',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              defaultAnswer: {
                resourceId: 'STATIC_FILES_FICHE_RENSEIGNEMENT_VENDEUR'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'fiche_renseignement_vendeur_document',
              label: 'Fiche Renseignement Vendeur',
              type: 'UPLOAD'
            },
            {
              defaultAnswer: {
                resourceId: 'STATIC_FILES_FICHE_RENSEIGNEMENT_VENDEUR_BUSINESS'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'fiche_renseignement_vendeur_business_document',
              label: 'Fiche Renseignement Vendeur Business',
              type: 'UPLOAD'
            }
          ],
          id: '8bc3ghj6_06kl_pvh6_pm1a_fu60557b57rf',
          label: 'Fiche Renseignement Vendeur',
          type: 'CATEGORY'
        }
      ],
      id: '64184197_629f_4bd7_8b03_99b7782dbcb1',
      label: 'Informations générales sur le mandat',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'Non'
            },
            {
              id: 'oui',
              label: 'Oui'
            }
          ],
          description: "Il ne sera pas possible d'être supérieur au barème ou inférieur de 30%",
          id: 'mandat_bareme_application',
          label: 'Application du barème des honoraires',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_libre_honoraires',
          label: 'La clause sur les honoraires est-elle complétée avant le rendez-vous ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'recherche_pourcentage',
              label: 'En pourcentage'
            },
            {
              id: 'recherche_fixe',
              label: 'En montant fixe'
            }
          ],
          id: 'mandat_recherche_calcul',
          label: 'Les honoraires de recherche du Mandataire sont calculés',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_recherche_forfait',
          label: 'Montant des honoraires du mandat de recherche',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_recherche_calcul',
                value: 'recherche_fixe',
                type: 'EQUALS'
              }
            ]
          ],
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'mandat_recherche_forfait_cfp',
          label: 'Montant des honoraires du mandat de recherche',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'mandat_recherche_pourcentage',
          label: 'Pourcentage des honoraires du mandat de recherche',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'recherche_pourcentage',
              label: 'En pourcentage'
            },
            {
              id: 'recherche_fixe',
              label: 'En montant fixe'
            }
          ],
          id: 'mandat_vente_calcul',
          label: "Honoraires de l'agence indiqués",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'ttc',
              label: 'En TTC'
            },
            {
              id: 'ht',
              label: 'En HT'
            },
            {
              id: 'double',
              label: 'TTC et HT'
            }
          ],
          id: 'mandat_vente_honoraires_affichage',
          label: 'Affichage des honoraires',
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'honoraires_charge_vendeur',
              label: 'du Vendeur'
            },
            {
              id: 'honoraires_charge_acquereur',
              label: "de l'Acquéreur"
            },
            {
              id: 'honoraires_charge_double',
              label: "du Vendeur et de l'Acquéreur"
            }
          ],
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Débiteur des honoraires'
            }
          ],
          id: 'mandat_honoraires_charge',
          label: "Honoraires d'agence à la charge",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'vendeur',
              label: 'Du Vendeur'
            },
            {
              id: 'acquereur',
              label: "De l'Acquéreur"
            }
          ],
          id: 'mandat_honoraires_charge_simple',
          label: "Honoraires d'agence à la charge",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'honoraires_charge_vendeur',
              label: 'du Vendeur'
            },
            {
              id: 'honoraires_charge_acquereur',
              label: "de l'Acquéreur"
            },
            {
              id: 'honoraires_charge_double',
              label: "du Vendeur et de l'Acquéreur"
            }
          ],
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Nouveau débiteur des honoraires'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_debiteur'
              }
            ]
          ],
          id: 'mandat_avenant_honoraires_charge',
          label: "Honoraires d'agence à la charge",
          type: 'SELECT'
        },
        {
          children: [
            {
              id: 'mandat_pourcentage_honoraires_vendeur',
              label: 'Pourcentage des honoraires charge Vendeur',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'mandat_pourcentage_honoraires_acquereur',
              label: 'Pourcentage des honoraires charge Acquéreur',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              conditionalTitles: [
                {
                  conditions: [
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                          'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                          'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                          'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                          'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                          'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                          'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                          'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                          'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                          'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                          'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                          'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                          'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                          'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                          'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                          'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                          'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                          'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                          'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                          'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                        ]
                      }
                    ]
                  ],
                  title: 'Honoraires initiaux charge Vendeur'
                }
              ],
              conditions: [
                [
                  {
                    id: 'mandat_avenant_signature_electronique',
                    type: 'NOT_EMPTY'
                  },
                  {
                    type: 'DIFFERENT_CONTRACT_MODELS',
                    model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__EFFICITY__IMMOBILIER']
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type',
                    type: 'CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: [
                      'OPERATION__EFFICITY__IMMOBILIER',
                      'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                      'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                    ]
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: ['OPERATION__EFFICITY__IMMOBILIER', 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                    ]
                  }
                ],
                [
                  {
                    type: 'DIFFERENT_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER']
                  }
                ]
              ],
              id: 'mandat_honoraires_charge_double_vendeur',
              label: 'Montant des honoraires charge Vendeur',
              type: 'PRICE'
            },
            {
              conditionalTitles: [
                {
                  conditions: [
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                          'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                          'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                          'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                          'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                          'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                          'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                          'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                          'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                          'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                          'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                          'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                          'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                          'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                          'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                          'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                          'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                          'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                          'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                          'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                        ]
                      }
                    ]
                  ],
                  title: 'Honoraires initiaux charge Acquéreur'
                }
              ],
              conditions: [
                [
                  {
                    id: 'mandat_avenant_signature_electronique',
                    type: 'NOT_EMPTY'
                  },
                  {
                    type: 'DIFFERENT_CONTRACT_MODELS',
                    model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__EFFICITY__IMMOBILIER']
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type',
                    type: 'CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: [
                      'OPERATION__EFFICITY__IMMOBILIER',
                      'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                      'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                    ]
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: ['OPERATION__EFFICITY__IMMOBILIER', 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                    ]
                  }
                ],
                [
                  {
                    type: 'DIFFERENT_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER']
                  }
                ]
              ],
              id: 'mandat_honoraires_charge_double_acquereur',
              label: 'Montant des honoraires charge Acquéreur',
              type: 'PRICE'
            },
            {
              conditionalTitles: [
                {
                  conditions: [
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                          'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                          'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                          'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                          'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                          'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                          'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                          'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                          'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                          'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                          'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                          'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                          'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                          'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                          'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                          'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                          'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                          'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                          'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                          'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                        ]
                      }
                    ]
                  ],
                  title: 'Honoraires initiaux charge Vendeur'
                }
              ],
              conditions: [
                [
                  {
                    id: 'mandat_avenant_signature_electronique',
                    type: 'NOT_EMPTY'
                  },
                  {
                    type: 'DIFFERENT_CONTRACT_MODELS',
                    model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__EFFICITY__IMMOBILIER']
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type',
                    type: 'CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: [
                      'OPERATION__EFFICITY__IMMOBILIER',
                      'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                      'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                    ]
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: ['OPERATION__EFFICITY__IMMOBILIER', 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                    ]
                  }
                ],
                [
                  {
                    type: 'DIFFERENT_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER']
                  }
                ]
              ],
              description:
                "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
              id: 'mandat_honoraires_charge_double_vendeur_cfp',
              label: 'Montant des honoraires charge Vendeur',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              conditionalTitles: [
                {
                  conditions: [
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: [
                          'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                          'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                          'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                          'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                          'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                          'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                          'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                          'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                          'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                          'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                          'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                          'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                          'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                          'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                          'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                          'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                          'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                          'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                          'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                          'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                        ]
                      }
                    ]
                  ],
                  title: 'Honoraires initiaux charge Acquéreur'
                }
              ],
              conditions: [
                [
                  {
                    id: 'mandat_avenant_signature_electronique',
                    type: 'NOT_EMPTY'
                  },
                  {
                    type: 'DIFFERENT_CONTRACT_MODELS',
                    model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__EFFICITY__IMMOBILIER']
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type',
                    type: 'CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: [
                      'OPERATION__EFFICITY__IMMOBILIER',
                      'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                      'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                    ]
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: ['OPERATION__EFFICITY__IMMOBILIER', 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS'
                    ]
                  }
                ],
                [
                  {
                    id: 'mandat_avenant_type_pp',
                    type: 'NOT_CONTAINS',
                    value: 'avenant_honoraires'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                      'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                    ]
                  }
                ],
                [
                  {
                    type: 'DIFFERENT_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
                  }
                ],
                [
                  {
                    type: 'EMPTY',
                    id: 'mandat_nouveau_honoraires_montant'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                      'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                      'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                      'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                      'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                      'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                      'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                      'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                      'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                      'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                      'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                      'EFFICITY_IMMOBILIER_BON_VISITE',
                      'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                      'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                    ]
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER']
                  }
                ]
              ],
              description:
                "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
              id: 'mandat_honoraires_charge_double_acquereur_cfp',
              label: 'Montant des honoraires charge Acquéreur',
              suffix: 'CFP',
              type: 'PRICE'
            }
          ],
          id: '2cd584be_c0df_451b_8abd_b05aa7d6a992',
          label: 'Répartition des honoraires',
          type: 'CATEGORY'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Total Honoraires initiaux'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_signature_electronique',
                type: 'EMPTY'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandat_avenant_signature_electronique',
                type: 'NOT_EMPTY'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandat_avenant_signature_electronique',
                type: 'NOT_EMPTY'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                type: 'NOT_CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__EFFICITY__IMMOBILIER',
                  'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                  'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'NOT_CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER', 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'NOT_CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              }
            ],
            [
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER']
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER']
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              }
            ]
          ],
          id: 'mandat_honoraires_montant',
          label: 'Montant total des honoraires',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Nouveau montant des honoraires charge Vendeur'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ]
          ],
          id: 'mandat_avenant_honoraires_charge_double_vendeur',
          label: 'Montant des honoraires charge Vendeur',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Nouveau montant des honoraires charge Acquéreur'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ]
          ],
          id: 'mandat_avenant_honoraires_charge_double_acquereur',
          label: 'Montant des honoraires charge Acquéreur',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Nouveau montant total des honoraires'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_signature_electronique',
                type: 'NOT_EMPTY'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ],
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              }
            ],
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
              }
            ],
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER']
              }
            ]
          ],
          id: 'mandat_nouveau_honoraires_montant',
          label: 'Montant total des honoraires',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Total Honoraires initiaux'
            }
          ],
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'nouveau_prix_de_vente',
                    recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'CONDITIONS_GENERALES', '0'],
                    type: 'EMPTY'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandat_avenant_signature_electronique',
                type: 'NOT_EMPTY'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                type: 'NOT_CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__EFFICITY__IMMOBILIER',
                  'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                  'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'NOT_CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER', 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'NOT_CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              }
            ],
            [
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'mandat_nouveau_honoraires_montant'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER']
              }
            ]
          ],
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'mandat_honoraires_montant_cfp',
          label: 'Montant total des honoraires',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Nouveau montant des honoraires charge Vendeur'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ]
          ],
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'mandat_avenant_honoraires_charge_double_vendeur_cfp',
          label: 'Montant des honoraires charge Vendeur',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Nouveau montant des honoraires charge Acquéreur'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ]
          ],
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'mandat_avenant_honoraires_charge_double_acquereur_cfp',
          label: 'Montant des honoraires charge Acquéreur',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Nouveau montant total des honoraires'
            }
          ],
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'nouveau_prix_de_vente',
                    recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'CONDITIONS_GENERALES', '0'],
                    type: 'NOT_EMPTY'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ],
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ]
          ],
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'mandat_nouveau_honoraires_montant_cfp',
          label: 'Montant total des honoraires',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Pourcentage initial des honoraires'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EMPTY',
                id: 'mandat_pourcentage_nouveau_honoraires_vente_total'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                type: 'NOT_CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__EFFICITY__IMMOBILIER',
                  'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                  'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EMPTY',
                id: 'mandat_pourcentage_nouveau_honoraires_vente_total'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'NOT_CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER', 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'NOT_CONTAINS',
                value: 'avenant_honoraires'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO'
                ]
              }
            ],
            [
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
                  'BEAUX_VILLAGES__IMMOBILIER__OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
                  'IMMOBILIER_POLYNESIE_VENTE_OFFRE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_OFFRE_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_ACCEPTATION_OFFRE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'EFFICITY_IMMOBILIER_OFFRE_ACHAT'
                ]
              }
            ]
          ],
          description: 'Pourcentage du Montant TTC le cas échéant',
          id: 'mandat_pourcentage_honoraires_vente_total',
          label: 'Pourcentage total des honoraires :',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'de Départ',
              label: 'depart'
            },
            {
              id: 'Idéal Vendeur',
              label: 'ideal'
            }
          ],
          id: 'mandat_encheres_calcul_prix',
          label: 'Honoraires calculés sur le prix',
          type: 'SELECT'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES_MIZAPRI_AVENANT_MANDAT']
                  }
                ]
              ],
              title: 'Montant initial des honoraires sur le prix de départ'
            }
          ],
          id: 'mandat_honoraires_encheres_depart',
          label: 'Montant total TTC des honoraires, calculés sur le prix de départ',
          type: 'PRICE'
        },
        {
          id: 'mandat_honoraires_encheres_depart_nouveau',
          label: 'Nouveau montant total TTC des honoraires, calculés sur le prix de départ',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES_MIZAPRI_AVENANT_MANDAT']
                  }
                ]
              ],
              title: 'Montant initial du pourcentage sur le prix de départ'
            }
          ],
          id: 'mandat_honoraires_pourcentage_encheres_depart',
          label: 'Pourcentage total TTC des honoraires, calculés sur le prix de départ',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'mandat_honoraires_pourcentage_encheres_depart_nouveau',
          label: 'Nouveau pourcentage total TTC des honoraires, calculés sur le prix de départ',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES_MIZAPRI_AVENANT_MANDAT']
                  }
                ]
              ],
              title: 'Montant initial des honoraires sur le prix idéal'
            }
          ],
          id: 'mandat_honoraires_encheres_ideal',
          label: 'Montant total TTC des honoraires, calculés sur le prix idéal vendeur',
          type: 'PRICE'
        },
        {
          id: 'mandat_honoraires_encheres_ideal_nouveau',
          label: 'Nouveau montant total TTC des honoraires, calculés sur le prix idéal vendeur',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES_MIZAPRI_AVENANT_MANDAT']
                  }
                ]
              ],
              title: 'Montant initial du pourcentage sur le prix idéal'
            }
          ],
          id: 'mandat_honoraires_pourcentage_encheres_ideal',
          label: 'Pourcentage total TTC des honoraires, calculés sur le prix idéal vendeur',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'mandat_honoraires_pourcentage_encheres_ideal_nouveau',
          label: 'Nouveau pourcentage total TTC des honoraires, calculés sur le prix idéal vendeur',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'SELECTION_HABITAT__TRANSACTION__AVENANT_AU_MANDAT',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_PAUL_PARKER_MANDAT_VENTE_AVENANT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Nouveau pourcentage des honoraires'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_type',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                type: 'CONTAINS',
                value: 'avenant_honoraires'
              }
            ]
          ],
          description: 'Pourcentage du Montant TTC le cas échéant',
          id: 'mandat_pourcentage_nouveau_honoraires_vente_total',
          label: 'Pourcentage total des honoraires :',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_pourcentage_prix_statut',
          label: 'Afficher le pourcentage des honoraires par rapport au prix dans le mandat',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: "Si 'non' le montant des honoraires sera à renseigner manuellement",
          id: 'kw_mandat_calcul_honoraires_automatique',
          label: 'Le montant du prix de vente hors honoraire est-il calculé automatiquement ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'kw_mandat_calcul_honoraires_automatique_montant',
          label: 'Montant du prix de vente hors honoraires',
          type: 'PRICE'
        },
        {
          id: 'kw_mandat_calcul_honoraires_automatique_montant_direct',
          label: 'Montant des honoraires',
          type: 'PRICE'
        },
        {
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'kw_mandat_calcul_honoraires_automatique_montant_cfp',
          label: 'Montant du prix de vente hors honoraires',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'kw_mandat_calcul_honoraires_automatique_montant_direct_cfp',
          label: 'Montant des honoraires',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'kw_mandat_calcul_honoraires_automatique_justification',
          label: 'Justification du refus du calcul automatique des honoraires',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'kw_option_tva',
          label: "Le mandant opte-t-il pour l'assujettissement de la vente à la TVA ?",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_pourcentage',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'kw_prix_hors_honoraires',
          label: 'Montant du prix sans honoraires',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_pourcentage',
                type: 'EQUALS'
              }
            ]
          ],
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'kw_prix_hors_honoraires_cfp',
          label: 'Montant du prix sans honoraires',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'comandat_mandataire_realisateur',
          label: "Pourcentage des honoraires revenant au Mandataire réalisant l'opération",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'comandat_mandataire_realisateur_autre',
          label: "Pourcentage des honoraires revenant à l'autre Mandataire",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'comandat_mandataire_intervention',
          label: "Pourcentage des honoraires revenant au Mandataire à l'origine de l'intervention du tiers",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'comandat_mandataire_intervention_autre',
          label: "Pourcentage des honoraires revenant à l'autre Mandataire",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'mandat_semi_pourcentage_vendeur',
          label: "En cas de présentation d'un Acquéreur par le Vendeur, pourcentage de la rémunération lui revenant",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_mise_aux_encheres',
          label: 'Le bien fera-t-il l’objet d’une mise aux enchères ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_evaluation_mandataire',
          label: 'Prix de vente fixé suite à évaluation du Mandataire',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_honoraires_redaction',
          label: 'Ajouter des honoraires de rédaction',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'vendeur',
              label: 'Vendeur'
            },
            {
              id: 'acquereur',
              label: 'Acquéreur'
            }
          ],
          id: 'mandat_honoraires_redaction_charge',
          label: 'Charge des honoraires de rédaction',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_honoraires_redaction_montant',
          label: 'Montant des honoraires de rédaction',
          type: 'PRICE'
        },
        {
          id: 'cloture_apporteur_negociateur',
          label: 'Apporteur / Négociateur',
          type: 'TEXT'
        }
      ],
      id: '8bc37eb7_f88e_41d4_8140_a675557bee67',
      label: 'Honoraires',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'site_vente_intercative',
          label: 'Site / Service utilisé pour la vente interactive',
          type: 'TEXT'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES_MIZAPRI_AVENANT_MANDAT']
                  }
                ]
              ],
              title: 'Prix idéal initial'
            }
          ],
          description: 'Ce montant s’entend avant le paiement des honoraires',
          id: 'mizapri_prix_ideal_vendeur',
          label: 'Prix idéal souhaité par le mandant',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES_MIZAPRI_AVENANT_MANDAT']
                  }
                ]
              ],
              title: 'Prix idéal initial'
            }
          ],
          description: 'Ce montant doit inclure les honoraires acquéreur',
          id: 'mizapri_prix_ideal',
          label: 'Prix idéal souhaité par le mandant',
          type: 'PRICE'
        },
        {
          description: 'Ce montant s’entend avant le paiement des honoraires',
          id: 'mizapri_prix_ideal_vendeur_nouveau',
          label: 'Nouveau prix idéal souhaité par le mandant',
          type: 'PRICE'
        },
        {
          description: 'Ce montant doit inclure les honoraires acquéreur',
          id: 'mizapri_prix_ideal_nouveau',
          label: 'Nouveau prix idéal souhaité par le mandant',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES_MIZAPRI_AVENANT_MANDAT']
                  }
                ]
              ],
              title: 'Prix de départ initial'
            }
          ],
          description: 'Ce montant doit inclure les honoraires acquéreur',
          id: 'mizapri_prix_offre',
          label: 'Prix de départ des offres',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES_MIZAPRI_AVENANT_MANDAT']
                  }
                ]
              ],
              title: 'Prix de départ initial'
            }
          ],
          description: 'Ce montant s’entend avant le paiement des honoraires',
          id: 'mizapri_prix_offre_vendeur',
          label: 'Prix de départ des offres',
          type: 'PRICE'
        },
        {
          description: 'Ce montant doit inclure les honoraires acquéreur',
          id: 'mizapri_prix_offre_nouveau',
          label: 'Nouveau prix de départ des offres',
          type: 'PRICE'
        },
        {
          description: 'Ce montant s’entend avant le paiement des honoraires',
          id: 'mizapri_prix_offre_vendeur_nouveau',
          label: 'Nouveau prix de départ des offres',
          type: 'PRICE'
        },
        {
          id: 'date_commercialisation_debut',
          label: 'Date de début de commercialisation',
          type: 'DATE'
        },
        {
          id: 'date_commercialisation_fin',
          label: 'Date de fin de commercialisation',
          type: 'DATE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['PROPRIETES_PRIVEES_MIZAPRI_AVENANT_MANDAT']
                  }
                ]
              ],
              title: 'Date initiale'
            }
          ],
          id: 'mizapri_date_debut_offre',
          label: 'Date de début de collecte des offres',
          type: 'DATE'
        },
        {
          id: 'mizapri_date_debut_offre_nouvelle',
          label: 'Nouvelle date de début de collecte des offres',
          type: 'DATE'
        },
        {
          id: 'date_debut_offre',
          label: 'Date de début de collecte des offres',
          type: 'DATE'
        },
        {
          description: ' au format 08 h 30',
          id: 'heure_debut_offre',
          label: 'Heure de début de collecte des offres',
          type: 'TEXT'
        },
        {
          id: 'date_fin_offre',
          label: 'Date de fin de collecte des offres',
          type: 'DATE'
        },
        {
          description: ' au format 08 h 30',
          id: 'heure_fin_offre',
          label: 'Heure de fin de collecte des offres',
          type: 'TEXT'
        },
        {
          id: 'palier_encheres',
          label: 'Palier des enchères',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mizapri_validation_offre',
          label: 'Le Mandat souhaite-t-il recourir à l\'option "Période de validation des offre" ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'vingt_quatre',
              label: '24 h'
            },
            {
              id: 'quarante_huit',
              label: '48 h'
            },
            {
              id: 'soixante_douze',
              label: '72 h'
            },
            {
              id: 'sept_jours',
              label: '7 jours calendaires'
            }
          ],
          conditions: [
            [
              {
                id: 'mizapri_validation_offre',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mizapri_delai_offre',
          label: "Délai pour que les enchérisseurs s'alignent avec l'offre la plus chère",
          type: 'SELECT'
        },
        {
          children: [
            {
              after: {
                type: 'N_DAYS_FROM_NOW',
                value: '0'
              },
              id: 'mizapri_visite_liste_mizapri_visite_date',
              label: 'Date de visite',
              type: 'DATE'
            },
            {
              description: 'Exemple  De 14h à 18h',
              id: 'mizapri_visite_liste_mizapri_visite_tranche',
              label: 'Tranche horaire',
              type: 'TEXT'
            }
          ],
          id: 'mizapri_visite_liste',
          label: 'Ajouter des créneaux de visites',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: ' '
              },
              {
                type: 'VARIABLE',
                value: 'mizapri_visite_liste_mizapri_visite_date'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          id: 'mizapri_date_signature_ssp',
          label: 'Date extrême pour la signature du compromis de vente',
          type: 'DATE'
        }
      ],
      id: '559049cf_0220_474f_a05a_841cd063b510',
      label: 'Conditions des enchères',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'lgm_duree_mandat',
              label: 'Conserver le délai de 3 mois pour la dénonciation du mandat ?',
              type: 'SELECT-BINARY'
            },
            {
              description: 'Le délai ne peut être supérieur à 3 mois',
              id: 'lgm_duree_mandat_autre',
              label: 'Délai de dénonciation du mandat',
              suffix: 'mois',
              type: 'NUMBER'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'actions_lgm_site',
              label: 'Diffusion sur le site lgm-immobilier.fr',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'panneau',
                  label: 'Panneau à Vendre'
                },
                {
                  id: 'evaluation',
                  label: 'Evaluation du bien par écrit'
                },
                {
                  id: 'conseil',
                  label: 'Conseil sur la mise en valeur du bien'
                },
                {
                  id: 'acquereur',
                  label: 'Proposer le bien à la base acquéreur'
                },
                {
                  id: 'suivi',
                  label: "Suivi de l'acquéreur"
                }
              ],
              id: 'actions_lgm_liste',
              label: 'Actions particulières',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              choices: [
                {
                  id: 'mois',
                  label: 'Certificat de parution tous les mois'
                },
                {
                  id: 'visite',
                  label: 'Compte rendus à chque visite'
                },
                {
                  id: 'personnalise',
                  label: 'Bilans personnalisés pour optimiser la vente'
                }
              ],
              id: 'actions_lgm_periodicite',
              label: 'Modalités et périodicité des comptes-rendus',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              id: 'actions_lgm_bilan_personnalise',
              label: 'Bilans personnalisés :',
              type: 'TEXT'
            },
            {
              defaultAnswer: {
                resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_LGM'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'formulaire_retractation_lgm',
              label: 'Formulaire de rétractation',
              type: 'UPLOAD'
            }
          ],
          id: 'f2a3fbbc_1530_4867_a2e4_933af18947de',
          label: 'CONDITION_BLOCK_LGM',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'orpi_reduction_honoraires',
              label: "Réduction des honoraires en cas d'Acquéreur trouvé par le Mandant",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'un_mois',
                  label: '1 mois'
                },
                {
                  id: 'deux_mois',
                  label: '2 mois'
                },
                {
                  id: 'trois_mois',
                  label: '3 mois'
                }
              ],
              id: 'orpi_irrevocabilite_defaut_duree',
              label: "Durée de l'irrévocabilité",
              type: 'PICK_LIST'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'orpi_bien_occupe',
              label: 'Le Bien est actuellement occupé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'orpi_bien_occupe_libre',
              label: 'Le Bien devra être vendu libre',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'simple',
                  label: 'Congé simple'
                },
                {
                  id: 'vente',
                  label: 'Congé pour vendre'
                },
                {
                  id: 'serieux',
                  label: 'Congé pour motif légitime et sérieux'
                }
              ],
              id: 'orpi_bien_occupe_conge',
              label: 'Congé du vendeur',
              type: 'SELECT'
            },
            {
              id: 'orpi_bien_occupe_conge_date',
              label: 'Date du congé',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'a_jour',
                  label: 'DPE postérieur au 1er juillet 2021'
                },
                {
                  id: 'en_cours',
                  label: 'DPE réalisé avant le 1er Juillet 2021'
                },
                {
                  id: 'aucun',
                  label: 'Aucun DPE fourni'
                },
                {
                  id: 'non_soumis',
                  label: 'Bien non concerné par le DPE'
                }
              ],
              id: 'orpi_dpe_statut',
              label: 'Concernant le DPE',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'orpi_pieces_alur_agence',
              label: "Pouvoir donné à l'agence pour récupérer les documents de copropriété",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'orpi_ddt_mandant',
              label: 'Les diagnostics sont fournis par le Mandant',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'orpi_assainissement',
              label: "Le Bien est relié à un système d'assainissement collectif",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'recueil',
                  label: 'Recueil des informations nécessaires'
                },
                {
                  id: 'valeur',
                  label: 'Estimation - méthode professionnelle'
                },
                {
                  id: 'explication',
                  label: 'Explication de la méthode'
                },
                {
                  id: 'dossier',
                  label: "Remise d'un dossier complet d'étude de projet"
                },
                {
                  id: 'autre',
                  label: 'Autres actions'
                }
              ],
              id: 'orpi_estimation',
              label: "Concernant l'estimation du bien",
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              id: 'orpi_estimation_autre',
              label: "Autres actions d'estimation",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'document_orpi',
                  label: "Remise d'une documentation ORPI"
                },
                {
                  id: 'remise_liste',
                  label: 'Remise de la liste des documents à fournir'
                },
                {
                  id: 'remise_fiche',
                  label: 'Remise de la fiche des diagnostics'
                },
                {
                  id: 'organisation',
                  label: 'Organisation de la réalisation des diagnostics'
                },
                {
                  id: 'succes',
                  label: 'Mise en place de la « Garantie Succès »'
                },
                {
                  id: 'autre',
                  label: 'Autres actions'
                }
              ],
              id: 'orpi_constitution',
              label: 'Concernant la constitution du dossier de commercialisation',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              id: 'orpi_constitution_autre',
              label: 'Autres actions de constitution du dossier',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'photo',
                  label: 'Réalisation du reportage photo'
                },
                {
                  id: 'visite',
                  label: 'Réalisation d’une visite virtuelle'
                },
                {
                  id: 'plan',
                  label: 'Réalisation d’un plan'
                },
                {
                  id: 'publicite',
                  label: 'Rédaction de l’annonce publicitaire'
                },
                {
                  id: 'presentation',
                  label: 'Présentation du texte publicitaire et photos'
                },
                {
                  id: 'conseils',
                  label: 'Conseils pour mieux vendre'
                },
                {
                  id: 'autre',
                  label: 'Autres actions'
                }
              ],
              id: 'orpi_valeur',
              label: 'Concernant la mise en valeur du bien',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              id: 'orpi_valeur_autre',
              label: 'Autres actions de mise en valeur du bien',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'orpi',
                  label: 'Diffusion sur le site ORPI.com'
                },
                {
                  id: 'agence',
                  label: "Diffusion sur le site de l'agence"
                },
                {
                  id: 'reseaux',
                  label: "Diffusion sur les réseaux sociaux de l'agence"
                },
                {
                  id: 'pack_orpi',
                  label: 'Diffusion sur les sites du Pack Visibilité ORPI'
                },
                {
                  id: 'orpi_around',
                  label: "Diffusion sur l'application Orpi around me"
                },
                {
                  id: 'emailing',
                  label: "Diffusion de l'annonce par emailing"
                },
                {
                  id: 'autre',
                  label: 'Autres actions'
                }
              ],
              id: 'orpi_visibilite',
              label: 'Concernant la visibilité optimale sur internet',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              id: 'orpi_visibilite_autre',
              label: 'Autres actions de visibilité',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'fichier',
                  label: 'Apport au Fichier Commun Orpi'
                },
                {
                  id: 'vitrine',
                  label: "Présentation dans la vitrine de l'agence"
                },
                {
                  id: 'mailing',
                  label: "Diffusion de l'annonce par mailing papier"
                },
                {
                  id: 'panneau',
                  label: "Pose d'un panneau"
                },
                {
                  id: 'autre',
                  label: 'Autres actions'
                }
              ],
              id: 'orpi_communication',
              label: 'Concernant la communication de proximité',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              id: 'orpi_communication_autre',
              label: 'Autres actions de communication',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'visite',
                  label: 'A chaque visite'
                },
                {
                  id: 'semaine',
                  label: 'Toutes les semaines'
                },
                {
                  id: 'mois',
                  label: 'Tous les mois'
                },
                {
                  id: 'autre',
                  label: 'Autres modalités'
                }
              ],
              id: 'orpi_accompagnement_comptes_rendus',
              label: 'Modalités des comptes rendus',
              type: 'SELECT'
            },
            {
              id: 'orpi_accompagnement_comptes_rendus_autre',
              label: 'Autres modalités des comptes-rendus',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'telephone',
                  label: 'Téléphone'
                },
                {
                  id: 'email',
                  label: 'Email'
                }
              ],
              id: 'orpi_accompagnement_telephone_email',
              label: 'Accompagnement par',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'seul_interlocuteur',
                  label: 'Interlocuteur unique'
                },
                {
                  id: 'statistique',
                  label: 'Suivi des statistiques de consultation'
                },
                {
                  id: 'espace_client',
                  label: "Ouverture d'un espace client"
                },
                {
                  id: 'newsletter',
                  label: 'Inscription à la newsletter ORPI'
                }
              ],
              id: 'orpi_accompagnement',
              label: "Concernant l'accompagnement personnalisé",
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              choices: [
                {
                  id: 'telephone',
                  label: 'Contact des acquéreurs par téléphone'
                },
                {
                  id: 'entretien',
                  label: 'Entretien préalable avec les acquéreurs'
                },
                {
                  id: 'verification',
                  label: "Vérification de l'identité des acquéreurs"
                },
                {
                  id: 'information',
                  label: 'Information préalable avant chaque visite'
                },
                {
                  id: 'fiche',
                  label: "Remise aux acquéreurs d'une fiche commerciale du bien"
                },
                {
                  id: 'autre',
                  label: 'Autres actions'
                }
              ],
              id: 'orpi_recherche',
              label: 'Concernant la recherche active des acquéreurs',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              id: 'orpi_recherche_autre',
              label: "Autres actions de recherche d'acquéreurs",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'information',
                  label: 'Information sur les étapes de la vente'
                },
                {
                  id: 'compromis',
                  label: 'Préparation de la promesse'
                },
                {
                  id: 'signature',
                  label: 'Signature du compromis sous 72 h'
                },
                {
                  id: 'sequestre',
                  label: "Encaissement de l'acompte"
                },
                {
                  id: 'concierge',
                  label: 'Service de conciergerie'
                },
                {
                  id: 'assistance',
                  label: "Assistance de l'Acquéreur"
                },
                {
                  id: 'suivi',
                  label: 'Informations sur le suivi des démarches'
                },
                {
                  id: 'suivi_notaire',
                  label: 'Suivi avec le notaire'
                },
                {
                  id: 'autre',
                  label: 'Autres actions'
                }
              ],
              id: 'orpi_avant_notaire',
              label: 'Concernant les actions avant la signature chez le notaire',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              id: 'orpi_avant_notaire_autre',
              label: 'Autres actions avant signature',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'date',
                  label: 'Confirmation du rendez-vous chez le notaire'
                },
                {
                  id: 'presence',
                  label: 'Présence chez le notaire pour la signature '
                },
                {
                  id: 'satisfaction',
                  label: 'Evaluation de la satisfaction'
                },
                {
                  id: 'autre',
                  label: 'Autres actions'
                }
              ],
              id: 'orpi_signature_notaire',
              label: 'Concernant la signature chez le notaire',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              id: 'orpi_signature_notaire_autre',
              label: 'Autres actions signature notaire',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    conditions: [
                      {
                        id: 'mandant_statut',
                        type: 'EQUALS',
                        value: 'personnelles'
                      }
                    ],
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__KELLER_WILLIAMS__IMMOBILIER',
                      'OPERATION__KW_ABONDANCE__IMMOBILIER',
                      'OPERATION__KELLER_WILLIAMS__VIAGER',
                      'OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL'
                    ]
                  },
                  {
                    id: 'mandant_dip_integre_statut',
                    type: 'EQUALS',
                    value: 'non'
                  },
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  }
                ],
                [
                  {
                    id: 'mandant_dip_integre_statut',
                    type: 'EQUALS',
                    value: 'non'
                  },
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  },
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: [
                      'OPERATION__KELLER_WILLIAMS__IMMOBILIER',
                      'OPERATION__KW_ABONDANCE__IMMOBILIER',
                      'OPERATION__KELLER_WILLIAMS__VIAGER',
                      'OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL'
                    ]
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'orpi_dip',
              label: "Document d'information précontractuelle",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['ORPI_IMMOBILIER_VENTE_PRESTATION_SERVICE_AVANT_PREMIERE']
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'orpi_dip_avant_premiere',
              label: "Document d'information précontractuelle",
              type: 'UPLOAD'
            },
            {
              defaultAnswer: {
                resourceId: 'STATIC_FILES_ORPI_AVANT_PREMIERE_CGU'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'orpi_avant_premiere_cgu',
              label: 'Conditions générales d’utilisation du service ORPI AVANT PREMIERE',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'orpi_constitution',
                    value: 'remise_liste',
                    type: 'CONTAINS'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_ORPI_PIECES_DOSSIER'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'orpi_liste_pieces',
              label: 'Liste des pièces constitutives du dossier de vente',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'orpi_constitution',
                    value: 'succes',
                    type: 'CONTAINS'
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'orpi_notice',
              label: 'Notice d\'information "Garantie Succès"',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_retractation',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_ORPI_RETRACTATION'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'orpi_retractation',
              label: 'Formulaire de rétractation',
              type: 'UPLOAD'
            }
          ],
          id: '3a6fefa1_9627_4e67_ba67_27d6e00e7a6a',
          label: 'CONDITION_BLOCK_Orpi',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'giboire_recherche_occupation',
              label: 'Le bien recherché devra être loué',
              type: 'SELECT-BINARY'
            },
            {
              id: 'giboire_recherche_description',
              label: 'Description du bien à rechercher',
              type: 'TEXT'
            },
            {
              id: 'giboire_recherche_secteur',
              label: 'Secteur géographique du bien à rechercher',
              type: 'TEXT'
            },
            {
              id: 'giboire_recherche_prix_bas',
              label: 'Prix fourchette basse',
              type: 'PRICE'
            },
            {
              id: 'giboire_recherche_prix_haut',
              label: 'Prix fourchette haute',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'affichage',
                  label: 'Affichage en vitrine'
                },
                {
                  id: 'panneau',
                  label: "Pose d'un panneau"
                },
                {
                  id: 'diffusion',
                  label: 'Diffusion sur sites spécialisés'
                },
                {
                  id: 'diagnostic',
                  label: 'Remboursement des diags si vente conclue'
                },
                {
                  id: 'diagnostic_total',
                  label: 'Prise en charge totale des diags'
                },
                {
                  id: 'autre',
                  label: 'Autres actions'
                }
              ],
              id: 'giboire_actions_communication',
              label: 'Actions de communication',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              choices: [
                {
                  id: 'dossier',
                  label: 'Dossier de présentation'
                },
                {
                  id: 'diffusion_internet',
                  label: 'Diffusion sur des sites spécialisés'
                },
                {
                  id: 'affichage',
                  label: 'Affichage en vitrine'
                },
                {
                  id: 'panneau',
                  label: "Pose d'un panneau"
                },
                {
                  id: 'diffusion_client',
                  label: 'Diffusion sur fichier client'
                },
                {
                  id: 'cr',
                  label: 'Compte rendu sous 48h'
                },
                {
                  id: 'cr_periodique',
                  label: 'Compte rendu complet - 15j'
                },
                {
                  id: 'pack',
                  label: "Pack 'Giboire Service'"
                },
                {
                  id: 'diagnostic',
                  label: 'Remboursement des diags si vente conclue'
                },
                {
                  id: 'diagnostic_total',
                  label: 'Prise en charge totale des diags'
                }
              ],
              id: 'giboire_actions_communication_mandat_giboire',
              label: 'Actions de communication',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'giboire_actions_communication',
                    value: 'diagnostic',
                    type: 'CONTAINS'
                  }
                ],
                [
                  {
                    id: 'giboire_actions_communication_mandat_giboire',
                    value: 'diagnostic',
                    type: 'CONTAINS'
                  }
                ]
              ],
              id: 'giboire_actions_communication_mandat_diag_offert',
              label: 'Diagnostics offerts seulement si vente du bien par le Mandataire',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'giboire',
                  label: 'Giboire.com'
                },
                {
                  id: 'specialise',
                  label: 'Sites internet spécialisés'
                }
              ],
              id: 'giboire_internet_liste',
              label: 'Site internet',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              choices: [
                {
                  id: 'photo',
                  label: 'Dossier Photo'
                },
                {
                  id: 'texte',
                  label: 'Dossier Texte'
                }
              ],
              id: 'giboire_actions_communication_dossier',
              label: 'Le dossier de présentation comporte',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              id: 'giboire_actions_communication_autres',
              label: "Autres actions à accomplir par l'Agence",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'giboire_execution_anticipee',
              label: 'Exécution anticipée du mandat',
              type: 'SELECT-BINARY'
            },
            {
              defaultAnswer: {
                resourceId: 'STATIC_FILES_GIBOIRE_DIP_RETRACTATION'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'giboire_dip_retractation',
              label: 'Informations précontractuelles',
              type: 'UPLOAD'
            }
          ],
          id: '3a917ea6_2053_44a1_814f_0fa6eff854d4',
          label: 'CONDITION_BLOCK_Gibboire',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      id: 'dossier',
                      label: "Réalisation d'un dossier de présentation"
                    },
                    {
                      id: 'annonce',
                      label: "Affichage de l'annonce en vitrine"
                    },
                    {
                      id: 'panonceau',
                      label: "Pose d'un panonceau sur les biens"
                    },
                    {
                      id: 'diffusion',
                      label: "Diffusion de l'annonce sur sites spécialisés"
                    },
                    {
                      id: 'publication',
                      label: "Diffusion de l'annonce dans certaines publications"
                    },
                    {
                      id: 'distribution',
                      label: "Distribution d'un mailing de présentation"
                    },
                    {
                      id: 'autre',
                      label: 'Autres actions'
                    }
                  ],
                  id: 'taylor_actions_liste',
                  label: 'Actions à réaliser',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'distribution',
                      label: "Distribution d'un mailing à des prospects"
                    },
                    {
                      id: 'autre',
                      label: 'Autres actions'
                    }
                  ],
                  id: 'taylor_actions_recherche_liste',
                  label: 'Actions à réaliser',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'photo',
                      label: "Réalisation d'un dossier photos"
                    },
                    {
                      id: 'video',
                      label: "Réalisation d'un dossier vidéo"
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'taylor_actions_liste',
                        type: 'CONTAINS',
                        value: 'dossier'
                      }
                    ]
                  ],
                  id: 'taylor_actions_dossier',
                  label: 'Dossier comportant',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'taylor',
                      label: 'Diffusion sur John Taylor.com'
                    },
                    {
                      id: 'autre',
                      label: "Diffusion sur d'autres sites"
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'taylor_actions_liste',
                        type: 'CONTAINS',
                        value: 'diffusion'
                      }
                    ]
                  ],
                  id: 'taylor_actions_site',
                  label: 'Sites internet utilisés',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'taylor_actions_liste',
                        type: 'CONTAINS',
                        value: 'diffusion'
                      },
                      {
                        id: 'taylor_actions_site',
                        type: 'CONTAINS',
                        value: 'autre'
                      }
                    ]
                  ],
                  id: 'taylor_actions_site_autre',
                  label: 'Autre sites internet',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'photo',
                      label: 'Photos'
                    },
                    {
                      id: 'video',
                      label: 'Vidéo'
                    },
                    {
                      id: 'geolocalisation',
                      label: 'Géolocalisation'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'taylor_actions_liste',
                        type: 'CONTAINS',
                        value: 'diffusion'
                      }
                    ]
                  ],
                  id: 'taylor_actions_site_publication',
                  label: 'Dossier à réaliser pour publication',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  id: 'taylor_actions_publication_liste',
                  label: "Liste des publications pour diffusion de l'annonce",
                  type: 'TEXTAREA'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'taylor_actions_liste',
                        type: 'CONTAINS',
                        value: 'autre'
                      }
                    ],
                    [
                      {
                        id: 'taylor_actions_recherche_liste',
                        type: 'CONTAINS',
                        value: 'autre'
                      }
                    ]
                  ],
                  id: 'taylor_actions_autres_liste',
                  label: 'Autres actions à réaliser',
                  type: 'TEXT'
                },
                {
                  id: 'taylor_actions_periodicite',
                  label: "Périodicité d'information du client par le mandataire",
                  type: 'TEXT'
                }
              ],
              id: '61becb60_dd83_4ff3_9b30_3d46c7ae4ef8',
              label: 'Actions de communication',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'visite',
                      label: 'Compte rendu à chaque visite'
                    },
                    {
                      id: 'offre',
                      label: 'Transmission des offres dès réception'
                    }
                  ],
                  id: 'taylor_suivi_information',
                  label: "Mode d'information du Mandant",
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'taylor_suivi_recherche_information',
                  label: 'Compte rendu après chaque visite',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'mail',
                      label: 'Email'
                    },
                    {
                      id: 'autre',
                      label: 'Autres moyens'
                    }
                  ],
                  id: 'taylor_suivi_transmission',
                  label: 'Transmission des informations par',
                  multiple: 'true',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'taylor_suivi_transmission',
                        type: 'CONTAINS',
                        value: 'autre'
                      }
                    ]
                  ],
                  id: 'taylor_suivi_transmission_autre',
                  label: 'Autre modalités de transmission',
                  type: 'TEXT'
                }
              ],
              id: '6c164df4_45f2_4dd5_865f_2d9538a33e88',
              label: 'Suivi des actions de communication',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'taylor_execution_anticipee',
                  label: "Demande d'excécution anticipée du mandat",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'taylor_donnees_utilisation',
                  label: 'Accord du Mandant pour utilisation de ses données personnelles',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'taylor_donnees_transmission',
                  label: 'Accord du Mandant pour transmission de ses données personnelles',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '3fa5fa67_f9f6_4260_a174_d084c1a1caa6',
              label: 'Signature du mandat',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_anglais',
                        type: 'DIFFERENT',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_DIP_LIBRE'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'taylor_dip_doc',
                  label: "Document d'informations précontractuelles",
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_anglais',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_DIP_LIBRE_ANGLAIS'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'taylor_dip_anglais_doc',
                  label: "Document d'informations précontractuelles - Anglais",
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_anglais',
                        type: 'DIFFERENT',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_TAYLOR_RETRACTATION'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'taylor_retractation_doc',
                  label: 'Formulaire de rétractation',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'mandat_anglais',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_TAYLOR_RETRACTATION_ANGLAIS'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'taylor_retractation_anglais_doc',
                  label: 'Formulaire de rétractation - Anglais',
                  type: 'UPLOAD'
                }
              ],
              id: 'e90fb056_4d4f_43a3_a84a_4b613563d0b3',
              label: 'Droit de la consommation',
              type: 'CATEGORY'
            }
          ],
          id: '3805a374_5e4c_4a67_b0ea_b53eb6a166a3',
          label: 'CONDITION_BLOCK_John Taylor',
          type: 'CONDITION_BLOCK'
        },
        {
          choices: [
            {
              id: 'carrez',
              label: 'Le mesurage loi Carrez'
            },
            {
              id: 'ddt',
              label: 'Le dossier de diagnostic technique'
            },
            {
              id: 'alur',
              label: 'Les documents de L 721-2 (coproprietés)'
            }
          ],
          id: 'mandant_efficity_liste_document',
          label: 'Le Mandant doit fournir les documents suivants :',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_revocabilite',
          label: 'Le Mandat est révocable à tout moment, sans délai',
          type: 'SELECT-BINARY'
        },
        {
          description: '90 Jours maximum',
          id: 'mandat_revocabilite_jours',
          label: "Nombre de jours d'irrévocabilité",
          max: 90,
          suffix: 'jours',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_semi_vendeur_seul',
          label: 'Le Vendeur peut-il vendre le bien de son côté ?',
          type: 'SELECT-BINARY'
        },
        {
          description: '70% maximum',
          id: 'mandat_semi_vendeur_seul_reduction',
          label: 'Pourcentage de réduction des honoraires, si bien vendu par le vendeur',
          max: 70,
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_semi_agence',
          label: 'Le Vendeur est-il engagé en mandat simple avec une autre agence ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_semi_agence_liste',
          label: 'Liste des agences',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_libre_duree',
          label: 'La clause sur la durée du mandat est-elle complétée avant le rendez-vous ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_offre_base_prix',
          label: 'Faire offre à partir du prix',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_duree',
          label: 'Durée du mandat',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: '1',
              label: '1 Mois'
            },
            {
              id: '2',
              label: '2 Mois'
            },
            {
              id: '3',
              label: '3 Mois'
            }
          ],
          id: 'mandat_duree_cote_particulier',
          label: 'Durée du mandat',
          type: 'SELECT'
        },
        {
          description: '90 Jours maximum',
          id: 'mandat_duree_giboire',
          label: 'Durée du mandat',
          max: 90,
          suffix: 'jours',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: '1_mois',
              label: '1 mois'
            },
            {
              id: '2_mois',
              label: '2 mois'
            },
            {
              id: '3_mois',
              label: '3 mois'
            }
          ],
          id: 'mandat_duree_giboire_exclusif',
          label: 'Durée du mandat',
          type: 'PICK_LIST'
        },
        {
          description: '4 mois minimum',
          id: 'mandat_duree_giboire_simple',
          label: 'Durée du mandat',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: '3_mois',
              label: '3 mois'
            },
            {
              id: '6_mois',
              label: '6 mois'
            },
            {
              id: '9_mois',
              label: '9 mois'
            },
            {
              id: '12_mois',
              label: '12 mois'
            }
          ],
          id: 'mandat_duree_3_6_9_12',
          label: 'Durée du mandat',
          type: 'PICK_LIST'
        },
        {
          id: 'mandat_debut',
          label: 'Date de début du mandat',
          type: 'DATE'
        },
        {
          id: 'mandat_extinction_mls',
          label: "Date d'extinction du mandat",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_reconduction',
          label: 'Reconduction tacite du mandat',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_reconduction',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'mandat_duree_recondution',
          label: 'Durée de la reconduction',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_reconduction',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'mandat_duree_recondution_totale',
          label: 'Durée totale du mandat',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: '3',
              label: '3 Mois'
            },
            {
              id: '6',
              label: '6 Mois'
            },
            {
              id: '9',
              label: '9 Mois'
            }
          ],
          id: 'pp_mandat_duree',
          label: 'Durée du mandat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'irrevocabilite_defaut_trois_mois',
          label: 'Irrévocabilité du mandat fixée à 3 mois. Conserver cette durée ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'irrevocabilite_defaut_duree_libre',
          label: "Durée de l'irrévocabilité (3 mois maximum)",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'un_mois',
              label: '1 mois'
            },
            {
              id: 'deux_mois',
              label: '2 mois'
            },
            {
              id: 'quarante_cinq_jours',
              label: '45 Jours'
            }
          ],
          id: 'irrevocabilite_defaut_duree',
          label: "Durée de l'irrévocabilité",
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: '2',
              label: '2 Mois'
            },
            {
              id: '3',
              label: '3 Mois'
            },
            {
              id: '4',
              label: '4 Mois'
            },
            {
              id: '5',
              label: '5 Mois'
            },
            {
              id: '6',
              label: '6 Mois'
            }
          ],
          id: 'irrevocabilite_duree_6_mois',
          label: "Durée de l'irrévocabilité du mandat",
          suffix: 'mois',
          type: 'PICK_LIST'
        },
        {
          id: 'imoconseil_date_fin_irrevocabilite',
          label: "Date de fin de l'irrévocabilité",
          type: 'DATE'
        },
        {
          id: 'pp_date_rdv',
          label: 'Date du prochain rendez-vous entre mandant et négociateur',
          type: 'DATE'
        },
        {
          id: 'pp_heure_rdv',
          label: 'Heure du prochain rendez-vous entre mandant et négociateur',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'delegation_mandat',
          label: 'Ajouter une clause de délégation',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pp_duo_confiance',
          label: 'Option DUO CONFIANCE',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'spanc_realisation',
          label: 'Diagnostic assainissement non collectif realisé',
          type: 'SELECT-BINARY'
        },
        {
          id: 'spanc_realisation_organisme',
          label: "Nom de l'organisme chargé du contrôle",
          type: 'TEXT'
        },
        {
          id: 'spanc_realisation_date',
          label: 'Date du contrôle',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pp_visites_reelles',
          label: 'Visites réelles autorisées',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_libre_actions',
          label: "La clause sur les actions commerciales de l'Agence est-elle complétée avant le rendez-vous ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'actions_mls_panneau',
              label: "Pose d'un panneau à vendre"
            },
            {
              id: 'actions_mls_tour',
              label: 'Inclure le bien dans le Tour Inter Agence'
            },
            {
              id: 'actions_mls_cle',
              label: 'Remise des clés aux confrères'
            }
          ],
          id: 'mandat_mls_actions_mandataire',
          label: 'Actions autorisées par le mandant :',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          id: 'mandat_efficity_sites',
          label: "Liste des sites internet de diffusion de l'annonce",
          type: 'TEXT'
        },
        {
          id: 'mandat_imoconseil_annonce',
          label: 'Moyen de diffusion des annonces commerciales',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_moyens_diffusion_autre',
          label: "Ajouter d'autres moyens de diffusion",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_moyens_diffusion_autre_liste',
          label: 'Autres moyens de diffusion',
          type: 'TEXT'
        },
        {
          id: 'mandat_imoconseil_visite',
          label: 'Modalités des visites',
          type: 'TEXT'
        },
        {
          id: 'mandat_imoconseil_cr',
          label: 'Modalités des comptes rendus',
          type: 'TEXT'
        },
        {
          id: 'mandat_efficity_prospection',
          label: 'Prospection par',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_appel_offre',
          label: "Vente par Appel d'offre",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_appel_offre_notaire',
          label: "Appel d'offre notaire",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_efficity_actions_autres',
          label: 'Des actions supplémentaires sont accomplies par le Mandataire ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_efficity_actions_autres_liste',
          label: 'Liste des actions supplémentaires :',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_compte_rendu_ajout',
          label: 'Ajouter des modalités de comptes-rendus',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_efficity_frequence',
          label: 'Fréquence des comptes rendus des visites',
          type: 'TEXT'
        },
        {
          id: 'mandat_efficity_frequence_modalite',
          label: 'Modalités des comptes rendus des visites',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'document_folliot_amiante',
              label: 'Diag Amiante'
            },
            {
              id: 'document_folliot_plomb',
              label: 'Diag Plomb'
            },
            {
              id: 'document_folliot_carrez',
              label: 'Loi Carrez'
            },
            {
              id: 'document_folliot_dpe',
              label: 'DPE'
            },
            {
              id: 'document_folliot_electricite',
              label: 'Electricité'
            },
            {
              id: 'document_folliot_assainissement',
              label: 'Assainissement'
            },
            {
              id: 'document_folliot_autre',
              label: 'Autre'
            }
          ],
          id: 'mandat_folliot_document',
          label: 'Liste des documents à fournir par le Mandant :',
          multiple: true,
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_folliot_document',
                value: 'document_folliot_autre',
                type: 'CONTAINS'
              }
            ]
          ],
          id: 'mandat_folliot_document_autre',
          label: 'Autres documents à fournir par le Mandant :',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'mandat_actions_folliot_emplacement',
              label: "Réserver un emplacement privilégié dans la vitrine de l'agence"
            },
            {
              id: 'mandat_actions_folliot_dossier',
              label: 'Réaliser un dossier complet de présentation'
            },
            {
              id: 'mandat_actions_folliot_photo',
              label: 'Réaliser un reportage photographique 360°'
            },
            {
              id: 'mandat_actions_folliot_panneau',
              label: 'Poser un panneau à Vendre'
            },
            {
              id: 'mandat_actions_folliot_courrier',
              label: 'Distribuer des courriers publicitaires'
            },
            {
              id: 'mandat_actions_folliot_diffusion',
              label: "Diffuser l'annonce sur le site www.cabinetfolliot.com"
            },
            {
              id: 'mandat_actions_folliot_rapport',
              label: 'Etablir un rapport mensuel'
            },
            {
              id: 'mandat_actions_folliot_diffusion_agence',
              label: "Diffuser l'annonce dans les autres agences du groupe Folliot"
            },
            {
              id: 'mandat_actions_folliot_delegation',
              label: 'Délégation de la commercialisation'
            }
          ],
          id: 'mandat_actions_commerciales_folliot',
          label: 'Liste des actions commerciales à réaliser par le Mandataire :',
          multiple: true,
          type: 'SELECT'
        },
        {
          id: 'mandat_actions_commerciales_folliot_agence_delegation',
          label: "Nom de l'agence délégataire :",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'personnelles',
              label: 'Personnelles'
            },
            {
              id: 'professionnelles',
              label: 'Professionnelles'
            }
          ],
          id: 'mandant_statut',
          label: 'Le client agit dans le cadre de ses activités',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_signature_hors_etablissement',
          label: 'Mandat signé hors établissement',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_signature_hors_agence',
          label: 'Mandat signé hors agence',
          type: 'SELECT-BINARY'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_ACCEPTATION_TREVI_TRACFIN'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'belgique_tracfin_client',
          label: 'Politique d’acceptation du client - blanchiment de capitaux et du financement du terrorisme',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_TREVI_LISTE_DOCUMENT_MANDAT'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'belgique_trevi_liste_document',
          label: 'Liste des documents à fournir',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_comettant_autorisation_anticipee',
          label: "Le commettant autorise l'agence à débuter sa mission",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_jours_heures_visite',
          label: 'Jours et heures préférentielles pour les visites de l’immeuble',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_panneau_publicitaire',
          label: 'Panneaux publicitaire en façade',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_information_cadastre',
          label: 'Accord pour récupération des informations cadastrales',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_influence_valeur_venale',
          label: 'Éléments susceptibles d’influencer la valeur vénale',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER',
                  'OPERATION__KW_ABONDANCE__IMMOBILIER',
                  'OPERATION__KELLER_WILLIAMS__VIAGER',
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL'
                ]
              }
            ],
            [
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER',
                  'OPERATION__KW_ABONDANCE__IMMOBILIER',
                  'OPERATION__KELLER_WILLIAMS__VIAGER',
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL'
                ]
              }
            ]
          ],
          description: "Si 'non' le DIP doit obligatoirement être généré et signé préalablement au mandat",
          id: 'mandant_dip_integre_statut',
          label: "Document d'Informations Précontractuelles intégré au début du mandat",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'dip_signature',
          label: 'Signature électronique du DIP',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_commercial_honoraires_frais_statut',
          label: 'Des honoraires pour frais annexes seront-ils dus ?',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'mandat_commercial_honoraires_frais_liste',
              label: 'Liste des frais annexes',
              type: 'TEXT'
            },
            {
              id: 'mandat_commercial_honoraires_frais_montant',
              label: 'Montant des frais',
              type: 'PRICE'
            },
            {
              id: 'mandat_commercial_modalite',
              label: 'Modalités spécifiques de rémunération',
              type: 'TEXT'
            }
          ],
          id: 'ca669fa7_90c5_42a6_9adb_cd5303d8ebf1',
          label: 'Frais annexes - mandant professionnel',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandant_execution_libre',
          label: "Le mandant demande l'exécution immédiate du mandat",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              conditions: [
                [
                  {
                    id: 'mandant_retractation',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_SEXTANT_RETRACTATION'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'retractation_sextant',
              label: 'Formulaire de rétractation',
              type: 'UPLOAD'
            },
            {
              defaultAnswer: {
                resourceId: 'STATIC_FILES_SEXTANT_DIP'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'dip_sextant',
              label: "Document d'information précontractuel",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  }
                ]
              ],
              id: 'mandant_retractation',
              label: 'Droit de rétractation applicable',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  }
                ]
              ],
              id: 'mandant_execution',
              label: 'Exécution immédiate du mandat demandée',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_statut',
                    type: 'DIFFERENT',
                    value: 'professionnelles'
                  },
                  {
                    id: 'mandat_type',
                    type: 'EMPTY'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_VENTE_ANCIEN_MANDAT_SIMPLIFIE']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_DIP_LIBRE'
              },
              id: 'dip_mandat_simplifie',
              label: "Document d'informations précontractuelles",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'PVCI_SENS_IMMOBILIER_VENTE_SEULE_MANDAT_VENTE',
                      'PVCI_SENS_IMMOBILIER_VENTE_SEULE_MANDAT_ANGLAIS'
                    ]
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_DIP_LIBRE'
              },
              id: 'dip_mandat_simplifie_pvci',
              label: "Document d'informations précontractuelles",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_statut',
                    type: 'DIFFERENT',
                    value: 'professionnelles'
                  },
                  {
                    id: 'mandat_type',
                    type: 'EQUALS',
                    value: 'exclusif'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_VENTE_ANCIEN_MANDAT_SIMPLIFIE']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_DIP_EXCLUSIF'
              },
              id: 'dip_exclusif_simplifie',
              label: "Document d'informations précontractuelles - Mandat Exclusif",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_statut',
                    type: 'DIFFERENT',
                    value: 'professionnelles'
                  },
                  {
                    id: 'mandat_type',
                    type: 'EQUALS',
                    value: 'semi'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_VENTE_ANCIEN_MANDAT_SIMPLIFIE']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_DIP_SEMI'
              },
              id: 'dip_semi_simplifie',
              label: "Document d'informations précontractuelles - Mandat Semi-Exclusif",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_statut',
                    type: 'DIFFERENT',
                    value: 'professionnelles'
                  },
                  {
                    id: 'mandat_type',
                    type: 'EQUALS',
                    value: 'simple'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_VENTE_ANCIEN_MANDAT_SIMPLIFIE']
                  }
                ],
                [
                  {
                    id: 'mandant_statut',
                    type: 'DIFFERENT',
                    value: 'professionnelles'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['ISM_IMMOBILIER_MANDAT_ISM']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_DIP_SIMPLE'
              },
              id: 'dip_simple_simplifie',
              label: "Document d'informations précontractuelles - Mandat Simple",
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_statut',
                    type: 'DIFFERENT',
                    value: 'professionnelles'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_VENTE_ANCIEN_MANDAT_SIMPLIFIE']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
              },
              id: 'mandat_retractation_document_simplifie',
              label: 'Formulaire de rétractation',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'PVCI_SENS_IMMOBILIER_VENTE_SEULE_MANDAT_VENTE',
                      'PVCI_SENS_IMMOBILIER_VENTE_SEULE_MANDAT_ANGLAIS'
                    ]
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
              },
              id: 'mandat_retractation_document_pvci',
              label: 'Formulaire de rétractation',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_retractation',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'mandat_retractation_document',
              label: 'Formulaire de rétractation',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_retractation',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT_BVI'
              },
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'mandat_retractation_document_village',
              label: 'Formulaire de rétractation',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  conditions: [
                    [
                      {
                        id: 'mandant_statut',
                        type: 'EQUALS',
                        value: 'personnelles'
                      },
                      {
                        id: 'mandat_signature_hors_etablissement',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'mandat_retractation_era',
                  label: 'Formulaire de rétractation',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['ERA_IMMOBILIER_VENTE_MANDAT_UNIQUE', 'ERA_IMMOBILIER_VENTE_MANDAT_SERENITE']
                      }
                    ]
                  ],
                  defaultAnswer: {
                    resourceId: 'STATIC_FILES_DIP_EXCLUSIF_ERA'
                  },
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'mandat_dip_era',
                  label: "Document d'informations précontractuelles",
                  type: 'UPLOAD'
                }
              ],
              id: '4be27087_f221_4ac4_994e_bab2b4da0c5c',
              label: 'CONDITION_BLOCK_ERA',
              type: 'CONDITION_BLOCK'
            },
            {
              conditions: [
                [
                  {
                    id: 'mandant_retractation',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_VENTE_ANCIEN_MANDAT_ANGLAIS']
                  }
                ],
                [
                  {
                    id: 'mandant_retractation',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_KW_ANGLAIS']
                  }
                ]
              ],
              defaultAnswer: {
                resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT_ANGLAIS'
              },
              id: 'mandat_retractation_document_anglais',
              label: 'Formulaire de rétractation - Anglais',
              type: 'UPLOAD'
            }
          ],
          id: '3aa151e0_8f91_4ba3_a396_4e98ec20ee24',
          label: 'CONDITION_BLOCK_Règlementation applicable du droit de la consommation',
          type: 'CONDITION_BLOCK'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_pouvoir',
          label: "Procuration donnée à l'Agence pour signature du compromis",
          type: 'SELECT-BINARY'
        },
        {
          description: 'Au moins une fois par trimestre par mail et/ou appel téléphonique',
          id: 'compte_rendu_sextant',
          label: 'Modalités de compte-rendu',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'accord_prospection_email',
          label: 'Accord du Mandant pour recevoir de la prospection sur son mail',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'mandat_actions_dossier',
              label: 'Dossier complet de présentation du bien'
            },
            {
              id: 'mandat_actions_photo',
              label: 'Dossier photo'
            },
            {
              id: 'mandat_actions_video',
              label: 'Dossier vidéo'
            },
            {
              id: 'mandat_actions_vitrine',
              label: 'Annonce en vitrine'
            },
            {
              id: 'mandat_actions_panonceau',
              label: 'Panneau sur le bien'
            },
            {
              id: 'mandat_actions_site_agence',
              label: "Annonce sur le site internet de l'agence"
            },
            {
              id: 'mandat_actions_site_reseau',
              label: 'Annonce sur le site internet du réseau'
            },
            {
              id: 'mandat_actions_site_speciaux',
              label: 'Annonce sur des sites web spécialisés'
            },
            {
              id: 'mandat_actions_autre',
              label: 'Autre action commerciale'
            }
          ],
          id: 'mandat_actions_commerciales',
          label: 'Actions commerciales du Mandataire',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'mandat_actions_dossier',
              label: 'Dossier complet de présentation du bien'
            },
            {
              id: 'mandat_actions_photo',
              label: 'Dossier photo'
            },
            {
              id: 'mandat_actions_video',
              label: 'Dossier vidéo'
            },
            {
              id: 'mandat_actions_panonceau',
              label: 'Panneau sur le bien'
            },
            {
              id: 'mandat_actions_site_agence',
              label: "Annonce sur le site internet de l'agence"
            },
            {
              id: 'mandat_actions_site_speciaux',
              label: 'Annonce sur des sites web spécialisés'
            },
            {
              id: 'mandat_actions_autre',
              label: 'Autre action commerciale'
            }
          ],
          id: 'mandat_actions_commerciales_mcm',
          label: 'Actions commerciales du Mandataire',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          id: 'reseaux_sociaux',
          label: 'Liste des réseaux sociaux',
          type: 'TEXT'
        },
        {
          id: 'mandat_duree_annonce_vitrine',
          label: "Nombre de jours d'affichage de l'annonce en vitrine",
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_actions_commerciales',
                value: 'mandat_actions_autre',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'mandat_actions_commerciales_mcm',
                value: 'mandat_actions_autre',
                type: 'CONTAINS'
              }
            ]
          ],
          id: 'mandat_actions_commerciales_autre',
          label: 'Précisez les actions',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'systematique',
              label: 'après chaque visite'
            },
            {
              id: 'hebdomadaire',
              label: 'chaque semaine'
            },
            {
              id: 'frequence_autre',
              label: 'autre'
            }
          ],
          id: 'mandat_frequence',
          label: 'Fréquence de compte rendu des visites',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_frequence',
                type: 'EQUALS',
                value: 'frequence_autre'
              }
            ]
          ],
          id: 'mandat_frequence_autre_detail',
          label: 'Précisez les modalités de comptes rendus',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_recommande_electronique',
          label: 'Accord pour recommandé électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'kw_viager_fionds',
              label: 'Comités d’investissement fonds institutionnels'
            },
            {
              id: 'kw_viager_virage',
              label: 'Diffusion via Virage Viager'
            },
            {
              id: 'kw_viager_diffusion_autre',
              label: 'Autres précisions '
            }
          ],
          id: 'kw_mandat_viager_mutualise_diffusion',
          label: 'Moyens de diffusion commerciale',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          conditions: [
            [
              {
                id: 'kw_mandat_viager_mutualise_diffusion',
                value: 'kw_viager_diffusion_autre',
                type: 'CONTAINS'
              }
            ]
          ],
          id: 'kw_mandat_viager_mutualise_diffusion_autre',
          label: 'Précisions',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_clauses_particulieres',
          label: 'Le mandat comporte-t-il des clauses particulières ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_clauses_particulieres',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mandat_clauses_particulieres_liste',
          label: 'Clauses particulières',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_lieu_agence',
          label: 'Mandat signé en agence',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_mizapri_resiliation',
          label: 'Permettre la résiliation à tout moment',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_lieu_agence',
                value: 'oui',
                type: 'DIFFERENT'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_RETRACTATION_COTE_PARTICULIERS'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'formulaire_retractation_cote_particulier',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pp_mandant_execution',
          label: "Le mandant demande-t-il l'exécution immédiate du mandat ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_agence_directe_signature_date',
          label: 'Date de signature du mandat',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_presence_copropriete',
          label: 'Le bien est en copropriété',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'document_ad_carrez',
              label: 'Surface Carrez'
            },
            {
              id: 'document_ad_amiante',
              label: 'Amiante'
            },
            {
              id: 'document_ad_plomb',
              label: 'Saturnisme'
            },
            {
              id: 'document_ad_termite',
              label: 'Etat parasitaire'
            },
            {
              id: 'document_ad_assainissement',
              label: 'Assainissement'
            },
            {
              id: 'document_ad_dpe',
              label: 'DPE'
            },
            {
              id: 'document_ad_gaz',
              label: 'Gaz'
            },
            {
              id: 'document_ad_electricite',
              label: 'Electricité'
            },
            {
              id: 'document_ad_erp',
              label: 'Etat des risques'
            },
            {
              id: 'document_ad_carnet',
              label: "Carnet d'entretien"
            },
            {
              id: 'document_ad_alur',
              label: 'Documents de la copropriété'
            },
            {
              id: 'document_ad_rien',
              label: 'Aucun document'
            }
          ],
          id: 'mandat_agence_direct_document',
          label: 'Liste des documents à fournir par le Mandant',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'electricite',
              label: 'Diag Electrique'
            },
            {
              id: 'erp',
              label: 'Etat des risques'
            },
            {
              id: 'gaz',
              label: 'Diag Gaz'
            },
            {
              id: 'amiante',
              label: 'Diag Amiante'
            },
            {
              id: 'plomb',
              label: 'Diag Plomb'
            },
            {
              id: 'termites',
              label: 'Diag Termites'
            },
            {
              id: 'assainissement',
              label: 'Contrôle Assainissement'
            },
            {
              id: 'dpe',
              label: 'DPE'
            },
            {
              id: 'bornage',
              label: 'Bornage'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          id: 'mandat_document_iparticulier',
          label: 'Liste des documents à fournir par le Mandant',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'merules',
              label: 'Mérules'
            },
            {
              id: 'ddt',
              label: 'DDT'
            },
            {
              id: 'geotechnique',
              label: 'Géotechnique'
            }
          ],
          id: 'mandat_era_document',
          label: 'Liste des documents à fournir par le Mandant',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_era_document_copropriete',
          label: 'Le Mandant a fourni le certificat Carrez',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_document_autre',
          label: 'Autres documents :',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_agence_direct_document_charge',
          label: "Les frais de diagnostics sont-ils remboursés par l'Agence en cas de réalisation de la vente ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            'Le calcul automatique prévoit un montant de 3.9% du prix de vente; 5.900 € sous 100.000€ ; 6.825 € sous 175.000 €',
          id: 'mandat_agence_directe_honoraires_libres',
          label: "Le montant des honoraires de l'Agence sont-ils renseignés manuellement ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_agence_directe_honoraires_montant',
          label: 'Montant des honoraires',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'delegation_ad_ensemble',
              label: 'Ensemble des pouvoirs'
            },
            {
              id: 'delegation_ad_visite',
              label: 'Visiter et faire visiter les biens'
            },
            {
              id: 'delegation_ad_publicite',
              label: 'Faire toute publicité'
            }
          ],
          id: 'mandat_agence_direct_delegation',
          label: "Etendue de l'autorisation de délégation",
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_suisse_semi_exclu',
          label: 'Ajouter une clause en cas de vente par un autre intermédiaire ou le mandant',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_suisse_non_echeance',
          label: 'Ajouter une clause en cas de non vente à échéance du contrat',
          type: 'SELECT-BINARY'
        },
        {
          id: 'mandat_support_publicite',
          label: 'Supports de publicité utilisés',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_delegation_amepi',
          label: 'Accord pour délégation AMEPI',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: 'Groupements régionaux ; FIPA ...',
          id: 'mandat_delegation_autre_amepi',
          label: 'Accord pour délégation autre que AMEPI',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_delegation_autre_amepi',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'mandat_delegation_autre_document',
          label: 'Autorisation délégation',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_delegation_amepi',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_AUTORISATION_AMEPI'
          },
          id: 'mandat_delegation_amepi_document',
          label: 'Autorisation délégation AMEPI',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandant_support_accord_panneau',
          label: 'Accord du Mandant pour poser un panneau Exclusivité',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'notification_electronique',
          label: 'Accord du Mandant pour notification électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'accord_clause_hitbo',
          label: 'Accord du Mandant pour utilisation de ses données personnelles',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'clause_particuliere',
          label: 'Ajouter une clause particulière supplémentaire',
          type: 'SELECT-BINARY'
        },
        {
          id: 'clause_particuliere_libre',
          label: 'Clause particulière',
          type: 'TEXT'
        },
        {
          children: [
            {
              id: 'clause_particuliere_liste_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'clause_particuliere_liste_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          id: 'clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'clause_particuliere_liste_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 0
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_mandat_clauses_particulieres',
          label: "L'avenant au mandat comporte-t-il des clauses particulières ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'avenant_mandat_clauses_particulieres_liste',
          label: 'Clauses particulières',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_avenant_signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'mandat_vente',
              label: 'Vente'
            },
            {
              id: 'mandat_recherche',
              label: 'Recherche'
            }
          ],
          id: 'mandat_deux_statut',
          label: 'Le second mandat est un mandat de',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'exclusif',
              label: 'Exclusif'
            },
            {
              id: 'semi',
              label: 'Semi-exclusif'
            },
            {
              id: 'simple',
              label: 'Simple'
            }
          ],
          id: 'mandat_deux_type',
          label: 'Type du second mandat',
          type: 'SELECT'
        },
        {
          id: 'mandat_deux_numero',
          label: 'Numéro du second mandat',
          type: 'TEXT'
        },
        {
          id: 'mandat_recherche_numero_recherche',
          label: 'Numéro du mandat de recherche',
          type: 'TEXT'
        },
        {
          id: 'mandat_deux_date',
          label: 'Date de signature du second mandat',
          type: 'DATE'
        },
        {
          id: 'mandat_deux_repartition',
          label: 'Montant des honoraires du second professionnel',
          type: 'PRICE'
        },
        {
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'mandat_deux_repartition_cfp',
          label: 'Montant des honoraires du second professionnel',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_deux_statut',
                value: 'mandat_recherche',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandat_deux_statut',
                value: 'mandat_vente',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mandat_deux',
          label: 'Mandat du deuxième professionnel',
          type: 'UPLOAD'
        },
        {
          id: 'mandat_date',
          label: 'Date de signature du mandat initial',
          type: 'DATE'
        },
        {
          id: 'mandat_recherche_date_pvci',
          label: 'Date de signature du mandat de recherche',
          type: 'DATE'
        },
        {
          id: 'mandat',
          label: 'Mandat',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_retractation',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_signature_electronique',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandant_execution_mention',
          label: 'Mention manuscrite - exécution anticipée',
          templateId: 'mandantExecutionMention.pdf',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_signature_electronique',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_AGENCE_DIRECTE_DIP_PAPIER'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'agence_directe_dip_papier',
          label: 'Informations précontractuelles du consommateur',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_signature_electronique',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_AGENCE_DIRECTE_DIP_PAPIER_EXCLUSIF'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'agence_directe_dip_papier_exclusif',
          label: 'Informations précontractuelles du consommateur',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_signature_electronique',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_AGENCE_DIRECTE_RETRACTATION_PAPIER'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'agence_directe_retractation_papier',
          label: 'Formulaire rétractation',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_signature_electronique',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_AGENCE_DIRECTE_DIP_ELECTRONIQUE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'agence_directe_dip_electronique',
          label: 'Informations précontractuelles du consommateur',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_signature_electronique',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_AGENCE_DIRECTE_DIP_ELECTRONIQUE_EXCLUSIF'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'agence_directe_dip_electronique_exclusif',
          label: 'Informations précontractuelles du consommateur',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_signature_electronique',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_AGENCE_DIRECTE_RETRACTATION_ELECTRONIQUE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'agence_directe_retractation_electronique',
          label: 'Formulaire rétractation',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_pierre_revente',
          label: 'Un des signataires est-il étranger ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_pierre_revente',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_PIERRE_REVENTE_MANDAT_EXCLUSIF_ANGLAIS'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_pierre_revente_mandat_exclusif',
          label: 'Exclusive Agency Agreement',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_pierre_revente',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_PIERRE_REVENTE_MANDAT_NON_EXCLUSIF_ANGLAIS'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_pierre_revente_mandat_non_exclusif',
          label: 'Non Exclusive Agency Agreement',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_pierre_revente',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_PIERRE_REVENTE_AVENANT_ANGLAIS'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_pierre_revente_avenant',
          label: 'Amendment to the sales mandate',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'pierre_mandat_retractation_document',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_lotissement_annexe_informatique',
          label: 'La liste du programme est-elle complétée informatiquement ?',
          type: 'SELECT-BINARY'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_lotissement_doc_pa',
          label: "Permis d'aménager",
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_lotissement_plan',
          label: 'Plans de division et de bornage',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_lotissement_autre',
          label: 'Autres pièces du lotissement',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_lotissement_annexe_informatique',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'doc_mandat_etat_descriptif',
          label: 'Etat descriptif des lots',
          templateId: 'mandatEtatDescriptif.pdf',
          type: 'UPLOAD'
        },
        {
          id: 'mandat_lotissement_section',
          label: 'Numéro de section du cadastre',
          type: 'TEXT'
        },
        {
          children: [
            {
              id: 'mandat_lotissement_programme_liste_lotissement_lot',
              label: 'Numéro de lot',
              type: 'TEXT'
            },
            {
              id: 'mandat_lotissement_programme_liste_lotissement_surface',
              label: 'Surface',
              type: 'TEXT'
            },
            {
              id: 'mandat_lotissement_programme_liste_lotissement_cadastre',
              label: 'Numéro de cadastre',
              type: 'TEXT'
            },
            {
              id: 'mandat_lotissement_programme_liste_lotissement_sp',
              label: 'Surface de plancher',
              type: 'TEXT'
            },
            {
              id: 'mandat_lotissement_programme_liste_lotissement_dimension',
              label: 'Dimension du lot (environ)',
              type: 'TEXT'
            },
            {
              id: 'mandat_lotissement_programme_liste_lotissement_prix',
              label: 'Prix de vente',
              type: 'PRICE'
            },
            {
              id: 'mandat_lotissement_programme_liste_lotissement_etat',
              label: 'Etat (vendu ou non vendu)',
              type: 'TEXT'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_lotissement_annexe_informatique',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'mandat_lotissement_programme_liste',
          label: 'Ajouter un lot',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: ' Lot n°'
              },
              {
                type: 'VARIABLE',
                value: 'mandat_lotissement_programme_liste_lotissement_lot'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_iparticulier_rgpd',
          label: "Le Mandant autorise l'utilisation de ses données personnelles",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_iparticulier_anticipe',
          label: "Le Mandant autorise l'exécution anticipée du mandat",
          type: 'SELECT-BINARY'
        }
      ],
      id: 'bc8e3f8b_77e0_496f_9a8e_6b6d649ee489',
      label: 'Conditions du Mandat',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'kw_mandat_date_fin',
          label: 'Date de fin du mandat',
          type: 'DATE'
        }
      ],
      id: 'ba4e9f24_107d_4af5_ab9f_a329a0a6fa36',
      label: 'Durée du Mandat',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'kw_abondance_acceptation_ferme',
          label: "Le Mandant s'engage à accepter une offre d'achat sans condition suspensive de financenement",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'kw_abondance_acceptation_raison',
          label: 'Le Mandant autorise à communiquer la raison de la vente de son bien',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'kw_abondance_acceptation_1161',
          label: 'Le Mandant renonce à se prévaloir du bénéfice des articles 1161 et 1596',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'kw_abondance_donnees_personnelles',
          label: "Le Mandant accepte l'exploitation des données personnelles",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'option_liberte',
              label: 'Clause Liberté'
            },
            {
              id: 'option_win',
              label: 'Clause Win / Win'
            },
            {
              id: 'option_open',
              label: 'Clause Open'
            },
            {
              id: 'option_reporting',
              label: 'Clause Reporting'
            },
            {
              id: 'option_visibilite',
              label: 'Clause Visibilité'
            },
            {
              id: 'option_qualite',
              label: 'Clause Qualité'
            },
            {
              id: 'option_confiance',
              label: 'Clause Confiance'
            },
            {
              id: 'option_fidelite',
              label: 'Clause Fidélité'
            },
            {
              id: 'option_garanties',
              label: 'Clause Garanties'
            },
            {
              id: 'option_vip',
              label: 'Clause VIP'
            },
            {
              id: 'option_cares',
              label: 'Clause Cares'
            }
          ],
          id: 'kw_option_mandant',
          label: 'Quelles options sont choisies par le Mandant',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'option_financiere',
              label: 'Clause Financière'
            },
            {
              id: 'option_liberte',
              label: 'Clause Liberté'
            },
            {
              id: 'option_win',
              label: 'Clause Win / Win'
            },
            {
              id: 'option_open',
              label: 'Clause Open'
            },
            {
              id: 'option_reporting',
              label: 'Clause Reporting'
            },
            {
              id: 'option_visibilite',
              label: 'Clause Visibilité'
            },
            {
              id: 'option_qualite',
              label: 'Clause Qualité'
            },
            {
              id: 'option_confiance',
              label: 'Clause Confiance'
            },
            {
              id: 'option_fidelite',
              label: 'Clause Fidélité'
            },
            {
              id: 'option_garanties',
              label: 'Clause Garanties'
            },
            {
              id: 'option_vip',
              label: 'Clause VIP'
            },
            {
              id: 'option_cares',
              label: 'Clause Cares'
            }
          ],
          id: 'kw_option_mandant_abondance',
          label: 'Quelles options sont choisies par le Mandant',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'option_liberte',
              label: 'Clause Liberté'
            },
            {
              id: 'option_win',
              label: 'Clause Win / Win'
            },
            {
              id: 'option_open',
              label: 'Clause Open'
            },
            {
              id: 'option_reporting',
              label: 'Clause Reporting'
            },
            {
              id: 'option_visibilite',
              label: 'Clause Visibilité'
            },
            {
              id: 'option_qualite',
              label: 'Clause Qualité'
            },
            {
              id: 'option_fidelite',
              label: 'Clause Fidélité'
            },
            {
              id: 'option_cares',
              label: 'Clause Cares'
            }
          ],
          id: 'kw_commercial_option_mandant',
          label: 'Quelles options sont choisies par le Mandant',
          multiple: true,
          type: 'PICK_LIST'
        }
      ],
      id: '376efd4d_4d54_4a98_ab33_4c53c6404cf1',
      label: 'Informations particulières sur le Mandat',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'kw_exclusion_delegation',
          label: 'Le Mandant souhaite-t-il exclure certaines agences de la délégation ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_open',
                type: 'CONTAINS'
              },
              {
                id: 'kw_exclusion_delegation',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_open',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_open',
                type: 'CONTAINS'
              },
              {
                id: 'kw_exclusion_delegation',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_open',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_open',
                type: 'CONTAINS'
              },
              {
                id: 'kw_exclusion_delegation',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'kw_exclusion_delegation',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_open',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_open',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_exclusion_delegation',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_open',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_exclusion_delegation',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_open',
                type: 'CONTAINS'
              }
            ]
          ],
          id: 'kw_exclusion_agence_liste',
          label: 'Liste des agences exclues de la délégation',
          type: 'TEXT'
        }
      ],
      conditions: [
        [
          {
            id: 'kw_commercial_option_mandant',
            value: 'option_open',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant',
            value: 'option_open',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant_abondance',
            value: 'option_open',
            type: 'CONTAINS'
          }
        ]
      ],
      id: '869c2842_87dc_404e_9d19_0a095619c63c',
      label: 'Clause Open',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'kw_rdv_commercialisation_date',
          label: 'Date du premier rendez-vous de commercialisation',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'periodicite_hebdomadaire',
              label: 'Hebdomadaire'
            },
            {
              id: 'periodicite_mensuelle',
              label: 'Mensuelle'
            },
            {
              id: 'periodicite_bimensuelle',
              label: 'Bimensuelle'
            },
            {
              id: 'periodicite_trimestrielle',
              label: 'Trimestrielle'
            },
            {
              id: 'periodicite_quotidien',
              label: 'Après chaque visite'
            }
          ],
          id: 'kw_commercialisation_periode',
          label: 'Périodicité des comptes rendus',
          type: 'SELECT'
        }
      ],
      conditions: [
        [
          {
            id: 'kw_commercial_option_mandant',
            value: 'option_reporting',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant',
            value: 'option_reporting',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant_abondance',
            value: 'option_reporting',
            type: 'CONTAINS'
          }
        ]
      ],
      id: 'dcbbb679_945c_4ca6_9de2_11679582ca52',
      label: 'Clause Reporting',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'diffusion_panneau',
              label: "Pose d'un panneau de commercialisation"
            },
            {
              id: 'diffusion_site_interne',
              label: 'Diffusion sur les sites du MC et du Groupe'
            },
            {
              id: 'diffusion_internet',
              label: 'Diffusion sur les principaux sites internet'
            },
            {
              id: 'diffusion_reseau',
              label: 'Diffusion sur les réseaux sociaux'
            },
            {
              id: 'diffusion_sms',
              label: 'Diffusion par le biais de mail et sms'
            },
            {
              id: 'diffusion_internationale',
              label: "Diffusion à l'internationale"
            },
            {
              id: 'diffusion_autre',
              label: 'Autres moyens'
            }
          ],
          id: 'kw_diffusion',
          label: 'Moyens employés par le Mandataire pour diffuser le bien',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          conditions: [
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_diffusion',
                value: 'diffusion_internet',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_diffusion',
                value: 'diffusion_internet',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_diffusion',
                value: 'diffusion_internet',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_diffusion',
                value: 'diffusion_internet',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_diffusion',
                value: 'diffusion_internet',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_diffusion',
                value: 'diffusion_internet',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ]
          ],
          id: 'kw_portail',
          label: 'Liste des portails internet',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_diffusion',
                value: 'diffusion_reseau',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_diffusion',
                value: 'diffusion_reseau',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_diffusion',
                value: 'diffusion_reseau',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_diffusion',
                value: 'diffusion_reseau',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_diffusion',
                value: 'diffusion_reseau',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_diffusion',
                value: 'diffusion_reseau',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ]
          ],
          id: 'kw_reseaux',
          label: 'Liste des réseaux sociaux',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_diffusion',
                value: 'diffusion_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_diffusion',
                value: 'diffusion_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_diffusion',
                value: 'diffusion_autre',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_diffusion',
                value: 'diffusion_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_diffusion',
                value: 'diffusion_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_diffusion',
                value: 'diffusion_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_visibilite',
                type: 'CONTAINS'
              }
            ]
          ],
          id: 'kw_diffusion_autre',
          label: 'Autres moyens',
          type: 'TEXT'
        }
      ],
      conditions: [
        [
          {
            id: 'kw_commercial_option_mandant',
            value: 'option_visibilite',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant',
            value: 'option_visibilite',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant_abondance',
            value: 'option_visibilite',
            type: 'CONTAINS'
          }
        ]
      ],
      id: '81ba54cd_8ef2_4c20_9070_d027d2f4814a',
      label: 'Clause Visibilité',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'actions_photo',
              label: 'Un ensemble de photographies'
            },
            {
              id: 'actions_video',
              label: 'Une vidéo de présentation'
            },
            {
              id: 'actions_visite',
              label: 'Une visite virtuelle'
            },
            {
              id: 'actions_autre',
              label: 'Autre'
            }
          ],
          id: 'kw_actions',
          label: 'Actions à réaliser par le Mandataire',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          conditions: [
            [
              {
                id: 'kw_actions',
                value: 'actions_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_qualite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_qualite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_actions',
                value: 'actions_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_qualite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_qualite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_actions',
                value: 'actions_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_commercial_option_mandant',
                value: 'option_qualite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_actions',
                value: 'actions_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_qualite',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_qualite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_actions',
                value: 'actions_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant',
                value: 'option_qualite',
                type: 'CONTAINS'
              }
            ],
            [
              {
                id: 'kw_actions',
                value: 'actions_autre',
                type: 'CONTAINS'
              },
              {
                id: 'kw_option_mandant_abondance',
                value: 'option_qualite',
                type: 'CONTAINS'
              }
            ]
          ],
          id: 'kw_actions_autre',
          label: 'Précisez les autres actions',
          type: 'TEXT'
        }
      ],
      conditions: [
        [
          {
            id: 'kw_commercial_option_mandant',
            value: 'option_qualite',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant',
            value: 'option_qualite',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant_abondance',
            value: 'option_qualite',
            type: 'CONTAINS'
          }
        ]
      ],
      id: '8b2a2ee5_76eb_4c08_b3ef_ce6529baed6c',
      label: 'Clause Qualité',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'kw_estimation_montant',
          label: "Montant de l'estimation",
          type: 'PRICE'
        },
        {
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'kw_estimation_montant_cfp',
          label: "Montant de l'estimation",
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'kw_estimation_date',
          label: "Date de l'estimation",
          type: 'DATE'
        }
      ],
      conditions: [
        [
          {
            id: 'kw_commercial_option_mandant',
            value: '',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant',
            value: 'option_confiance',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant_abondance',
            value: 'option_confiance',
            type: 'CONTAINS'
          }
        ]
      ],
      id: '6687932a_c551_4ad9_9d4d_c5e53ce14cef',
      label: 'Clause Confiance',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'kw_donation_montant',
          label: 'Montant de la donation',
          type: 'PRICE'
        },
        {
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'kw_donation_montant_cfp',
          label: 'Montant de la donation',
          suffix: 'CFP',
          type: 'PRICE'
        }
      ],
      conditions: [
        [
          {
            id: 'kw_commercial_option_mandant',
            value: 'option_cares',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant',
            value: 'option_cares',
            type: 'CONTAINS'
          }
        ],
        [
          {
            id: 'kw_option_mandant_abondance',
            value: 'option_cares',
            type: 'CONTAINS'
          }
        ]
      ],
      id: '2373cfc8_db9a_41c5_9f66_c5aff908413f',
      label: 'Clause Cares',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'kw_clause_particuliere',
          label: 'Le Mandat Success comporte-t-il des clauses particulières ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [[]],
          id: 'kw_clause_particuliere_liste',
          label: 'Clauses particulières',
          type: 'TEXTAREA'
        }
      ],
      id: '54c25953_7e10_4dcc_8cde_006a1ae4f963',
      label: 'Clauses particulières',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'kw_sequestre_pourcentage',
          label: "Pourcentage du séquestre à verser par l'Acquéreur",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'kw_sequestre_notaire_statut',
          label: 'Le séquestre est-il versé au notaire en charge du dossier ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'kw_sequestre_notaire',
          label: 'Nom du notaire en charge du dossier',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'kw_sequestre_commercial_notaire_statut',
          label: 'Le séquestre est-il versé à un notaire ou un avocat en charge du dossier ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'kw_sequestre_commercial_versement_notaire',
              label: 'A un notaire'
            },
            {
              id: 'kw_sequestre_commercial_versement_avocat',
              label: 'A un avocat'
            }
          ],
          id: 'kw_sequestre_commercial_notaire_avocat',
          label: 'Le séquestre est versé',
          type: 'SELECT'
        },
        {
          id: 'kw_sequestre_commercial_nom',
          label: "Nom du notaire ou de l'avocat en charge du dossier",
          type: 'TEXT'
        }
      ],
      id: '51663a81_1a45_4fe4_808a_e1e6c37fdd64',
      label: 'Séquestre Conventionnel',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'delegation_statut',
          label: 'Délégation de mandat',
          type: 'SELECT-BINARY'
        },
        {
          id: 'delegation_agence_denomination',
          label: "Désignation de l'agence en délégation",
          type: 'TEXT'
        },
        {
          id: 'delegation_agence_adresse',
          label: "Adresse de l'agence en délégation",
          type: 'ADDRESS'
        },
        {
          id: 'delegation_agence_carte_pro_numero',
          label: "Numéro de carte pro de l'agence en délégation",
          type: 'TEXT'
        },
        {
          id: 'delegation_mandat_date',
          label: "Date d'échéance du mandat initial",
          type: 'DATE'
        },
        {
          id: 'delegation_mandat_numero',
          label: 'Numéro de la délégation de mandat',
          type: 'TEXT'
        },
        {
          id: 'delegation_sous_mandat_numero',
          label: "Numéro d'inscription du mandat du délégataire",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'delegation_partage_moitie',
              label: 'A la moitié des honoraires prévus'
            },
            {
              id: 'delegation_partage_autre',
              label: 'A un autre montant'
            }
          ],
          id: 'delegation_mandat_honoraire',
          label: 'En cas de réalisation de la transaction, le Délégataire aura droit',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'delegation_mandat_honoraire',
                value: 'delegation_partage_autre',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'delegation_mandat_partage_autre',
          label: 'Montant des honoraires revenant à la seconde agence',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'delegation_mandat_honoraire',
                value: 'delegation_partage_autre',
                type: 'EQUALS'
              }
            ]
          ],
          description:
            "Le prix de commercialisation doit être le prix final payé par l'Acquéreur. Pour plus d'informations consultez la bulle d'aide",
          id: 'delegation_mandat_partage_autre_cfp',
          label: 'Montant des honoraires revenant à la seconde agence',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'delegation_mandat_partage_pp_pourcentage',
          label: 'Pourcentage des honoraires revenant à Propriétés Privées',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'delegation_mandat_partage_autre_pourcentage',
          label: 'Pourcentage des honoraires revenant à la seconde agence délégataire',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'delegation_clause_particuliere',
          label: 'Ajouter une clause particulière',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'delegation_clause_particuliere_liste_delegation_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'delegation_clause_particuliere_liste_delegation_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          conditions: [
            [
              {
                id: 'delegation_clause_particuliere',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'delegation_clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'delegation_clause_particuliere_liste_delegation_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'delegation_mandat_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '8bd39225_9d7a_4245_86fd_ffd65671a34e',
      label: 'Délégation au mandat',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_LIBRE_ANGLAIS'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_libre_anglais',
          label: "Document d'informations précontractuelles - Anglais",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                type: 'EQUALS',
                value: 'exclusif'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['MY_CHEZ_MOI_IMMOBILIER_VENTE_MANDAT']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                type: 'EQUALS',
                value: 'exclusif'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['SELECTION_HABITAT__TRANSACTION__MANDAT_DE_VENTE']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                type: 'EQUALS',
                value: 'exclusif'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__COLDWELL_BANKER__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'exclusif',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_fixe',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'exclusif',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_pourcentage',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type_confiance',
                type: 'EQUALS',
                value: 'confiance'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__CANNISIMMO__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type_confiance',
                type: 'EQUALS',
                value: 'exclusif'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['CANNISIMMO__IMMOBILIER__MANDAT_VENTE']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['FRANCE_FORESTRY_IMMOBILIER_MANDAT_VENTE_FRANCE_FORESTRY']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_EXCLUSIF'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_exclusif',
          label: "Document d'informations précontractuelles - Mandat Exclusif",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                type: 'EQUALS',
                value: 'semi'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['MY_CHEZ_MOI_IMMOBILIER_VENTE_MANDAT']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                type: 'EQUALS',
                value: 'semi'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['SELECTION_HABITAT__TRANSACTION__MANDAT_DE_VENTE']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                type: 'EQUALS',
                value: 'semi'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__COLDWELL_BANKER__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'semi',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_fixe',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'semi',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_pourcentage',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type_confiance',
                type: 'EQUALS',
                value: 'semi'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['CANNISIMMO__IMMOBILIER__MANDAT_VENTE']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_SEMI'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_semi',
          label: "Document d'informations précontractuelles - Mandat Semi-Exclusif",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                type: 'EQUALS',
                value: 'simple'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['MY_CHEZ_MOI_IMMOBILIER_VENTE_MANDAT']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                type: 'EQUALS',
                value: 'simple'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['SELECTION_HABITAT__TRANSACTION__MANDAT_DE_VENTE']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                type: 'EQUALS',
                value: 'simple'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__COLDWELL_BANKER__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'simple',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_fixe',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'simple',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_pourcentage',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type_confiance',
                type: 'EQUALS',
                value: 'simple'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['CANNISIMMO__IMMOBILIER__MANDAT_VENTE']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_SIMPLE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_simple',
          label: "Document d'informations précontractuelles - Mandat Simple",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'exclusif',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_fixe',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'exclusif',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_pourcentage',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_EXCLUSIF_BVI'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_exclusif_villages',
          label: "Document d'informations précontractuelles - Mandat Exclusif",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'semi',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_fixe',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'semi',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_pourcentage',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_SEMI_BVI'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_semi_villages',
          label: "Document d'informations précontractuelles - Mandat Semi-Exclusif",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'simple',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_fixe',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_type',
                value: 'simple',
                type: 'EQUALS'
              },
              {
                id: 'mandat_vente_calcul',
                value: 'recherche_pourcentage',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_SIMPLE_BVI'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_simple_villages',
          label: "Document d'informations précontractuelles - Mandat Simple",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_recherche_calcul',
                value: 'recherche_fixe',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'mandat_recherche_calcul',
                value: 'recherche_pourcentage',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_RECHERCHE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_recherche',
          label: "Document d'informations précontractuelles - Mandat de Recherche",
          type: 'UPLOAD'
        }
      ],
      conditions: [
        [
          {
            id: 'mandant_statut',
            type: 'EQUALS',
            value: 'personnelles'
          }
        ]
      ],
      id: '2240380f_d5ee_42d3_af0e_bb04985f7de5',
      label: 'Droit de la consommation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
          },
          id: 'mandat_retractation_document_tapuscrit',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT_BVI'
          },
          id: 'mandat_retractation_document_tapuscrit_bvi',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT_ANGLAIS'
          },
          id: 'mandat_retractation_document_tapuscrit_anglais',
          label: 'Formulaire de rétractation - Anglais',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_LIBRE'
          },
          id: 'dip_tapuscrit',
          label: "Document d'informations précontractuelles",
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_LIBRE_BVI'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_tapuscrit_villages',
          label: "Document d'informations précontractuelles",
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_LIBRE_ANGLAIS'
          },
          id: 'dip_tapuscrit_anglais',
          label: "Document d'informations précontractuelles - Anglais",
          type: 'UPLOAD'
        }
      ],
      conditions: [
        [
          {
            id: 'mandat_tapuscrit',
            value: 'oui',
            type: 'EQUALS'
          }
        ]
      ],
      id: 'e7639c9e_0c4f_4a08_92a4_eb84bd6eb5fe',
      label: 'Droit de la consommation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER',
                  'OPERATION__KW_ABONDANCE__IMMOBILIER',
                  'OPERATION__KELLER_WILLIAMS__VIAGER',
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL'
                ]
              },
              {
                id: 'mandant_dip_integre_statut',
                value: 'non',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_dip_integre_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER',
                  'OPERATION__KW_ABONDANCE__IMMOBILIER',
                  'OPERATION__KELLER_WILLIAMS__VIAGER',
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL'
                ]
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'coldwell_dip',
          label: 'Informations Précontractuelles',
          type: 'UPLOAD'
        }
      ],
      id: 'a19cf990_2999_4323_8cb9_1b79f7283579',
      label: 'Droit de la consommation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_KW_ABONDANCE_ORGANIGRAMME'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_abondance_organigramme',
          label: 'Organigramme',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'mandant_statut',
                    type: 'EQUALS',
                    value: 'personnelles'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER',
                  'OPERATION__KW_ABONDANCE__IMMOBILIER',
                  'OPERATION__KELLER_WILLIAMS__VIAGER',
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL'
                ]
              },
              {
                id: 'mandant_dip_integre_statut',
                value: 'non',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'mandant_dip_integre_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER',
                  'OPERATION__KW_ABONDANCE__IMMOBILIER',
                  'OPERATION__KELLER_WILLIAMS__VIAGER',
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL'
                ]
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_dip',
          label: 'Informations Précontractuelles',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_retractation',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_KW'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_formulaire_retractation',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandant_statut',
                type: 'EQUALS',
                value: 'personnelles'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_KW'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_commercial_formulaire_retractation',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_KW'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_formulaire_retractation_dip',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_tapuscrit',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_KW'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'kw_formulaire_retractation_tapuscrit',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        }
      ],
      id: 'ebfa529e_dbb4_42a5_b304_960df7d51083',
      label: 'Droit de la consommation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                id: 'coldwell_mandat_statut',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_SIMPLE'
          },
          id: 'coldwell_dip_simple',
          label: "Document d'Information Précontractuel",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'coldwell_mandat_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_EXCLUSIF'
          },
          id: 'coldwell_dip_exclusif',
          label: "Document d'Information Précontractuel",
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'coldwell_retractation',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'coldwell_liste_media_statut',
          label: 'La liste média est-elle annexée au mandat ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'coldwell_liste_media_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'coldwell_plan_media',
          label: 'Plan média',
          type: 'UPLOAD'
        }
      ],
      id: '65edea9f_8076_41ef_a934_6f94d83d861d',
      label: 'Documents',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'formulaire_retractation_pp',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['PROPRIETES_PRIVEES_IMMOBILIER_MANDAT']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_PP'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_pp',
          label: 'Document Informations Précontractuelles',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['PROPRIETES_PRIVEES_IMMOBILIER_MANDAT_HAPPY']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_PP_HAPPY'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_pp_happy',
          label: 'Document Informations Précontractuelles',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['PROPRIETES_PRIVEES_IMMORESEAU_MANDAT_VENTE']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_IMMORESEAU'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_immoreseau',
          label: 'Document Informations Précontractuelles',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['PROPRIETES_PRIVEES_IMMORESEAU_MANDAT_VENTE_EASY']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_IMMORESEAU_EASY'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_immoreseau_easy',
          label: 'Document Informations Précontractuelles',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['PROPRIETES_PRIVEES_REZOXIMO_MANDAT_VENTE']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_REZOXIMO'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_rezoximo',
          label: 'Document Informations Précontractuelles',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['PROPRIETES_PRIVEES_REZOXIMO_MANDAT_VENTE_LIBERTY']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_REZOXIMO_LIBERTY'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_rezoximo_liberty',
          label: 'Document Informations Précontractuelles',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pp_duo_confiance',
                type: 'DIFFERENT',
                value: 'oui'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['PROPRIETES_PRIVEES_IMMOBILIER_MANDAT_VIP_2']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_PP_VIP'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_pp_vip',
          label: 'Document Informations Précontractuelles',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pp_duo_confiance',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['PROPRIETES_PRIVEES_IMMOBILIER_MANDAT_VIP_2']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_PP_VIP_CONFIANCE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_pp_vip_confiance',
          label: 'Document Informations Précontractuelles',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['PROPRIETES_PRIVEES_IMMOBILIER_MANDAT_VIP']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_PP_ULTRA_VIP'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_pp_ultra_vip',
          label: 'Document Informations Précontractuelles',
          type: 'UPLOAD'
        }
      ],
      id: '0e64250c_c7b2_459a_804c_d52a23fcbfcc',
      label: 'Droit de la consommation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              id: 'blot_honoraires',
              label: 'Honoraires de négociation',
              type: 'PRICE'
            },
            {
              id: 'blot_lieu_signature',
              label: 'Lieu de signature du mandat',
              type: 'TEXT'
            },
            {
              id: 'blot_date',
              label: 'Date de signature du mandat',
              type: 'DATE'
            },
            {
              id: 'blot_numero',
              label: 'Numéro du mandat',
              type: 'TEXT'
            }
          ],
          id: '93a6dd2c_2748_435f_8de5_4414459e1340',
          label: 'CONDITION_BLOCK_Blot',
          type: 'CONDITION_BLOCK'
        }
      ],
      id: '4306281e_fbde_4960_9015_076a17f61966',
      label: 'Informations Générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'reconnaissance_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'b3427781_7615_4f54_a2ed_d148769f6cc6',
      label: "Reconnaissance d'honoraires",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'engagement_confidentialite_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '2fabcf24_2bf5_42a6_8249_b482f95284ae',
      label: 'Engagement de Confidentialité',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_RETRACTATION_EFFICITY_2'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'retractation_efficity_2',
          label: 'Formulaire de rétractation',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DIP_LIBRE_ANGLAIS'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dip_libre_anglais_efficity',
          label: "Document d'informations précontractuelles - Anglais",
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_FORMULAIRE_RETRACTATION_MANDAT_ANGLAIS'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_retractation_document_anglais_efficity',
          label: 'Formulaire de rétractation - Anglais',
          type: 'UPLOAD'
        }
      ],
      id: 'e60da3a0_c0c0_44fc_af73_040fa561bd21',
      label: 'Documents',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'cadastre',
              label: 'Documents cadastraux'
            },
            {
              id: 'urbanisme',
              label: 'Renseignements urbanistiques'
            },
            {
              id: 'attestation_sols',
              label: 'Attestations du sol BDES'
            },
            {
              id: 'acte_achat',
              label: "Acte d'achat"
            },
            {
              id: 'acte_division',
              label: 'Acte de division et modification éventuelle'
            },
            {
              id: 'titre_propriete',
              label: 'Autre(s) titre(s) de propriété'
            },
            {
              id: 'certificat_hypothecaire',
              label: 'Certificat Hypothécaire'
            },
            {
              id: 'acte_base',
              label: 'Acte de base et modification éventuelle'
            },
            {
              id: 'date_signature',
              label: "Date prévue pour la signature de l'acte authentique"
            },
            {
              id: 'autre',
              label: 'Autres documents'
            }
          ],
          id: 'belgique_document_transmis',
          label: 'Accord pour demander au notaire les documents suivants',
          multiple: true,
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'belgique_document_transmis',
                type: 'CONTAINS',
                value: 'autre'
              }
            ]
          ],
          id: 'belgique_document_transmis_autre_liste',
          label: 'Autres documents',
          type: 'TEXTAREA'
        }
      ],
      id: '6cdf54a4_99ea_4b50_9ec7_aa9494cdc03e',
      label: 'Transmission de données personnelles',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__MANDAT',
  label: 'Mandat',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__MANDAT',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_ANCIEN', 'MANDAT'],
  type: 'RECORD'
};
