{"questionTemplates": {"ABO": {"type": "SELECT", "choices": [{"id": "abonnement_mensuel", "label": "<PERSON><PERSON><PERSON>"}, {"id": "abonnement_annuel", "label": "<PERSON><PERSON>"}]}, "OFFRE": {"type": "SELECT", "choices": [{"id": "offre_specifique", "label": "Spécifique"}, {"id": "offre_freemium", "label": "\"Freemium\""}, {"id": "offre_solo", "label": "\"Pro\""}, {"id": "offre_team", "label": "\"Business\""}, {"id": "offre_entreprise", "label": "\"Premium\""}]}, "PAIEMENT": {"type": "SELECT", "choices": [{"id": "paiement_carte", "label": "par carte bancaire"}, {"id": "paiement_virement", "label": "par virement bancaire"}, {"id": "paiement_prelevement", "label": "par prélèvement bancaire"}]}, "OPTIONS_CHOIX": {"type": "SELECT", "choices": [{"id": "options_utilisateurs", "label": "Licences d'utilisateurs supplémentaires"}, {"id": "registre_transaction", "label": "Registre de transaction"}, {"id": "registre_gestion", "label": "Registre de gestion"}, {"id": "options_signatures_simples", "label": "Pack de signatures simples"}, {"id": "options_signatures_avancees", "label": "Pack de signatures avancées"}, {"id": "options_recommande", "label": "Pack de recommandés"}, {"id": "options_marque_grise", "label": "<PERSON><PERSON> (disponible uniquement pour des offres business et premimum)"}, {"id": "options_marque_blanche", "label": "<PERSON><PERSON> (disponible uniquement pour des offres business et premimum)"}]}, "SIMPLE": {"type": "SELECT", "choices": [{"id": "signature_simple_200", "label": "Pack de 200 signatures simples d’une valeur unitaire de 0,9€ht/signataire soit un total de 180€ht."}, {"id": "signature_simple_500", "label": "Pack de 500 signatures simples d’une valeur unitaire de 0,8€ht/signataire soit un total de 400€ht."}, {"id": "signature_simple_2000", "label": "Pack de 2000 signatures simples d’une valeur unitaire de 0,7€ht/signataire soit un total de 1400€ht."}, {"id": "signature_simple_5000", "label": "Pack de 5000 signatures simples d’une valeur unitaire de 0,6€ht/signataire soit un total de 3000€ht."}]}, "AVANCEE": {"type": "SELECT", "choices": [{"id": "signature_avancee_50", "label": "Pack de 50 signatures avancées d’une valeur unitaire de 2,99€ht/signataire soit un total de 149,50€ht."}, {"id": "signature_avancee_200", "label": "Pack de 200 signatures avancées d’une valeur unitaire de 2,78€ht/signataire soit un total de 558€ht."}, {"id": "signature_avancee_2000", "label": "Pack de 2000 signatures avancées d’une valeur unitaire de 2,3€ht/signataire soit un total de 4600€ht."}, {"id": "signature_avancee_5000", "label": "Pack de 5000 signatures avancées d’une valeur unitaire de 2€ht/signataire soit un total de 10000€ht."}]}, "RECO": {"type": "SELECT", "choices": [{"id": "recommande_50", "label": "Pack de 50 recommandés électroniques d’une valeur unitaire de 2,99€ht/recommandé soit un total de 149,50€ht"}, {"id": "recommande_200", "label": "Pack de 200 recommandés électroniques d’une valeur unitaire de 2,78€ht/recommandé soit un total de 558€ht"}, {"id": "recommande_500", "label": "Pack de 500 recommandés électroniques d’une valeur unitaire de 2,69€ht/recommandé soit un total de 1345€ht"}]}}, "conditions": {"SPECIFIQUE": [{"id": "offre_type", "value": "offre_specifique", "type": "EQUALS"}], "OPTION": [{"id": "option_presence", "value": "oui", "type": "EQUALS"}], "UTILISATEUR": [{"id": "option_choix", "value": "options_utilisateurs", "type": "EQUALS"}], "SIMPLE": [{"id": "option_choix", "value": "options_signatures_simples", "type": "EQUALS"}], "AVANCEE": [{"id": "option_choix", "value": "options_signatures_avancees", "type": "EQUALS"}], "RECOMMANDE": [{"id": "option_choix", "value": "options_recommande", "type": "EQUALS"}], "EXECUTION_IMMEDIATE": [{"id": "execution_immediate", "value": "non", "type": "EQUALS"}], "TRANSACTION": [{"id": "option_choix", "value": "registre_transaction", "type": "EQUALS"}], "GESTION": [{"id": "option_choix", "value": "registre_gestion", "type": "EQUALS"}]}}