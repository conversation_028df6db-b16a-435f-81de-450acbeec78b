{"questionTemplates": {"CONSTRUCTION": {"type": "SELECT-PICTURES", "choices": [{"id": "moins_10_ans", "label": "Il y a moins de 10 ans", "icon": "calendar.svg"}, {"id": "apres_1997", "label": "Après le 1er juillet 1997 (date du permis)", "icon": "calendar.svg"}, {"id": "1949_1997", "label": "Après le 1er janvier 1949", "icon": "calendar.svg"}, {"id": "avant_1949", "label": "Avant 1949", "icon": "calendar.svg"}]}, "BAIL_TYPE": {"type": "SELECT", "choices": [{"label": "Un bail professionnel", "id": "professionnel"}, {"label": "Un bail commercial", "id": "commercial"}, {"label": "Un bail simple", "id": "simple"}, {"label": "Un bail habitation loi 1948", "id": "1948"}, {"label": "Un bail rural", "id": "rural"}, {"label": "Autre type de bail", "id": "bail_autre"}]}, "OCCUPATION": {"type": "SELECT", "choices": [{"label": "Occupé à titre gratuit", "id": "occupation_gratuit"}, {"label": "<PERSON><PERSON> selon un contrat de bail", "id": "location_bail"}]}, "PLOMB": {"type": "SELECT", "choices": [{"label": "Il n'a pas été repéré de présence de plomb", "id": "absence"}, {"label": "Il a été repéré des mesures de plomb de classe 0", "id": "classe_0"}, {"label": "Il a été repéré des mesures de plomb de classe 1", "id": "classe_1"}, {"label": "Il a été repéré des mesures de plomb de classe 2", "id": "classe_2"}, {"label": "Il a été repéré des mesures de plomb de classe 3", "id": "classe_3"}], "multiple": true}, "AMIANTE": {"type": "SELECT", "choices": [{"label": "Il n'a pas été repéré des matériaux contenant de l'amiante", "id": "absence"}, {"label": "Il a été repéré des matériaux contenant de l'amiante de la liste A", "id": "liste_a"}, {"label": "Il a été repéré des matériaux contenant de l'amiante de la liste B", "id": "liste_b"}, {"label": "Des locaux ou parties de locaux n'ont pas pu être visités", "id": "non_visite"}], "multiple": true}, "TERMITES": {"type": "SELECT", "choices": [{"label": "Absence de traces d'infestation de termite", "id": "absence"}, {"label": "Présence de traces d'infestation de termite", "id": "presence"}]}, "TRAVAUX_AUTORISATION": {"type": "SELECT", "choices": [{"label": "Un permis de construire", "id": "permis"}, {"label": "Une déclaration préalable", "id": "declaration"}, {"label": "Aucune autorisation obtenue", "id": "aucune"}, {"label": "Travaux sans autorisation spécifique / travaux à l'identique", "id": "non_necessaire"}]}, "LOCATION_PARTIELLE": {"type": "SELECT", "choices": [{"label": "Sur la totalité du bien", "id": "location_totale"}, {"label": "Sur une partie du bien seulement", "id": "location_partielle"}]}, "CONTRAT_FREQUENCE": {"type": "SELECT", "choices": [{"id": "contrat_frequence_annuelle", "label": "<PERSON><PERSON><PERSON>"}, {"id": "contrat_frequence_mensuelle", "label": "Mensuellement"}]}, "TVA": {"type": "SELECT", "choices": [{"id": "tva_20", "label": "20 %"}, {"id": "tva_5", "label": "5,5 %"}]}, "DISPONIBILITE": {"type": "SELECT", "choices": [{"id": "disponibilite_libre", "label": "Libre"}, {"id": "disponibilite_reserve", "label": "Reservé"}, {"id": "disponibilite_prereserve", "label": "Préreservé"}]}, "ORIGINE": {"type": "SELECT", "choices": [{"id": "origine_propriete_acquisition", "label": "Acquisition"}, {"id": "origine_propriete_donation", "label": "Donation"}, {"id": "origine_propriete_succession", "label": "Succession"}, {"id": "origine_propriete_partage", "label": "Partage - Licitation"}, {"id": "origine_propriete_echange", "label": "Echange"}, {"id": "origine_propriete_adjudication", "label": "Adjudication - Vente aux enchères"}, {"id": "origine_propriete_remembrement", "label": "Remembrement"}]}, "CARACTERISTIQUES": {"type": "SELECT", "choices": [{"id": "caracteristique_neuf", "label": "<PERSON><PERSON><PERSON>"}, {"id": "caracteristique_ancien", "label": "Ancien"}, {"id": "caracteristique_vefa", "label": "En état futur d'achèvement"}, {"id": "caracteristique_renover", "label": "A rénover"}]}, "OCCUPATION_MANDAT": {"type": "SELECT", "choices": [{"id": "occupation_mandat_libre", "label": "Libre de toute occupation"}, {"id": "occupation_mandat_liberation", "label": "Libre à la date"}, {"id": "occupation_mandat_loue", "label": "Loué selon état locatif figurant en annexe"}]}, "MANDAT_SUPERFICIE": {"type": "SELECT", "choices": [{"id": "mandat_superficie_mandant", "label": "Le Mandant"}, {"id": "mandat_superficie_mandataire", "label": "Le Mandataire"}, {"id": "mandat_superficie_diagnostiqueur", "label": "Un Diagnostiqueur"}]}, "NATURE_BIEN": {"type": "SELECT", "choices": [{"id": "nature_bien_cave", "label": "Cave"}, {"id": "nature_bien_cellier", "label": "<PERSON><PERSON>"}, {"id": "nature_bien_parking", "label": "Parking"}, {"id": "nature_bien_garage", "label": "Garage"}, {"id": "nature_bien_local", "label": "Local commercial ou professionnel"}, {"id": "nature_bien_autre", "label": "<PERSON><PERSON>"}]}, "PARKING": {"type": "SELECT", "choices": []}, "DIAG_MLS": {"type": "SELECT", "choices": [{"id": "diag_mls_present", "label": "Le Mandant remet le diagnostic à la signature du mandat"}, {"id": "diag_mls_mandant", "label": "Le Mandant établit le diagnostic avant le compromis"}, {"id": "diag_mls_mandataire", "label": "Le Mandant charge le Mandataire d'établir le diagnostic"}]}, "NATURE_BIEN_LEIZEE": {"type": "SELECT", "choices": [{"id": "leizee_batiment", "label": "Bâtiment"}, {"id": "leizee_bureau", "label": "Bureau"}, {"id": "leizee_casier_ski", "label": "<PERSON><PERSON><PERSON> ski"}, {"id": "leizee_cave", "label": "Cave"}, {"id": "leizee_commerce", "label": "Commerce"}, {"id": "leizee_local_velo", "label": "Local à vélo"}, {"id": "leizee_stationnement", "label": "Stationnement"}]}, "ANNEXES_TYPE_LEIZEE": {"type": "SELECT", "choices": [{"id": "annexe_leizee_abri", "label": "Abri"}, {"id": "annexe_leizee_auvent", "label": "Auvent"}, {"id": "annexe_leizee_balcon", "label": "Balcon"}, {"id": "annexe_leizee_casier_ski", "label": "<PERSON><PERSON><PERSON>"}, {"id": "annexe_leizee_cave", "label": "Cave"}, {"id": "annexe_leizee_cour", "label": "Cour"}, {"id": "annexe_leizee_jardin", "label": "Jardin"}, {"id": "annexe_leizee_local", "label": "Local"}, {"id": "annexe_leizee_local_velo", "label": "Local à vélo"}, {"id": "annexe_leizee_loggia", "label": "Loggia"}, {"id": "annexe_leizee_nom", "label": "Nom"}, {"id": "annexe_leizee_parcelle", "label": "<PERSON><PERSON><PERSON>"}, {"id": "annexe_leizee_patio", "label": "<PERSON><PERSON>"}, {"id": "annexe_leizee_porche", "label": "Porche"}, {"id": "annexe_leizee_rangement_exterieur", "label": "Rangement extérieur"}, {"id": "annexe_leizee_remise", "label": "Remise"}, {"id": "annexe_leizee_rentabilite", "label": "Rentabilité en %"}, {"id": "annexe_leizee_sechoir", "label": "Séchoir"}, {"id": "annexe_leizee_solarium", "label": "Solarium"}, {"id": "annexe_leizee_stationnement_1", "label": "Stationnement 1"}, {"id": "annexe_leizee_stationnement_2", "label": "Stationnement 2"}, {"id": "annexe_leizee_stationnement_3", "label": "Stationnement 3"}, {"id": "annexe_leizee_stationnement_4", "label": "Stationnement 4"}, {"id": "annexe_leizee_surface_defiscalisable", "label": "Surface défiscalisable"}, {"id": "annexe_leizee_terrasse", "label": "Terrasse"}, {"id": "annexe_leizee_varangue", "label": "Varangue"}, {"id": "annexe_leizee_veranda", "label": "Véranda"}]}, "DESTINATION": {"type": "SELECT", "choices": [{"id": "destination_principale", "label": "Résidence principale"}, {"id": "destination_secondaire", "label": "Résidence secondaire"}, {"id": "destination_locatif", "label": "Investissement locatif"}]}, "COPRO_AUTORISATION": {"type": "SELECT", "choices": [{"id": "autorisation_non_necessaire", "label": "L'autorisation de l'assemblée générale n'est pas nécessaire"}, {"id": "autorisation_obtenue", "label": "L'autorisation de l'assemblée générale a été obtenue"}, {"id": "autorisation_a_obtenir", "label": "L'autorisation de l'assemblée générale sera demandée par le Vendeur"}, {"id": "autorisation_non_obtenue", "label": "L'autorisation de l'assemblée générale n'a pas été obtenue"}]}, "TEXT] [@TEXT": {"type": "SELECT", "choices": []}, "TRAVAUX_SIMPLE_OBJET_LISTE": {"type": "SELECT", "choices": [{"id": "exterieur", "label": "L'aspect extérieur du bâti"}, {"id": "structure", "label": "La structure du bâtiment"}, {"id": "aucun", "label": "Aucun des deux"}]}, "RISQUES_NATURELS": {"type": "SELECT", "choices": [{"label": "Zone non concernée par un plan de prévention des risques naturels", "id": "aucun_plan"}, {"label": "Périmètre d'un plan de prévention des risques naturels prescrits", "id": "plan_prescrit"}, {"label": "Périmètre d'un plan de prévention des risques naturels anticipés", "id": "plan_anticipe"}, {"label": "Périmètre d'un plan de prévention des risques naturels approuvés", "id": "plan_approuve"}], "multiple": true}, "RISQUES_MINIERS": {"type": "SELECT", "choices": [{"label": "Zone non concernée par un plan de prévention des risques miniers", "id": "aucun_plan"}, {"label": "Périmètre d'un plan de prévention des risques miniers prescrits", "id": "plan_prescrit"}, {"label": "Périmètre d'un plan de prévention des risques miniers anticipés", "id": "plan_anticipe"}, {"label": "Périmètre d'un plan de prévention des risques miniers approuvés", "id": "plan_approuve"}], "multiple": true}, "RISQUES_TECHNOLOGIQUES": {"type": "SELECT", "choices": [{"label": "Zone non concernée par un plan de prévention des risques technologiques", "id": "aucun_plan"}, {"label": "Périmètre d'un plan de prévention des risques technologiques prescrits", "id": "plan_prescrit"}, {"label": "Périmètre d'un plan de prévention des risques technologiques anticipés", "id": "plan_anticipe"}, {"label": "Périmètre d'un plan de prévention des risques technologiques approuvés", "id": "plan_approuve"}], "multiple": true}, "PRICE_CFP": {"type": "PRICE", "suffix": "CFP"}, "MANDAT_TYPE": {"type": "SELECT", "choices": [{"id": "simple", "label": "de location simple"}, {"id": "semi", "label": "de location semi-exclusif"}, {"id": "exclusif", "label": "de location exclusif"}, {"id": "gestion", "label": "de gestion"}]}, "EQUIPEMENTS": {"type": "PICK_LIST", "choices": [{"id": "cable", "label": "Raccordement au câble"}, {"id": "adsl", "label": "Raccordement à l'ADSL"}, {"id": "fibre", "label": "Raccordement à la fibre"}, {"id": "aucun", "label": "Aucun équipement"}, {"id": "autre", "label": "Autre équipement"}], "multiple": true}, "INDIVIDUEL_COLLECTIF": {"type": "SELECT", "choices": [{"id": "individuel", "label": "Individuel"}, {"id": "collectif", "label": "Collectif"}]}, "PARKING_GARAGE": {"type": "SELECT-BINARY", "choices": [{"id": "garage", "label": "Garage - box fermé"}, {"id": "parking", "label": "Parking"}]}, "ORIGINE_GIBOIRE": {"type": "SELECT", "choices": [{"id": "origine_propriete_acquisition", "label": "Acquisition"}, {"id": "origine_propriete_vefa", "label": "VEFA"}, {"id": "origine_propriete_donation", "label": "Donation"}, {"id": "origine_propriete_succession", "label": "Succession"}, {"id": "origine_propriete_partage", "label": "Partage - Licitation"}, {"id": "origine_propriete_echange", "label": "Echange"}, {"id": "origine_propriete_adjudication", "label": "Adjudication - Vente aux enchères"}, {"id": "origine_propriete_remembrement", "label": "Remembrement"}]}, "LOUE_LIBRE": {"type": "SELECT-BINARY", "choices": [{"id": "libre", "label": "Libre"}, {"id": "loue", "label": "<PERSON><PERSON>"}]}, "CESSION_PRO_TYPE": {"type": "SELECT", "choices": [{"id": "fonds", "label": "Fonds de commerce"}, {"id": "mur", "label": "Murs"}, {"id": "bail", "label": "<PERSON><PERSON> au bail"}, {"id": "titre", "label": "Titres de sociétés ou de parts"}], "multiple": true}, "MEUBLE_SITUATION": {"type": "SELECT", "choices": [{"label": "Isolé", "id": "isole"}, {"label": "Dans une ferme", "id": "ferme"}, {"label": "Dans un hameau", "id": "hameau"}, {"label": "Dans un village", "id": "village"}, {"label": "Dans une ville", "id": "ville"}]}, "CLASSEMENT_CATEGORIE": {"type": "SELECT", "choices": [{"label": "1 étoile", "id": "1"}, {"label": "2 étoiles", "id": "2"}, {"label": "3 étoiles", "id": "3"}, {"label": "4 étoiles", "id": "4"}, {"label": "5 étoiles", "id": "5"}, {"label": "Aucun classement", "id": "aucun"}]}, "ETAT_DESCRIPTIF_TYPE_CONSTRUCTION": {"type": "SELECT", "choices": [{"label": "Neuve", "id": "neuve"}, {"label": "Ré<PERSON><PERSON>", "id": "recente"}, {"label": "Ancienne", "id": "ancienne"}]}, "ETAT_DESCRIPTIF_TYPE_BIEN": {"type": "SELECT", "choices": [{"label": "Une maison", "id": "maison"}, {"label": "Indépendante", "id": "independant"}, {"label": "Avec jardin", "id": "jardin"}, {"label": "Un studio", "id": "studio"}, {"label": "Un appartement", "id": "appartement"}], "multiple": true}, "ETAT_DESCRIPTIF_PIECES_SITUATION": {"type": "SELECT", "choices": [{"label": "Un appartement", "id": "appartement"}, {"label": "Une villa", "id": "villa"}, {"label": "Occupées partiellement par le propriétaire", "id": "proprietaire"}, {"label": "Occupées par d'autres locataires", "id": "locataire"}], "multiple": true}, "PIECES_CARACTERISTIQUES": {"type": "SELECT", "choices": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "id": "cuisine_separee"}, {"label": "Coin-cuisine dans la pièce principale", "id": "cuisine"}, {"label": "Existence d'une entrée", "id": "entree"}], "multiple": true}, "JOUISSANCE_PRIVATIVE": {"type": "SELECT", "choices": [{"label": "Jardin privatif", "id": "jardin"}, {"label": "Parc privatif", "id": "parc"}, {"label": "Cour privative", "id": "cour"}, {"label": "Garage privatif", "id": "garage"}, {"label": "Emplacement de voiture privatif", "id": "emplacement"}, {"label": "Terrasse privative", "id": "terrasse"}, {"label": "Loggia privative", "id": "loggia"}, {"label": "Balcon privatif", "id": "balcon"}], "multiple": true}, "EXPOSITION": {"type": "SELECT", "choices": [{"label": "Nord", "id": "nord"}, {"label": "Sud", "id": "sud"}, {"label": "Est", "id": "est"}, {"label": "Ouest", "id": "ouest"}]}, "ETAT_DESCRIPTIF_AGENCEMENT_CUISINE": {"type": "SELECT", "choices": [{"label": "Evier avec eau chaude/froide", "id": "evier"}, {"label": "VMC", "id": "vmc"}, {"label": "<PERSON><PERSON> aspi<PERSON>e", "id": "hotte"}, {"label": "Table de cuisson", "id": "cuisson"}, {"label": "Four", "id": "four"}, {"label": "Four à micro-ondes", "id": "microonde"}, {"label": "Réfrigérateur", "id": "refrigerateur"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "id": "congelateur"}, {"label": "Lave-vais<PERSON>le", "id": "lave"}, {"label": "Batterie de cuisine", "id": "batterie"}, {"label": "Autocuiseur", "id": "autocuiseur"}], "multiple": true}, "ALIMENTATION_PLAQUE": {"type": "SELECT", "choices": [{"label": "Gaz de ville", "id": "gaz"}, {"label": "Bouteille de gaz", "id": "bouteille"}, {"label": "Electricité", "id": "electricite"}, {"label": "Mixte", "id": "mixte"}]}, "ETAT_DESCRIPTIF_EQUIPEMENT_BIEN": {"type": "SELECT", "choices": [{"label": "Téléphone dans le logement", "id": "telephone"}, {"label": "Téléphone à proximité", "id": "telephone_proximite"}, {"label": "Accès internet haut debit", "id": "internet"}, {"label": "TV couleur", "id": "tv"}, {"label": "Lecteur DVD", "id": "lecteur"}, {"label": "Chaîne Hi-Fi avec radio", "id": "radio"}, {"label": "Lave-linge électrique", "id": "lave_linge"}, {"label": "Sèche-Linge électrique", "id": "seche_linge"}, {"label": "Etendoir à linge", "id": "etendoir"}, {"label": "Fer à repasser", "id": "fer"}, {"label": "Sèche-cheveux", "id": "seche_cheveux"}, {"label": "As<PERSON>rateur", "id": "aspirateur"}, {"label": "Autres équipements", "id": "autre"}], "multiple": true}, "ETAT_DESCRIPTIF_CHAUFFAGE": {"type": "SELECT", "choices": [{"label": "Chauffage central", "id": "chauffage"}, {"label": "Climatisation", "id": "climatisation"}, {"label": "Rafraîchissement d'air", "id": "rafraichissement"}], "multiple": true}, "STATUT_PV_ELECTRIQUE": {"type": "SELECT", "choices": [{"label": "PV de contrôle établi", "id": "etabli"}, {"label": "PV de contrôle non établi", "id": "non_etabli"}, {"label": "Dispense de contrôle", "id": "dispense"}]}, "ELECTRICITE_DISPENSE": {"type": "SELECT", "choices": [{"label": "Démolition du bâtiment", "id": "demolition"}, {"label": "Rénovation de l'installation", "id": "renovation"}]}, "BELGIQUE_CERTIBEAU_STATUT": {"type": "SELECT", "choices": [{"label": "Raccordement antérieur au 1er Juin 2021", "id": "raccordement_ante_2021"}, {"label": "Raccordement postérieur au 1er Juin 2021", "id": "raccordement_post_2021"}, {"label": "Immeuble sur plan", "id": "plan"}, {"label": "Local où l'eau est fournie au public", "id": "public"}]}, "GAZ_MAZOUT": {"type": "SELECT-BINARY", "choices": [{"label": "GAZ", "id": "gaz"}, {"label": "MAZOUT", "id": "mazout"}]}, "PERMIS_DECLARATION": {"type": "SELECT-BINARY", "choices": [{"label": "PERMIS", "id": "permis"}, {"label": "DECLARATION", "id": "declaration"}]}, "ETAT_POSTE_PRINCIPAUX": {"type": "SELECT", "choices": [{"label": "<PERSON><PERSON><PERSON>", "id": "neuf"}, {"label": "Excellent", "id": "excellent"}, {"label": "Remis à neuf", "id": "remis_neuf"}, {"label": "Bon état", "id": "bon_etat"}, {"label": "A rafraîchir", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "A rénover", "id": "renover"}, {"label": "A réhabiliter", "id": "rehabiliter"}, {"label": "A démolir", "id": "demolir"}, {"label": "<PERSON><PERSON>", "id": "autre"}]}, "AERIENNE_ENTERREE": {"type": "SELECT", "choices": [{"label": "Aérienne", "id": "aerienne"}, {"label": "Enterrée", "id": "enterree"}]}, "BELGIQUE_SITUATION_BIEN": {"type": "SELECT", "choices": [{"label": "En région Wallonne", "id": "wallonne"}, {"label": "En région Bruxelles-Capitale", "id": "bruxelles"}]}, "LISTE_CHAUFFAGE": {"type": "SELECT", "choices": [{"id": "chaudiere_gaz", "label": "Chaudière G<PERSON>"}, {"id": "chaudiere_fioul", "label": "<PERSON><PERSON><PERSON>"}, {"id": "cheminee", "label": "Cheminée"}, {"id": "poele_bois", "label": "<PERSON><PERSON><PERSON> à bois"}, {"id": "poele_granules", "label": "Poêle à granulés"}, {"id": "radiateur", "label": "Convecteurs électriques"}, {"id": "pompe", "label": "Pompe à Chaleur"}, {"id": "aucun", "label": "Aucun système de chauffage"}], "multiple": true}, "CHAUDIERE_TYPE": {"type": "SELECT", "choices": [{"id": "gaz", "label": "Gaz"}, {"id": "fioul", "label": "<PERSON><PERSON><PERSON>"}, {"id": "autre", "label": "<PERSON><PERSON>"}]}, "ETAT_CUVE_FIOUL": {"type": "SELECT", "choices": []}, "USAGE_GESTION": {"type": "SELECT", "choices": [{"id": "habitation", "label": "Habitation"}, {"id": "mixte", "label": "Mixte"}, {"id": "commercial", "label": "Commercial"}, {"id": "professionnel", "label": "Professionnel"}]}, "LMNP_TYPE": {"type": "SELECT", "choices": [{"id": "tourisme", "label": "Tourisme"}, {"id": "affaire", "label": "<PERSON>e"}, {"id": "ehpad", "label": "EHPAD"}, {"id": "etudiant", "label": "Etudiante"}, {"id": "senior", "label": "Sénior"}]}}, "conditions": {"PARTIES_COMMUNES_SPECIALES": [{"id": "parties_communes_speciales_statut", "type": "EQUALS", "value": "oui"}], "MESURAGE_CARREZ": [{"id": "mesurage_carrez_statut", "type": "EQUALS", "value": "oui"}], "CONTRATS_ATTACHES": [{"id": "contrats_attaches_statut", "type": "EQUALS", "value": "oui"}], "OCCUPATION": [{"id": "occupation_statut", "type": "EQUALS", "value": "oui"}], "OCCUPATION_GRATUIT": [{"id": "occupation_location", "type": "EQUALS", "value": "occupation_gratuit"}], "LOCATION": [{"id": "occupation_location", "type": "EQUALS", "value": "location_bail"}], "LOCATION_BAIL_TYPE_AUTRE": [{"id": "location_bail_type", "type": "EQUALS", "value": "bail_autre"}], "1949_1997": [{"id": "construction", "type": "EQUALS", "value": "1949_1997"}], "AVANT_1949": [{"id": "construction", "type": "EQUALS", "value": "avant_1949"}], "MOINS_10_ANS": [{"id": "construction", "type": "EQUALS", "value": "moins_10_ans"}], "DIAGNOSTIQUEUR_UNIQUE": [{"id": "diagnostics_techniques_diagnostiqueur_unique", "type": "EQUALS", "value": "oui"}], "PAS_DIAGNOSTIQUEUR_UNIQUE": [{"id": "diagnostics_techniques_diagnostiqueur_unique", "type": "EQUALS", "value": "non"}], "DPE": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}], "AMIANTE": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}], "TERMITES": [{"id": "diagnostic_termites_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX": [{"id": "travaux_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_PERMIS": [{"id": "travaux_autorisation_administration", "type": "EQUALS", "value": "permis"}], "TRAVAUX_DECLARATION": [{"id": "travaux_autorisation_administration", "type": "EQUALS", "value": "declaration"}], "TRAVAUX_DECLARATION_ACHEVEMENT": [{"id": "travaux_declaration_achevement_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_CERTIFICAT_CONFORMITE": [{"id": "travaux_certificat_conformite_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_PROFESSIONNEL": [{"id": "travaux_professionnel_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_MOINS_10_ANS": [{"id": "travaux_date_achevement", "type": "NOT_OLDER_THAN_N_MONTHS", "value": "120"}], "TRAVAUX_COPROPRIETE_AUTORISATION": [{"id": "travaux_copropriete_autorisation", "type": "EQUALS", "value": "oui"}], "TRAVAUX_COPROPRIETE_AUTORISATION_OBTENUE": [{"id": "travaux_copropriete_autorisation_statut", "type": "EQUALS", "value": "oui"}], "OP_DONATION": [{"id": "origine_liste", "value": "origine_propriete_donation", "type": "EQUALS"}], "OP_DONATION_UNIQUE": [{"id": "origine_donation_unique", "value": "non", "type": "EQUALS"}], "OP_SUCCESSION": [{"id": "origine_liste", "value": "origine_propriete_succession", "type": "EQUALS"}], "OP_ADJUDICATION": [{"id": "origine_liste", "value": "origine_propriete_adjudication", "type": "EQUALS"}], "OP_ACQUISITION": [{"id": "origine_liste", "value": "origine_propriete_acquisition", "type": "EQUALS"}], "OP_ECHANGE": [{"id": "origine_liste", "value": "origine_propriete_echange", "type": "EQUALS"}], "OP_PARTAGE": [{"id": "origine_liste", "value": "origine_propriete_partage", "type": "EQUALS"}], "OP_SUCCESSION_TERMINEE": [{"id": "origine_succession_statut", "value": "oui", "type": "EQUALS"}], "OP_SUCCESSION_NON_TERMINEE": [{"id": "origine_succession_statut", "value": "non", "type": "EQUALS"}], "OP_REMEMBREMENT": [{"id": "origine_liste", "value": "origine_propriete_remembrement", "type": "EQUALS"}], "OCCUPATION_MANDAT_LIBERATION": [{"id": "occupation_mandat", "value": "occupation_mandat_liberation", "type": "EQUALS"}], "OCCUPATION_MANDAT_LOUE": [{"id": "occupation_mandat", "value": "occupation_mandat_loue", "type": "EQUALS"}], "NATURE_AUTRE": [{"id": "nature_bien", "value": "nature_bien_autre", "type": "EQUALS"}], "GARAGE": [], "BALCON": [], "JARDIN": [], "TERRASSE": [], "CAVE": [], "LEIZEE_ANNEXE_ABRI": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_abri", "type": "EQUALS"}], "LEIZEE_ANNEXE_CASIER": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_casier_ski", "type": "EQUALS"}], "LEIZEE_ANNEXE_CAVE": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_cave", "type": "EQUALS"}], "LEIZEE_ANNEXE_LOCAL": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_local", "type": "EQUALS"}], "LEIZEE_ANNEXE_LOCAL_VELO": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_local_velo", "type": "EQUALS"}], "LEIZEE_ANNEXE_RANGEMENT": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_rangement_exterieur", "type": "EQUALS"}], "LEIZEE_ANNEXE_REMISE": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_remise", "type": "EQUALS"}], "LEIZEE_ANNEXE_SECHOIR": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_sechoir", "type": "EQUALS"}], "LEIZEE_ANNEXE_STATIONNEMENT": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_stationnement_1", "type": "EQUALS"}, {"id": "", "value": ""}], "LEIZEE_ANNEXE_AUVENT": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_auvent", "type": "EQUALS"}], "LEIZEE_ANNEXE_BALCON": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_balcon", "type": "EQUALS"}], "LEIZEE_ANNEXE_COUR": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_cour", "type": "EQUALS"}], "LEIZEE_ANNEXE_JARDIN": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_jardin", "type": "EQUALS"}], "LEIZEE_ANNEXE_LOGGIA": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_loggia", "type": "EQUALS"}], "LEIZEE_ANNEXE_SOLARIUM": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_solarium", "type": "EQUALS"}], "LEIZEE_ANNEXE_TERRASSE": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_terrasse", "type": "EQUALS"}], "LEIZEE_ANNEXE_VARANGUE": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_varangue", "type": "EQUALS"}], "LEIZEE_ANNEXE_VERANDA": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_veranda", "type": "EQUALS"}], "LEIZEE_ANNEXE_STATIONNEMENT_1": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_stationnement_1", "type": "EQUALS"}], "LEIZEE_ANNEXE_STATIONNEMENT_2": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_stationnement_2", "type": "EQUALS"}], "LEIZEE_ANNEXE_STATIONNEMENT_3": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_stationnement_3", "type": "EQUALS"}], "LEIZEE_ANNEXE_STATIONNEMENT_4": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_stationnement_4", "type": "EQUALS"}], "AD_PLAN_LOT": [{"id": "ad_plan_lot", "value": "oui", "type": "EQUALS"}], "TRAVAUX_ASSURANCE_PARTICULIER": [{"id": "travaux_particulier_assurance_dommage_statut", "value": "oui", "type": "EQUALS"}], "TERMITES_FACULTATIF": [{"id": "diagnostic_termites_informatif_effectue", "value": "oui", "type": "EQUALS"}], "TRAVAUX_AUTORISATION_GLOBAL_AG": [{"id": "travaux_copropriete_autorisation_global", "value": "autorisation_obtenue", "type": "EQUALS"}], "AMIANTE_ETABLI": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_BIENS_PROFESSIONNELS", "OPERATION__EFFICITY__IMMOBILIER_COMMERCIAL", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_PRO", "OPERATION__SEXTANT__IMMOBILIER_COMMERCIAL", "OPERATION__FOLLIOT__IMMOBILIER_PRO"]}], "DPE_ETABLI": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_BIENS_PROFESSIONNELS", "OPERATION__EFFICITY__IMMOBILIER_COMMERCIAL", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_PRO", "OPERATION__SEXTANT__IMMOBILIER_COMMERCIAL", "OPERATION__FOLLIOT__IMMOBILIER_PRO"]}], "SUPERFICIE": [{"conditions": [{"id": "programme_superficie_statut", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE", "OPERATION__OZANAM__IMMOBILIER__PSLA_PRELIMINAIRE", "OPERATION__OZANAM__IMMOBILIER__PSLA_PROGRAMME", "OPERATION__IMMOBILIER__VENTE_NEUF", "OPERATION__DESIMO__IMMOBILIER__VENTE", "OPERATION__DESIMO__IMMOBILIER__PROGRAMME", "OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME", "OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION", "OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME", "OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE"]}], "AD_DIAG_UNIQUE": [{"conditions": [{"id": "diagnostics_techniques_diagnostiqueur_unique", "type": "EQUALS", "value": "non"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AGENCE_DIRECTE__IMMOBILIER"]}], "AMIANTE_ETABLI_SOCIAL": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "AMIANTE_ETABLI_LOCATION": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__LOCATION__FICHES", "LOCATION", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO"]}], "DPE_ETABLI_SOCIAL": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "DPE_ETABLI_LOCATION": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__LOCATION__FICHES", "LOCATION", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "AMIANTE_SANS_RESULTAT": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__ALLOWA__IMMOBILIER"]}], "DPE_SANS_RESULTAT": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__ALLOWA__IMMOBILIER"]}], "NOT_VENTE_ANCIEN": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__GIBOIRE__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__LOCATION_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_BIENS_PROFESSIONNELS", "OPERATION__EFFICITY__IMMOBILIER_COMMERCIAL", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_PRO", "OPERATION__SEXTANT__IMMOBILIER_COMMERCIAL", "OPERATION__FOLLIOT__IMMOBILIER_PRO", "OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE", "OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO", "OPERATION__ALLOWA__IMMOBILIER", "OPERATION__BLOT__IMMOBILIER", "OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME", "OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__VENTE"]}], "NOT_AGENCE_DIRECTE": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__AGENCE_DIRECTE__IMMOBILIER"]}], "DESIGNATION_MODIFIEE": [{"id": "designation_modifiee", "type": "EQUALS", "value": "oui"}], "NOT_NEUF": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE", "OPERATION__OZANAM__IMMOBILIER__PSLA_PRELIMINAIRE", "OPERATION__OZANAM__IMMOBILIER__PSLA_PROGRAMME", "OPERATION__IMMOBILIER__VENTE_NEUF", "OPERATION__DESIMO__IMMOBILIER__VENTE", "OPERATION__DESIMO__IMMOBILIER__PROGRAMME", "OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME", "OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION", "OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME", "OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE"]}], "AUTRES_CARACTERISTIQUES": [{"id": "autres_caracteristiques", "type": "EQUALS", "value": "oui"}], "AMIANTE_LFEUR": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "DPE_LFEUR": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "AMIANTE_COMMERCIAL": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL", "BAIL", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__LOCATION_COMMERCIAL"]}], "DPE_COMMERCIAL": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL", "BAIL", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__LOCATION_COMMERCIAL"]}], "OP_PAS_SUCCESSION_TERMINEE": [{"id": "origine_succession_statut", "value": "oui", "type": "EQUALS"}], "?OP_PAS_SUCCESSION": [{"id": "origine_liste", "value": "origine_propriete_succession", "type": "DIFFERENT"}], "ZONE_TERMITES": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__AQUITANIS__VENTE_ANCIEN", "OPERATION__AQUITANIS__PROGRAMME_ANCIEN"]}, {"id": "diagnostic_zone_termites_statut", "value": "oui", "type": "EQUALS"}], "FACTURE_TRAVAUX": [{"id": "travaux_facture", "value": "oui", "type": "EQUALS"}], "DPE_FICHE_CLOTURE": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["IMMOBILIER_VENTE_ANCIEN_FICHE_CLOTURE"]}], "MANDAT_TYPE_GESTION": [{"id": "mandat_type", "value": "gestion", "type": "EQUALS"}], "MANDAT_TYPE_TRANSACTION": [{"id": "mandat_type", "value": "gestion", "type": "DIFFERENT"}], "COMPLEMENT": [{"id": "loyer_complement", "type": "EQUALS", "value": "oui"}], "CONSTRUCTION_2023": [{"id": "construction_autorisation_date", "type": "DATE_NOT_OLDER_THAN", "value": "2022-12-31"}], "TRAVAUX_ENERGETIQUE": [{"id": "travaux_energetique", "value": "oui", "type": "EQUALS"}], "CIL_TRAVAUX": [{"id": "carnet_information_travaux_statut", "value": "oui", "type": "EQUALS"}], "DPE_ANNEXE_IDENTIQUE": [{"id": "diagnostic_annexe", "value": "oui", "type": "DIFFERENT"}], "BAIL_SAISONNIER": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["IMMOBILIER_LOCATION_BAIL_SAISONNIER"]}], "MESURAGE_PAS_PRO": [{"id": "superficie_carrez_professionnel", "type": "EQUALS", "value": "non"}], "OP_SUCCESSION_SIMPLE": [{"id": "origine_liste_giboire", "value": "origine_propriete_succession", "type": "EQUALS"}], "SECURITE_SALUBRITE": [{"id": "salubrite_arretes_statut", "value": "oui", "type": "EQUALS"}], "SERVITUDES": [], "DIAGNOSTIQUEUR_LIBRE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__CDC__IMMOBILIER__VENTE", "OPERATION__MA_GESTION_LOCATIVE__LOCATION"]}], "VENTE_SOCIAL_OCCUPE": [{"id": "occupation_sociale", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__FICHE_VENTE", "VENTE", "0"], "type": "DIFFERENT", "value": "non"}], "ATTESTATION_40_M2": [{"id": "dpe_40_m2_statut", "value": "oui", "type": "EQUALS"}], "ZONE_TERMITES_PROGRAMME_CDC": [{"id": "programme_zone_termites", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "PERMIS_NON_OBTENU": [{"id": "belgique_travaux_permis_obtenu", "value": "non", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_TOITURE": [{"id": "belgique_poste_toiture", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_MENUISERIE": [{"id": "belgique_poste_menuiserie", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_ELECTRIQUE": [{"id": "belgique_poste_electrique", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_SANITAIRE": [{"id": "belgique_poste_sanitaire", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_CHAUFFAGE": [{"id": "belgique_poste_chauffage", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_REVETEMENT_SOL": [{"id": "belgique_poste_revetement_sol", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_REVETEMENT_MUR": [{"id": "belgique_poste_revetement_mur", "value": "autre", "type": "EQUALS"}], "DEBROUSSAILLEMENT": [{"id": "obligation_debroussaillement_statut", "value": "oui", "type": "EQUALS"}], "ENTRETIEN_CHAUDIERE": [{"id": "chaudiere_entretien_statut", "value": "oui", "type": "EQUALS"}], "CUVE_CONTRAT": [{"id": "cuve_fioul_contrat", "value": "oui", "type": "EQUALS"}], "CHAUDIERE_AUTRE": [{"id": "chaudiere_statut_type", "value": "autre", "type": "EQUALS"}], "OPERATION_TEMPLATE_AFFICHAGE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__CDC__IMMOBILIER__VENTE"]}], "TEMPLATE_PRECISION_DEROGATION": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP"]}], "PPRN_SIMPLE": [{"id": "erp_pprn_bien", "type": "EQUALS", "value": "oui"}], "PPRM_SIMPLE": [{"id": "erp_pprm_bien", "type": "EQUALS", "value": "oui"}], "PPRT_SIMPLE": [{"id": "erp_pprt_bien", "type": "EQUALS", "value": "oui"}], "RETRAIT_COTE": [{"id": "erp_retrait_cote_bien", "type": "EQUALS", "value": "oui"}], "RADON": [{"id": "potentiel_radon_classe", "type": "EQUALS", "value": "oui"}], "INDEMINTE_CATASTROPHE": [{"id": "indemnite_catastrophe", "type": "EQUALS", "value": "oui"}], "SISMICITE_2": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 2}], "SISMICITE_3": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 3}], "SISMICITE_4": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 4}], "SISMICITE_5": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 5}], "ZONE_TERMITES_OBLIGATOIRE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AQUITANIS__VENTE_ANCIEN", "OPERATION__AQUITANIS__PROGRAMME_ANCIEN"]}], "NUMERO_UG": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__CDC__IMMOBILIER__VENTE"]}], "NUMERO_REFERENCE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AQUITANIS__PROGRAMME_ANCIEN", "OPERATION__AQUITANIS__VENTE_ANCIEN", "OPERATION__AQUITANIS__PROGRAMME_BRS", "OPERATION__AQUITANIS__RESERVATION_BRS"]}], "AMIANTE_PARKING_EXTERIEUR": [{"id": "parking_exterieur", "type": "DIFFERENT", "value": "oui"}], "PRIX_REMISE": [{"id": "programme_prix_vente_remise", "type": "EQUALS", "value": "oui"}], "TEMPLATE_GESTION": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__BENEDIC__DOSSIER_DE_LOCATION_BENEDIC"]}], "DESIGNATION_TRAVAUX": [{"id": "designation_modifiee", "type": "EQUALS", "value": "oui"}], "PREPARATION_EFFICITY": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["EFFICITY__TRANSACTION__PREPARATION_COMPROMIS"]}]}}