// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationImmobilierConstructionMaisonIndividuelleConstruction: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'signature_electronique',
          label: 'Le contrat sera-t-il signé de manière électronique ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'acceptation_recommande_electronique',
          label: "Le maître de l'ouvrage accepte-t-il de recevoir des notifications par recommandé électronique ?",
          type: 'SELECT-BINARY'
        }
      ],
      id: 'd4e0e5b5_a308_493e_8eaf_dabb196bb5b0',
      label: 'Signature et notification',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'maison_type',
          label: 'Type de maison',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'maison_usage_habitation',
              label: "Usage exclusif d'habitation"
            },
            {
              id: 'maison_usage_mixte',
              label: "Usage professionnel et d'habitation"
            }
          ],
          id: 'maison_usage',
          label: 'Le maison est à usage',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'maison_usage_personnel',
          label: "Le maître de l'ouvrage construit-il pour son propre usage ?",
          type: 'SELECT-BINARY'
        }
      ],
      id: '91121d99_6190_46ce_bcc6_b70b32c95da7',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'titre_propriete',
              label: "D'un titre de propriété ou d'un titre justifiant d'un droit de construire"
            },
            {
              id: 'titre_promesse',
              label: "D'une promesse de vente"
            }
          ],
          id: 'titre',
          label: "Le maître de l'ouvrage est titulaire",
          type: 'SELECT'
        },
        {
          before: {
            type: 'N_DAYS_FROM_NOW',
            value: '0'
          },
          id: 'titre_date',
          label: "Date de l'acte",
          type: 'DATE'
        },
        {
          id: 'titre_redacteur_nom',
          label: "Nom du rédacteur de l'acte",
          type: 'TEXT'
        },
        {
          id: 'titre_redacteur_adresse',
          label: "Adresse du rédacteur de l'acte",
          type: 'ADDRESS'
        }
      ],
      id: '463c0b05_62a8_4bec_9b7e_5b719daa0bb9',
      label: 'Information sur le titre de propriété',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_geotechnique_zone_concernee',
          label: 'Le bien est-il situé dans une zone concernée par la réalisation d’une étude géotechnique ?',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'geotechnique_zone_moyen',
                  label: 'Zone à risques moyens'
                },
                {
                  id: 'geotechnique_zone_fort',
                  label: 'Zone à risques forts'
                }
              ],
              id: 'diagnostic_geotechnique_zone_type',
              label: 'Type de zone',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'geotechnique_prealable',
                  label: "Une étude géotechnique préalable est fournie par le maître de l'ouvrage"
                },
                {
                  id: 'geotechnique_conception',
                  label:
                    "Une étude géotechnique prenant en compte l'implatation des bâtiments est fournie par le maître de l'ouvrage"
                },
                {
                  id: 'geotechnique_constructeur',
                  label: 'Une étude géotechnique est réalisée par le constructeur'
                }
              ],
              id: 'geotechnique_statut',
              label: "Concernant l'étude géotechnique",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'geotechnique_recommandations_suivies',
                  label: "Devra suivre les recommandations de l'étude"
                },
                {
                  id: 'geotechnique_recommandations_techniques_constructions',
                  label: 'Devra respecter les techniques particulières de construction'
                },
                {
                  id: 'geotechnique_recommandations_sans',
                  label: "N'a pas à opter pour une de ces deux solutions"
                }
              ],
              id: 'geotechnique_recommandations',
              label: 'Le constructeur',
              type: 'SELECT'
            }
          ],
          conditions: [
            [
              {
                id: 'diagnostic_geotechnique_zone_concernee',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'bdeabeb9_7b86_49b6_8cfe_d1bbd9a10a2a',
          label: 'CONDITION_BLOCK_Zone concernée par une étude géotechnique',
          type: 'CONDITION_BLOCK'
        }
      ],
      id: '2105ee40_d5ce_4baa_b120_5df8b23a23e7',
      label: 'Etude géotechnique',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'prix_travaux_definitif',
          label: 'Prix convenu (coût du bâtiment hors travaux réservés)',
          type: 'PRICE'
        },
        {
          id: 'prix_travaux_mo_montant',
          label: "Montant des travaux à la charge du maître de l'ouvrage",
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'prix_travaux_revision_fixe',
              label: "Selon l'article 3-2a)"
            },
            {
              id: 'travaux_revision_variable',
              label: "Selon l'article 3-2b)"
            },
            {
              id: 'prix_travaux_revision_sans',
              label: 'Le prix ne sera pas révisable'
            }
          ],
          id: 'prix_travaux_revision',
          label: 'La révision du prix sera effectuée',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'prix_garantie_remboursement',
              label: "Selon l'article 3-3a) (avec garantie de remboursement)"
            },
            {
              id: 'prix_garantie_remboursement_sans',
              label: "Selon l'article 3-3b) (sans garantie de remboursement)"
            }
          ],
          id: 'prix_garantie',
          label: "Le prix convenu est payé en application de l'article",
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'prix_garantie',
                type: 'EQUALS',
                value: 'prix_garantie_remboursement'
              }
            ]
          ],
          id: 'prix_acompte_montant',
          label: "Montant de l'acompte versé",
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'prix_garantie',
                type: 'EQUALS',
                value: 'prix_garantie_remboursement_sans'
              }
            ]
          ],
          id: 'prix_acompte_option',
          label: 'Un dépôt de garantie est-il prévu au contrat ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'prix_acompte_option',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'prix_garantie',
                type: 'EQUALS',
                value: 'prix_garantie_remboursement_sans'
              }
            ]
          ],
          id: 'prix_depot_garantie_montant',
          label: 'Montant du dépôt de garantie',
          type: 'PRICE'
        }
      ],
      id: '40e713b6_b89b_414a_8813_7a1eeabce33c',
      label: 'Coût de la construction et Prix',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'delai_communication_document',
          label: "Délai de communication des documents de l'article 1-4 par le maître de l'ouvrage",
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          id: 'delai_conditions_suspensives',
          label: 'Délai de réalisation des conditions suspensives',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          id: 'delai_depot_permis',
          label: 'Délai de dépôt du permis de construire',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          id: 'delai_debut_travaux',
          label: 'Délai de commencement des travaux',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          id: 'delai_duree_travaux',
          label: "Durée d'exécution des travaux",
          suffix: 'mois',
          type: 'NUMBER'
        }
      ],
      id: '32925b25_f23f_4358_bcae_d8a9c57d3283',
      label: 'Délais',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'assurance_ouvrage_prix_inclus',
              label:
                "L'assurance dommage-ouvrage est souscrite par le constructeur et son coût est compris dans le prix"
            },
            {
              id: 'assurance_ouvrage_prix_non_inclus',
              label:
                "L'assurance dommage-ouvrage est souscrite par le constructeur et son coût n'est pas compris dans le prix"
            },
            {
              id: 'assurance_ouvrage_mo',
              label: "L'assurance dommage-ouvrage est souscrite directement par le maître de l'ouvrage"
            }
          ],
          id: 'assurance_ouvrage_statut',
          label: "Concernant l'assurance dommage-ouvrage",
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'assurance_ouvrage_statut',
                type: 'EQUALS',
                value: 'assurance_ouvrage_prix_non_inclus'
              }
            ]
          ],
          id: 'assurance_ouvrage_constructeur_montant',
          label: "Montant de l'assurance dommage-ouvrage",
          type: 'PRICE'
        }
      ],
      id: '57aa83fe_feca_4856_aed5_4928b031d2cb',
      label: 'Assurances',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'pieces_annexes_echanges',
              label: "L'historique des échanges"
            },
            {
              id: 'pieces_annexes_autres',
              label: 'Autres annexes'
            },
            {
              id: 'pieces_annexes_aucune',
              label: 'Aucune autre annexe'
            }
          ],
          id: 'pieces_annexes',
          label: 'Quelles pièces sont annexées au contrat, en plus du plan et de la notice descriptive ?',
          multiple: true,
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'pieces_annexes',
                type: 'CONTAINS',
                value: 'pieces_annexes_autres'
              }
            ]
          ],
          id: 'pieces_annexes_autres_liste',
          label: 'Précisez les autres pièces annexes',
          type: 'TEXTAREA'
        },
        {
          id: 'notice_informative',
          label: 'Notice descriptive',
          templateId: 'NoticeDescriptive.docx',
          type: 'UPLOAD'
        },
        {
          id: 'plans_construction',
          label: 'Plans',
          type: 'UPLOAD'
        },
        {
          id: 'document_information_precontractuelle',
          label: "Document d'Information Précontractuelle",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'signature_electronique',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'declaration_usage_maison',
          label: "Déclaration d'usage de la maison",
          templateId: 'DeclarationUsageMaisonIndividuelle.pdf',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'prix_travaux_revision',
                type: 'EQUALS',
                value: 'prix_travaux_revision_fixe'
              }
            ],
            [
              {
                id: 'prix_travaux_revision',
                type: 'EQUALS',
                value: 'prix_travaux_revision_variable'
              }
            ],
            [
              {
                id: 'signature_electronique',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'modalite_prix',
          label: 'Modalité de révision du prix',
          templateId: 'ModalitePrixConvenu.pdf',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pieces_annexes',
                type: 'CONTAINS',
                value: 'pieces_annexes_echanges'
              }
            ]
          ],
          id: 'historique_echange',
          label: 'Historique des échanges',
          type: 'UPLOAD'
        }
      ],
      id: '28c78ca8_f302_4c7b_beeb_58f1afcc4b09',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                id: 'emprunt_statut',
                recordPath: ['OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__FICHES', 'FINANCEMENT', '0'],
                type: 'EQUALS',
                value: 'non'
              },
              {
                id: 'signature_electronique',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'mention_manuscrite',
          label: 'Déclaration de non-recours à un prêt',
          templateId: 'MentionManuscriteFinancement.pdf',
          type: 'UPLOAD'
        }
      ],
      id: '9fb31277_6f57_440a_9cfd_9249aaa99584',
      label: 'Financement',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sollicitation_constructeur',
          label: 'Le client souhaite-t-il recevoir les actualités et sollicitation du constructeur ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sollicitation_partenaires',
          label: 'Le client souhaite-t-il recevoir les actualités et sollicitation des partenaires du constructeur ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'dip_signature_electronique',
          label: "Signature électronique du document d'information précontractuelle",
          type: 'SELECT-BINARY'
        }
      ],
      id: 'b718c50d_8233_4e7a_b496_83eec311730d',
      label: 'Attestation du client',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_numero_statut',
          label: "Ajouter un numéro d'avenant",
          type: 'SELECT-BINARY'
        },
        {
          id: 'avenant_numero',
          label: "Numéro de l'avenant",
          type: 'TEXT'
        },
        {
          id: 'avenant_date_signature_initial',
          label: 'Date de signature du contrat de construction intial',
          type: 'DATE'
        },
        {
          id: 'avenant_lieu_signature_initial',
          label: 'Lieu de signature du contrat de construction intial',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'travaux_mo',
              label: 'Travaux supplémentaires charge Maître Ouvrage'
            },
            {
              id: 'travaux_constructeur',
              label: 'Travaux supplémentaires charge Constructeur'
            },
            {
              id: 'delai_construction',
              label: 'Prolongation du délai de construction'
            },
            {
              id: 'delai_financement',
              label: 'Prolongation du délai de financement'
            },
            {
              id: 'libre',
              label: 'Avenant libre'
            }
          ],
          id: 'avenant_objet_type',
          label: "Objet de l'avenant",
          multiple: true,
          type: 'SELECT'
        },
        {
          children: [
            {
              id: 'avenant_objet_type_travaux_mo',
              label: "Travaux à la charge du Maître de l'Ouvrage",
              type: 'TEXTAREA'
            },
            {
              id: 'avenant_objet_type_travaux_mo_cout',
              label: "Coût des travaux à la charge du Maître de l'Ouvrage",
              type: 'PRICE'
            },
            {
              id: 'avenant_objet_type_travaux_mo_cout_total',
              label: "Prix global de la construction à la charge du Maître de l'Ouvrage",
              type: 'PRICE'
            }
          ],
          id: '15ccbfhc4_dy43_222a_ck66_12748ec5567y',
          label: "Travaux - Maître de l'Ouvrage",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'avenant_objet_type_travaux_constructeur',
              label: 'Travaux à la charge du Constructeur',
              type: 'TEXTAREA'
            },
            {
              id: 'avenant_objet_type_travaux_constructeur_cout',
              label: 'Coût des travaux à la charge du Constructeur',
              type: 'PRICE'
            },
            {
              id: 'avenant_objet_type_travaux_constructeur_cout_total',
              label: 'Prix global de la construction à la charge du Constructeur',
              type: 'PRICE'
            }
          ],
          id: '15ccbfhc4_fo34_55tf_09ff_12748ec5cp66',
          label: 'Travaux - Constructeur',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'avenant_objet_clause_libre',
              label: "Texte de l'avenant libre",
              type: 'TEXTAREA'
            }
          ],
          id: '15ccbfhc4_fo34_fg44_2edf_df658ec5dp33',
          label: 'Avenant Libre',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'avenant_objet_travaux_duree',
              label: 'Durée de prolongation des travaux',
              type: 'TEXT'
            },
            {
              id: 'avenant_objet_duree_travaux_duree_totale',
              label: 'Durée totale des travaux',
              type: 'TEXT'
            }
          ],
          id: 'df23bfpo5_fo34_fg44_56rf_df658ec5fo44',
          label: 'Prolongation des travaux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'avenant_objet_duree_financement_date',
              label: "Nouvelle date d'obtention du financement",
              type: 'DATE'
            }
          ],
          id: 'df23bfg45_gp45_5h5y_99iy_dgpo67ec5fpo5',
          label: 'Prolongation financement',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'avenant_signature_electronique',
              label: 'Signature electronique',
              type: 'SELECT-BINARY'
            }
          ],
          id: 'df23bfg45_23yp_gfp4_1fp4_dgfpo4ec5dfp4',
          label: 'Clotûre',
          type: 'CATEGORY'
        }
      ],
      id: '15ccbc5b_3aac_47c3_81d9_12748ec56595',
      label: 'Avenant',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__CONSTRUCTION',
  label: 'Construction',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__CONSTRUCTION',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'CONSTRUCTION_MAISON_INDIVIDUELLE', 'CONSTRUCTION'],
  type: 'RECORD'
};
