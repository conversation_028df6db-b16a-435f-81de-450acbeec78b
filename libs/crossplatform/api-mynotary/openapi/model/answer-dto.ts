import { AdditionalDocumentDto } from '../model/additional-document-dto';
import { AnswerValueDto } from '../model/answer-value-dto';

/**
 * AnswerDto has been modified manually because we can't add nullable attribute on additionalProperties in `api-mynotary.openapi.yaml`.
 * This restriction prevent `answer.creating` and `answer.additionalDocuments` to have correct type.
 */
export interface AnswerDto {
  additionalDocuments?: { [key: string]: AdditionalDocumentDto | null };
  creating?: { [key: string]: boolean | null };
  locked?: boolean;
  value?: AnswerValueDto | null;
}
