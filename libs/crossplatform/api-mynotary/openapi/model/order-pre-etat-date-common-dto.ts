/**
 * main-api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PreEtatDateLotDto } from './pre-etat-date-lot-dto';
import { AddressDto } from './address-dto';


export interface OrderPreEtatDateCommonDto { 
    contactCivilite: string;
    contactEmail: string;
    contactFirstname: string;
    contactLastname: string;
    contactPhone: string;
    coproprieteAdresse: AddressDto;
    destinationLegalRecordId: string;
    lots: Array<PreEtatDateLotDto>;
    syndicIdentifiant: string;
    syndicName: string;
    syndicPassword: string;
    type: OrderPreEtatDateCommonDtoTypeEnum;
    venteTousLesLotsCopropriete: boolean;
}
export enum OrderPreEtatDateCommonDtoTypeEnum {
    PRE_ETAT_DATE = 'PRE_ETAT_DATE'
};



