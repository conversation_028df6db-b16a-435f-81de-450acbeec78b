/**
 * main-api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface LegalLinkNewDto { 
    /**
     * The ID of the contract when the link is specific to a contract
     */
    contractId?: string;
    /**
     * the ID of the user creating the link
     */
    creatorId: string;
    /**
     * the ID of the from legal entity to link
     */
    fromLegalId: string;
    /**
     * the ID of the template for the link
     */
    legalLinkTemplateId: string;
    /**
     * The ID of the other record to link
     */
    toLegalId?: string;
    /**
     * The token to use for authentication in case of anonymous user
     */
    token?: string;
}

