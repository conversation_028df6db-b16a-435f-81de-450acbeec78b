/**
 * main-api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { RegisterLetterReceiverDto } from './register-letter-receiver-dto';


export interface RegisteredLetterToSendDto { 
    batchId: string;
    creationTime: string;
    cancelationTime?: string | null;
    id: string;
    letterFileId: string;
    receiver: RegisterLetterReceiverDto;
    status: RegisteredLetterToSendDtoStatusEnum;
}
export enum RegisteredLetterToSendDtoStatusEnum {
    TO_SEND = 'TO_SEND'
};



