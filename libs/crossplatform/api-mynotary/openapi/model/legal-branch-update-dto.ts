/**
 * main-api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface LegalBranchUpdateDto { 
    /**
     * The ID of the existing record to associate with the branch
     */
    toLegalId: string;
    /**
     * The token to use for authentication in case of anonymous user
     */
    token?: string;
}

