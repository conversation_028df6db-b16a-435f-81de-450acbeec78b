/**
 * main-api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface LegalBranchDto { 
    /**
     * id of the legal record or legal operation from which the branch originates
     */
    fromLegalId: string;
    /**
     * LegalOperationTemplateId | LegalRecordTemplateId of the from legal entity
     */
    fromLegalTemplateId: string;
    id: string;
    /**
     * id of the legal link owning the branch
     */
    legalLinkId: string;
    /**
     * type LegalLinkTemplateId in core
     */
    legalLinkTemplateId: string;
    reverseType?: string;
    toLegalId?: string;
    /**
     * LegalOperationTemplateId | LegalRecordTemplateId of the to legal entity
     */
    toLegalTemplateId?: string;
    type: string;
}

