openapi: 3.0.0
info:
  title: organizations
  version: '1.0'
paths:
  '/organizations':
    get:
      tags: ['Organizations']
      summary: Get Organizations
      description: Retrieve a list of organizations
      parameters:
        - in: query
          name: subscriptionId
          schema:
            type: string
          description: Subscription ID to filter organizations.
        - in: query
          name: legalOperationTemplateId
          schema:
            type: string
          description: If set, only organizations with access to this legal operation template will be returned.
        - in: query
          name: operationId
          schema:
            type: string
          description: find organization hosting the given operation
        - in: query
          name: uniqueIdentifier
          schema:
            type: string
          description: this is the unique identifier of the organization (siren concatenated with zip code)
        - in: query
          name: type
          schema:
            type: string
        - in: query
          name: ids
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organizations'
    post:
      tags: ['Organizations']
      summary: Create organization
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrganizationNew'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
  '/organizations/{organizationId}':
    get:
      tags: ['Organizations']
      summary: Get organization by ID
      parameters:
        - schema:
            type: string
          name: organizationId
          in: path
          required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
    patch:
      tags: ['Organizations']
      summary: Update organization properties
      parameters:
        - schema:
            type: string
          name: organizationId
          in: path
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrganizationUpdate'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
    delete:
        tags: ['Organizations']
        summary: Delete organization by ID
        description: Deletes an organization by its ID. Requires admin-level authorization with at least `AdminType.COLLABORATORS`.
        parameters:
          - in: path
            name: organizationId
            schema:
              type: string
            required: true
            description: The unique identifier of the organization to delete.
        responses:
          '204':
            description: Organization successfully deleted
          '403':
            description: Forbidden - User does not have the required admin level
          '404':
            description: Organization not found

components:
  schemas:
    Organizations:
      type: object
      properties:
        items:
          type: array
          description: List of organizations.
          items:
            $ref: '#/components/schemas/Organization'
      required:
        - items
    Organization:
      oneOf:
        - $ref: '#/components/schemas/MnOrganization'
        - $ref: '#/components/schemas/PlOrganization'
    PlOrganization:
      allOf:
        - $ref: '#/components/schemas/CommonOrganization'
        - type: object
          properties:
            antsRecoveryDelay:
              type: number
              nullable: true
            crpcen:
              type: string
            type:
              $ref: '#/components/schemas/PlOrganizationType'
            fax:
              type: string
            iban:
              $ref: '#/components/schemas/Iban'
            interopAppId:
              type: number
            interopVersion:
              type: string
              enum:
                - V3
                - V4
            notaryOfficeType:
              type: string
              enum:
                - NOT
                - SCP
                - SEL
                - SELARL
            phone:
              type: string
          required:
            - crpcen

    MnOrganization:
      allOf:
        - $ref: '#/components/schemas/CommonOrganization'
        - type: object
          properties:
            type:
              $ref: '../shared-components/organization-type.openapi.yaml#/OrganizationType'
    CommonOrganization:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the organization.
        name:
          type: string
          description: Name of the organization.
        subscriptionId:
          type: string
          nullable: true
          description: Subscription ID associated with the organization. Can be null if not subscribed.
        address:
          $ref: '../shared-components/address.openapi.yaml#/Address'
        apiKey:
          type: string
        creationTime:
          type: string
          format: date-time
        uniqueIdentifier:
          type: string
        rib:
          type: string
        email:
          type: string
        hubspotId:
          type: string
        hasHoldingSubscription:
          type: boolean
        adminRoleId:
          type: string
          nullable: true
        creatorUserId:
          type: string
          nullable: true
      required:
        - id
        - name
        - address
        - apiKey
        - creationTime
        - type
        - hasHoldingSubscription
        - uniqueIdentifier
    OrganizationUpdate:
      type: object
      properties:
        antsRecoveryDelay:
          type: number
          nullable: true
        id:
          type: string
          description: Unique identifier for the organization.
        name:
          type: string
          description: Name of the organization.
        subscriptionId:
          type: string
          nullable: true
          description: Subscription ID associated with the organization. Can be null if not subscribed.
        address:
          $ref: '../shared-components/address.openapi.yaml#/Address'
        rib:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        hubspotId:
          type: string
          nullable: true
        siren:
          type: string
        crpcen:
          type: string
        fax:
          type: string
          nullable: true
        iban:
          $ref: '#/components/schemas/Iban'
        interopAppId:
          type: number
        interopVersion:
          type: string
          enum:
            - V3
            - V4
        notaryOfficeType:
          type: string
          enum:
            - NOT
            - SCP
            - SEL
            - SELARL
        phone:
          type: string
          nullable: true


    OrganizationNew:
      oneOf:
        - $ref: '#/components/schemas/MnOrganizationNew'
        - $ref: '#/components/schemas/PlOrganizationNew'
    PlOrganizationNew:
      allOf:
        - $ref: '#/components/schemas/CommonOrganizationNew'
        - $ref: '#/components/schemas/PlOrganizationNewSpecific'
    PlOrganizationNewSpecific:
      type: object
      properties:
        antsRecoveryDelay:
          type: number
          nullable: true
        creatorUserId:
          type: string
        crpcen:
          type: string
        type:
          type: string
          enum:
            - PORTALYS_NOTARY_OFFICE
        email:
          type: string
          nullable: true
        fax:
          type: string
          nullable: true
        iban:
          $ref: '#/components/schemas/Iban'
        interopAppId:
          type: number
          nullable: true
        interopVersion:
          type: string
          nullable: true
          enum:
            - V3
            - V4
        name:
          type: string
        notaryOfficeType:
          type: string
          enum:
            - NOT
            - SCP
            - SEL
            - SELARL
        phone:
          type: string
          nullable: true
      required:
        - crpcen
        - type
    MnOrganizationNew:
      allOf:
        - $ref: '#/components/schemas/CommonOrganizationNew'
        - type: object
          properties:
            type:
              $ref: '../shared-components/organization-type.openapi.yaml#/OrganizationType'
    CommonOrganizationNew:
      type: object
      properties:
        address:
          $ref: '../shared-components/address.openapi.yaml#/Address'
        name:
          type: string
        parentOrganizationId:
          type: string
        siren:
          type: string
        crpcen:
          type: string
        ribFileId:
          type: string
        creatorUserId:
          type: string
        hubspotId:
          type: string
      required:
        - address
        - name
        - type
    PlOrganizationType:
      type: string
      enum:
        - PORTALYS_NOTARY_OFFICE
    Iban:
      type: object
      nullable: true
      properties:
        bban:
          type: string
        bic:
          type: string
        cle:
          type: string
        domiciliation:
          type: string
        guichet:
          type: string
          nullable: true
        pays:
          type: string
        titulaire:
          type: string
          nullable: true
      required:
        - bban
        - bic
        - cle
        - domiciliation
        - pays
