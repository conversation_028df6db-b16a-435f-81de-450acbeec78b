openapi: 3.0.0
info:
  title: organization-data-transfers
  version: '1.0'
paths:
  '/operation-transfers':
    post:
      summary: Transfer operation to another organization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OperationTransfer'
      responses:
        '200':
          description: No Content
      description: ...
      tags: ['Organization Data Transfers']
  '/record-transfers':
    post:
      summary: Transfer record to another organization
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RecordTransfer'
      responses:
        '200':
          description: No Content
      description: ...
      tags: [ 'Organization Data Transfers' ]

components:
  schemas:
    OperationTransfer:
      type: object
      properties:
        operationId:
          type: string
        organizationId:
          type: string
      required:
          - operationId
          - organizationId
    RecordTransfer:
      type: object
      properties:
        recordId:
          type: string
        organizationId:
          type: string
      required:
        - recordId
        - organizationId
