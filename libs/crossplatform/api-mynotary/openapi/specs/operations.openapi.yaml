openapi: 3.0.0
info:
  title: operations
  version: '1.0'
paths:
  '/operation-duplicates':
    post:
      tags: ['Operations']
      summary: Operation is duplicated
      requestBody:
        content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationDuplicatesBody'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationDuplicates'
  '/operations/{operationId}':
    put:
      summary: Update operation
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                items:
                  $ref: '#/components/schemas/Operation'
      description: Update operation
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OperationUpdate'
      tags: ['Operations']
  '/operations':
    parameters:
      - in: query
        name: userId
        required: true
        schema:
          type: string
      - in: query
        name: limit
        schema:
          type: integer
        description: The numbers of items to return
    get:
      summary: Get user's operation
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationList'
      tags: ['Operations']
  '/operation-views':
    post:
      tags: ['Operations']
      summary: Update operation view
      responses:
        '200':
          description: OK
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OperationView'
    parameters:
      - schema:
          type: string
        name: operationId
        in: path
        required: true

components:
  schemas:
    OperationUpdate:
      title: OperationUpdate
      type: object
      properties:
        label:
          type: string
        tags:
          $ref: '#/components/schemas/OperationTags'
    Operation:
      title: Operation
      type: object
      properties:
        id:
          type: string
          example: operation_1
        templateId:
          type: string
          example: '1'
        createdAt:
          type: string
          format: date-time
        creatorId:
          type: string
        organizationId:
          type: string
          example: organization-123
        label:
          type: string
        tags:
          $ref: '#/components/schemas/OperationTags'
      required:
        - id
        - templateId
        - createdAt
        - creatorId
        - organizationId
        - label
        - tags
    OperationTags:
      type: object
      additionalProperties:
        type: array
        items:
          type: string
    OperationDuplicatesBody:
      title: OperationDuplicatesBody
      type: object
      properties:
        creatorUserId:
          type: string
        operationId:
          type: string
      required:
        - creatorUserId
        - operationId
    OperationDuplicates:
      title: OperationDuplicates
      type: object
      properties:
        operationId:
          type: string
      required:
        - operationId
    OperationView:
      type: object
      properties:
        userId:
          type: string
        organizationId:
          type: string
        operationId:
          type: string
      required:
        - userId
        - organizationId
        - operationId
    OperationList:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Operation'
      required:
        - items
