openapi: 3.0.0
info:
  title: registered-letters
  version: '1.0'

paths:
  '/registered-letters':
    get:
      tags: [ 'Registered Letters' ]
      summary: Check submitted registered letters
      parameters:
        - name: startTime
          in: query
          schema:
            type: string
            format: date-time
        - name: endTime
          in: query
          schema:
            type: string
            format: date-time
        - name: status
          in: query
          schema:
            type: string
            enum: [TO_SEND, SUBMITTED, SENT, ACCEPTED, REFUSED, EXPIRED, BOUNCED, ERROR, INVALID_EMAIL, AWAITING_RECIPIENT_UPDATE]
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisteredLetterList'
          description: OK

  '/registered-letter-archives/{batchId}':
    get:
      tags: [ 'Registered Letters' ]
      summary: Create registered letter archive
      parameters:
        - name: token
          in: query
          required: true
          schema:
            type: string
        - name: batchId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisteredLetterBatchArchive'
  '/registered-letter-batches/{batchId}':
    get:
      tags: [ 'Registered Letters' ]
      summary: get batch
      parameters:
        - name: batchId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisteredLetterBatch'
  '/registered-letters-sync/{letterId}':
    post:
      tags: [ 'Registered Letters' ]
      summary: Sync registered letters
      parameters:
        - name: letterId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RegisteredLettersAr24Sync'
      responses:
        '200':
          description: OK
          content:
            application/json: { }
  '/registered-letter-receiver':
    post:
      tags: [ 'Registered Letters' ]
      summary: Update receiver
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReceiverUpdate'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Receiver'
components:
  schemas:
    RegisteredLetterArchives:
      type: object
      properties:
        token:
          type: string
      required:
        - token
    RegisteredLettersAr24Sync:
      type: object
      properties:
        id_mail:
          type: string
        new_state:
          $ref: '#/components/schemas/Ar24Status'
        proof_url:
          type: string
        ts_dp_date:
          type: string
        view_date:
          type: string
        refused_date:
          type: string
        negligence_date:
          type: string
        ts_ev_date:
          type: string
        ts_bc_date:
          type: string
        id_consent:
          type: string
        id_recipient_update:
          type: string
      required:
        - id_mail
        - new_state
    Ar24Status:
      type: string
      enum:
        - dp
        - ev
        - AR
        - refused
        - negligence
        - bounced
        - fail
        - consent
        - optimal_choice_done
        - cancelled_by_user
        - cancelled_after_7_days
        - recipient_update
        - price_set_to_free
        - waiting_manual_process

    Contact:
      type: object
      properties:
        email:
          type: string
        firstname:
          type: string
        lastname:
          type: string
        organizationAddress:
          type: string
        organizationName:
          type: string
        phone:
          type: string
      required:
        - email
        - firstname
        - lastname
        - organizationAddress
        - organizationName

    RegisteredLetterBatchArchive:
      type: object
      properties:
        folder:
          $ref: '../shared-components/folder.openapi.yaml#/Folder'
        sender:
          $ref: '#/components/schemas/Contact'
      required:
        - folder
        - sender

    ReceiverUpdate:
      type: object
      properties:
        isAccepted:
          type: boolean
        letterId:
          type: string
      required:
        - isAccepted
        - letterId

    Receiver:
      type: object
      properties:
        email:
          type: string
        firstname:
          type: string
        lastname:
          type: string
        civility:
          $ref: '../shared-components/civility.openapi.yaml#/Civility'
      required:
        - civility
        - email
        - firstname
        - lastname

    RegisteredLetterBatch:
        type: object
        properties:
          creationContext:
            type: string
          cancelationTime:
            type: string
            format: date-time
            nullable: true
          creationTime:
            type: string
            format: date-time
          draft:
            type: boolean
          contractId:
            type: string
          creatorFirstname:
            type: string
          creatorId:
            type: string
          creatorLastname:
            type: string
          id:
            type: string
          label:
            type: string
          letters:
            type: array
            items:
              $ref: '#/components/schemas/RegisteredLetter'
          operationId:
            type: string
          organizationId:
            type: string
          subscribers:
            type: array
            items:
              $ref: '#/components/schemas/RegisteredLetterBatchSubscriber'
        required:
          - creationTime
          - draft
          - contractId
          - creatorFirstname
          - creatorId
          - creatorLastname
          - id
          - label
          - letters
          - operationId
          - organizationId
          - subscribers
          - creationContext
    RegisteredLetter:
      oneOf:
        - $ref: '#/components/schemas/RegisteredLetterToSend'
        - $ref: '#/components/schemas/RegisteredLetterError'
        - $ref: '#/components/schemas/RegisteredLetterInvalidEmail'
        - $ref: '#/components/schemas/RegisteredLetterSubmitted'
        - $ref: '#/components/schemas/RegisteredLetterBounced'
        - $ref: '#/components/schemas/RegisteredLetterSent'
        - $ref: '#/components/schemas/RegisteredLetterAwaitingRecipientUpdate'
        - $ref: '#/components/schemas/RegisteredLetterAccepted'
        - $ref: '#/components/schemas/RegisteredLetterExpired'
        - $ref: '#/components/schemas/RegisteredLetterRefused'
    RegisteredLetterBase:
      type: object
      properties:
        batchId:
          type: string
        creationTime:
          type: string
          format: date-time
        cancelationTime:
          type: string
          format: date-time
          nullable: true
        id:
          type: string
        letterFileId:
          type: string
        receiver:
          $ref: '#/components/schemas/RegisterLetterReceiver'
      required:
        - creationTime
        - batchId
        - id
        - letterFileId
        - receiver
    RegisteredLetterToSend:
      allOf:
        - $ref: '#/components/schemas/RegisteredLetterBase'
        - type: object
          properties:
            status:
              type: string
              enum: [TO_SEND]
          required:
            - status
    RegisteredLetterError:
      allOf:
        - $ref: '#/components/schemas/RegisteredLetterBase'
        - type: object
          properties:
            providerId:
              type: string
            status:
              type: string
              enum: [ERROR]
          required:
            - providerId
            - status
    RegisteredLetterInvalidEmail:
      allOf:
        - $ref: '#/components/schemas/RegisteredLetterBase'
        - type: object
          properties:
            status:
              type: string
              enum: [INVALID_EMAIL]
          required:
            - status
    RegisteredLetterSubmitted:
      allOf:
        - $ref: '#/components/schemas/RegisteredLetterBase'
        - type: object
          properties:
            providerId:
              type: string
            status:
              type: string
              enum: [SUBMITTED]
          required:
            - status
    RegisteredLetterBounced:
      allOf:
        - $ref: '#/components/schemas/RegisteredLetterBase'
        - type: object
          properties:
            providerId:
              type: string
            status:
              type: string
              enum: [BOUNCED]
          required:
            - providerId
            - status
    RegisteredLetterSent:
      allOf:
        - $ref: '#/components/schemas/RegisteredLetterBase'
        - type: object
          properties:
            depositFileId:
              type: string
            depositTime:
              type: string
              format: date-time
            providerId:
              type: string
            status:
              type: string
              enum: [SENT]
          required:
            - depositFileId
            - depositTime
            - providerId
            - status
    RegisteredLetterAwaitingRecipientUpdate:
      allOf:
        - $ref: '#/components/schemas/RegisteredLetterBase'
        - type: object
          properties:
            depositFileId:
              type: string
            depositTime:
              type: string
              format: date-time
            providerId:
              type: string
            receiverUpdate:
              $ref: '#/components/schemas/RegisterLetterReceiverUpdate'
            status:
              type: string
              enum: [AWAITING_RECIPIENT_UPDATE]
          required:
            - depositFileId
            - depositTime
            - providerId
            - receiverUpdate
            - status
    RegisteredLetterAccepted:
      allOf:
        - $ref: '#/components/schemas/RegisteredLetterBase'
        - type: object
          properties:
            acceptationFileId:
              type: string
            acceptationTime:
              type: string
              format: date-time
            depositFileId:
              type: string
            depositTime:
              type: string
              format: date-time
            providerId:
              type: string
            status:
              type: string
              enum: [ACCEPTED]
          required:
            - acceptationFileId
            - acceptationTime
            - depositFileId
            - depositTime
            - providerId
            - status
    RegisteredLetterExpired:
      allOf:
        - $ref: '#/components/schemas/RegisteredLetterBase'
        - type: object
          properties:
            depositFileId:
              type: string
            depositTime:
              type: string
              format: date-time
            negligenceFileId:
              type: string
            negligenceTime:
              type: string
              format: date-time
            providerId:
              type: string
            status:
              type: string
              enum: [EXPIRED]
          required:
            - depositFileId
            - depositTime
            - negligenceFileId
            - negligenceTime
            - providerId
            - status
    RegisteredLetterRefused:
      allOf:
        - $ref: '#/components/schemas/RegisteredLetterBase'
        - type: object
          properties:
            depositFileId:
              type: string
            depositTime:
              type: string
              format: date-time
            providerId:
              type: string
            refusalFileId:
              type: string
            refusalTime:
              type: string
              format: date-time
            status:
              type: string
              enum: [REFUSED]
          required:
            - depositFileId
            - depositTime
            - providerId
            - refusalFileId
            - refusalTime
            - status
    RegisteredLetterBatchSubscriber:
      type: object
      properties:
        email:
          type: string
        id:
          type: string
      required:
        - email
        - id
    RegisterLetterReceiverUpdate:
      type: object
      properties:
        providerId:
          type: string
        firstname:
          type: string
        lastname:
          type: string
      required:
        - providerId
        - firstname
        - lastname
    RegisterLetterReceiver:
      type: object
      properties:
        email:
          type: string
        firstname:
          type: string
        lastname:
          type: string
        civility:
          $ref: '../shared-components/civility.openapi.yaml#/Civility'
      required:
        - civility
        - email
        - firstname
        - lastname
    RegisteredLetterList:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/RegisteredLetter'
      required:
        - items

