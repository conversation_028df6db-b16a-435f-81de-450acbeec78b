openapi: 3.0.0
info:
  title: operation-invitations
  version: '1.0'
paths:
  /operation-roles:
    get:
      summary: Get Operation Roles
      description: Retrieve a list of operation roles based on the provided query parameters.
      operationId: getOperationsRoles
      tags: ['Operation Invitations']
      parameters:
        - name: email
          in: query
          required: true
          schema:
            type: string
          description: The email of the target user to filter roles.
        - name: operationId
          in: query
          required: true
          schema:
            type: string
        - name: userId
          in: query
          required: true
          schema:
            type: string
          description: The ID of the current user to filter roles.
      responses:
        '200':
          description: A list of roles.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OperationInvitationRole'

  /operation-invitations:
    post:
      summary: Create Operation Invitation
      tags: ['Operation Invitations']
      description: Creates a new operation invitation.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OperationInvitationCreate'
      responses:
        '200':
          description: Operation invitation created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OperationInvitation'


  /operation-invitations/{id}:
    delete:
      summary: Delete Operation Invitation
      description: Deletes an operation invitation.
      tags: ['Operation Invitations']
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: The ID of the operation invitation to delete.
      responses:
        '200':
          description: Operation invitation deleted successfully.

    put:
      summary: Update Operation Invitation
      description: Updates an existing operation invitation.
      tags: ['Operation Invitations']
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: The ID of the operation invitation to update.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OperationInvitationUpdate'
      responses:
        '204':
          description: Operation invitation updated successfully.


components:
  schemas:
    OperationInvitationRole:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier of the role.
        name:
          type: string
          description: The name of the role.
      required:
        - id
        - name
    OperationInvitation:
      type: object
      properties:
        email:
          type: string
          description: The email of the user for the operation invitation.
        id:
          type: string
          description: The unique identifier of the operation invitation.
        operationId:
          type: string
          description: The unique identifier of the operation associated with the invitation.
        organizationId:
          type: string
          description: The unique identifier of the organization associated with the invitation.
        roleId:
          type: string
          description: The unique identifier of the role associated with the invitation.
        roleName:
          type: string
          description: The name of the role associated with the invitation.
        creatorUserId:
          type: string
          description: The unique identifier of the user associated with the invitation.
        creationTime:
          type: string
          format: date-time
          description: The time when the invitation was created.
        user:
          $ref: '#/components/schemas/InvitedUser'

      required:
        - creationTime
        - creatorUserId
        - email
        - id
        - operationId
        - organizationId
        - roleId
        - roleName
        - user

    OperationInvitationCreate:
      type: object
      properties:
        email:
          type: string
          description: The email of the user to invite.
        operationId:
          type: string
          description: The unique identifier of the operation for the invitation.
        roleId:
          type: string
          description: The unique identifier of the role for the invitation.
        userId:
          type: string
          description: The unique identifier of the user making the invitation.
        emailContent:
          $ref: '#/components/schemas/EmailContent'
      required:
        - email
        - operationId
        - roleId
        - userId

    OperationInvitationUpdate:
      type: object
      properties:
        roleId:
          type: string
          description: The unique identifier of the role for the invitation.
        userId:
          type: string
          description: The unique identifier of the user updating the invitation.
        emailContent:
          $ref: '#/components/schemas/EmailContent'
      required:
        - roleId
        - userId

    InvitedUser:
      oneOf:
        - $ref: '#/components/schemas/ExistingUser'
        - $ref: '#/components/schemas/AnonymousUser'

    ExistingUser:
      type: object
      properties:
        email:
          type: string
          description: The email of the existing user.
        firstname:
          type: string
          description: The first name of the existing user.
        id:
          type: string
          description: The unique identifier of the existing user.
        lastname:
          type: string
          description: The last name of the existing user.
      required:
        - email
        - firstname
        - id
        - lastname

    AnonymousUser:
      type: object
      properties:
        email:
          type: string
          description: The email of the anonymous user.
      required:
        - email

    EmailContent:
      type: object
      properties:
        body:
          type: string
          description: The body of the email.
        subject:
          type: string
          description: The subject of the email.
      required:
        - body
        - subject
