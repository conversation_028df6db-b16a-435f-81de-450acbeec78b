/**
 * api-emails
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SenderInfoDto } from './sender-info-dto';


export interface TaskReminderEmailDto { 
    organizationId: string;
    creatorName?: string;
    taskTitle: string;
    taskDescription: string;
    operationId: string;
    appUrl: string;
    taskId: string;
    taskDeadline?: string;
    sender: SenderInfoDto;
    templateId: TaskReminderEmailDtoTemplateIdEnum;
}
export enum TaskReminderEmailDtoTemplateIdEnum {
    TASK_REMINDER = 'TASK_REMINDER'
};



