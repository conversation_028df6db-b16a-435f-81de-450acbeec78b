import { InvalidInputError } from '@mynotary/crossplatform/shared/util';

export class PlaneteStreamGenerationError extends InvalidInputError {
  name = PlaneteStreamGenerationError.name;

  /*
  Before anyone tells me: yes I know InvalidInputErrors are supposed to define
  their own message, not take anything as params.
  In this case, the backend is called and returns a HTTP400 (crafted by another InvalidInputError)
  This exception is used to relay that HTTP400 to the frontend app
   */
  constructor({ displayedMessage, message }: { displayedMessage: string; message: string }) {
    super({
      displayedMessage,
      message
    });
  }
}
