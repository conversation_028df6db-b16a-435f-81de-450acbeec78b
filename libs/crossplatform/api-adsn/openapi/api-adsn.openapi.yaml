openapi: 3.0.0
info:
  title: API ADSN
  description: API
  version: 1.0.0
servers:
  - url: 'http://localhost:3059/api-adsn/'
paths:
  '/planete-historique':
    get:
      summary: Get the history of the 'demande de copie de document'
      tags: [ demande-copie-document ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlaneteHistoriqueList'
      operationId: find
      parameters:
        - in: query
          name: id
          required: false
          schema:
            type: string
        - in: query
          name: dossierId
          required: false
          schema:
            type: string
    post:
      summary: Add an entry to the history of the 'demande de copie de document'
      tags: [ demande-copie-document ]
      operationId: create
      responses:
        '200':
          description: OK. Returns the id of the created entry
          content:
            text/plain:
              schema:
                type: string
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlaneteHistorique'
        required: true
  '/planete-historique/{id}':
    get:
      summary: Get an entry of the history of the 'demande de copie de document'
      tags: [ demande-copie-document ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlaneteHistorique'
      operationId: getById
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
    put:
      summary: Update an entry of the history of the 'demande de copie de document'
      tags: [ planete-historique ]
      operationId: update
      responses:
        '200':
          description: OK
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlaneteHistorique'
        required: true
    delete:
      summary: Delete an entry of the history of the 'demande de copie de document'
      tags: [ planete-historique ]
      operationId: delete
      responses:
        '200':
          description: OK
        '500':
          description: Error processing request
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string

  '/planete-pjrecue':
    get:
      summary: Get the attachments of a PlaneteDossier
      tags: [ planete-pjrecue ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlanetePjRecueList'
      operationId: findPjRecue
      parameters:
        - in: query
          name: id
          required: false
          schema:
            type: string
        - in: query
          name: dossierId
          required: false
          schema:
            type: string
        - in: query
          name: historiqueId
          required: false
          schema:
            type: string
    post:
      summary: Add an attachment to the PlaneteDossier
      tags: [ planete-pjrecue ]
      operationId: createPjRecue
      responses:
        '200':
          description: OK. Returns the id of the created attachment
          content:
            text/plain:
              schema:
                type: string
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlanetePjRecue'
        required: true
  '/planete-pjrecue/{id}':
    get:
      summary: Get an attachment of a PlaneteDossier
      tags: [ planete-pjrecue ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlanetePjRecue'
      operationId: getPjRecueById
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
    put:
      summary: Update an attachment of a PlaneteDossier
      tags: [ planete-pjrecue ]
      operationId: updatePjRecue
      responses:
        '200':
          description: OK
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlanetePjRecue'
        required: true
    delete:
      summary: Delete an attachment of a PlaneteDossier
      tags: [ planete-pjrecue ]
      operationId: deletePjRecue
      responses:
        '200':
          description: OK
        '500':
          description: Error processing request
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string

  '/planete-virement':
    get:
      summary: Get the list of virements
      tags: [ virement ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Virement'
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      operationId: findVirement
      parameters:
        - in: query
          name: id
          required: false
          schema:
            type: string
        - in: query
          name: dossierId
          description: the id of the PlaneteDossier to which the virement belongs
          required: false
          schema:
            type: string

  '/planete-dossiers-by-status':
    get:
      summary: get count of dossiers grouped by status
      parameters:
        - in: query
          name: organizationId
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PlaneteDossierByStatus'

  '/interop-delegate/{crpcen}':
    get:
      summary: get Interop delegate info for a CRPCEN
      tags: [ interop ]
      operationId: getDelegate
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InteropDelegate'
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      parameters:
        - in: path
          name: crpcen
          required: true
          schema:
            type: string
  '/interop-sync/{crpcen}':
    post:
      summary: Interop data sync. Used for protocol negotiation between serveur and a Companion delegate for a given CRPCEN
      tags: [ interop ]
      operationId: sync
      responses:
        '200':
          description: OK
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      parameters:
        - in: path
          name: crpcen
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                flags:
                  description: flags discovered on the shared 'SYNC' folder
                  type: array
                  items:
                    type: string
        required: true
  '/interop-ddv/{id}':
    get:
      summary: get Interop DDV in xml format for a given Virement id
      tags: [ interop ]
      operationId: getDdv
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InteropDdvData'
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      parameters:
        - in: path
          name: id
          description: the id of the Virement (PlaneteVirement) for which to generate the DDV
          required: true
          schema:
            type: string
  '/interop-ao/{id}':
    post:
      summary: Used to upload the received AO ('avis d'opéré') issued after a 'demande de virement'
      tags: [ interop ]
      operationId: addInteropAo
      responses:
        '200':
          description: OK
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      parameters:
        - in: path
          name: id
          description: the id of the Virement (PlaneteVirement) for which to send the AO
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  description: the textual (xml) content of the AO
                  type: string

  '/referentiels-to-update':
    get:
      summary: Get the list of referentiels to update
      tags: [ referentiel ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReferentielUpdate'
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      operationId: getReferentiels
      parameters:
        - in: query
          name: id
          required: false
          schema:
            type: string
        - in: query
          name: dossierId
          description: the id of the PlaneteDossier to which the virement belongs
          required: false
          schema:
            type: string

  '/referentiel-topad-pays':
    get:
      summary: Find countries according to the template used as a filter
      tags: [ referentiel ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TopadPays'
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      operationId: findTopadPays
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TopadPays'
    put:
      summary: Update the TOPAD PAYS referentiel
      tags: [ referentiel ]
      operationId: updateTopadPays
      responses:
        '200':
          description: OK
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                update:
                  type: string
                  description: the content of the updated referentiel file. Empty if unchanged since last update
        required: true

  '/referentiel-topad-pays/{id}':
    get:
      summary: find TOPAD country by id
      tags: [ referentiel ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TopadPays'
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      operationId: getTopadPays
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string

  '/referentiel-topad-commune':
    get:
      summary: Find cities according to the filters
      tags: [ referentiel ]
      parameters:
        - in: query
          name: name
          schema:
            type: string
            description: filter on the name of the city
        - in: query
          name: zipCode
          schema:
            type: string
            description: filter on the zip code of the city
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TopadCommune'
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      operationId: findTopadCommune
    put:
      summary: Update the TOPAD COMMUNE referentiel
      tags: [ referentiel ]
      operationId: updateTopadCommune
      responses:
        '200':
          description: OK
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                update:
                  type: string
                  description: the content of the updated referentiel file. Empty if unchanged since last update
        required: true

  '/referentiel-topad-commune/{id}':
    get:
      summary: find TOPAD city by id
      tags: [ referentiel ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TopadCommune'
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      operationId: getTopadCommune
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string


  '/referentiel-service':
    get:
      summary: Find DGFIP Services according to the template used as a filter
      tags: [ referentiel ]
      parameters:
        - in: query
          name: label
          required: false
          schema:
            type: string
        - in: query
          name: sages
          required: false
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ServiceDgfip'
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      operationId: findServiceDgfip
    put:
      summary: Update the TOPAD COMMUNE referentiel
      tags: [ referentiel ]
      operationId: updateServiceDgfip
      responses:
        '200':
          description: OK
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                update:
                  type: string
                  description: the content of the updated referentiel file. Empty if unchanged since last update
        required: true

  '/referentiel-service/{id}':
    get:
      summary: find DGFIP service by id
      tags: [ referentiel ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceDgfip'
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      operationId: getServiceDgfip
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string

  '/referentiel-comedec-commune':
    get:
        summary: Find cities according to the template used as a filter
        tags: [ referentiel, etatcivil ]
        parameters:
          - in: query
            name: codeInsee
            required: false
            schema:
              type: string
          - in: query
            name: nom
            required: false
            allowEmptyValue: true
            allowReserved: true
            schema:
              type: string
        responses:
          '200':
            description: OK
            content:
              application/json:
                schema:
                  type: array
                  items:
                    $ref: '#/components/schemas/ComedecCommune'
          '400':
            description: Invalid parameters
          '500':
            description: Error processing request
        operationId: findComedecCommune
    put:
      summary: Update the COMEDEC COMMUNE referentiel
      tags: [ referentiel, etatcivil ]
      operationId: updateComedecCommune
      responses:
        '200':
          description: OK
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                update:
                  type: string
                  description: the content of the updated referentiel file. Empty if unchanged since last update
        required: true


  /planete-retour:
    get:
      tags:
        - planete
        - eventEmitter
      operationId: getPlaneteRetour
      summary: Triggers a fetch of Planete messages waiting in the inbox
      parameters:
        - in: query
          name: userId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
        '500':
          description: Internal server error

  /planete-retour/{id}:
    put:
      summary: add return stream to any ProduitPlanete
      operationId: uploadRetourPlanete
      tags: [ planete-retour ]
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: the id of the planete_dossier
      responses:
        '200':
          description: OK
        '400':
          description: Invalid parameters
        '500':
          description: Error processing request
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlaneteUpdateWithMessage'

  /planete-release:
    post:
      tags:
        - planete
      operationId: planeteRelease
      summary: Triggers a re-release of Planete messages
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                userId:
                  type: string
                  description: User ID
                dossierId:
                  type: string
                  description: PlaneteDossier ID to re-release the responses from
      responses:
        '201':
          description: OK
        '500':
          description: Internal server error

  /planete-change-lra:
    post:
      tags:
        - planete
      operationId: planeteChangeLra
      summary: Triggers a change of LRA for Planete messages
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                oldLra:
                  type: string
                  description: Old LRA identifier
                protocols:
                  type: array
                  description: List of protocols to change the LRA to
                  items:
                    type: string
                userId:
                  type: string
                  description: User ID
      responses:
        '201':
          description: OK
        '500':
          description: Internal server error

  /planete-change-lra-status:
    post:
      tags:
        - planete
        - callback
      operationId: planeteChangeLraSetStatus
      summary: sets the status of the change of LRA operation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangeLraStatus'
      responses:
        '201':
          description: OK
        '500':
          description: Internal server error
    get:
      tags:
        - planete
      operationId: planeteChangeLraGetStatus
      summary: gets the status of the change of LRA operation
      parameters:
        - in: query
          name: userId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChangeLraStatus'
        '500':
          description: Internal server error




  /user-event:
    post:
      tags:
        - eventEmitter
      operationId: sendUserEvent
      summary: Generic event emitter for user-specific events, most notably to trigger actions from the web client to Companion
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserEvent'
      responses:
        '201':
          description: OK
        '500':
          description: Internal server error

  /user-notification:
    post:
      tags:
          - eventEmitter
      operationId: sendUserNotification
      summary: Send notification to frontend
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserNotification'
      responses:
        '201':
          description: OK
        '500':
          description: Internal server error



components:
  schemas:
    PlaneteHistoriqueList:
      type: array
      items:
        $ref: '#/components/schemas/PlaneteHistorique'

    PlaneteHistorique:
      type: object
      properties:
        description:
          type: string
          description: a small text to give a little details on the entry in the history
        descriptionObj:
          type: object
          description: additional details on the entry in the history
        dossierId:
          type: string
          description: the id of the PlaneteDossier to which the entry belongs
        eventTime:
          type: string
          format: date-time
          description: the time at which the entry was created
        id:
          type: string
          description: the id of the entry in the history
        label:
          type: string
          description: A title for the entry
        messageId:
          type: string
          description: external id of the message/event that triggered the entry
        originServer:
          type: string
          description: the server that created the entry. Exactly one of originServer and OriginUserId must be set
          enum:
            - ANF
            - COMPTA
            - PLANETE
        originUserId:
          type: string
          description: the user that created the entry. Exactly one of originServer and OriginUserId must be set
        pjRecues:
          type: array
          description: the list of attachments received with the history event
          items:
            $ref: '#/components/schemas/PlanetePjRecue'
        subject:
          type: string
          description: the subject part of the history message. This is the part frontend clients should give more importance to when displaying the history
        type:
          type: string
          description: the type of the history entry
          enum:
            - ANF_CHECKED
            - CREATED
            - DELETED
            - GENERATED
            - PAID
            - PAYMENT_REFUSED
            - RESPONSE_RECEIVED
            - SENT
            - SIGNED
            - TO_ANF
            - TO_PAY
            - TO_SEND
            - TO_SIGN
        unread:
          type: boolean
          description: whether the entry has been read by any user


    PlanetePjRecueList:
      type: array
      items:
        $ref: '#/components/schemas/PlanetePjRecue'

    PlanetePjRecue:
      type: object
      properties:
        content:
          type: string
          description: for simple/small attachments, the direct content of the attachment
        contentFileId:
          type: string
          description: for larger attachments, the id of the file in the file storage (see file API)
        dossierId:
          type: string
          description: the id of the PlaneteDossier to which the attachment belongs
        historiqueId:
          type: string
          description: the id of the PlaneteHistorique entry to which the attachment belongs
        id:
          type: string
          description: the id of the attachment
        label:
          type: string
          description: the label of the attachment
        receivedTime:
          type: string
          format: date-time
          description: the time at which the attachment was received
        type:
          type: string
          description: the type of the attachment
    Virement:
      title: Virement
      type: object
      properties:
        compteClientOffice:
          type: string
          example: '1234567890'
        date:
          type: string
          format: date-time
          example: 2021-01-01T00:00:00.000Z
        demandeur:
          type: string
          example: 'Jean Dupont'
        destinataireCode:
          type: string
          example: '1304P02'
        destinataireType:
          type: string
          example: 'ADSN'
        dossierPlaneteId:
          type: string
          example: '90898dba-281f-4b06-b946-be7d8e729a33'
        iban:
          $ref: '#/components/schemas/Iban'
        id:
          type: string
          example: '8316d954-6999-4fba-8b66-44b743bdc0f4'
        label:
          type: string
          example: 'Virement pour la demande de copie de document'
        memo:
          type: string
          example: 'Commentaire pour le comptable'
        montant:
          type: number
          example: 123.45
        numero:
          type: string
          example: '1234567890'
        operationId:
          type: string
        status:
          type: string
          example: 'CREATED'
          enum:
            - ACCEPTED
            - DRAFT
            - PAID
            - REFUSED
            - TO_PAY
        typeTransaction:
          type: string
    Iban:
      title: Iban
      type: object
      properties:
        bban:
          type: string
          example: '12345678901234567890123'
        bic:
          type: string
          example: 'AGRIFRPP'
        cle:
          type: string
          example: '12'
        domiciliation:
          type: string
          example: 'Société Générale'
        guichet:
          type: string
          example: 'Agence de Lyon Part-Dieu'
        pays:
          type: string
          example: 'FR'
        titulaire:
          type: string
          example: 'Jean Dupont'

    PlaneteDossierByStatus:
      type: object
      properties:
        count:
          type: number
        status:
          type: string

    InteropDelegate:
      type: object
      properties:
        actAsDelegate:
          type: boolean
          description: whether the client calling this endpoint should act as an Interop delegate for the given CRPCEN
        activeVersion:
          type: string
          description: the active version of the Interop protocol, as deducted from protocol negotiation
        appId:
          type: number
          description: the id of the application in the Interop system. Valid numbers are 1-9
        delegateStatus:
          type: string
          description: the status of the delegate in the Interop system
          enum:
            - ELECTION_PENDING
            - ONLINE
        interopPath:
          type: string
          description: the path to the Interop shared folder on the client computers. The specs says it should always be defined to 'X:'. Dunno is this recommandation is really effective
        subPaths:
          type: object
          description: the subpaths of the Interop shared folder on the client computers. Should be standard for a given version of the protocol
          properties:
            ao:
              type: string
              description: the path to the AO subfolder
            ddv:
              type: string
              description: the path to the DDV subfolder
            err:
              type: string
              description: the path to the ERR subfolder
            fact:
              type: string
              description: the path to the FACT subfolder
            sync:
              type: string
              description: the path to the SYNC subfolder
        syncFlags:
          type: array
          description: the flags discovered on the shared 'SYNC' folder + flags that should be added by the delegate
          items:
            type: string

    InteropDdvData:
      type: object
      properties:
        content:
          type: string
          description: the content of the DDV in XML format
        filename:
          type: string
          description: the filename to be used when saving the DDV on the client computer
        subfolder:
          type: array
          description: the subfolder to be used when saving the DDV on the client computer
          items:
            type: string

    ReferentielUpdate:
      type: object
      properties:
        checksum:
          type: string
          description: the checksum (MD5) of the referentiel file
        id:
          type: string
          description: the id of the referentiel
          enum:
            - SERVICE_DGFIP
            - TOPAD_COMMUNE
            - TOPAD_PAYS
            - COMEDEC_COMMUNE
        lastAttempt:
          type: string
          format: date-time
          description: the last time the referentiel was attempted to be updated. The server gives a few minutes for the chosen Companion client to fetch the data
        updateTime:
          type: string
          format: date-time
          description: the last time the referentiel was effectively updated

    TopadPays:
      type: object
      properties:
        code:
          type: string
          description: the code of the country
        label:
          type: string
          description: the label of the country

    TopadCommune:
      type: object
      properties:
        code:
          type: string
          description: the code of the city
        codeCommune:
          type: string
          description: the city code of the city (3 chars)
        codeDepartement:
          type: string
          description: the code departement of the city (2 chars)
        codesPostaux:
          type: array
          description: the postal codes of the city
          items:
            type: string
        label:
          type: string
          description: the label of the city
        sages:
          type: string
          description: the SAGES code of the SPF of reference (see ServiceDgfip)

    ServiceDgfip:
      type: object
      properties:
        bic:
          type: string
          description: the BIC of the service
        iban:
          type: string
          description: the IBAN of the service
        label:
          type: string
          description: the label of the service
        sages:
          type: string
          description: (key) the SAGES code of the service
        type:
          type: string
          description: the type of the service
          enum:
            - SDE
            - SPE
            - SPF

    ChangeLraStatus:
      type: object
      properties:
        userId:
          type: string
          description: User ID
        status:
          type: string
          description: status of the operation
          enum:
            - 'INVALID_PARAMS'
            - 'NONE'
            - 'OK'
            - 'SECURITY_ERROR'
            - 'SERVER_ERROR'

    PlaneteUpdateWithMessage:
      type: object
      properties:
        data:
          type: object
          description: the data to be added to the 'produit Planete' (signed XML of the Demande, or return XML stream sent back by Planete)
        detailedErrorCode:
          type: string
          description: the detailed error code returned by Planete
        errorCode:
          type: string
          description: the error code returned by Planete (usually a 3 digit number)
        updateType:
          type: string
          description: the type of update to be performed on the 'produit Planete'
          enum:
            - AR
            - RI
            - SENT_OK
            - SENT_KO
            - SI
            - SIGNED
        userId:
          type: string
          description: the user performing the update

    PlaneteUpdate:
      type: object
      properties:
        data:
          type: string
          description: the data to be added to the 'produit Planete (demande, requi,...)' (signed XML of the product, or return XML stream sent back by Planete)
        detailedErrorCode:
          type: string
          description: the detailed error code returned by Planete
        errorCode:
          type: string
          description: the error code returned by Planete (usually a 3 digit number)
        updateType:
          type: string
          description: the type of update to be performed on the 'produit Planete'
          enum:
            - AR
            - RI
            - SENT_OK
            - SENT_KO
            - SI
            - SIGNED
        userId:
          type: string
          description: the user performing the update

    ComedecCommune:
      type: object
      properties:
        ancienNom:
          type: string
          description: the old name of the city
        codeDepartement:
          type: string
          description: the code of the department
        codeInsee:
          type: string
          description: the code INSEE of the city
        id:
          type: string
          description: the id of the city
        nom:
          type: string
          description: the name of the city
        serviceVAD:
          type: boolean
          description: whether the city delivers death certificates through the COMEDEC system
        serviceVAM:
          type: boolean
          description: whether the city delivers wedding certificates through the COMEDEC system
        serviceVAN:
          type: boolean
          description: whether the city delivers birth certificates through the COMEDEC system

    UserEvent:
      type: object
      properties:
        userId:
          type: string
          description: User ID
        queue:
          type: string
          description: LocalQueue on Companion to dispatch the event to
        payload:
          type: object
          description: Event data

    UserNotification:
      type: object
      properties:
        userId:
          type: string
          description: User ID
        payload:
          type: object
          description: Notification data
