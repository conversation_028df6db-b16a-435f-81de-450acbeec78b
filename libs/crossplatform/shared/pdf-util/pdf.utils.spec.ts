import * as fs from 'fs';
import { isValidPdf } from './pdf.utils';


describe(isValidPdf, () => {
  it('should failed if file is empty', async () => {
    const isValid = await isValidPdf(Buffer.from(''));
    expect(isValid).toBe(false);
  });

  it('should failed if file is image', async () => {
    const file = fs.readFileSync('libs/testing/assets/dummy.jpg');
    const isValid = await isValidPdf(file);
    expect(isValid).toBe(false);
  });

  it('should failed if file is invalid pdf', async () => {
    const file = fs.readFileSync('libs/testing/assets/invalid-pdf.pdf');
    const isValid = await isValidPdf(file);
    expect(isValid).toBe(false);
  });

  it('should pass if file is valid pdf', async () => {
    const file = fs.readFileSync('libs/testing/assets/dummy.pdf');

    const isValid = await isValidPdf(new Uint8Array(file));
    expect(isValid).toBe(true);
  });
});
