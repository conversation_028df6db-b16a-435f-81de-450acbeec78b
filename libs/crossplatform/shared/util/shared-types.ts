import { fns } from '@mynotary/crossplatform/shared/dates-util';

export type Dictionary<T> = Record<string, T>;

export type StringDate = string;

export type AbstractType<T> = abstract new () => T;
export type Type<T> = new () => T;
export type SameTypeAs<T> = T extends infer U ? U : never;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type JSONValue = any;
export type JSONObject = Dictionary<JSONValue>;
export type JSONArray = JSONValue[];

export type FormattedLocation = {
  lat?: number;
  lng?: number;
};
export type MnAddress = MnStandardAddress & MnStructuredAddress;

type MnStandardAddress = {
  additionalAddress?: string /* "Chez M.Mme Dupond" . additional info on the address that may be entered manually. On a structured address this is the "line 2" */;
  address?: string /* "1 bis rue de la Paix" */;
  city?: string /* "Lyon" */;
  cityCode?: string /* "69123" */;
  country?: string /* "France" */;
  departmentCode?: string /* "69" */;
  departmentName?: string /* "Rhône" */;
  district?: string;
  formattedAddress?: string /* "1 bis rue de la Paix, 69001 Lyon, France" */;
  location?: FormattedLocation /* { lat: 45.767, lng: 4.833 } */;
  placeId?: string /* Google Maps' PlaceId */;
  regionName?: string /* "Auvergne-Rhône-Alpes" */;
  street?: string /* "rue de la Paix" */;
  streetNumber?: string /* "1bis" */;
  zip?: string /* "69001" */;
};

type MnStructuredAddress = {
  additionalAddress2?: string /* "2e étage escalier C". This is the "line 3" of a standard address */;
  locality?: string /* "Le Trou Paumé" ('lieu-dit') */;
  postalBox?: string /* "BP 12345" */;
  streetName?: string /* "de la Paix" */;
  streetNumberExtension?: string /* "bis" see extensionVoie in address-reference.ts */;
  streetType?: string /* "RUE" see typeVoie in address-reference.ts */;
};

export type MnDate = {
  day?: string;
  month?: string;
  timestamp: number;
  year: string;
};

export type City = {
  cityCode: string;
  id: string;
  label: string;
  zipCodes: string[];
};

export const formatAddress = (args: {
  city?: string;
  country?: string;
  street?: string;
  streetNumber?: string;
  zip?: string;
}): string => {
  const zip = args?.zip ? `(${args.zip})` : '';

  const streetAndAddress = args.streetNumber ? `${args.streetNumber} ${args.street}` : args.street;

  return `${streetAndAddress}, ${args.city ?? ''} ${zip}, ${args.country ?? ''}`;
};

export function getFormattedAddress(address?: MnAddress): string {
  const { city, formattedAddress, street, streetNumber } = address || {};

  /**
   * Addresses from our system are typically formatted using street, street number, and city.
   * However, for addresses coming from external integrations, some fields might be missing.
   * In such cases, we use the `formattedAddress` as a fallback.
   */
  return street && streetNumber && city ? `${streetNumber} ${street}, ${city}` : (formattedAddress ?? '');
}

/**
 * a structured address is incomplete:
 * - if any of country, cityCode (if country == FR), city name, zip is missing, or
 * - no classic address (street type + name), no postal box, no locality
 * @param address the (structured!) address to check
 **/
export const isAddressIncomplete = (address: MnAddress | null): boolean => {
  return (
    !address ||
    (address.country === 'France' && !address.cityCode) ||
    !address.city ||
    !address.zip ||
    !(address.postalBox || address.locality || (address.streetType && address.streetName) || address.additionalAddress)
  );
};

export const isValidStandardAddress = (address: MnAddress): boolean => {
  return address.formattedAddress !== undefined;
};

export function datePartsToDate(annee: string, mois?: string, jour?: string, heure?: string, minutes?: string): Date {
  /* refDate holds the default values for day/month (and time) */
  const refDate = new Date('01/01/2000');
  if (heure) {
    refDate.setHours(Number(heure));
  }
  if (minutes) {
    refDate.setMinutes(Number(minutes));
  }
  if (jour) {
    return fns.parse(`${jour}/${mois}/${annee}`, 'dd/MM/yyyy', refDate);
  } else if (mois) {
    return fns.parse(`${mois}/${annee}`, 'MM/yyyy', refDate);
  } else {
    return fns.parse(annee, 'yyyy', refDate);
  }
}
