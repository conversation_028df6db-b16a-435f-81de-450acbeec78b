// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
export const ConcoursMandataireProprietePrivee = {
  children: [
    {
      id: 'si-presence-mandataire',
      condition: 'PRESENCE_MANDATAIRE',
      children: [
        {
          id: 'si-presence-mandataire-texte',
          content: 'Acte établi par :\n'
        },
        {
          id: 'si-presence-mandataire-liste-mandataires',
          repetition: {
            source: 'MANDATAIRES',
            item: 'MANDATAIRE'
          },
          children: [
            {
              id: 'si-presence-mandataire-liste-mandataires-saut-de-ligne',
              enhancedVariable: {
                type: 'lineBreak',
                parameters: {
                  number: 1
                }
              }
            },
            {
              id: 'si-presence-mandataire-liste-mandataires-texte',
              content:
                'Nom du négociateur : **{{ MANDATAIRE.PRENOM }} {{ MANDATAIRE.NOM }}** Tél : **{{ MANDATAIRE.TELEPHONE }}**\n',
              prerequisites: {
                variables: {
                  'MANDATAIRE.PRENOM': true,
                  'MANDATAIRE.NOM': true,
                  'MANDATAIRE.TELEPHONE': true
                }
              }
            },
            {
              id: 'si-presence-mandataire-liste-mandataires-saut-de-ligne-1',
              enhancedVariable: {
                type: 'lineBreak',
                parameters: {
                  number: 1
                }
              }
            },
            {
              id: 'si-presence-mandataire-liste-mandataires-texte-1',
              content:
                '{{ MANDATAIRE.STATUT }} ; Titulaire d’une assurance responsabilité civile professionnelle auprès de {{ MANDATAIRE.RCP_NOM }} sous le numéro de police n° {{ MANDATAIRE.RCP_NUMERO }};\n',
              prerequisites: {
                variables: {
                  'MANDATAIRE.STATUT': true,
                  'MANDATAIRE.RCP_NOM': true,
                  'MANDATAIRE.RCP_NUMERO': true
                }
              }
            },
            {
              id: 'si-presence-mandataire-liste-mandataires-si-mandataire-rsac',
              condition: 'MANDATAIRE.RSAC',
              content:
                ' \nRSAC n° {{ MANDATAIRE.RSAC_NUMERO }} inscrit auprès du greffe du Tribunal de {{ MANDATAIRE.RSAC_VILLE }}; \n',
              prerequisites: {
                variables: {
                  'MANDATAIRE.RSAC_NUMERO': true,
                  'MANDATAIRE.RSAC_VILLE': true
                },
                conditions: {
                  'MANDATAIRE.RSAC': true
                }
              }
            },
            {
              id: 'si-presence-mandataire-liste-mandataires-texte-2',
              content: 'Agissant pour le compte de l’établissement ci-dessus désigné.\n'
            }
          ],
          prerequisites: {
            repeats: {
              MANDATAIRES: {
                variables: {},
                conditions: {},
                repeats: {},
                raws: {}
              }
            }
          }
        }
      ],
      prerequisites: {
        conditions: {
          PRESENCE_MANDATAIRE: true
        }
      }
    },
    {
      id: 'si-non-presence-mandataire',
      condition: '!PRESENCE_MANDATAIRE',
      children: [
        {
          id: 'si-non-presence-mandataire-saut-de-ligne',
          enhancedVariable: {
            type: 'lineBreak',
            parameters: {
              number: 1
            }
          }
        },
        {
          id: 'si-non-presence-mandataire-texte',
          content:
            'Acte établi par : Nom du négociateur : \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ Tél : \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n'
        },
        {
          id: 'si-non-presence-mandataire-saut-de-ligne-1',
          enhancedVariable: {
            type: 'lineBreak',
            parameters: {
              number: 1
            }
          }
        },
        {
          id: 'si-non-presence-mandataire-texte-1',
          content:
            'Ayant le statut \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ ; Titulaire d’une assurance responsabilité civile professionnelle auprès de \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ sous le numéro de police n° \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ ; RSAC n° \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ inscrit auprès du greffe du Tribunal de \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_, agissant pour le compte de l’établissement ci-dessus désigné.\n'
        }
      ],
      prerequisites: {
        conditions: {
          PRESENCE_MANDATAIRE: true
        }
      }
    }
  ],
  mapping: {
    conditions: {
      PRESENCE_MANDATAIRE: {
        value: '_.size(RAWS.CONSEILLER) > 0',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'CONSEILLER'
          }
        ]
      }
    },
    variables: {},
    repeats: {
      MANDATAIRES: {
        value: 'records.OPERATION__IMMOBILIER__VENTE__MANDATAIRES.MANDATAIRE',
        item: 'MANDATAIRE',
        dependencies: [],
        mapping: {
          conditions: {
            RSAC: {
              value: "answers[MANDATAIRE.id].intermediaire_statut_pp.value === 'agent'",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'MANDATAIRE.id',
                  questionId: 'intermediaire_statut_pp'
                }
              ]
            }
          },
          variables: {
            KEY: {
              value: 'MANDATAIRE.id',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            },
            PRENOM: {
              value: 'answers[MANDATAIRE.id].prenoms.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'MANDATAIRE.id',
                  questionId: 'prenoms'
                }
              ]
            },
            NOM: {
              value: 'answers[MANDATAIRE.id].nom.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'MANDATAIRE.id',
                  questionId: 'nom'
                }
              ]
            },
            TELEPHONE: {
              value: '_.formatPhone(answers[MANDATAIRE.id].telephone.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'MANDATAIRE.id',
                  questionId: 'telephone'
                }
              ]
            },
            STATUT: {
              value:
                '({agent:"Ayant le statut d\'Agent Commercial",portage:"Sous portage salarial"})[answers[MANDATAIRE.id].intermediaire_statut_pp.value]',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'MANDATAIRE.id',
                  questionId: 'intermediaire_statut_pp'
                }
              ]
            },
            RCP_NOM: {
              value: 'answers[MANDATAIRE.id].assurance_rcp_nom.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'MANDATAIRE.id',
                  questionId: 'assurance_rcp_nom'
                }
              ]
            },
            RCP_NUMERO: {
              value: 'answers[MANDATAIRE.id].assurance_rcp_numero.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'MANDATAIRE.id',
                  questionId: 'assurance_rcp_numero'
                }
              ]
            },
            RSAC_NUMERO: {
              value: 'answers[MANDATAIRE.id].numero_rsac.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'MANDATAIRE.id',
                  questionId: 'numero_rsac'
                }
              ]
            },
            RSAC_VILLE: {
              value: 'answers[MANDATAIRE.id].numero_rsac_commune.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'MANDATAIRE.id',
                  questionId: 'numero_rsac_commune'
                }
              ]
            }
          },
          repeats: {},
          raws: []
        }
      }
    },
    raws: [
      {
        id: 'AGENCE',
        value: 'records.OPERATION__IMMOBILIER__VENTE__AGENTS.AGENT_IMMOBILIER',
        dependencies: []
      },
      {
        id: 'CONSEILLER',
        value: 'records.OPERATION__IMMOBILIER__VENTE__MANDATAIRES.MANDATAIRE',
        dependencies: []
      },
      {
        id: 'MANDANT',
        value: 'records.OPERATION__IMMOBILIER__VENTE__VENDEURS.VENDEUR',
        dependencies: []
      }
    ]
  },
  metadata: {}
};
