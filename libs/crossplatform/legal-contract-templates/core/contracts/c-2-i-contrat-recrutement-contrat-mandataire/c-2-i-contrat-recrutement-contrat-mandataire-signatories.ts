import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const agencesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__RECRUTEMENT__AGENT__AGENCE'
  });
  const agent_commercialsSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL'
  });

  return [agencesSignatories, agent_commercialsSignatories].flat();
}

export const C2IContratRecrutementContratMandataireSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
