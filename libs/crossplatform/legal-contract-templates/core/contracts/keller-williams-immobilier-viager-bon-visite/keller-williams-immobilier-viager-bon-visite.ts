// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const Keller<PERSON>illiamsImmobilierViagerBonVisite: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES: {
        branches: {
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          },
          MANDAT: {
            constraints: {
              min: 1
            }
          },
          VISITE: {
            constraints: {
              min: 1,
              contractSpecific: true,
              autoCreate: true
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS: {
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE', 'COMPOSITION__ENSEMBLE_IMMOBILIER', 'COMPOSITION__LOT_ANNEXE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__VISITEURS: {
        branches: {
          VISITEUR: {
            constraints: {
              min: 1,
              contractSpecific: true
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__IMMOBILIER__VENTE__VISITEURS', 'BRANCHES', 'VISITEUR', 'RECORDS'],
          documentIds: ['carte_identite', 'personne_morale_KBIS']
        }
      ]
    },
    signature: {
      uncertifiedAllowed: true,
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__VISITEURS: {
          branches: {
            VISITEUR: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'KELLER_WILLIAMS_IMMOBILIER_VIAGER_BON_VISITE',
  jeffersonPath: 'mynotary/kellerWilliams/immobilierViager/bonVisite.json',
  label: 'Bon de Visite - Viager',
  mainContract: false,
  originTemplate: 'IMMOBILIER_VENTE_ANCIEN_BON_VISITE'
};
