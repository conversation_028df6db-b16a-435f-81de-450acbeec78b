import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const vendeursSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS'
  });

  return [vendeursSignatories].flat();
}

export const ColdwellBankerImmobilierDipColdwellSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
