// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const CdcImmobilierVenteIntegrationCdcHabitatNonConventionneCollectif: LegalContractTemplate = {
  config: {
    defaultSubscribers: [
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME',
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__GESTIONNAIRE_VENTE',
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__REFERENT_VENTE',
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__CLERC_NOTAIRE',
      'OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__VENDEUR_CDC'
    ],
    operationRecords: {
      OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS: {
        constraints: {
          subOperationOnly: true
        },
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE']
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__BAILLEUR_SOCIAL: {
        branches: {
          BAILLEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR: {
        branches: {
          REPRESENTANT_BAILLEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME: {
        branches: {
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_VENTE_PROGRAMME: {
        branches: {
          FICHE_VENTE: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__COPROPRIETE: {
        branches: {
          COPROPRIETE: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME: {
        branches: {
          NOTAIRE_PROGRAMME: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__FICHE_VENTE: {
        branches: {
          VENTE: {
            constraints: {
              min: 1
            }
          },
          FINANCEMENT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__ACQUEREUR: {
        branches: {
          ACQUEREUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__INTERVENANTS: {
        branches: {
          INTERVENANT: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR: {
        branches: {
          NOTAIRE_ACQUEREUR: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__GESTIONNAIRE_VENTE: {
        branches: {
          GESTIONNAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__REFERENT_VENTE: {
        branches: {
          REFERENT: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__CLERC_NOTAIRE: {
        branches: {
          CLERC: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__VENDEUR_CDC: {
        branches: {
          VENDEUR_CDC: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__ACQUEREUR: {
          branches: {
            ACQUEREUR: true
          },
          defaultGroup: 1
        },
        OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__INTERVENANTS: {
          branches: {
            INTERVENANT: true
          },
          defaultGroup: 1
        },
        OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR: {
          branches: {
            REPRESENTANT_BAILLEUR: true
          },
          defaultGroup: 2
        }
      }
    },
    registeredLetter: {
      letterTemplateId: 'REGISTERED_LETTER_CDC_PROMESSE',
      letterSubstitutions: {
        senderAddress: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'address', 'formattedAddress']
            }
          ]
        },
        senderName: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'firstname']
            },
            {
              type: 'CONSTANT',
              constant: ' '
            },
            {
              type: 'VARIABLE',
              path: ['sender', 'lastname']
            }
          ],
          aggregate: {
            type: 'CONCAT'
          }
        },
        signatureDate: {
          items: [
            {
              type: 'VARIABLE',
              path: ['configuration', 'signatureTime'],
              transformers: [
                {
                  type: 'DATE'
                }
              ]
            }
          ]
        }
      },
      defaultReceivers: {
        OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__ACQUEREUR: {
          branches: {
            ACQUEREUR: true
          }
        }
      }
    }
  },
  id: 'CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_NON_CONVENTIONNE_COLLECTIF',
  jeffersonPath: 'mynotary/cdc/immobilier/vente/integrationCdcHabitatNonConventionneCollectif.json',
  label: 'Promesse - CDC Habitat Non Conventionné - Collectif',
  mainContract: false,
  originTemplate: 'CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_NON_CONVENTIONNE_COLLECTIF'
};
