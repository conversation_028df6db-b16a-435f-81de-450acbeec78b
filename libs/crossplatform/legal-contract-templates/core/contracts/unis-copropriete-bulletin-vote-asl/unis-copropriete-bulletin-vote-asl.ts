// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const UnisCoproprieteBulletinVoteAsl: LegalContractTemplate = {
  config: {
    operationRecords: {},
    registeredLetter: {},
    signature: {
      blockingConditions: [],
      creationLockedAnswers: {},
      mandatoryDocuments: []
    },
    isMail: true
  },
  id: 'UNIS_COPROPRIETE_BULLETIN_VOTE_ASL',
  jeffersonPath: 'mynotary/unis/copropriete/bulletinVoteAsl.json',
  label: 'Bulletin vote par correspondance ASL',
  mainContract: false,
  originTemplate: null
};
