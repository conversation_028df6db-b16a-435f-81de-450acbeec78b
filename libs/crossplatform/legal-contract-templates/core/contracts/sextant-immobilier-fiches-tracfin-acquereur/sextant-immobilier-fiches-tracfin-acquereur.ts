// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const SextantImmobilierFichesTracfinAcquereur: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES: {
        branches: {
          MANDAT: {
            constraints: {
              min: 1
            }
          },
          FINANCEMENT_ACQUEREUR: {
            constraints: {
              min: 1
            }
          },
          TRACFIN: {
            constraints: {
              min: 1,
              autoCreate: true,
              contractSpecific: true
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__ACQUEREURS: {
        branches: {
          ACQUEREUR: {
            constraints: {
              min: 0
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS: {
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 0
            },
            recordLinks: ['COMPOSITION__COPROPRIETE', 'COMPOSITION__ENSEMBLE_IMMOBILIER', 'COMPOSITION__LOT_ANNEXE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {}
    },
    registeredLetter: {}
  },
  id: 'SEXTANT_IMMOBILIER_FICHES_TRACFIN_ACQUEREUR',
  jeffersonPath: 'mynotary/sextant/immobilier/fichesTracfinAcquereur.json',
  label: 'Fiche Tracfin - Acquéreur',
  mainContract: false,
  originTemplate: null
};
