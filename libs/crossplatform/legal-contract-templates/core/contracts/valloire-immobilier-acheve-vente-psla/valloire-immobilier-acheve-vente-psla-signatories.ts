import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const acquereursSignatories = createDefaultSignatoriesFromRecords({
    config: { defaultGroup: 1 },
    ctx,
    linkTemplateId: 'LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__VENTE__ACQUEREUR'
  });
  const representant_bailleursSignatories = createDefaultSignatoriesFromRecords({
    config: { defaultGroup: 2 },
    ctx,
    linkTemplateId: 'LINK__OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME__REPRESENTANT_BAILLEUR'
  });

  return [acquereursSignatories, representant_bailleursSignatories].flat();
}

export const ValloireImmobilierAcheveVentePslaSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
