// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecrutementAgentConventionBsa: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL: {
        branches: {
          AGENT_COMMERCIAL: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__RECRUTEMENT__AGENT__SPONSORING: {
        branches: {
          CONTRAT_SPONSORING: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL', 'BRANCHES', 'AGENT_COMMERCIAL', 'RECORDS'],
          documentIds: ['carte_identite']
        }
      ]
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL: {
          branches: {
            AGENT_COMMERCIAL: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'RECRUTEMENT_AGENT_CONVENTION_BSA',
  jeffersonPath: 'mynotary/recrutement/agent/conventionBsa.json',
  label: 'Convention de Sponsoring - Formulaire BSA',
  mainContract: false,
  originTemplate: 'RECRUTEMENT_AGENT_CONVENTION_BSA'
};
