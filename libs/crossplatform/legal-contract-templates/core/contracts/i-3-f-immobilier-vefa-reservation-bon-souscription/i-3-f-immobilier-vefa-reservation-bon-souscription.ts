// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const I3FImmobilierVefaReservationBonSouscription: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES: {
        branches: {
          RESERVATAIRE: {
            constraints: {
              min: 1,
              contractSpecific: true
            }
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      defaultSignatories: {
        OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES: {
          branches: {
            RESERVATAIRE: true
          }
        },
        OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__COOPERATIVE: {
          branches: {
            COOPERATIVE: true
          }
        }
      },
      creationLockedAnswers: {}
    },
    registeredLetter: {}
  },
  id: 'I3F_IMMOBILIER_VEFA_RESERVATION_BON_SOUSCRIPTION',
  jef<PERSON>on<PERSON>ath: 'mynotary/i3F/immobilier/vefaReservation/bonSouscription.json',
  label: 'Bulletin de souscription',
  mainContract: false,
  originTemplate: null
};
