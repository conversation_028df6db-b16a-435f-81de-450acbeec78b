// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LcaFfbImmobilierConstructionMaisonIndividuelleAvenant: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__TERRAIN_CONSTRUCTIBLE: {
        branches: {
          TERRAIN_CONSTRUCTIBLE: {
            constraints: {
              min: 1,
              max: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__CONSTRUCTEUR: {
        branches: {
          CONSTRUCTEUR: {
            constraints: {
              min: 1,
              max: 1
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__MAITRE_OUVRAGE: {
        branches: {
          MAITRE_OUVRAGE: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__FICHES: {
        branches: {
          CONSTRUCTION: {
            constraints: {
              min: 1,
              max: 1
            }
          }
        }
      }
    },
    signature: {
      mandatoryDocuments: [],
      creationLockedAnswers: {
        signatureType: {
          items: [
            {
              type: 'CONSTANT',
              constant: 'ELECTRONIC',
              condition: [
                [
                  {
                    comparator: 'EQUALS',
                    left: {
                      items: [
                        {
                          type: 'VARIABLE',
                          path: [
                            'records',
                            'OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__FICHES',
                            'CONSTRUCTION',
                            '0',
                            'signature_electronique',
                            'value'
                          ]
                        }
                      ]
                    },
                    right: {
                      items: [
                        {
                          type: 'CONSTANT',
                          constant: 'oui'
                        }
                      ]
                    }
                  }
                ]
              ]
            },
            {
              type: 'CONSTANT',
              constant: 'PAPER',
              condition: [
                [
                  {
                    comparator: 'EQUALS',
                    left: {
                      items: [
                        {
                          type: 'VARIABLE',
                          path: [
                            'records',
                            'OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__FICHES',
                            'CONSTRUCTION',
                            '0',
                            'signature_electronique',
                            'value'
                          ]
                        }
                      ]
                    },
                    right: {
                      items: [
                        {
                          type: 'CONSTANT',
                          constant: 'non'
                        }
                      ]
                    }
                  }
                ]
              ]
            }
          ],
          aggregate: {
            type: 'CONCAT'
          }
        }
      },
      defaultSignatories: {
        OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__CONSTRUCTEUR: {
          branches: {
            CONSTRUCTEUR: true
          }
        },
        OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__MAITRE_OUVRAGE: {
          branches: {
            MAITRE_OUVRAGE: true
          }
        }
      }
    },
    registeredLetter: {
      letterTemplateId: 'REGISTERED_LETTER_CCMI',
      letterSubstitutions: {
        senderAddress: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'address', 'formattedAddress']
            }
          ]
        },
        senderName: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'firstname']
            },
            {
              type: 'CONSTANT',
              constant: ' '
            },
            {
              type: 'VARIABLE',
              path: ['sender', 'lastname']
            }
          ],
          aggregate: {
            type: 'CONCAT'
          }
        },
        signatureDate: {
          items: [
            {
              type: 'VARIABLE',
              path: ['configuration', 'signatureTime'],
              transformers: [
                {
                  type: 'DATE'
                }
              ]
            }
          ]
        }
      },
      defaultReceivers: {
        OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__MAITRE_OUVRAGE: {
          branches: {
            MAITRE_OUVRAGE: true
          }
        }
      }
    }
  },
  id: 'LCA_FFB__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE__AVENANT',
  jeffersonPath: 'lcaFfb/immobilier/constructionMaisonIndividuelle/avenant.json',
  label: 'Avenant - CCMI',
  mainContract: false,
  originTemplate: null
};
