import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory,
  getBailCommercialBailleurOrRepresentantSignatories
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const bailleursOrRepresentantSignatories = getBailCommercialBailleurOrRepresentantSignatories({ ctx });
  const locatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__LOCATAIRES'
  });

  return [bailleursOrRepresentantSignatories, locatairesSignatories].flat();
}

export const UnisLocationCommercialAvenantBailEcoenergieSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
