import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const visiteursSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__VISITEURS'
  });

  return [visiteursSignatories].flat();
}

export const EfficityImmobilierBonVisiteSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
