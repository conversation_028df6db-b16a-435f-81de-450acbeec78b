// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LocationCautionDroitCommun: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__LOCATION__FICHES: {
        branches: {
          LOCATION: {
            constraints: {
              min: 1,
              max: 1
            }
          },
          CAUTIONNEMENT: {
            constraints: {
              min: 1,
              max: 1,
              autoCreate: true,
              contractSpecific: true
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
        branches: {
          LOCATAIRE: {
            constraints: {
              min: 1,
              contractSpecific: true
            },
            recordLinks: ['SITUATION_MARITALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__BAILLEURS: {
        branches: {
          BAILLEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__GARANTS: {
        branches: {
          GARANT: {
            constraints: {
              min: 1,
              contractSpecific: true
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES: {
        branches: {
          BIEN_LOUE: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE', 'COMPOSITION__ENSEMBLE_IMMOBILIER', 'COMPOSITION__LOT_ANNEXE']
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__IMMOBILIER__LOCATION__GARANTS', 'BRANCHES', 'GARANT', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__GARANTS',
            'BRANCHES',
            'GARANT',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__GARANTS',
            'BRANCHES',
            'GARANT',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__GARANTS',
            'BRANCHES',
            'GARANT',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        }
      ]
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__LOCATION__GARANTS: {
          config: {
            mention: {
              text: "Je m'engage en qualité de caution à payer à {{ BAILLEUR }}, ce que lui doit {{ LOCATAIRE }}, le LOCATAIRE dans la présente mention, en cas de défaillance de celui-ci, dans le paiement des loyers, complément de loyer si exigible, charges, et tous les frais accessoires y relatifs dont les éventuels frais de procédure, dans la limite des montants dûs par le locataire, d'un montant maximum de {{ PRIX }}. Je reconnais également ne pas pouvoir exiger du BAILLEUR qu'il poursuive d'abord le LOCATAIRE ou qu'il divise ses poursuites entre les autres cautions éventuelles",
              variables: {
                BAILLEUR: {
                  type: 'CONCAT',
                  path: ['OPERATION__IMMOBILIER__LOCATION__BAILLEURS', 'BRANCHES', 'BAILLEUR', 'RECORDS'],
                  items: [
                    {
                      type: 'QUESTION_ID',
                      value: 'personne_morale_denomination'
                    },
                    {
                      type: 'QUESTION_ID',
                      value: 'personne_morale_forme_sociale',
                      withPrefix: ' '
                    },
                    {
                      type: 'QUESTION_ID',
                      value: 'prenoms'
                    },
                    {
                      type: 'QUESTION_ID',
                      value: 'nom',
                      withPrefix: ' '
                    }
                  ]
                },
                LOCATAIRE: {
                  type: 'CONCAT',
                  path: ['OPERATION__IMMOBILIER__LOCATION__LOCATAIRES', 'BRANCHES', 'LOCATAIRE', 'RECORDS'],
                  items: [
                    {
                      type: 'QUESTION_ID',
                      value: 'prenoms'
                    },
                    {
                      type: 'QUESTION_ID',
                      value: 'nom',
                      withPrefix: ' '
                    }
                  ]
                },
                PRIX: {
                  type: 'ADD',
                  path: ['OPERATION__IMMOBILIER__LOCATION__FICHES', 'BRANCHES', 'CAUTIONNEMENT', 'RECORDS'],
                  items: [
                    {
                      type: 'QUESTION_ID',
                      value: 'garantie_cautionnement_montant_total'
                    }
                  ]
                }
              }
            }
          },
          branches: {
            GARANT: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'LOCATION__CAUTION_DROIT_COMMUN',
  jeffersonPath: 'mynotary/location/cautionDroitCommun.json',
  label: 'Caution - Droit commun',
  mainContract: false,
  originTemplate: null
};
