import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const mandatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES'
  });
  const agence_delegatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__AGENCE_DELEGATAIRE'
  });

  return [mandatairesSignatories, agence_delegatairesSignatories].flat();
}

export const ImmobilierVenteAncienDelegationMandatSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
