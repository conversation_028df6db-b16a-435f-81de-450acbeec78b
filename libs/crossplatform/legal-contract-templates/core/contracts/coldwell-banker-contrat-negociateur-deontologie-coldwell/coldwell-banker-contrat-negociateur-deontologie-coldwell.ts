// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const ColdwellBankerContratNegociateurDeontologieColdwell: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__COLDWELL_BANKER__MANDANT: {
        branches: {
          MANDANT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__COLDWELL_BANKER__AGENT: {
        branches: {
          AGENT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__COLDWELL_BANKER__CONTRAT_NEGOCIATEUR: {
        branches: {
          CONTRAT_NEGOCIATEUR: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__COLDWELL_BANKER__AGENT: {
          branches: {
            AGENT: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'COLDWELL_BANKER_CONTRAT_NEGOCIATEUR_DEONTOLOGIE_COLDWELL',
  jeffersonPath: 'mynotary/coldwellBanker/contratNegociateur/deontologieColdwell.json',
  label: 'DEONTOLOGIE_COLDWELL',
  mainContract: false,
  originTemplate: 'COLDWELL_BANKER_CONTRAT_NEGOCIATEUR_DEONTOLOGIE_COLDWELL'
};
