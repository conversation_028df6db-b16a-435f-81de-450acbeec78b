import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const agentsSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__COLDWELL_BANKER__AGENT'
  });

  return [agentsSignatories].flat();
}

export const ColdwellBankerContratNegociateurDeontologieColdwellSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
