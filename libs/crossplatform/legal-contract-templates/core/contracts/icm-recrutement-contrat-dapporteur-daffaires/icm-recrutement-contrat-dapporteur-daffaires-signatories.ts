import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const apporteursSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__APPORTEUR'
  });
  const agentsSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__AGENTS'
  });

  return [apporteursSignatories, agentsSignatories].flat();
}

export const IcmRecrutementContratDapporteurDaffairesSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
