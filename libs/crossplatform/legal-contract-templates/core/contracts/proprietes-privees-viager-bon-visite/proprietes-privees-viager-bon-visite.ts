// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const ProprietesPriveesViagerBonVisite: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES: {
        branches: {
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          },
          MANDAT: {
            constraints: {
              min: 1
            }
          },
          VISITE: {
            constraints: {
              min: 1,
              contractSpecific: true,
              autoCreate: true
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO: {
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE', 'COMPOSITION__LOTISSEMENT', 'COMPOSITION__LOT_ANNEXE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__VISITEURS: {
        branches: {
          VISITEUR: {
            constraints: {
              min: 1,
              contractSpecific: true
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__IMMOBILIER__VENTE__VISITEURS', 'BRANCHES', 'VISITEUR', 'RECORDS'],
          documentIds: ['carte_identite', 'personne_morale_KBIS']
        }
      ]
    },
    signature: {
      uncertifiedAllowed: true,
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {
        signatureType: {
          items: [
            {
              type: 'CONSTANT',
              constant: 'PAPER'
            }
          ]
        }
      },
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__VISITEURS: {
          branches: {
            VISITEUR: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'PROPRIETES_PRIVEES_VIAGER_BON_VISITE',
  jeffersonPath: 'mynotary/proprietesPrivees/viager/bonVisite.json',
  label: 'Bon de Visite',
  mainContract: false,
  originTemplate: 'IMMOBILIER_VENTE_ANCIEN_BON_VISITE'
};
