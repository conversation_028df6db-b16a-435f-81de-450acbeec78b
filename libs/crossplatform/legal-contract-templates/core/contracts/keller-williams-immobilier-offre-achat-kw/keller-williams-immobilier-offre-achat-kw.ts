// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const Keller<PERSON>illiamsImmobilierOffreAchatKw: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE__OFFRE: {
        branches: {
          FICHE_OFFRE: {
            constraints: {
              min: 1,
              autoCreate: true,
              contractSpecific: true
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__VENDEURS: {
        branches: {
          VENDEUR: {
            constraints: {
              min: 0
            },
            recordLinks: ['SITUATION_MARITALE', 'CAPACITE', 'PROCURATION', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__OFFRANTS: {
        branches: {
          OFFRANT: {
            constraints: {
              min: 1,
              contractSpecific: true
            },
            recordLinks: ['SITUATION_MARITALE', 'CAPACITE', 'PROCURATION', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES: {
        branches: {
          MANDAT: {
            constraints: {
              min: 1
            }
          },
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS: {
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: [
              'COMPOSITION__COPROPRIETE',
              'COMPOSITION__ENSEMBLE_IMMOBILIER',
              'COMPOSITION__LOT_ANNEXE',
              'COMPOSITION__LOTISSEMENT'
            ]
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__IMMOBILIER__VENTE__VENDEURS', 'BRANCHES', 'VENDEUR', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__VENDEURS',
            'BRANCHES',
            'VENDEUR',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__VENDEURS',
            'BRANCHES',
            'VENDEUR',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__VENDEURS',
            'BRANCHES',
            'VENDEUR',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__VENDEURS',
            'BRANCHES',
            'VENDEUR',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__VENDEURS',
            'BRANCHES',
            'VENDEUR',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: ['OPERATION__IMMOBILIER__VENTE__OFFRANTS', 'BRANCHES', 'OFFRANT', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__OFFRANTS',
            'BRANCHES',
            'OFFRANT',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__OFFRANTS',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__OFFRANTS',
            'BRANCHES',
            'OFFRANT',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__OFFRANTS',
            'BRANCHES',
            'OFFRANT',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__OFFRANTS',
            'BRANCHES',
            'OFFRANT',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'BRANCHES', 'FINANCEMENT_ACQUEREUR', 'RECORDS'],
          documentIds: ['emprunt_simulation']
        }
      ]
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {
        signatureType: {
          items: [
            {
              type: 'CONSTANT',
              constant: 'PAPER',
              condition: [
                [
                  {
                    comparator: 'EQUALS',
                    left: {
                      items: [
                        {
                          type: 'VARIABLE',
                          path: [
                            'records',
                            'OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES',
                            'OFFRE_ACHAT',
                            '0',
                            'tapuscrit',
                            'value'
                          ]
                        }
                      ]
                    },
                    right: {
                      items: [
                        {
                          type: 'CONSTANT',
                          constant: 'oui'
                        }
                      ]
                    }
                  }
                ]
              ]
            }
          ]
        }
      },
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__OFFRANTS: {
          branches: {
            OFFRANT: true
          }
        },
        OPERATION__IMMOBILIER__VENTE__VENDEURS: {
          branches: {
            VENDEUR: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'KELLER_WILLIAMS_IMMOBILIER_OFFRE_ACHAT_KW',
  jeffersonPath: 'mynotary/kellerWilliams/immobilier/offreAchatKw.json',
  label: "Offre d'achat",
  mainContract: false,
  originTemplate: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_AVEC_VENDEUR'
};
