// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OrganisationInterneProtocoleInterneTracfin: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {}
    },
    registeredLetter: {}
  },
  id: 'ORGANISATION_INTERNE_PROTOCOLE_INTERNE_TRACFIN',
  jeffersonPath: 'mynotary/organisationInterne/protocoleInterneTracfin.json',
  label: 'Cadre Général - Protocole TRACFIN',
  mainContract: false,
  originTemplate: null
};
