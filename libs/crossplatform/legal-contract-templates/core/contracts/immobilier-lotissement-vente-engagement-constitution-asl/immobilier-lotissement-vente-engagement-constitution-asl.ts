// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const ImmobilierLotissementVenteEngagementConstitutionAsl: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__LOTS: {
        constraints: {
          subOperationOnly: true
        },
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__LOTISSEUR: {
        branches: {
          LOTISSEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOTISSEMENT_VENTE__CONTRAT_VENTE: {
        branches: {
          CONTRAT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__REPRESENTANT_LOTISSEUR: {
        branches: {
          REPRESENTANT_LOTISSEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__FICHE_PROGRAMME: {
        branches: {
          PROGRAMME: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    folder: {},
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__REPRESENTANT_LOTISSEUR: {
          branches: {
            REPRESENTANT_LOTISSEUR: true
          }
        }
      }
    }
  },
  id: 'IMMOBILIER_LOTISSEMENT_VENTE_ENGAGEMENT_CONSTITUTION_ASL',
  jeffersonPath: 'mynotary/immobilier/lotissementVente/engagementConstitutionAsl.json',
  label: "Engagement de constitution d'une association syndicale",
  mainContract: false,
  originTemplate: null
};
