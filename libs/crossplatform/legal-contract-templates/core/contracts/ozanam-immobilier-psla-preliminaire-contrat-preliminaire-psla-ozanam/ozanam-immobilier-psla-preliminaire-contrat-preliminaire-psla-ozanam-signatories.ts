import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const representant_bailleursSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__PSLA_PROGRAMME__REPRESENTANT_BAILLEUR'
  });
  const acquereursSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE__ACQUEREUR'
  });

  return [representant_bailleursSignatories, acquereursSignatories].flat();
}

export const OzanamImmobilierPslaPreliminaireContratPreliminairePslaOzanamSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
