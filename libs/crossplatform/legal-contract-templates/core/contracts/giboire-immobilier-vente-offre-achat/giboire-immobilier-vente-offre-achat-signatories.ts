import {
  GetDefaultSignatoriesArgs,
  getMention,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const offrantsSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS'
  });
  const vendeursSignatories = createDefaultSignatoriesFromRecords({
    config: {
      mention: getMention({
        ctx,
        mentions: {
          text: 'Bon pour acceptation de vendre au prix de {{ PRIX }} ',
          variables: {
            PRIX: {
              items: [{ type: 'QUESTION_ID', value: 'offre_prix' }],
              path: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'BRANCHES', 'OFFRE_ACHAT', 'RECORDS'],
              type: 'ADD'
            }
          }
        }
      })
    },
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS'
  });

  return [offrantsSignatories, vendeursSignatories].flat();
}

export const GiboireImmobilierVenteOffreAchatSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
