// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const SdAccessImmobilierVenteContratPreliminaireBrs: LegalContractTemplate = {
  config: {
    defaultSubscribers: ['OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME'],
    operationRecords: {
      OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__LOTS: {
        constraints: {
          subOperationOnly: true
        },
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE']
          }
        }
      },
      OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR: {
        branches: {
          ACQUEREUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE']
          }
        }
      },
      OPERATION__SD_ACCESS__IMMOBILIER__VENTE__FICHE_BRS: {
        branches: {
          CONTRAT_PRELIMINAIRE: {
            constraints: {
              min: 1
            }
          },
          FINANCEMENT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__FICHE_BRS: {
        branches: {
          PROGRAMME: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__BAILLEUR: {
        branches: {
          BAILLEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR: {
        branches: {
          REPRESENTANT_BAILLEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME: {
        branches: {
          NOTAIRE_PROGRAMME: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__SD_ACCESS__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR: {
        branches: {
          NOTAIRE_ACQUEREUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__COPROPRIETE: {
        branches: {
          COPROPRIETE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR', 'BRANCHES', 'ACQUEREUR', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        }
      ]
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR: {
          branches: {
            REPRESENTANT_BAILLEUR: true
          }
        },
        OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR: {
          branches: {
            ACQUEREUR: true
          }
        }
      }
    },
    registeredLetter: {
      letterTemplateId: 'REGISTERED_LETTER_PSLA',
      letterSubstitutions: {
        senderAddress: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'address', 'formattedAddress']
            }
          ]
        },
        senderName: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'firstname']
            },
            {
              type: 'CONSTANT',
              constant: ' '
            },
            {
              type: 'VARIABLE',
              path: ['sender', 'lastname']
            }
          ],
          aggregate: {
            type: 'CONCAT'
          }
        },
        signatureDate: {
          items: [
            {
              type: 'VARIABLE',
              path: ['configuration', 'signatureTime'],
              transformers: [
                {
                  type: 'DATE'
                }
              ]
            }
          ]
        }
      },
      defaultReceivers: {
        OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR: {
          branches: {
            ACQUEREUR: true
          }
        }
      }
    }
  },
  id: 'SD_ACCESS_IMMOBILIER_VENTE_CONTRAT_PRELIMINAIRE_BRS',
  jeffersonPath: 'mynotary/sdAccess/immobilier/vente/contratPreliminaireBrs.json',
  label: 'Contrat Préliminaire BRS',
  mainContract: false,
  originTemplate: null
};
