import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const acquereursSignatories = createDefaultSignatoriesFromRecords({
    config: { defaultGroup: 1 },
    ctx,
    linkTemplateId: 'LINK__OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__ACQUEREUR'
  });
  const intervenantsSignatories = createDefaultSignatoriesFromRecords({
    config: { defaultGroup: 1 },
    ctx,
    linkTemplateId: 'LINK__OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__INTERVENANTS'
  });
  const representant_bailleursSignatories = createDefaultSignatoriesFromRecords({
    config: { defaultGroup: 2 },
    ctx,
    linkTemplateId: 'LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR'
  });

  return [acquereursSignatories, intervenantsSignatories, representant_bailleursSignatories].flat();
}

export const CdcImmobilierVentePromesseCdcHabitatNonConventionnePavillonSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
