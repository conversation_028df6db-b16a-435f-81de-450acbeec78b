// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const ProprietesPriveesImmobilierFicheDeRenseignementVendeurBusiness: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES: {
        branches: {
          MANDAT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: []
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {}
    },
    registeredLetter: {}
  },
  id: 'PROPRIETES_PRIVEES__IMMOBILIER__FICHE_DE_RENSEIGNEMENT_VENDEUR_BUSINESS',
  jef<PERSON>onPath: 'mynotary/proprietesPrivees/immobilier/ficheDeRenseignementVendeurBusiness.json',
  label: 'Fiche de Renseignement Vendeur Business - Procédure Papier',
  mainContract: false,
  originTemplate: null
};
