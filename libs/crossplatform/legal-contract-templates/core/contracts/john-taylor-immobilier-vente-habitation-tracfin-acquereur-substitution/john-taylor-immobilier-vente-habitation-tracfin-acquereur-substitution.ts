// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const JohnTaylorImmobilierVenteHabitationTracfinAcquereurSubstitution: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE__ACQUEREURS: {
        branches: {
          ACQUEREUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__ACQUEREUR_CESSIONNAIRE: {
        branches: {
          ACQUEREUR_CESSIONNAIRE: {
            constraints: {
              min: 1
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__VENDEURS: {
        branches: {
          VENDEUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS: {
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE', 'COMPOSITION__ENSEMBLE_IMMOBILIER', 'COMPOSITION__LOT_ANNEXE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES: {
        branches: {
          MANDAT: {
            constraints: {
              min: 1
            }
          },
          TRACFIN: {
            constraints: {
              min: 1,
              autoCreate: true,
              contractSpecific: true
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANAGER_AGENCE: {
        branches: {
          MANAGER: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__DECLARANT_TRACFIN: {
        branches: {
          DECLARANT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__CORRESPONDANT_TRACFIN: {
        branches: {
          CORRESPONDANT: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    registeredLetter: {},
    signature: {
      blockingConditions: [],
      uncertifiedAllowed: true,
      creationLockedAnswers: {},
      mandatoryDocuments: [],
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
          branches: {
            MANDATAIRE: true
          }
        },
        OPERATION__IMMOBILIER__VENTE__MANAGER_AGENCE: {
          branches: {
            MANAGER: true
          }
        },
        OPERATION__IMMOBILIER__VENTE__DECLARANT_TRACFIN: {
          branches: {
            DECLARANT: true
          }
        },
        OPERATION__IMMOBILIER__VENTE__CORRESPONDANT_TRACFIN: {
          branches: {
            CORRESPONDANT: true
          }
        }
      }
    }
  },
  id: 'JOHN_TAYLOR_IMMOBILIER_VENTE_HABITATION_TRACFIN_ACQUEREUR_SUBSTITUTION',
  jeffersonPath: 'mynotary/johnTaylor/immobilier/venteHabitation/tracfinAcquereurSubstitution.json',
  label: 'TRACFIN Acquéreur - Substitution',
  mainContract: false,
  originTemplate: null
};
