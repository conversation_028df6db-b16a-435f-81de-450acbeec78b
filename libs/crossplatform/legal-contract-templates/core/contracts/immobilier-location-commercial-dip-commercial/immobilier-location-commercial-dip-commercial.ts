// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const ImmobilierLocationCommercialDipCommercial: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL: {
        branches: {
          MANDAT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__BAILLEURS: {
        branches: {
          BAILLEUR: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
        branches: {
          LOCATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__LOCATION__BAILLEURS: {
          branches: {
            BAILLEUR: true
          }
        },
        OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
          branches: {
            LOCATAIRE: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'IMMOBILIER_LOCATION_COMMERCIAL_DIP_COMMERCIAL',
  jeffersonPath: 'mynotary/immobilier/locationCommercial/dipCommercial.json',
  label: "Document d'informations précontractuelles",
  mainContract: false,
  originTemplate: 'IMMOBILIER_LOCATION_COMMERCIAL_DIP_COMMERCIAL'
};
