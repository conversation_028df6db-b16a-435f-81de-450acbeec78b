// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const HermesImmobilierTransactionProConventionApporteurAffaire: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE__APPORTEUR: {
        branches: {
          APPORTEUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES: {
        branches: {
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__APPORTEUR: {
          branches: {
            APPORTEUR: true
          }
        },
        OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
          branches: {
            MANDATAIRE: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'HERMES_IMMOBILIER_TRANSACTION_PRO_CONVENTION_APPORTEUR_AFFAIRE',
  jeffersonPath: 'mynotary/hermes/immobilier/transactionPro/conventionApporteurAffaire.json',
  label: "Convention d'apporteur d'affaires",
  mainContract: false,
  originTemplate: null
};
