/**
 * This code is autogenerated don't modify it manually
 */
import * as clauses from './clauses';

export const legalContractClauseMap: Record<string, object> = {
  AVENANT_MANDAT: clauses.AvenantMandat,
  AVENANT_MANDAT_RECHERCHE: clauses.AvenantMandatRecherche,
  AVIS_VALEUR: clauses.AvisValeur,
  CAPACITE_REPRESENTATION: clauses.CapaciteRepresentation,
  CAPACITE_REPRESENTATION_ANGLAIS: clauses.CapaciteRepresentationAnglais,
  CAPACITE_REPRESENTATION_ANGLAIS_SANS: clauses.CapaciteRepresentationAnglaisSans,
  CAPACITE_REPRESENTATION_GIBOIRE: clauses.CapaciteRepresentationGiboire,
  CAPACITE_REPRESENTATION_GIBOIRE_SANS: clauses.CapaciteRepresentationGiboireSans,
  CAPACITE_REPRESENTATION_SANS: clauses.CapaciteRepresentationSans,
  CAPACITE_REPRESENTATION_VIAGER_CONSULTING: clauses.CapaciteRepresentationViagerConsulting,
  CAPACITE_SIMPLE: clauses.CapaciteSimple,
  CONCOURS_AGENT_IMMOBILIER: clauses.ConcoursAgentImmobilier,
  CONCOURS_AGENT_IMMOBILIER_ANGLAIS: clauses.ConcoursAgentImmobilierAnglais,
  CONCOURS_AGENT_IMMOBILIER_BENEDIC: clauses.ConcoursAgentImmobilierBenedic,
  CONCOURS_AGENT_IMMOBILIER_LOCATION: clauses.ConcoursAgentImmobilierLocation,
  CONCOURS_AGENT_IMMOBILIER_LOCATION_BENEDIC: clauses.ConcoursAgentImmobilierLocationBenedic,
  CONCOURS_AGENT_IMMOBILIER_POLYNESIE: clauses.ConcoursAgentImmobilierPolynesie,
  CONCOURS_AGENT_IMMOBILIER_SIMPLE: clauses.ConcoursAgentImmobilierSimple,
  CONCOURS_AGENT_KW: clauses.ConcoursAgentKw,
  CONCOURS_AGENT_KW_POLYNESIE: clauses.ConcoursAgentKwPolynesie,
  CONCOURS_BAILLEUR: clauses.ConcoursBailleur,
  CONCOURS_MANDATAIRE: clauses.ConcoursMandataire,
  CONCOURS_MANDATAIRE_KW: clauses.ConcoursMandataireKw,
  CONCOURS_MANDATAIRE_LOCATION: clauses.ConcoursMandataireLocation,
  CONCOURS_MANDATAIRE_PROPRIETE_PRIVEE: clauses.ConcoursMandataireProprietePrivee,
  CONCOURS_PROMOTEUR: clauses.ConcoursPromoteur,
  ETAT_CIVIL: clauses.EtatCivil,
  ETAT_CIVIL_AGENCE_DIRECTE: clauses.EtatCivilAgenceDirecte,
  ETAT_CIVIL_AJP: clauses.EtatCivilAjp,
  ETAT_CIVIL_AJP_SANS: clauses.EtatCivilAjpSans,
  ETAT_CIVIL_AJP_SIMPLE: clauses.EtatCivilAjpSimple,
  ETAT_CIVIL_AJP_SIMPLE_SANS: clauses.EtatCivilAjpSimpleSans,
  ETAT_CIVIL_ANGLAIS: clauses.EtatCivilAnglais,
  ETAT_CIVIL_BEAUX_VILLAGES: clauses.EtatCivilBeauxVillages,
  ETAT_CIVIL_BEAUX_VILLAGES_SANS: clauses.EtatCivilBeauxVillagesSans,
  ETAT_CIVIL_DESIMO: clauses.EtatCivilDesimo,
  ETAT_CIVIL_DUVAL: clauses.EtatCivilDuval,
  ETAT_CIVIL_GIBOIRE: clauses.EtatCivilGiboire,
  ETAT_CIVIL_GIBOIRE_SANS: clauses.EtatCivilGiboireSans,
  ETAT_CIVIL_LIA: clauses.EtatCivilLia,
  ETAT_CIVIL_MANDAT: clauses.EtatCivilMandat,
  ETAT_CIVIL_MANDAT_MGL: clauses.EtatCivilMandatMgl,
  ETAT_CIVIL_MINIMAL: clauses.EtatCivilMinimal,
  ETAT_CIVIL_MINIMAL_SANS_REGIME: clauses.EtatCivilMinimalSansRegime,
  ETAT_CIVIL_POLYNESIE: clauses.EtatCivilPolynesie,
  ETAT_CIVIL_PP_CPV: clauses.EtatCivilPpCpv,
  ETAT_CIVIL_PP_CPV_SANS: clauses.EtatCivilPpCpvSans,
  ETAT_CIVIL_SANS: clauses.EtatCivilSans,
  ETAT_CIVIL_SANS_ANGLAIS: clauses.EtatCivilSansAnglais,
  ETAT_CIVIL_SANS_POLYNESIE: clauses.EtatCivilSansPolynesie,
  ETAT_CIVIL_SIMPLE: clauses.EtatCivilSimple,
  ETAT_CIVIL_SIMPLE_ANGLAIS: clauses.EtatCivilSimpleAnglais,
  ETAT_CIVIL_SIMPLE_ANGLAIS_SANS: clauses.EtatCivilSimpleAnglaisSans,
  ETAT_CIVIL_SIMPLE_CCMI: clauses.EtatCivilSimpleCcmi,
  ETAT_CIVIL_SIMPLE_POLYNESIE: clauses.EtatCivilSimplePolynesie,
  ETAT_CIVIL_SIMPLE_SANS: clauses.EtatCivilSimpleSans,
  ETAT_CIVIL_SIMPLE_SANS_POLYNESIE: clauses.EtatCivilSimpleSansPolynesie,
  ETAT_CIVIL_SIMPLE_SANS_REGIME: clauses.EtatCivilSimpleSansRegime,
  ETAT_CIVIL_SIMPLE_SANS_WHITEBIRD: clauses.EtatCivilSimpleSansWhitebird,
  ETAT_CIVIL_SYNTHESE: clauses.EtatCivilSynthese,
  ETAT_CIVIL_SYNTHESE_POLYNESIE: clauses.EtatCivilSynthesePolynesie,
  FICHE_BENEFICIAIRE: clauses.FicheBeneficiaire,
  LIA: clauses.Lia,
  MANDAT_CGV: clauses.MandatCgv,
  MANDAT_CGV_2: clauses.MandatCgv2,
  MANDAT_DIP: clauses.MandatDip,
  MANDAT_LOTISSEMENT: clauses.MandatLotissement,
  MANDAT_RECHERCHE_CGV: clauses.MandatRechercheCgv,
  MANDAT_RECHERCHE_DIP: clauses.MandatRechercheDip,
  PROCURATION_VENDEUR: clauses.ProcurationVendeur,
  REPRESENTATION: clauses.Representation
};
export function getLegalContractClause(id: string) {
  return legalContractClauseMap[id];
}
