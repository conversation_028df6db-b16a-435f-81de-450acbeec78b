// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationTroisGImmoTroisGImmoLocationHabitation: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ nom_bailleur }} / {{ adresse }} / {{ nom_locataire }}',
    emailLabelPattern: '{{ adresse }}',
    tags: {
      adresse: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES',
        questionId: 'adresse',
        format: 'ADDRESS',
        max: 1,
        label: 'Adresse'
      },
      nom_bailleur: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom bailleur'
      },
      nom_locataire: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom locataire'
      },
      biens_loues: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES',
        order: 0,
        max: 1
      },
      bailleurs: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        order: 1,
        max: 1
      },
      locataires: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_VISITE', 'VISITEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'BAILLEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'LOCATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'NOUVEAU_LOCATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'REPRESENTANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'BIENS_LOUES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'GARANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'SIGNATAIRE_AGENCE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'AGENCE_DELEGATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_DIP',
        label: "Document d'Informations Précontractuelles"
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_LOCATION',
        label: 'Mandat de location ou de gestion'
      },
      {
        id: 'LOCATION__MANDAT_DE_LOCATION_ANGLAIS',
        label: 'Mandat de location - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_DROIT_COMMUN',
        label: 'Mandat de location - Parking, box, cave'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_LOCATION_AVENANT',
        label: 'Mandat de Location / Gestion - Avenant'
      },
      {
        id: 'LOCATION__AVENANT_MANDAT_DE_LOCATION_ANGLAIS',
        label: 'Mandat de Location / Gestion - Avenant Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_RECHERCHE',
        label: 'Mandat de recherche'
      },
      {
        id: 'LOCATION__MANDAT_DE_RECHERCHE_ANGLAIS',
        label: 'Mandat de Recherche - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_RECHERCHE_AVENANT',
        label: 'Mandat de recherche - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_SAISONNIER',
        label: 'Mandat de location Saisonnière'
      },
      {
        id: 'LOCATION__MANDAT_DE_LOCATION_SAISONNIERE_ANGLAIS',
        label: 'Mandat de location Saisonnière - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
        label: 'Délégation de Mandat de Location'
      },
      {
        id: 'LOCATION__DELEGATION_DE_MANDAT_DE_RECHERCHE',
        label: 'Délégation de Mandat de Recherche de location'
      },
      {
        id: 'IMMOBILIER_LOCATION_BON_VISITE_LOCATION',
        label: 'Bon de Visite'
      },
      {
        id: 'IMMOBILIER_LOCATION_ETAT_DES_LIEUX',
        label: 'Etat des lieux'
      },
      {
        id: 'IMMOBILIER_LOCATION_ENGAGEMENT_LOCATION',
        label: 'Engagement de Location'
      },
      {
        id: 'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
        label: 'Procuration Bail - Bailleur'
      },
      {
        id: 'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
        label: 'Procuration Bail - Locataire'
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL',
        label: "Bail d'habitation"
      },
      {
        id: 'LOCATION__BAIL_HABITATION_ANGLAIS',
        label: "Bail d'habitation - Anglais"
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_AVENANT',
        label: "Bail d'habitation - Avenant libre"
      },
      {
        id: 'LOCATION__AVENANT_BAIL_LOCATION_ANGLAIS',
        label: "Bail d'habitation - Avenant libre Anglais"
      },
      {
        id: 'IMMOBILIER__LOCATION__AVENANT_BAIL_CHANGEMENT_COLOCATAIRE',
        label: "Bail d'habitation - Avenant Changement Colocataire"
      },
      {
        id: 'IMMOBILIER_LOCATION_CAUTIONNEMENT',
        label: 'Acte de Cautionnement'
      },
      {
        id: 'LOCATION__ACTE_DE_CAUTIONNEMENT_ANGLAIS',
        label: 'Acte de Cautionnement - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_MOBILITE',
        label: 'Bail de mobilite'
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_SAISONNIER',
        label: 'Bail location saisonnière'
      },
      {
        id: 'LOCATION__BAIL_LOCATION_SAISONNIERE_ANGLAIS',
        label: 'Bail location saisonnière - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_COMMUN',
        label: 'Bail civil de droit commun (Parking, box, cave...)'
      },
      {
        id: 'IMMOBILIER_LOCATION_TRACFIN_LOCATION',
        label: 'Evaluation TRACFIN - Location'
      }
    ],
    recordExtensions: [],
    hiddenPages: {
      documents: true
    },
    documentsToExclude: [
      'acte_depot',
      'autorisation_urbanisme',
      'autorisation_urbanisme_delivrance',
      'autorisation_vente_anticipee',
      'cahier_charges',
      'eddv',
      'erp',
      'etude_geotechnique',
      'external_georisque',
      'lotissement_certificat_non_opposition',
      'mandat_bail',
      'plan_lotissement',
      'reglement_lotissement',
      'taxe_fonciere'
    ],
    documentsToInclude: [
      'assainissement_non_collectif_controle_libre',
      'assurance_do_initial',
      'attestation_diagnostiqueur',
      'attestation_hebergement',
      'attestation_surface',
      'autorisation_location',
      'avis_imposition',
      'bulletin_salaire',
      'carnet_information_construction_document',
      'carte_etudiant',
      'carte_professionnelle',
      'cheminee_facture_ramonage',
      'contrat_attaches_list_contrat_attaches_document',
      'contrat_travail',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'convention_stage',
      'declaration_prealable_location',
      'dernieres_quittances_loyer',
      'derniers_bilans',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'dpe',
      'dpe_audit_document',
      'etude_geotechnique',
      'facture_eau_electricite',
      'facture_ramonage',
      'justificatif_allocation',
      'justificatif_autre',
      'justificatif_bourse',
      'justificatif_fonctionnaire',
      'justificatif_immatriculation',
      'justificatif_militaire',
      'justificatif_pension',
      'justificatif_rentier',
      'justificatif_retraite',
      'location_bail_liste_contrat_bail',
      'location_bail_liste_etat_des_lieux_document',
      'location_bail_liste_quittance_loyer',
      'location_bail_liste_quittances_loyer',
      'mandat_bail',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'piscine_note_securite_document',
      'piscine_securite_attestation_document',
      'pv_ag',
      'saisonnier_etat_descriptif',
      'servitude_acte',
      'taxe_fonciere',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION',
  label: '3G Immo - Location Habitation',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__LOCATION',
  specificTypes: ['TROIS_G_IMMO', 'TROIS_G_IMMO_LOCATION_HABITATION'],
  type: 'OPERATION'
};
