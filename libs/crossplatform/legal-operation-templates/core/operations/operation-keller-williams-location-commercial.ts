// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationKellerWilliamsLocationCommercial: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ adresse }} / {{ nom_bailleur }} / {{ nom_locataire }}',
    emailLabelPattern: '{{ adresse }}',
    tags: {
      adresse: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES',
        questionId: 'adresse',
        format: 'ADDRESS',
        max: 1,
        label: 'Adresse'
      },
      nom_bailleur: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom bailleur'
      },
      nom_locataire: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom locataire'
      },
      biens_loues: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES',
        order: 0,
        max: 1
      },
      bailleurs: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        order: 1,
        max: 1
      },
      locataires: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_COMMERCIAL', 'BIENS_LOUES_COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_COMMERCIAL', 'BAIL_COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'BAILLEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'LOCATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'GARANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_DIP_COMMERCIAL',
        label: "Document d'informations précontractuelles"
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_LOCATION_COMMERCIAL_KELLER',
        label: 'Mandat de location - Local Commercial'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_LOCATION_AVENANT_COMMERCIAL_KELLER',
        label: 'Mandat de location - Local Commercial - Avenant'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_COMMERCIAL_KELLER',
        label: 'Mandat de recherche - Local Commercial'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_AVENANT_COMMERCIAL_KELLER',
        label: 'Mandat de recherche - Local Commercial - Avenant'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_LOCATION_PRO_KELLER',
        label: 'Mandat de location - Local Professionnel'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_LOCATION_AVENANT_PRO_KELLER',
        label: 'Mandat de location - Local Professionnel - Avenant'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_PRO_KELLER',
        label: 'Mandat de recherche - Local Professionnel'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_AVENANT_PRO_KELLER',
        label: 'Mandat de recherche - Local Professionnel - Avenant'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_CESSION_BAIL_KELLER',
        label: 'Mandat - Cession de bail'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_CESSION_BAIL_AVENANT_KELLER',
        label: 'Mandat  - Cession de bail - Avenant'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_BAIL_KELLER',
        label: 'Mandat de recherche - Cession de Bail'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_BAIL_AVENANT_KELLER',
        label: 'Mandat de recherche - Cession de Bail - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_DELEGATION_COMMERCIAL',
        label: 'Délégation de mandat - commercial'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_DELEGATION_PROFESSIONNEL',
        label: 'Délégation de mandat - professionnel'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_OFFRE_LOCATION',
        label: 'Offre de Prise à Bail'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_OFFRE_CESSION_BAIL',
        label: "Offre d'achat - Cession de Bail"
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_COMMERCIAL',
        label: 'Bail Commercial'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_COMMERCIAL_AVENANT',
        label: 'Bail Commercial - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_CAUTIONNEMENT_COMMERCIAL',
        label: 'Cautionnement Commercial'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PROFESSIONNEL',
        label: 'Bail Professionnel'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PROFESSIONNEL_AVENANT',
        label: 'Bail Professionnel - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_CAUTIONNEMENT_PROFESSIONNEL',
        label: 'Cautionnement Professionnel'
      },
      {
        id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_TRACFIN_LOCATION',
        label: "Fiche d'Analyse TRACFIN"
      }
    ],
    documentsToExclude: [],
    documentsToInclude: [
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_dommage_construction',
      'attestation_diagnostiqueur',
      'cartographie_kw',
      'certificat_conformite_construction',
      'cheminee_facture_ramonage',
      'construction_particulier_assurance_dommage',
      'contrat_attaches_list_contrat_attaches_document',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'declaration_achevement_construction',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_diagnostic_dtg',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'dip_kw',
      'dpe',
      'factures_entreprises_construction',
      'gestion_lotissement_gestion_lotissement_autre_statuts',
      'gestion_lotissement_gestion_lotissement_statuts_aful',
      'gestion_lotissement_gestion_lotissement_statuts_asl',
      'gestion_volume_gestion_volume_autre_statuts',
      'gestion_volume_gestion_volume_statuts_aful',
      'gestion_volume_gestion_volume_statuts_asl',
      'kw_formulaire_retractation',
      'location_bail_liste_contrat_bail',
      'location_bail_liste_etat_des_lieux_document',
      'location_bail_liste_quittances_loyer',
      'mandat_bail',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'permis_construire_construction',
      'pv_ag',
      'pv_ag_liste_pv_ag_doc',
      'servitude_acte',
      'taxe_fonciere',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_certificat_conformite',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_permis_construire',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__KELLER_WILLIAMS__LOCATION_COMMERCIAL',
  label: 'Location - Bien Professionnel - Keller Williams',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL',
  specificTypes: ['KELLER_WILLIAMS', 'LOCATION_COMMERCIAL'],
  type: 'OPERATION'
};
