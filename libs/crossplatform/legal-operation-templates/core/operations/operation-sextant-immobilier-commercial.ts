// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationSextantImmobilierCommercial: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    labelPattern: '{{ adresse }} / {{ nom_vendeur }} / {{ nom_acquereur }}',
    emailLabelPattern: '{{ adresse }}',
    tags: {
      adresse: {
        link: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
        questionId: 'adresse',
        format: 'ADDRESS',
        max: 1,
        label: 'Adresse'
      },
      nom_vendeur: {
        link: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom vendeur'
      },
      nom_acquereur: {
        link: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
        order: 0,
        max: 1
      },
      vendeurs: {
        link: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
        order: 1,
        max: 1
      },
      acquereurs: {
        link: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VENDEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'ACQUEREURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'REPRESENTANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'ACQUEREUR_CESSIONNAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'BIENS_VENDUS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_ANCIEN', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENCE_DELEGATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'NOTAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_MANDAT_DIP',
        label: "Document d'Informations Précontractuelles"
      },
      {
        id: 'SEXTANT__TRANSACTION__MANDAT_DE_VENTE_LMNP',
        label: 'Mandat de vente Exclusif - LMNP'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_MANDAT',
        label: 'Mandat de vente - Local commercial ou professionnel'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
        label: 'Mandat de vente - Local commercial ou professionnel - Avenant'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_MANDAT_RECHERCHE',
        label: 'Mandat de Recherche - Local commercial ou professionnel'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_MANDAT_RECHERCHE_AVENANT',
        label: 'Mandat de Recherche - Local commercial ou professionnel - Avenant'
      },
      {
        id: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_VENTE_FONDS_COMMERCE',
        label: 'Mandat de vente - Fonds de commerce ou Titres'
      },
      {
        id: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_VENTE_FONDS_COMMERCE_AVENANT',
        label: 'Mandat de vente - Fonds de commerce ou Titres - Avenant'
      },
      {
        id: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_RECHERCHE_FONDS_COMMERCE',
        label: 'Mandat de Recherche - Fonds de commerce ou Titres'
      },
      {
        id: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_RECHERCHE_FONDS_COMMERCE_AVENANT',
        label: 'Mandat de Recherche - Fonds de commerce ou Titres - Avenant '
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_DELEGATION_MANDAT',
        label: 'Délégation de Mandat'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_AVIS_DE_VALEUR',
        label: 'Avis de Valeur'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
        label: 'Bon de visite'
      },
      {
        id: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_OFFRE_VENTE_COMMERCIAL',
        label: "Offre d'achat"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_PROCURATION_VENTE',
        label: 'Procuration pour vendre'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_PROCURATION_ACQUERIR',
        label: 'Procuration pour acquérir'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
        label: 'Synthèse - Transmission notaire'
      },
      {
        id: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
        label: 'Compromis - Bien Professionnel'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_AVENANT',
        label: 'Avenant au compromis'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_RECONNAISSANCE_HONORAIRES',
        label: "Reconnaissance d'honoraires"
      },
      {
        id: 'SEXTANT_IMMOBILIER_FICHES_TRACFIN_VENDEUR',
        label: 'Fiche LCB/FT (Tracfin) - Vendeur'
      },
      {
        id: 'SEXTANT_IMMOBILIER_FICHES_TRACFIN_ACQUEREUR',
        label: 'Fiche LCB/FT (Tracfin) - Acquéreur '
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'ACQUEREUR'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
          indirectLinks: ['SITUATION_MARITALE__MARIAGE', 'SITUATION_MARITALE__DIVORCE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__NOTAIRES',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'BIEN'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
          directRecords: [
            'BIEN__INDIVIDUEL_HABITATION',
            'BIEN__INDIVIDUEL_HORS_HABITATION',
            'BIEN__TERRAIN_CONSTRUCTIBLE',
            'BIEN__TERRAIN_NON_CONSTRUCTIBLE',
            'BIEN__MONOPROPRIETE_HABITATION',
            'BIEN__MONOPROPRIETE_HORS_HABITATION',
            'BIEN__LOT_HABITATION',
            'BIEN__LOT_HORS_HABITATION'
          ]
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'LOTISSEMENT'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
          indirectLinks: ['COMPOSITION__LOTISSEMENT__LOTISSEMENT'],
          indirectRecords: ['STRUCTURE__LOTISSEMENT']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'VENDEUR'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
          indirectLinks: [
            'CAPACITE__TUTELLE',
            'CAPACITE__MINORITE',
            'CAPACITE__MANDAT_PROTECTION_FUTURE',
            'CAPACITE__HABILITATION_FAMILIALE',
            'SITUATION_MARITALE__MARIAGE'
          ]
        }
      }
    ],
    hiddenPages: {
      documents: true
    },
    documentsToExclude: [],
    documentsToInclude: ['retractation_sextant', 'dip_sextant']
  },
  id: 'OPERATION__SEXTANT__IMMOBILIER_COMMERCIAL',
  label: 'Dossier de Vente - Commercial / Professionnel - Sextant',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL',
  specificTypes: ['SEXTANT', 'IMMOBILIER_COMMERCIAL'],
  type: 'OPERATION'
};
