// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationErigereErigereVefaReservation: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    labelPattern: '{{ numero_lot }} / {{ programme_nom }} /  {{ reservataire }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__FICHE_PROGRAMME',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      reservataire: {
        link: 'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Réservataire'
      },
      biens_vendus: {
        link: 'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS',
        order: 0,
        max: 1
      },
      notaires: {
        link: 'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__NOTAIRE_PROGRAMME',
        order: 1,
        max: 1
      },
      commercialisateurs: {
        link: 'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__REPRESENTANT_PROMOTEURS',
        order: 3,
        max: 5,
        group: 0
      },
      reservataires: {
        link: 'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
        order: 2,
        max: 1
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_RESERVATION', 'RESERVATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_RESERVATION', 'CONTRAT_RESERVATION'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_RESERVATION', 'NOTAIRE_RESERVATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'PROMOTEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'REPRESENTANT_PROMOTEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'COOPERATIVE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_RESERVATION', 'COMMERCIALISATEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_RESERVATION', 'REPRESENTANT_COMMERCIALISATEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__FICHE_PROGRAMME',
      'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__PROMOTEURS',
      'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__REPRESENTANT_PROMOTEURS',
      'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__NOTAIRE_PROGRAMME',
      'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__COOPERATIVE'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'VENTES_ERIGERE']
      }
    ],
    contracts: [
      {
        id: 'ERIGERE__VEFA__CONTRAT_DE_RESERVATION',
        label: 'Contrat de Réservation'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      '3f_acquereur_social_information',
      '3f_acquereur_social_proposition_achat',
      '3f_bulletin_paie',
      '3f_justificatif',
      'fiscal_n_2',
      'notice_descriptive',
      'plan_etages',
      'plan_lot',
      'plan_masse',
      'plan_sous_sol',
      'plans_lots_copropriete',
      'programme_plan'
    ]
  },
  id: 'OPERATION__ERIGERE__ERIGERE_VEFA_RESERVATION',
  label: 'Erigère - VEFA Réservation',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_NEUF',
  specificTypes: ['ERIGERE', 'ERIGERE_VEFA_RESERVATION'],
  type: 'OPERATION'
};
