// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationI3FImmobilierBrsPreliminaire: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    hiddenPages: {
      documents: true
    },
    labelPattern: '{{ numero_lot }} / {{ programme_nom }} /  {{ nom_acquereur }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__FICHE_PROGRAMME',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS',
        order: 0,
        max: 1
      },
      bailleurs: {
        link: 'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__BAILLEUR',
        order: 1,
        max: 1
      },
      commercialisateurs: {
        link: 'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__REPRESENTANT_COMMERCIALISATEUR',
        order: 3,
        max: 5,
        group: 0
      },
      acquereurs: {
        link: 'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PRELIMINAIRE', 'ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PRELIMINAIRE', 'CONTRAT_PRELIMINAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PRELIMINAIRE', 'NOTAIRE_ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'COOPERATIVE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PRELIMINAIRE', 'COMMERCIALISATEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PRELIMINAIRE', 'REPRESENTANT_COMMERCIALISATEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__FICHE_PROGRAMME',
      'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__BAILLEUR',
      'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__REPRESENTANT_BAILLEUR',
      'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__NOTAIRE_PROGRAMME',
      'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__COOPERATIVE'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'VENTES']
      }
    ],
    contracts: [
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_CONTRAT_RESERVATION_BRS',
        label: 'Contrat Préliminaire - Bail Réel Solidaire'
      },
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_CONTRAT_PRELIMINAIRE_BRS_V2',
        label: 'Contrat Préliminaire BRS - sur VEFA'
      },
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_BRS_PLESSIS',
        label: 'Contrat Préliminaire - BRS Modèle Plessis Trévise'
      },
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_BON_SOUSCRIPTION',
        label: 'Bulletin de souscription (un par foyer)'
      },
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_CONVENTION_HONORAIRES',
        label: "Convention d'honoraires / Lettre de mission"
      },
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_LETTRE_DE_MISSION_PLESSIS',
        label: 'Lettre de mission - Modèle Plessis Trévise'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      '3f_acquereur_social_information',
      '3f_acquereur_social_proposition_achat',
      '3f_bulletin_paie',
      '3f_justificatif',
      'fiscal_n_2',
      'notice_descriptive',
      'plan_exposition_bruit_libre',
      'programme_plan'
    ]
  },
  id: 'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE',
  label: '3F - Contrat préliminaire BRS',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE',
  specificTypes: ['I3F', 'IMMOBILIER', 'BRS_PRELIMINAIRE'],
  type: 'OPERATION'
};
