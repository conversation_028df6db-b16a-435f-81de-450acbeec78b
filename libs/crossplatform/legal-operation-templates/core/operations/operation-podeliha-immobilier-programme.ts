// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationPodelihaImmobilierProgramme: LegalOperationTemplate = {
  config: {
    tags: {
      OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__REPRESENTANT_PROMOTEUR: {
        order: 0,
        max: 1,
        group: 0
      },
      OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME: {
        order: 1,
        max: 1,
        group: 0
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'PROMOTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'REPRESENTANT_PROMOTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'FICHE_VEFA'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'FICHE_PSLA'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'COPROPRIETE'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 10
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'LOTS'],
        creation: {
          autoCreate: true
        },
        restrictNewLinkToParentOperation: true,
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [
      {
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 999
        },
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'VENTES']
      }
    ],
    subOperations: 'Ventes',
    synthesis: {
      tables: [
        {
          id: 'LOTS',
          title: 'Grille de lots',
          titleWithArticle: 'un lot',
          linkSpecificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'LOTS'],
          type: 'PRIMARY',
          columns: [
            {
              label: 'N° de lot',
              property: 'numero_lot.value',
              bold: true,
              responsive: 'TITLE'
            },
            {
              label: 'N° de commercialisation',
              property: 'numero_commercialisation_lot.value',
              responsive: 'CONTENT'
            },
            {
              label: 'Type',
              property: 'nature_bien.value',
              format: 'SELECT',
              responsive: 'TITLE'
            },
            {
              label: 'Prix',
              property: 'programme_prix_vente_ttc.value',
              format: 'PRICE',
              responsive: 'CONTENT'
            },
            {
              label: 'Reservation',
              source: 'RESERVATION',
              property: 'status',
              responsive: 'CONTENT'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HABITATION']
            },
            {
              path: 'links.OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HORS_HABITATION']
            },
            {
              path: 'links.OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HABITATION']
            },
            {
              path: 'links.OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HORS_HABITATION']
            },
            {
              path: 'links.OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'TERRAIN_CONSTRUCTIBLE']
            }
          ]
        },
        {
          id: 'COPROS',
          title: 'Copropriétés',
          titleWithArticle: 'une copropriété',
          linkSpecificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'COPROPRIETE'],
          type: 'SECONDARY',
          columns: [
            {
              label: 'Dénomination',
              bold: true,
              property: 'denomination.value',
              responsive: 'TITLE'
            },
            {
              label: 'Adresse',
              property: 'adresse.value.formattedAddress',
              responsive: 'TITLE'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*.links.COMPOSITION__COPROPRIETE.\\d*.link.branches.CONTENU.\\d*.to.\\d*',
              specificType: ['STRUCTURE', 'COPROPRIETE']
            }
          ]
        }
      ]
    },
    contractModels: [
      {
        id: 'PODELIHA_IMMOBILIER_VEFA_RESERVATION_CONTRAT_RESERVATION_PODELIHA',
        label: "Contrat de réservation - Groupe d'habitation"
      },
      {
        id: 'PODELIHA_IMMOBILIER_PSLA_PRELIMINAIRE_PSLA_PRELIMINAIRE',
        label: 'Contrat Préliminaire - PSLA'
      },
      {
        id: 'PODELIHA_IMMOBILIER_TERRAIN_VENTE_TERRAIN_AMENAGEUR',
        label: 'Compromis Terrain - Aménageur'
      },
      {
        id: 'PODELIHA_IMMOBILIER_TERRAIN_VENTE_TERRAIN_VIABILISE',
        label: 'Compromis Terrain - Viabilisé'
      },
      {
        id: 'PODELIHA_IMMOBILIER_VEFA_RESERVATION_CONTRAT_RESERVATION_INDIVIDUEL_PODELIHA',
        label: 'Contrat de réservation - Bien Individuel'
      },
      {
        id: 'PODELIHA_IMMOBILIER_VENTE_CONTRAT_RESERVATION_TRANCHE_DEUX',
        label: "Contrat de réservation - Groupe d'habitation Tranche 2"
      },
      {
        id: 'PODELIHA_IMMOBILIER_VENTE_CONTRAT_RESERVATION_INDIVIDUEL_TRANCHE_2',
        label: 'Contrat de réservation - Bien Individuel - Tranche 2'
      },
      {
        id: 'PODELIHA__IMMOBILIER__BRS__CESSION_BRS',
        label: 'Contrat de cession partielle de VEFA BRS - Ideal'
      },
      {
        id: 'PODELIHA__IMMOBILIER__BRS__BRS_BARATERIE',
        label: 'Contrat de réservation BRS - Modèle Baraterie'
      }
    ],
    isParentOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'declaration_honneur',
      'dossier_communal',
      'georisques',
      'plan_lot',
      'programme_plan',
      'erp_2',
      'georisques_2',
      'dossier_communal_2'
    ]
  },
  id: 'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME',
  label: 'Podeliha - Programme',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTES_PROGRAMME',
  specificTypes: ['PODELIHA', 'IMMOBILIER', 'PROGRAMME'],
  type: 'OPERATION'
};
