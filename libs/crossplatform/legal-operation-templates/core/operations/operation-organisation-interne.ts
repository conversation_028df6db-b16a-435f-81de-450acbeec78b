// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationOrganisationInterne: LegalOperationTemplate = {
  config: {
    emailLabelPattern: '{{ nom_agence }}',
    labelPattern: '{{ nom_agence }}',
    tags: {
      nom_agence: {
        link: 'OPERATION__IMMOBILIER__VENTE__AGENTS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Dénomination Agence'
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_ANCIEN', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'ORGANISATION_INTERNE_PROTOCOLE_INTERNE_TRACFIN',
        label: 'Cadre Général - Protocole Interne TRACFIN'
      },
      {
        id: 'ORGANISATION_INTERNE_DECLARATION_TRACFIN_MANDATAIRE',
        label: 'Engagement du Négociateur - Protocole TRACFIN'
      }
    ],
    documentsToExclude: [],
    documentsToInclude: []
  },
  id: 'OPERATION__ORGANISATION_INTERNE',
  label: 'Organisation Interne - Agence',
  mynotaryTemplate: true,
  originTemplate: 'OPERATION__ORGANISATION_INTERNE',
  specificTypes: ['ORGANISATION_INTERNE'],
  type: 'OPERATION'
};
