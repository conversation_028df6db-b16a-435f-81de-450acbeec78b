// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationAquitanisProgrammeBrs: LegalOperationTemplate = {
  config: {
    hiddenPages: {
      documents: true
    },
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    tags: {
      OPERATION__AQUITANIS__PROGRAMME__LOTS: {
        order: 0,
        max: 1,
        group: 0
      },
      OPERATION__AQUITANIS__VENTE__ACQUEREUR: {
        order: 1,
        max: 1,
        group: 0
      },
      OPERATION__AQUITANIS__PROGRAMME__BAILLEUR_SOCIAL: {
        order: 2,
        max: 1,
        group: 0
      },
      OPERATION__AQUITANIS__PROGRAMME__NOTAIRE_PROGRAMME: {
        order: 3,
        max: 1,
        group: 0
      },
      OPERATION__AQUITANIS__PROGRAMME__REPRESENTANT_BAILLEUR: {
        order: 4,
        max: 1,
        group: 0
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'BAILLEUR_SOCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'FICHE_VENTE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'COPROPRIETE'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 10
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [
      {
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 999
        },
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'VENTES_BRS']
      }
    ],
    subOperations: 'Ventes',
    synthesis: {
      tables: [
        {
          id: 'LOTS',
          title: 'Grille de lots',
          titleWithArticle: 'un lot',
          linkSpecificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'LOTS'],
          type: 'PRIMARY',
          columns: [
            {
              label: 'N° de lot',
              property: 'numero_lot.value',
              bold: true,
              responsive: 'TITLE'
            },
            {
              label: 'Référence du lot',
              property: 'numero_commercialisation_lot.value',
              responsive: 'CONTENT'
            },
            {
              label: 'Superficie',
              property: 'programme_superficie.value'
            },
            {
              label: 'Batiment',
              property: 'programme_batiment.value'
            },
            {
              label: 'Etage',
              property: 'programme_niveau.value'
            },
            {
              label: 'Type',
              property: 'nature_bien.value',
              format: 'SELECT',
              responsive: 'CONTENT'
            },
            {
              label: 'Type Autre',
              property: 'nature_bien_autre.value',
              responsive: 'TITLE'
            },
            {
              label: 'Prix',
              property: 'programme_prix_vente_ttc.value',
              format: 'PRICE',
              responsive: 'CONTENT'
            },
            {
              label: 'Reservation',
              source: 'RESERVATION',
              property: 'status',
              responsive: 'CONTENT'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__AQUITANIS__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HABITATION']
            },
            {
              path: 'links.OPERATION__AQUITANIS__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HORS_HABITATION']
            },
            {
              path: 'links.OPERATION__AQUITANIS__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HABITATION']
            },
            {
              path: 'links.OPERATION__AQUITANIS__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HORS_HABITATION']
            }
          ]
        },
        {
          id: 'COPROS',
          title: 'Copropriétés',
          titleWithArticle: 'une copropriété',
          linkSpecificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'COPROPRIETE'],
          type: 'SECONDARY',
          columns: [
            {
              label: 'Dénomination',
              bold: true,
              property: 'denomination.value',
              responsive: 'TITLE'
            },
            {
              label: 'Adresse',
              property: 'adresse.value.formattedAddress',
              responsive: 'TITLE'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__AQUITANIS__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*.links.COMPOSITION__COPROPRIETE.\\d*.link.branches.CONTENU.\\d*.to.\\d*',
              specificType: ['STRUCTURE', 'COPROPRIETE']
            }
          ]
        }
      ]
    },
    contractModels: [
      {
        id: 'AQUITANIS__BRS__RESERVATION_BRS',
        label: 'Contrat de Réservation - Bail Réel Solidaire'
      },
      {
        id: 'AQUITANIS__TRANSACTION__ATTESTATION_ALUR',
        label: 'Attestation ALUR'
      },
      {
        id: 'AQUITANIS__BRS__CONVENTION_HONORAIRES',
        label: "Convention d'honoraires"
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__AQUITANIS__VENTE__NOTAIRE_ACQUEREUR',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__AQUITANIS__PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isParentOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'fiscal_n_2',
      'notice_descriptive',
      'programme_plan',
      'plan_exposition_bruit_libre',
      'programme_aquitanis_demande_agrement_modele',
      'programme_aquitanis_pieces_agrement_modele',
      'detecteur_fumee_attestation',
      'servitude_acte',
      'document_pre_etat_date'
    ]
  },
  id: 'OPERATION__AQUITANIS__PROGRAMME_BRS',
  label: 'Aquitanis - Programme BRS',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__BRS_PROGRAMME_HYBRIDE',
  specificTypes: ['AQUITANIS', 'PROGRAMME_BRS'],
  type: 'OPERATION'
};
