// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationPvciSensImmobilierProgrammeAcheve: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    hiddenPages: {
      documents: true
    },
    tags: {},
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'LOTS_PVCI_SENS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'COPROPRIETE'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 10
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      }
    ],
    operationLinks: [
      {
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 999
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'VENTE_ACHEVE_PVCI_SENS']
      }
    ],
    subOperations: 'Réservations',
    synthesis: {
      tables: [
        {
          id: 'LOTS',
          title: 'Grille de lots',
          titleWithArticle: 'un lot',
          linkSpecificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'LOTS_PVCI_SENS'],
          type: 'PRIMARY',
          columns: [
            {
              label: 'N° de lot',
              property: 'numero_lot.value',
              bold: true,
              responsive: 'TITLE'
            },
            {
              label: 'Nature',
              property: 'nature_bien_leizee.value',
              format: 'SELECT',
              responsive: 'CONTENT'
            },
            {
              label: 'Prix',
              property: 'programme_prix_vente_ttc.value',
              format: 'PRICE',
              responsive: 'CONTENT'
            },
            {
              label: 'Reservation',
              source: 'RESERVATION',
              property: 'status',
              responsive: 'CONTENT'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_PVCI_SENS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HABITATION']
            },
            {
              path: 'links.OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_PVCI_SENS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HORS_HABITATION']
            }
          ]
        },
        {
          id: 'COPROS',
          title: 'Copropriétés',
          titleWithArticle: 'une copropriété',
          linkSpecificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'COPROPRIETE'],
          type: 'SECONDARY',
          columns: [
            {
              label: 'Dénomination',
              bold: true,
              property: 'denomination.value',
              responsive: 'TITLE'
            },
            {
              label: 'Adresse',
              property: 'adresse.value.formattedAddress',
              responsive: 'TITLE'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_PVCI_SENS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*.links.COMPOSITION__COPROPRIETE.\\d*.link.branches.CONTENU.\\d*.to.\\d*',
              specificType: ['STRUCTURE', 'COPROPRIETE']
            }
          ]
        }
      ]
    },
    contractModels: [
      {
        id: 'PVCI_SENS_IMMOBILIER_VENTE_ACHEVE_MANDAT_PROGRAMME',
        label: 'Mandat de Vente'
      },
      {
        id: 'PVCI_SENS_IMMOBILIER_VENTE_ACHEVE_MANDAT_AVENANT',
        label: 'Mandat de Vente - Avenant'
      },
      {
        id: 'PVCI_SENS_IMMOBILIER_VENTE_ACHEVE_VENTE_PROGRAMME',
        label: 'Compromis de vente - Achevé'
      },
      {
        id: 'PVCI_SENS_IMMOBILIER_VENTE_ACHEVE_COMPROMIS_RECHERCHE',
        label: 'Compromis de Vente - Achevé - Mandat de recherche'
      },
      {
        id: 'PVCI_SENS_IMMOBILIER_VENTE_ACHEVE_COMPROMIS_AVENANT',
        label: 'Compromis de Vente - Avenant'
      }
    ],
    isParentOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_dommage_construction',
      'attestation_diagnostiqueur',
      'certificat_conformite_construction',
      'cheminee_facture_ramonage',
      'contrat_attaches_list_contrat_attaches_document',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_diagnostic_dtg',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'dpe',
      'gestion_lotissement_gestion_lotissement_autre_statuts',
      'gestion_lotissement_gestion_lotissement_statuts_aful',
      'gestion_lotissement_gestion_lotissement_statuts_asl',
      'gestion_volume_gestion_volume_autre_statuts',
      'gestion_volume_gestion_volume_statuts_aful',
      'gestion_volume_gestion_volume_statuts_asl',
      'location_bail_liste_contrat_bail',
      'location_bail_liste_etat_des_lieux_document',
      'location_bail_liste_quittances_loyer',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'permis_construire_construction',
      'pv_ag',
      'pv_ag_liste_pv_ag_doc',
      'servitude_acte',
      'taxe_fonciere',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_certificat_conformite',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_permis_construire',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE',
  label: 'Pierre et Sens Développement - Programme Achevé',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__SOCIAL_PROGRAMME',
  specificTypes: ['PVCI_SENS', 'IMMOBILIER', 'PROGRAMME_ACHEVE'],
  type: 'OPERATION'
};
